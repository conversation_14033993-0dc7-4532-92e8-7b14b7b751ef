import '@testing-library/jest-dom/vitest';
import { render, screen } from '@testing-library/react';
import { describe, expect, it } from 'vitest';
import { LoadingPanel } from './LoadingPanel';
import loadingPanelClasses from './LoadingPanel.classes';

describe('LoadingPanel', () => {
  it('should correctly render root element', () => {
    render(<LoadingPanel />);
    expect(screen.getByTestId(loadingPanelClasses.root)).toBeInTheDocument();
  });

  it('should correctly render loading title', () => {
    const { rerender } = render(<LoadingPanel />);
    expect(screen.queryByTestId(loadingPanelClasses.loadingTitle)).not.toBeInTheDocument();

    rerender(<LoadingPanel loadingTitle="TestLoadingTitle" />);
    expect(screen.getByTestId(loadingPanelClasses.loadingTitle).textContent).toBe('TestLoadingTitle');

    rerender(<LoadingPanel loadingTitle={<p>TestLoadingTitle</p>} />);
    expect(screen.getByText('TestLoadingTitle')).toBeInTheDocument();
  });

  it('should correctly render loading message', () => {
    const { rerender } = render(<LoadingPanel />);
    expect(screen.queryByTestId(loadingPanelClasses.loadingMessage)).not.toBeInTheDocument();

    rerender(<LoadingPanel loadingMessage="TestLoadingMessage" />);
    expect(screen.getByTestId(loadingPanelClasses.loadingMessage).textContent).toBe('TestLoadingMessage');

    rerender(<LoadingPanel loadingMessage={<p>TestLoadingMessage</p>} />);
    expect(screen.getByText('TestLoadingMessage')).toBeInTheDocument();
  });

  it('should correctly render loader progress', () => {
    const { rerender } = render(<LoadingPanel />);
    expect(screen.queryByTestId(loadingPanelClasses.progress)).toBeInTheDocument();

    rerender(<LoadingPanel loader={<p>TestLoader</p>} />);
    expect(screen.getByText('TestLoader')).toBeInTheDocument();
  });
});
