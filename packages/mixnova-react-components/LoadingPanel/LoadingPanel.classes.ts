import generateUtilityClasses from '@mui/utils/generateUtilityClasses';

export interface LoadingPanelClasses {
  /** Class name applied to the root element. */
  root: string;
  /** Class name applied to the loading title element. */
  loadingTitle: string;
  /** Class name applied to the loading message element. */
  loadingMessage: string;
  /** Class name applied to the loader progress element. */
  progress: string;
}

const loadingPanelClasses: LoadingPanelClasses = generateUtilityClasses('MixNovaLoadingPanel', [
  'root',
  'loadingTitle',
  'loadingMessage',
  'progress',
]);

export default loadingPanelClasses;
