import { NovaTheme } from '@hxnova/themes';
import { extendTheme, pigment } from '@pigment-css/vite-plugin';
import react from '@vitejs/plugin-react';
import { defineConfig } from 'vitest/config';

export default defineConfig({
  plugins: [
    react(),
    pigment({
      theme: extendTheme(NovaTheme),
      transformLibraries: ['@hxnova/react-components'],
      babelOptions: {
        plugins: [`@babel/plugin-transform-export-namespace-from`],
      },
    }),
  ],
  test: {
    environment: 'jsdom',
    setupFiles: './vitest.setup.ts',
    coverage: {
      provider: 'v8',
      reporter: ['cobertura', 'html'],
      reportsDirectory: '../../coverage',
    },
    reporters: ['default', ['junit', { outputFile: '../../coverage/junit.xml' }]],
  },
});
