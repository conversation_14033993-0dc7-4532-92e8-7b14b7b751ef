'use client';

import { Button } from '@hxnova/react-components/Button';
import { CardContent, CardRoot } from '@hxnova/react-components/Card';
import { Skeleton } from '@hxnova/react-components/Skeleton';
import { Typography } from '@hxnova/react-components/Typography';
import { styled } from '@pigment-css/react';
import { forwardRef, useMemo } from 'react';
import { CardLoadingManager } from '../Card/components/CardLoadingManager';
import { useCardActions } from '../Card/hooks/useCardActions';
import { useCardLoading } from '../Card/hooks/useCardLoading';
import { isReactNode } from '../utils/functions/isReactNode';
import plainCardClasses from './PlainCard.classes';
import { PlainCardProps } from './PlainCard.types';

type StyledSlotProps = {
  cardColor?: PlainCardProps['color'];
  dense?: PlainCardProps['dense'];
};
const shouldForwardProp = (prop: string) => prop !== 'cardColor' && prop !== 'dense';

const StyledIconContainer = styled('div', {
  shouldForwardProp,
})<StyledSlotProps>(({ theme }) => ({
  marginBottom: 8,
  variants: [
    {
      props: { cardColor: 'neutral' },
      style: {
        color: theme.vars.palette.primary,
      },
    },
    {
      props: { cardColor: 'success' },
      style: {
        color: theme.vars.palette.system.onSuccessContainer,
      },
    },
    {
      props: { cardColor: 'info' },
      style: {
        color: theme.vars.palette.system.onInfoContainer,
      },
    },
    {
      props: { cardColor: 'primary' },
      style: {
        color: theme.vars.palette.onPrimaryContainer,
      },
    },
    {
      props: { dense: true },
      style: {
        fontSize: 24,
      },
    },
    {
      props: { dense: false },
      style: {
        fontSize: 32,
      },
    },
  ],
}));

const StyledCardRoot = styled(CardRoot, {
  shouldForwardProp,
})<StyledSlotProps>(({ theme }) => ({
  maxWidth: '100%',
  variants: [
    {
      props: { dense: true },
      style: {
        padding: `${theme.vars.sys.viewport.spacing.padding.topBottom.md} ${theme.vars.sys.viewport.spacing.padding.leftRight.lg}`,
        width: 260,
      },
    },
    {
      props: { dense: false },
      style: {
        padding: `${theme.vars.sys.viewport.spacing.padding.topBottom.lg} ${theme.vars.sys.viewport.spacing.padding.leftRight.xl}`,
        width: 360,
      },
    },
    {
      props: { cardColor: 'neutral' },
      style: {
        backgroundColor: theme.vars.palette.surfaceContainer,
        color: theme.vars.palette.onSurface,
      },
    },
    {
      props: { cardColor: 'success' },
      style: {
        backgroundColor: theme.vars.palette.system.successContainer,
        color: theme.vars.palette.onSurface,
      },
    },
    {
      props: { cardColor: 'info' },
      style: {
        backgroundColor: theme.vars.palette.system.infoContainer,
        color: theme.vars.palette.onSurface,
      },
    },
    {
      props: { cardColor: 'primary' },
      style: {
        backgroundColor: theme.vars.palette.system.onInfoContainer,
        color: theme.vars.palette.onPrimaryContainer,
      },
    },
  ],
}));

const StyledButton = styled(Button, {
  shouldForwardProp,
})<StyledSlotProps>(({ theme }) => ({
  variants: [
    {
      props: (props) => props.cardColor === 'neutral' && !props.disabled,
      style: {
        backgroundColor: theme.vars.palette.primary,
        color: theme.vars.palette.onPrimary,
      },
    },
    {
      props: (props) => props.cardColor === 'success' && !props.disabled,
      style: {
        backgroundColor: theme.vars.palette.system.onSuccessContainer,
        color: theme.vars.palette.onPrimary,
        '&:hover': {
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.system.onSuccessContainer}, ${theme.vars.palette.system.successContainer} ${theme.vars.palette.stateLayers.hoverOnSurface})`,
        },
        '&:focus-visible': {
          outline: `2px solid ${theme.vars.palette.system.onSuccessContainer}`,
          outlineOffset: 2,
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.system.onSuccessContainer}, ${theme.vars.palette.system.successContainer} ${theme.vars.palette.stateLayers.focusOnSurface})`,
        },
        '&:active': {
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.system.onSuccessContainer}, ${theme.vars.palette.system.successContainer} ${theme.vars.palette.stateLayers.pressOnSurface})`,
        },
      },
    },
    {
      props: (props) => props.cardColor === 'info' && !props.disabled,
      style: {
        backgroundColor: theme.vars.palette.system.onInfoContainer,
        color: theme.vars.palette.onPrimary,
      },
    },
    {
      props: (props) => props.cardColor === 'primary' && !props.disabled,
      style: {
        backgroundColor: theme.vars.palette.surfaceContainer,
        color: theme.vars.palette.onSurface,
        '&:hover': {
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.surfaceContainer}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.hoverOnSurface})`,
        },
        '&:focus-visible': {
          outline: `2px solid ${theme.vars.palette.surfaceContainer}`,
          outlineOffset: 2,
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.surfaceContainer}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.focusOnSurface})`,
        },
        '&:active': {
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.surfaceContainer}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.pressOnSurface})`,
        },
      },
    },
  ],
}));

const StyledContentContainer = styled('div', {
  shouldForwardProp,
})<StyledSlotProps>(({ theme }) => ({
  variants: [
    {
      props: { dense: true },
      style: {
        marginBottom: theme.vars.sys.viewport.spacing.spaceBetween.vertical.lg,
      },
    },
    {
      props: { dense: false },
      style: {
        marginBottom: 48,
      },
    },
  ],
}));

export const PlainCard = forwardRef<HTMLDivElement, PlainCardProps>((props, ref) => {
  const {
    color = 'neutral',
    dense = false,
    headline,
    content,
    icon,
    actions,
    actionButtons,
    loading = false,
    loadingMode = 'spinner',
    ...restProps
  } = props;

  const defaultButtons = actionButtons?.map((button, index) =>
    isReactNode(button) ? (
      button
    ) : (
      <StyledButton
        key={`${plainCardClasses.action}-${index}`}
        className={plainCardClasses.action}
        data-testid={plainCardClasses.action}
        cardColor={color}
        variant="filled"
        size={dense ? 'small' : 'medium'}
        {...button}
      />
    ),
  );

  const renderIcon = useMemo(() => {
    if (!icon) return null;
    return (
      <StyledIconContainer
        cardColor={color}
        dense={dense}
        className={plainCardClasses.icon}
        data-testid={plainCardClasses.icon}
      >
        {icon}
      </StyledIconContainer>
    );
  }, [icon, color, dense]);

  const renderHeadline = useMemo(() => {
    if (!headline) return null;
    return (
      <div
        className={plainCardClasses.headlineContainer}
        data-testid={plainCardClasses.headlineContainer}
        sx={(theme) => ({
          marginBottom: theme.vars.sys.viewport.spacing.spaceBetween.vertical.lg,
        })}
      >
        {typeof headline === 'string' ? (
          <Typography
            variant={dense ? 'titleMedium' : 'titleLarge'}
            className={plainCardClasses.headline}
            data-testid={plainCardClasses.headline}
            sx={{
              fontWeight: 400,
            }}
          >
            {headline}
          </Typography>
        ) : (
          headline
        )}
      </div>
    );
  }, [headline, dense]);

  const renderContent = useMemo(() => {
    if (!content) return null;
    return typeof content === 'string' ? (
      <StyledContentContainer
        className={plainCardClasses.contentContainer}
        data-testid={plainCardClasses.contentContainer}
        dense={dense}
      >
        <Typography
          variant={dense ? 'bodySmall' : 'bodyMedium'}
          className={plainCardClasses.content}
          data-testid={plainCardClasses.content}
        >
          {content}
        </Typography>
      </StyledContentContainer>
    ) : (
      content
    );
  }, [content, dense]);

  const { renderActions } = useCardActions({
    actions,
    actionButtons: defaultButtons,
    skip: !actions && !actionButtons,
    classes: plainCardClasses,
    padding: 0,
  });

  const { renderLoadingPanel } = useCardLoading(loading, loadingMode);

  return (
    <StyledCardRoot
      dense={dense}
      cardColor={color}
      ref={ref}
      className={plainCardClasses.root}
      data-testid={plainCardClasses.root}
      sx={{
        position: 'relative',
      }}
      {...restProps}
    >
      <CardLoadingManager loading={loading} loadingMode={loadingMode} skeleton={PlainCardSkeleton}>
        <CardContent
          sx={{
            padding: 0,
            gap: 0,
          }}
        >
          {renderIcon}
          {renderHeadline}
          {renderContent}
        </CardContent>
        {renderActions}
        {renderLoadingPanel}
      </CardLoadingManager>
    </StyledCardRoot>
  );
});

export const PlainCardSkeleton = (
  <div
    sx={{
      display: 'flex',
      flexDirection: 'column',
    }}
  >
    <Skeleton variant="rounded" width={40} height={40} />
    <Skeleton variant="rounded" width="70%" height={24} sx={{ marginTop: 10 }} />
    <Skeleton
      variant="rounded"
      width="100%"
      height={32}
      sx={(theme) => ({ marginBlock: theme.vars.sys.viewport.spacing.spaceBetween.vertical.lg })}
    />
    <Skeleton variant="rounded" width="25%" height={24} />
  </div>
);
