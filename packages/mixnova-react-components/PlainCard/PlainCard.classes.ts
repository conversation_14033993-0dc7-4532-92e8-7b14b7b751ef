import generateUtilityClasses from '@mui/utils/generateUtilityClasses';
import { CardActionsClasses } from '../Card/Card.classes';

export interface PlainCardClasses extends CardActionsClasses {
  /** Class name applied to the root element. */
  root: string;
  /** Class name applied to the icon element. */
  icon: string;
  /** Class name applied to the headline container element. */
  headlineContainer: string;
  /** Class name applied to the headline element. */
  headline: string;
  /** Class name applied to the content container element. */
  contentContainer: string;
  /** Class name applied to the content element.*/
  content: string;
}

const plainCardClasses: PlainCardClasses = generateUtilityClasses('MIxNovaPlainCard', [
  'root',
  'icon',
  'headlineContainer',
  'headline',
  'contentContainer',
  'content',
  'actionsContainer',
  'actionsStack',
  'action',
]);

export default plainCardClasses;
