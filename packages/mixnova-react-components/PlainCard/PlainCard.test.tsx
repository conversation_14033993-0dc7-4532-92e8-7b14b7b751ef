import { Button, buttonClasses } from '@hxnova/react-components/Button';
import { skeletonClasses } from '@hxnova/react-components/Skeleton';
import '@testing-library/jest-dom/vitest';
import { cleanup, render, screen } from '@testing-library/react';
import { afterEach, describe, expect, it } from 'vitest';
import { loadingPanelClasses } from '../LoadingPanel';
import { PlainCard } from './PlainCard';
import plainCardClasses from './PlainCard.classes';

afterEach(() => {
  cleanup();
});

describe('PlainCard', () => {
  it('should correctly render root element', () => {
    render(<PlainCard headline={undefined} />);
    expect(screen.getByTestId(plainCardClasses.root)).toBeInTheDocument();
  });

  it('should correctly render icon element', () => {
    const { rerender } = render(<PlainCard headline={undefined} icon={undefined} />);
    expect(screen.queryByTestId(plainCardClasses.icon)).not.toBeInTheDocument();

    rerender(<PlainCard headline={undefined} icon={<p>TestIcon</p>} />);
    expect(screen.getByTestId(plainCardClasses.icon).textContent).toBe('TestIcon');
  });

  it('should correctly render headline element', () => {
    const { rerender } = render(<PlainCard headline={undefined} />);
    expect(screen.queryByTestId(plainCardClasses.headlineContainer)).not.toBeInTheDocument();
    expect(screen.queryByTestId(plainCardClasses.headline)).not.toBeInTheDocument();

    rerender(<PlainCard headline="TestHeadline" />);
    expect(screen.getByTestId(plainCardClasses.headlineContainer).textContent).toBe('TestHeadline');
    expect(screen.getByTestId(plainCardClasses.headline).textContent).toBe('TestHeadline');

    rerender(<PlainCard headline={<p>TestHeadline</p>} />);
    expect(screen.getByText('TestHeadline')).toBeInTheDocument();
  });

  it('should correctly render content element', () => {
    const { rerender } = render(<PlainCard headline={undefined} content="" />);
    expect(screen.queryByTestId(plainCardClasses.contentContainer)).not.toBeInTheDocument();
    expect(screen.queryByTestId(plainCardClasses.content)).not.toBeInTheDocument();

    rerender(<PlainCard headline={undefined} content="TestContent" />);
    expect(screen.getByTestId(plainCardClasses.contentContainer).textContent).toBe('TestContent');
    expect(screen.getByTestId(plainCardClasses.content).textContent).toBe('TestContent');

    rerender(<PlainCard headline={undefined} content={<p>TestContent</p>} />);
    expect(screen.getByText('TestContent')).toBeInTheDocument();
  });

  it('should correctly render action elements', () => {
    const { rerender } = render(<PlainCard headline={undefined} />);
    expect(screen.queryByTestId(plainCardClasses.actionsContainer)).not.toBeInTheDocument();
    expect(screen.queryByTestId(plainCardClasses.actionsStack)).not.toBeInTheDocument();
    expect(screen.queryByTestId(plainCardClasses.action)).not.toBeInTheDocument();

    rerender(<PlainCard headline={undefined} actions={<p>TestAction</p>} />);
    expect(screen.getByTestId(plainCardClasses.actionsContainer).textContent).toBe('TestAction');
    expect(screen.getByTestId(plainCardClasses.actionsStack).textContent).toBe('TestAction');

    rerender(<PlainCard headline={undefined} actionButtons={[{ children: 'TestAction', variant: 'text' }]} />);
    const actionEl = screen.getByTestId(plainCardClasses.action);
    expect(screen.getByTestId(plainCardClasses.actionsContainer).textContent).toBe('TestAction');
    expect(screen.getByTestId(plainCardClasses.actionsStack).textContent).toBe('TestAction');
    expect(actionEl.textContent).toBe('TestAction');
    expect(actionEl).toHaveClass(buttonClasses.text);

    rerender(
      <PlainCard
        headline={undefined}
        actionButtons={[
          { children: 'ButtonOne' },
          <Button key="ButtonTwo">ButtonTwo</Button>,
          { children: 'ButtonThree', variant: 'text' },
          <button key="ButtonFour">ButtonFour</button>,
        ]}
      />,
    );
    expect(screen.getByText('ButtonOne')).toBeInTheDocument();
    expect(screen.getByText('ButtonTwo')).toBeInTheDocument();
    expect(screen.getByText('ButtonThree')).toBeInTheDocument();
    expect(screen.getByText('ButtonFour')).toBeInTheDocument();

    rerender(
      <PlainCard
        headline={undefined}
        actions={<p>TestActions</p>}
        actionButtons={[
          {
            children: 'This should not be displayed',
          },
        ]}
      />,
    );
    expect(screen.getByTestId(plainCardClasses.actionsContainer).textContent).toBe('TestActions');
    expect(screen.queryByTestId(plainCardClasses.action)).not.toBeInTheDocument();
  });

  it('should correctly render loading state', () => {
    const { rerender, container } = render(<PlainCard headline={undefined} loading loadingMode="spinner" />);
    expect(container.querySelector(`.${skeletonClasses.root}`)).not.toBeInTheDocument();
    expect(screen.getByTestId(loadingPanelClasses.root)).toBeInTheDocument();
    expect(screen.getByTestId(loadingPanelClasses.progress)).toBeInTheDocument();

    rerender(<PlainCard headline={undefined} loading loadingMode="skeleton" />);
    expect(container.querySelector(`.${skeletonClasses.root}`)).toBeInTheDocument();
    expect(screen.queryByTestId(loadingPanelClasses.root)).not.toBeInTheDocument();
    expect(screen.queryByTestId(loadingPanelClasses.progress)).not.toBeInTheDocument();
  });
});
