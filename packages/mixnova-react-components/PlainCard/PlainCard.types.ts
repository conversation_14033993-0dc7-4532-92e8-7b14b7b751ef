import { HTMLAttributes, ReactNode } from 'react';
import { CardActionsProps, CardLoadingProps } from '../Card/Card.types';

export type PlainCardProps = Omit<HTMLAttributes<HTMLDivElement>, 'content' | 'children'> &
  CardActionsProps &
  CardLoadingProps & {
    /**
     * The color palette / theme to use for the card background / text / buttons.
     * @default 'neutral'.
     */
    color?: 'neutral' | 'success' | 'info' | 'primary';

    /**
     * The icon to display above the headline in the top-left corner of the card
     */
    icon?: ReactNode;

    /**
     * The main text to display below the icon in the top-left corner of the card
     */
    headline: ReactNode;

    /**
     * The main body content to display in the card
     */
    content?: ReactNode;

    /**
     * If `true`, the spacing in the card will be condensed, width will be reduced, and the text size of heading will be smaller
     * @default false
     */
    dense?: boolean;
  };
