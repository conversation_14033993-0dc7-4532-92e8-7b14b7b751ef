import { useMemo } from 'react';
import { LoadingPanel } from '../../LoadingPanel';

export const useCardLoading = (loading?: boolean, loadingMode: 'skeleton' | 'spinner' = 'spinner') => {
  const renderLoadingPanel = useMemo(
    () => <LoadingPanel open={loading ?? false} sx={{ position: 'absolute', zIndex: 10 }} />,
    [loading],
  );

  return {
    renderLoadingPanel: loadingMode === 'spinner' ? renderLoadingPanel : null,
  };
};
