import { CardActions } from '@hxnova/react-components/Card';
import { ReactNode, useMemo } from 'react';
import { CardActionsClasses } from '../Card.classes';

export type CardActionsConfig = {
  padding?: number | string;
  skip?: boolean;
  extraActions?: ReactNode;
  classes: CardActionsClasses;
  actions?: ReactNode;
  actionButtons?: ReadonlyArray<ReactNode>;
};

export const useCardActions = (config: CardActionsConfig) => {
  const { actions, actionButtons, extraActions, padding = 0, skip = !actions && !actionButtons, classes } = config;

  const renderActions = useMemo(() => {
    if (skip) return null;

    const actionsData = actions ?? actionButtons;

    return (
      <CardActions
        sx={{
          padding,
          justifyContent: 'flex-start',
        }}
        className={classes.actionsContainer}
        data-testid={classes.actionsContainer}
      >
        <div
          className={classes.actionsStack}
          data-testid={classes.actionsStack}
          sx={(theme) => ({
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            gap: theme.vars.sys.viewport.spacing.spaceBetween.horizontal.md,
          })}
        >
          {actionsData}
          {extraActions}
        </div>
      </CardActions>
    );
  }, [skip, actions, actionButtons, extraActions, padding, classes]);

  return {
    renderActions,
  };
};
