parameters:
  - name: semVer
    type: string
  - name: sonar
    type: boolean
    default: true
  - name: sonarSources
    type: string

steps:
  - task: JavaToolInstaller@0
    inputs:
      versionSpec: '17' # 17 is a required version for SonarQube
      jdkArchitectureOption: 'x64'
      jdkSourceOption: 'PreInstalled'
  - task: SonarQubePrepare@7
    condition: and(succeeded(), eq(${{ parameters.sonar }}, true))
    inputs:
      SonarQube: 'SonarQube v2'
      scannerMode: 'CLI'
      configMode: 'manual'
      cliProjectKey: 'MIxNova'
      cliProjectVersion: ${{parameters.semVer}}
      cliSources: '.'
      extraProperties: |
        sonar.qualitygate.wait=true
        sonar.qualitygate.timeout=3600
        sonar.ws.timeout=900
        sonar.javascript.lcov.reportPaths=./reports/coverage/lcov.info
        sonar.junit.reportPaths=**/TEST-*.xml,**/jest-junit.xml
        sonar.sources=${{ parameters.sonarSources }}
        sonar.exclusions=**/dist/**,**/node_modules/**
        sonar.log.level=INFO

  - bash: |
      echo 'Before: $(SONARQUBE_SCANNER_PARAMS)'
      UPDATED_PARAMS=$(echo $SONARQUBE_SCANNER_PARAMS | sed 's/"sonar.pullrequest.[^"]*":"[^"]*"\,//g' | sed 's/"sonar.branch.[^"]*":"[^"]*"\,//g') 
      echo "##vso[task.setvariable variable=SONARQUBE_SCANNER_PARAMS]$UPDATED_PARAMS"
      echo 'After: $(SONARQUBE_SCANNER_PARAMS)'
    displayName: Avoid SonarQube PR analysis
    condition: and(succeeded(), eq(${{ parameters.sonar }}, true))

  - task: SonarQubeAnalyze@7
    inputs:
      jdkversion: 'JAVA_HOME_17_X64'
    condition: and(succeeded(), eq(${{ parameters.sonar }}, true))

  - task: SonarQubePublish@7
    condition: and(succeeded(), eq(${{ parameters.sonar }}, true))
    inputs:
      pollingTimeoutSec: '600'
