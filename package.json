{"name": "mixnova", "version": "0.0.0", "license": "MIT", "repository": {"type": "git", "url": "https://dev.azure.com/hexagonmi/MI-Genesis/_git/MIxNova"}, "scripts": {"lint": "eslint -c eslint.config.mjs --cache .", "prettier": "npx prettier --check .", "format": "npx prettier --write .", "test": "nx run @hxnova/mi-react-components:test", "test:ci": "nx run @hxnova/mi-react-components:test:ci", "coverage": "nx run @hxnova/mi-react-components:coverage", "prepare": "husky"}, "prettier": "@nexusui/prettier-config", "devDependencies": {"@commitlint/cli": "^19.1.0", "@commitlint/config-conventional": "^19.1.0", "@nexusui/prettier-config": "^1.0.0", "@testing-library/jest-dom": "^6.6.2", "@testing-library/react": "^16.0.1", "@testing-library/user-event": "^14.5.2", "@types/node": "^22.10.2", "@types/react": "^18.2.67", "@types/react-dom": "^18.2.67", "@vitest/coverage-v8": "3.2.0", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.34.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "eslint-plugin-storybook": "^0.8.0", "globals": "^13.0.0", "husky": "^9.1.7", "lint-staged": "^16.1.0", "nx": "21.1.2", "prettier": "^3.2.5", "react": "^18.2.0", "react-docgen-typescript": "^2.2.2", "typescript": "5.5.3", "typescript-eslint": "^7.3.1", "vitest": "^3.1.4"}, "lint-staged": {"**/*.{js,ts,tsx}": ["eslint -c eslint.config.mjs --cache ."], "**/*": "npx prettier --check ."}, "nx": {}, "packageManager": "pnpm@9.14.2+sha512.6540583f41cc5f628eb3d9773ecee802f4f9ef9923cc45b69890fb47991d4b092964694ec3a4f738a420c918a333062c8b925d312f42e4f0c263eb603551f977"}