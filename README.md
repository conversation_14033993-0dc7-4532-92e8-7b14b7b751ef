# MIxNova 

MIxNova 

# Organization

This repository is a pnpm monorepo with nx consisting of `packages` and `apps`.

```text
├── apps/
│   ├── nextjs-demo/                                 # Next.js app     
│   ├── storybook/                                   # Storybook app
│   └── vite-demo/                                   # Vite app                             
├── packages/                                        # Monorepo packages
│   ├── mixnova-react-components/                    # MIxNova Components package
├── nx.json
├── package.json
├── pnpm-workspace.yaml
```

## Packages

The following packages can be found in the `/packages` folder:

- **@hxnova/mi-react-components** ([`/mixnova-react-components`](/packages/mixnova-react-components/README.md)): These are the MI components developed based on Nova.


## Apps

The following packages can be found in the `/apps` folder:

- **NextJS Demo** (`/nextjs-demo`): A sample NextJS project that can be used to demonstrate and test out the packages.
- **Vite Demo** (`/vite-demo`): A sample Vite (with React) project that can be used to demonstrate and test out the packages.
- **Storybook** (`/storybook`): A storybook project used for component / theme testing and maintaining basic documentation.

# Maintainers

## Setup

Before starting, please check and set up the necessary development environment.

### Node

Ensure you have Node.js version 18 or higher installed. For installation or upgrade instructions, visit the [official Node.js website](https://nodejs.org/). This is crucial for compatibility and accessing the latest features. Verify your installation by running:

```sh
node -v
```

### pnpm

This project is configured as a [pnpm workspace](https://pnpm.io/workspaces). Before starting, ensure pnpm is installed. The Nova project uses v9 of pnpm, which you can install globally with:

```sh
npm install -g pnpm@9
```

### Nx

We utilize [Nx](https://nx.dev/getting-started/intro) to enhance our monorepo setup, leveraging its powerful configuration options and caching capabilities. Nx significantly improves build performance and developer productivity by enabling features like task orchestration, dependency graph visualization, and incremental builds. You can install it globally using the following command:

```sh
npm install -g nx
```


## Installation

Install all necessary dependencies for the packages and applications in the monorepo:

```sh
pnpm install
```

## Run Lint & Prettier

To ensure code quality and formatting, run the following commands:

* **Prettier:** Automatically formats files according to the specified configuration.
    ```sh
    pnpm run format
    ```

* **ESLint:** Runs ESLint to fix any linting errors that can be automatically resolved.
    ```sh
    pnpm run lint --fix
    ```

## Run Unit Tests

Execute unit tests using Vitest:
```sh
npx nx test
```

## Build

Build all packages and applications in the correct order based on their dependency trees:

```sh
npx nx run-many -t build
```