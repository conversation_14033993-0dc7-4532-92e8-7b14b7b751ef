/* eslint-disable react-hooks/rules-of-hooks */
import '@hxnova/icons/icons.css';
import { CssBaseline } from '@hxnova/react-components/CssBaseline';
import { PickerProvider } from '@hxnova/react-components/DatePickers';
// import { NovaProvider } from '@hxnova/react-components/NovaProvider';
import '@hxnova/themes/styles.css';
import '@pigment-css/react/styles.css';
import { DocsContainer } from '@storybook/blocks';
import { useEffect } from 'react';
import { useDarkMode } from 'storybook-dark-mode';
// import { getLocale, locales } from './locales';
import { NovaDark, NovaLight } from './theme';

const storybookProvider = (Story, context) => {
  const isDark = useDarkMode();
  // const locale = context.globals.locale;
  const colorScheme = isDark ? 'dark' : 'light';
  useEffect(() => {
    document.body.classList.remove('light', 'dark');
    document.body.classList.add(colorScheme);
  }, [colorScheme]);

  return (
    // <NovaProvider locale={getLocale(locale)}>
    <PickerProvider>
      <CssBaseline />
      <Story {...context} />
    </PickerProvider>
    // </NovaProvider>
  );
};

const docsProvider = (props) => {
  const isDark = useDarkMode();
  const docTheme = isDark ? NovaDark : NovaLight;
  const colorScheme = isDark ? 'dark' : 'light';
  useEffect(() => {
    document.body.classList.remove('light', 'dark');
    document.body.classList.add(colorScheme);
  }, [colorScheme]);
  return <DocsContainer {...props} theme={docTheme} />;
};

export const parameters = {
  layout: 'centered',
  controls: {
    matchers: {
      color: /(background|color)$/i,
      date: /Date$/i,
    },
  },
  options: {
    storySort: {
      order: ['@hxnova', ['mi-react-components', ['Readme', 'Changelog', 'Localization', '*']]],
    },
  },
  docs: {
    container: docsProvider,
  },
  darkMode: {
    classTarget: 'html',
    stylePreview: true,
    // Override the default dark theme
    dark: { ...NovaDark },
    darkClass: 'dark-mode',
    // Override the default light theme
    light: { ...NovaLight },
    lightClass: 'light-mode',
  },
};

export const decorators = [storybookProvider];

export const globalTypes = {
  // locale: {
  //   name: 'Locale',
  //   description: 'Internationalization locale',
  //   defaultValue: 'en-US',
  //   toolbar: {
  //     icon: 'globe',
  //     title: 'Locale',
  //     items: locales,
  //   },
  // },
};
