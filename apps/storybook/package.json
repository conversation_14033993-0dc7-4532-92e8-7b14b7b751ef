{"name": "storybook", "private": true, "version": "0.0.0", "type": "module", "scripts": {"build": "storybook build", "storybook": "storybook dev -p 6006"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@babel/plugin-transform-export-namespace-from": "^7.27.1", "@chromatic-com/storybook": "^3.2.6", "@hxnova/icons": "1.0.0-alpha.0", "@hxnova/mi-react-components": "workspace:*", "@hxnova/react-components": "file:../../hxnova-react-components-1.0.0-alpha.10.tgz", "@hxnova/themes": "1.0.0-alpha.11", "@pigment-css/react": "^0.0.30", "@pigment-css/vite-plugin": "^0.0.30", "@storybook/addon-designs": "^8.0.4", "@storybook/addon-docs": "^8.6.14", "@storybook/addon-essentials": "^8.6.14", "@storybook/addon-interactions": "^8.6.14", "@storybook/addon-onboarding": "^8.6.14", "@storybook/blocks": "^8.6.14", "@storybook/manager-api": "^8.6.14", "@storybook/react": "^8.6.14", "@storybook/react-vite": "^8.6.14", "@storybook/theming": "^8.6.14", "@vitejs/plugin-react": "^4.5.0", "add": "^2.0.6", "hoist-non-react-statics": "^3.3.2", "prop-types": "^15.8.1", "react-is": "^18.2.0", "storybook": "^8.6.14", "storybook-dark-mode": "4.0.1", "vite": "^6.3.5"}}