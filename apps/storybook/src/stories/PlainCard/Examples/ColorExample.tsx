import { Icon } from '@hxnova/icons';
import { PlainCard } from '@hxnova/mi-react-components/PlainCard';

export default function ColorExample() {
  return (
    <div style={{ display: 'flex', flexDirection: 'row', flexWrap: 'wrap', gap: '2rem' }}>
      <PlainCard
        dense
        color="neutral"
        icon={<Icon family="material" name="edit" />}
        headline="Heading"
        content="Lorem ipsum dolor sit amet, consectetur adipiscing elit."
        actionButtons={[
          {
            children: 'Button',
          },
        ]}
      />
      <PlainCard
        dense
        color="success"
        icon={<Icon family="material" name="edit" />}
        headline="Heading"
        content="Lorem ipsum dolor sit amet, consectetur adipiscing elit."
        actionButtons={[
          {
            children: 'Button',
          },
        ]}
      />
      <PlainCard
        dense
        color="info"
        icon={<Icon family="material" name="edit" />}
        headline="Heading"
        content="Lorem ipsum dolor sit amet, consectetur adipiscing elit."
        actionButtons={[
          {
            children: 'Button',
          },
        ]}
      />
      <PlainCard
        dense
        color="primary"
        icon={<Icon family="material" name="edit" />}
        headline="Heading"
        content="Lorem ipsum dolor sit amet, consectetur adipiscing elit."
        actionButtons={[
          {
            children: 'Button',
          },
        ]}
      />
    </div>
  );
}
