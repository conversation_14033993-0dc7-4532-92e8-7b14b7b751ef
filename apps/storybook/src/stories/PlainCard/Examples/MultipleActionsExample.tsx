import { Icon } from '@hxnova/icons';
import { PlainCard } from '@hxnova/mi-react-components/PlainCard';
import { Link } from '@hxnova/react-components/Link';

export default function MultipleActionsExample() {
  return (
    <div style={{ display: 'flex', flexDirection: 'row', flexWrap: 'wrap', gap: '2rem', alignItems: 'start' }}>
      <PlainCard
        icon={<Icon family="material" name="edit" />}
        headline="Heading"
        content="Lorem ipsum dolor sit amet, consectetur adipiscing elit. Vulputate ut laoreet arcu nibh rutrum libero, amet. Lacus dignissim."
        actionButtons={[
          {
            children: 'Button1',
          },
          <Link
            key="view-more"
            href="#"
            variant="bodyMedium"
            endDecorator={<Icon family="material" name="arrow_forward" />}
            sx={{
              marginLeft: 4,
            }}
          >
            View More
          </Link>,
        ]}
      />
    </div>
  );
}
