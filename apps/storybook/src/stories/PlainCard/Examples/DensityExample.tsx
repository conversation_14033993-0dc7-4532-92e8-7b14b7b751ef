import { Icon } from '@hxnova/icons';
import { PlainCard } from '@hxnova/mi-react-components/PlainCard';

export default function DensityExample() {
  return (
    <div style={{ display: 'flex', flexDirection: 'row', flexWrap: 'wrap', gap: '2rem', alignItems: 'start' }}>
      <PlainCard
        icon={<Icon family="material" name="edit" />}
        headline="Heading"
        content="Lorem ipsum dolor sit amet, consectetur adipiscing elit. Vulputate ut laoreet arcu nibh rutrum libero, amet. Lacus dignissim."
        actionButtons={[
          {
            children: 'Button',
          },
        ]}
      />
      <PlainCard
        dense
        icon={<Icon family="material" name="edit" />}
        headline="Heading"
        content="Lorem ipsum dolor sit amet, consectetur adipiscing elit."
        actionButtons={[
          {
            children: 'Button',
          },
        ]}
      />
    </div>
  );
}
