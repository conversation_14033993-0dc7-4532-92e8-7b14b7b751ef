import { Icon } from '@hxnova/icons';
import { PlainCard } from '@hxnova/mi-react-components/PlainCard';

export default function LoadingExample() {
  return (
    <div style={{ display: 'flex', flexDirection: 'row', flexWrap: 'wrap', gap: '2rem', alignItems: 'start' }}>
      <PlainCard
        loading
        loadingMode="spinner"
        icon={<Icon family="material" name="edit" />}
        headline="Heading"
        content="Lorem ipsum dolor sit amet, consectetur adipiscing elit."
        actionButtons={[
          {
            children: 'Button',
          },
        ]}
      />
      <PlainCard loading loadingMode="skeleton" headline="Heading" />
    </div>
  );
}
