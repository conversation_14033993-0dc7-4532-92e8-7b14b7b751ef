import { Meta } from '@storybook/blocks';
import CodeExpand from '../../components/codeExpand/CodeExpand';
import ColorExample from './Examples/ColorExample';
import ColorExampleSource from './Examples/ColorExample?raw';
import DensityExample from './Examples/DensityExample';
import DensityExampleSource from './Examples/DensityExample?raw';
import LoadingExample from './Examples/LoadingExample';
import LoadingExampleSource from './Examples/LoadingExample?raw';
import MultipleActionsExample from './Examples/MultipleActionsExample';
import MultipleActionsExampleSource from './Examples/MultipleActionsExample?raw';

<Meta title="@hxnova/mi-react-components/PlainCard/Examples" />

## Colors

PlainCard comes in 4 colors: `neutral` (default), `success`, `info`, and `primary`. These can be adjusted using the `color` prop.

<div className="sb-unstyled">
  <ColorExample />
</div>
<CodeExpand code={ColorExampleSource} showBorderTop style={{marginTop: 16}} />

## Density

You can make the PlainCard more compact by setting the `dense` prop to `true`. This reduces the width, spacing and heading text size of the card.

<div className="sb-unstyled">
  <DensityExample />
</div>
<CodeExpand code={DensityExampleSource} showBorderTop style={{marginTop: 16}} />

## Loading

PlainCard has a loading state with 2 modes: `spinner` (default) and `skeleton`. The loading behavior can be controlled using the `loading` and `loadingMode` props.

<div className="sb-unstyled">
  <LoadingExample />
</div>
<CodeExpand code={LoadingExampleSource} showBorderTop style={{marginTop: 16}} />

## Multiple Action Buttons

You can have multiple action buttons with different styles at the bottom of PlainCard by configuring the `actionButtons` prop.

<div className="sb-unstyled">
  <MultipleActionsExample />
</div>
<CodeExpand code={MultipleActionsExampleSource} showBorderTop style={{marginTop: 16}} />
