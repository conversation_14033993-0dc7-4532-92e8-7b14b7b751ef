import { Icon } from '@hxnova/icons';
import { PlainCard } from '@hxnova/mi-react-components/PlainCard';
import type { Meta, StoryObj } from '@storybook/react';

const meta = {
  title: '@hxnova/mi-react-components/PlainCard',
  component: PlainCard,
  parameters: {
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/vYxc0XelXCJ9bgo3wNC44j/MI-x-Nova-Components?node-id=217-29076&p=f',
    },
  },
} satisfies Meta<typeof PlainCard>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Basic: Story = {
  args: {
    dense: false,
    icon: <Icon family="material" name="edit" />,
    color: 'neutral',
    loading: false,
    loadingMode: 'spinner',
    headline: 'Heading',
    content: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
    actionButtons: [
      {
        children: 'Button',
      },
    ],
  },

  argTypes: {
    headline: {
      control: 'text',
    },
    content: {
      control: 'text',
    },
    color: {
      control: 'select',
      options: ['neutral', 'success', 'info', 'primary'],
    },
    dense: {
      control: 'boolean',
    },
    loading: {
      control: 'boolean',
    },
    loadingMode: {
      control: 'radio',
      options: ['spinner', 'skeleton'],
    },
  },

  parameters: {
    controls: {
      include: ['dense', 'headline', 'content', 'color', 'loading', 'loadingMode'],
    },
  },
};
