# API Documentation

- [PlainCard](#plaincard)

# PlainCard

API reference docs for the React PlainCard component. Learn about the props, CSS, and other APIs of this exported module.

## Import

```jsx
import { PlainCard } from '@hxnova/mi-react-components/PlainCard';
// or
import { PlainCard } from '@hxnova/mi-react-components';
```

## Props

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **headline*** | `ReactNode` | - | The main text to display below the icon in the top-left corner of the card |
| **actionButtons** | `readonly (ReactNode ⏐ ButtonPropsWithTestId)[]` | - | Array of [Nova Button props](https://zeroheight.com/9a7698df1/p/64fa04-button-common/t/93e44d63e2)<br>or custom components to render in the `CardActions` panel |
| **actions** | `ReactNode` | - | Custom content to render in the `CardActions` panel. The priority is higher than `actionButtons` prop. |
| **content** | `ReactNode` | - | The main body content to display in the card |
| **dense** | `bool` | `false` | If `true`, the spacing in the card will be condensed, width will be reduced, and the text size of heading will be smaller |
| **icon** | `ReactNode` | - | The icon to display above the headline in the top-left corner of the card |
| **loading** | `bool` | `false` | If `true`, a loading indicator will be displayed blocking interaction with the card |
| **loadingMode** | `'spinner' ⏐ 'skeleton'` | `'spinner'` | The style of loader |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |

## CSS classes

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .MIxNovaPlainCard-root | `root` | Class name applied to the root element. |
| .MIxNovaPlainCard-icon | `icon` | Class name applied to the icon element. |
| .MIxNovaPlainCard-headlineContainer | `headlineContainer` | Class name applied to the headline container element. |
| .MIxNovaPlainCard-headline | `headline` | Class name applied to the headline element. |
| .MIxNovaPlainCard-contentContainer | `contentContainer` | Class name applied to the content container element. |
| .MIxNovaPlainCard-content | `content` | Class name applied to the content element. |

