import { Meta } from '@storybook/blocks';
import CodeExpand from '../../components/codeExpand/CodeExpand';
import CustomCircularLoaderExample from './Examples/CustomCircularLoaderExample';
import CustomCircularLoaderExampleSource from './Examples/CustomCircularLoaderExample?raw';
import CustomLinearLoaderExample from './Examples/CustomLinearLoaderExample';
import CustomLinearLoaderExampleSource from './Examples/CustomLinearLoaderExample?raw';
import DefaultExample from './Examples/DefaultExample';
import DefaultExampleSource from './Examples/DefaultExample?raw';
import WithTitleMessageExample from './Examples/WithTitleMessageExample';
import WithTitleMessageExampleSource from './Examples/WithTitleMessageExample?raw';

<Meta title="@hxnova/mi-react-components/LoadingPanel/Examples" />

## Default

The following example shows a LoadingPanel with default Nova CircularProgress loader.

<div className="sb-unstyled">
  <DefaultExample />
</div>
<CodeExpand code={DefaultExampleSource} showBorderTop style={{marginTop: 16}} />

## With Title and Message

You can provide a title and message to the LoadingPanel by passing `loadingTitle` and `loadingMessage` props.

<div className="sb-unstyled">
  <WithTitleMessageExample />
</div>
<CodeExpand code={WithTitleMessageExampleSource} showBorderTop style={{marginTop: 16}} />

## Custom Loader

The `loader` prop allows you to customize the loader component for the LoadingPanel.

<div className="sb-unstyled">
  <CustomLinearLoaderExample />
</div>
<CodeExpand code={CustomLinearLoaderExampleSource} showBorderTop style={{marginTop: 16}} />

<div className="sb-unstyled">
  <CustomCircularLoaderExample />
</div>
<CodeExpand code={CustomCircularLoaderExampleSource} showBorderTop style={{marginTop: 16}} />
