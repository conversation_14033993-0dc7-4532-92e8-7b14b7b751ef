import { LoadingPanel } from '@hxnova/mi-react-components/LoadingPanel';

export default function WithTitleMessageExample() {
  return (
    <div style={{ position: 'relative', height: '300px' }}>
      <LoadingPanel
        open
        loadingTitle="Loading Data"
        loadingMessage="Please wait while we fetch the latest data."
        sx={{
          position: 'absolute',
        }}
      />
    </div>
  );
}
