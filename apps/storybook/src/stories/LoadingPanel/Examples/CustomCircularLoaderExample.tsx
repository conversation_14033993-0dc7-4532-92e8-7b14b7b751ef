import { LoadingPanel } from '@hxnova/mi-react-components/LoadingPanel';
import { CircularProgress } from '@hxnova/react-components/CircularProgress';
import { Typography } from '@hxnova/react-components/Typography';
import { useEffect, useState } from 'react';

export default function CustomCircularLoaderExample() {
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    const timeout = setInterval(() => {
      setProgress((prev) => (prev >= 100 ? 0 : prev + 10));
    }, 500);

    return () => clearInterval(timeout);
  });

  return (
    <div style={{ position: 'relative', height: '300px' }}>
      <LoadingPanel
        open
        loader={
          <div
            sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 8,
            }}
          >
            <div sx={{ position: 'relative', display: 'inline-flex' }}>
              <CircularProgress variant="determinate" value={progress} />
              <div
                style={{
                  top: 0,
                  left: 0,
                  bottom: 0,
                  right: 0,
                  position: 'absolute',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <Typography
                  variant="bodySmall"
                  style={{ color: 'var(--palette-primary)' }}
                >{`${progress}%`}</Typography>
              </div>
            </div>
          </div>
        }
        loadingTitle="Loading Data"
        loadingMessage="Please wait while we fetch the latest data."
        sx={{
          position: 'absolute',
        }}
      />
    </div>
  );
}
