import { LoadingPanel } from '@hxnova/mi-react-components/LoadingPanel';
import { LinearProgress } from '@hxnova/react-components/LinearProgress';
import { Typography } from '@hxnova/react-components/Typography';
import { useEffect, useState } from 'react';

export default function CustomLoaderExample() {
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    const timeout = setInterval(() => {
      setProgress((prev) => (prev >= 100 ? 0 : prev + 10));
    }, 500);

    return () => clearInterval(timeout);
  });

  return (
    <div style={{ position: 'relative', height: '300px' }}>
      <LoadingPanel
        open
        loader={
          <div
            sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 8,
            }}
          >
            <div sx={{ width: 400 }}>
              <LinearProgress variant="determinate" value={progress} />
            </div>
            <Typography variant="bodySmall" sx={(theme) => ({ color: theme.vars.palette.primary })}>
              {progress}%
            </Typography>
          </div>
        }
        loadingTitle="Loading Data"
        loadingMessage="Please wait while we fetch the latest data."
        sx={{
          position: 'absolute',
        }}
      />
    </div>
  );
}
