import { LoadingPanel } from '@hxnova/mi-react-components/LoadingPanel';
import { Meta, StoryObj } from '@storybook/react';

const meta = {
  title: '@hxnova/mi-react-components/LoadingPanel',
  component: LoadingPanel,
  parameters: {
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/vYxc0XelXCJ9bgo3wNC44j/MI-x-Nova-Components?node-id=58-15616',
    },
  },
} satisfies Meta<typeof LoadingPanel>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Basic: Story = {
  args: {
    open: true,
    loadingTitle: 'Loading your experience',
    loadingMessage: 'Add description or context',
  },

  argTypes: {
    open: {
      control: 'boolean',
    },
    loadingTitle: {
      control: 'text',
    },
    loadingMessage: {
      control: 'text',
    },
  },

  parameters: {
    controls: {
      include: ['open', 'loadingTitle', 'loadingMessage'],
    },
  },
};
