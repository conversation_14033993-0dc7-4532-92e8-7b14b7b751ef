import { Icon } from '@hxnova/icons';
import { Drawer } from '@hxnova/react-components/Drawer';
import * as React from 'react';
import { ColorSchemeProvider } from './components/ColorScheme/ColorSchemeProvider';
import ColorSchemeToggleButton from './components/ColorScheme/ColorSchemeToggleButton';
import { kitchenComponentsMap, sortedKitchenLabels } from './components/Kitchen';

function Home() {
  const [selectedKitchen, setSelectedKitchen] = React.useState(() => {
    const saved = localStorage.getItem('MIxNovaSelectedKitchen');
    // Verify the saved component exists, otherwise default to a known valid component
    return saved && saved in kitchenComponentsMap ? saved : sortedKitchenLabels[0];
  });

  const handleKitchenClick = (kitchen: string) => {
    setSelectedKitchen(kitchen);
    localStorage.setItem('MIxNovaSelectedKitchen', kitchen);
  };

  const CurrentKitchen = kitchenComponentsMap[selectedKitchen as keyof typeof kitchenComponentsMap];
  if (!CurrentKitchen) {
    alert(`Kitchen ${selectedKitchen} not found.`);
    return null;
  }

  return (
    <div
      sx={{
        display: 'flex',
      }}
    >
      <Drawer.Root variant="permanent" width={300} sx={{ width: 300 }}>
        <Drawer.Body>
          <Drawer.NavGroup>
            {sortedKitchenLabels.map((kitchenLabel, index) => (
              <Drawer.NavItem
                key={index}
                onClick={() => handleKitchenClick(kitchenLabel)}
                selected={selectedKitchen === kitchenLabel}
                label={kitchenLabel}
                startDecorator={<Icon family="material" name="inbox" />}
              />
            ))}
          </Drawer.NavGroup>
        </Drawer.Body>
      </Drawer.Root>

      <div
        sx={(theme) => ({
          flexGrow: 1,
          backgroundColor: theme.vars.palette.surfaceContainer,
          padding: 32,
          height: '100vh',
          overflow: 'auto',
        })}
      >
        <React.Suspense>{CurrentKitchen ? <CurrentKitchen /> : null}</React.Suspense>
      </div>

      <ColorSchemeToggleButton />
    </div>
  );
}

const defaultColorScheme = localStorage.getItem('colorScheme') ?? 'light';

function App() {
  return (
    <ColorSchemeProvider colorScheme={defaultColorScheme}>
      <Home />
    </ColorSchemeProvider>
  );
}

export default App;
