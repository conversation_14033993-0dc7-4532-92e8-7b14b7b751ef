'use client';

import { Icon } from '@hxnova/icons';
import { Drawer } from '@hxnova/react-components/Drawer';
import * as React from 'react';
import ColorSchemeToggleButton from '../components/ColorScheme/ColorSchemeToggleButton';
import { kitchenComponentsMap, sortedKitchenLabels } from '../components/Kitchen';

export default function Home() {
  const [selectedKitchen, setSelectedKitchen] = React.useState('');

  React.useEffect(() => {
    if (typeof window !== 'undefined') {
      const storedKitchen = localStorage.getItem('MIxNovaSelectedKitchen');
      if (storedKitchen) {
        setSelectedKitchen(storedKitchen);
      }
    }
  }, []);

  const handleKitchenClick = (kitchen: string) => {
    setSelectedKitchen(kitchen);
    if (typeof window !== 'undefined') {
      localStorage.setItem('MIxNovaSelectedKitchen', kitchen);
    }
  };

  const CurrentKitchen = kitchenComponentsMap[selectedKitchen as keyof typeof kitchenComponentsMap];

  return (
    <div
      sx={{
        display: 'flex',
      }}
    >
      <Drawer.Root variant="permanent" width={300} sx={{ width: 300 }}>
        <Drawer.Body>
          <Drawer.NavGroup>
            {sortedKitchenLabels.map((kitchenLabel, index) => (
              <Drawer.NavItem
                key={index}
                onClick={() => handleKitchenClick(kitchenLabel)}
                selected={selectedKitchen === kitchenLabel}
                label={kitchenLabel}
                startDecorator={<Icon family="material" name="inbox" />}
              />
            ))}
          </Drawer.NavGroup>
        </Drawer.Body>
      </Drawer.Root>

      <div
        sx={(theme) => ({
          flexGrow: 1,
          backgroundColor: theme.vars.palette.surfaceContainer,
          padding: 32,
          height: '100vh',
          overflow: 'auto',
        })}
      >
        <React.Suspense>{CurrentKitchen ? <CurrentKitchen /> : null}</React.Suspense>
      </div>

      <ColorSchemeToggleButton />
    </div>
  );
}
