import App from '@/components/App';
import { ColorSchemeProvider } from '@/components/ColorScheme/ColorSchemeProvider';
import '@hxnova/icons/icons.css';
import '@hxnova/themes/styles.css';
import '@pigment-css/react/styles.css';
import type { Metadata } from 'next';
import { cookies } from 'next/headers';
import * as React from 'react';

export const metadata: Metadata = {
  title: 'MIxNova Nextjs Demo',
};

export default async function RootLayout(props: { children: React.ReactNode }) {
  const cookieStore = await cookies();
  const { value: colorScheme = 'light' } = cookieStore.get('colorScheme') ?? {};
  return (
    <html lang="en">
      <ColorSchemeProvider colorScheme={colorScheme}>
        <App>{props.children}</App>
      </ColorSchemeProvider>
    </html>
  );
}
