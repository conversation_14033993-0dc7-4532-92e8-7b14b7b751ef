'use client';

import { CssBaseline } from '@hxnova/react-components/CssBaseline';
import * as React from 'react';
import { useColorScheme } from './ColorScheme/ColorSchemeProvider';
import ColorSchemeToggleButton from './ColorScheme/ColorSchemeToggleButton';

function App({ children }: React.PropsWithChildren) {
  const { colorScheme } = useColorScheme();
  return (
    <body className={`${colorScheme}`}>
      <CssBaseline />
      {children}
      <ColorSchemeToggleButton />
    </body>
  );
}

export default App;
