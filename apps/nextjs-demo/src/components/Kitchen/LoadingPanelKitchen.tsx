import { LoadingPanel, LoadingPanelProps } from '@hxnova/mi-react-components/LoadingPanel';
import { CircularProgress } from '@hxnova/react-components/CircularProgress';
import { LinearProgress } from '@hxnova/react-components/LinearProgress';
import { Typography } from '@hxnova/react-components/Typography';
import { useEffect, useState } from 'react';
import Heading from '../Heading';

function AbsoluteLoadingPanel(props: LoadingPanelProps) {
  return (
    <div
      sx={{
        position: 'relative',
        height: '350px',
        marginBottom: 32,
      }}
    >
      <LoadingPanel
        open
        loadingTitle="Loading Data"
        loadingMessage="Please wait while we fetch the latest data."
        sx={{
          position: 'absolute',
        }}
        {...props}
      />
    </div>
  );
}

function CustomLinearProgress({ progress }: { progress: number }) {
  return (
    <div
      sx={{
        display: 'flex',
        alignItems: 'center',
        gap: 8,
      }}
    >
      <div sx={{ width: 400 }}>
        <LinearProgress variant="determinate" value={progress} />
      </div>
      <Typography variant="bodySmall" sx={(theme) => ({ color: theme.vars.palette.primary })}>
        {progress}%
      </Typography>
    </div>
  );
}

function CustomCircularProgress({ progress }: { progress: number }) {
  return (
    <div
      sx={{
        position: 'relative',
        display: 'inline-flex',
      }}
    >
      <CircularProgress variant="determinate" value={progress} />
      <div
        style={{
          top: 0,
          left: 0,
          bottom: 0,
          right: 0,
          position: 'absolute',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <Typography variant="bodySmall" style={{ color: 'var(--palette-primary)' }}>
          {`${progress}%`}
        </Typography>
      </div>
    </div>
  );
}

export default function LoadingPanelKitchen() {
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    const timeout = setInterval(() => {
      setProgress((prev) => (prev >= 100 ? 0 : prev + 10));
    }, 500);

    return () => clearInterval(timeout);
  });

  return (
    <div>
      <Heading>Default Loader</Heading>
      <AbsoluteLoadingPanel />

      <Heading>Custom Loader</Heading>
      <AbsoluteLoadingPanel loader={<CustomLinearProgress progress={progress} />} />
      <AbsoluteLoadingPanel loader={<CustomCircularProgress progress={progress} />} />
    </div>
  );
}
