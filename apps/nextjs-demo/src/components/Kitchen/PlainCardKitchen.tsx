import { Icon } from '@hxnova/icons';
import { PlainCard, PlainCardProps } from '@hxnova/mi-react-components/PlainCard';
import Heading from '../Heading';

function PlainCards(props: Partial<PlainCardProps>) {
  return (
    <div
      sx={{
        display: 'flex',
        flexWrap: 'wrap',
        gap: '32px',
        marginBottom: '32px',
      }}
    >
      {(['neutral', 'success', 'info', 'primary'] as const).map((color) => (
        <PlainCard
          key={color}
          icon={<Icon family="material" name="edit" />}
          headline="Heading"
          content="Lorem ipsum dolor sit amet, consectetur adipiscing elit."
          actionButtons={[
            {
              children: 'Button',
            },
          ]}
          color={color}
          {...props}
        />
      ))}
    </div>
  );
}

export default function PlainCardKitchen() {
  return (
    <div>
      <Heading>Dense = true</Heading>
      <PlainCards dense />

      <Heading>Dense = false</Heading>
      <PlainCards dense={false} />

      <Heading>Spinner Loading</Heading>
      <PlainCards dense loading />
      <PlainCards dense={false} loading />

      <Heading>Skeleton Loading</Heading>
      <PlainCards dense loading loadingMode="skeleton" />
      <PlainCards dense={false} loading loadingMode="skeleton" />
    </div>
  );
}
