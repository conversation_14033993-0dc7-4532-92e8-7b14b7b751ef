'use client';
import { IconButton } from '@hxnova/react-components/IconButton';
import { useColorScheme } from './ColorSchemeProvider';

function ColorSchemeToggleButton() {
  const { colorScheme, setColorScheme } = useColorScheme();

  const toggleColorScheme = () => {
    setColorScheme(colorScheme === 'dark' ? 'light' : 'dark');
  };

  return (
    <div sx={{ position: 'fixed', top: 20, right: 40, zIndex: 9999 }}>
      <IconButton variant="standard" size="medium" onClick={toggleColorScheme}>
        {colorScheme === 'light' ? '🌙' : '🔆'}
      </IconButton>
    </div>
  );
}

export default ColorSchemeToggleButton;
