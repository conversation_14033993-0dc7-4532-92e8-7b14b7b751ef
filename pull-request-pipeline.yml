pool:
  vmImage: ubuntu-latest

trigger:
  branches:
    include:
      - nonexistantbranch # prevent the PR pipeline from running when code is committed to main

variables:
  pnpm_config_cache: $(Pipeline.Workspace)/.pnpm-store
  CI: "true"
  NX_BRANCH: $(System.PullRequest.PullRequestNumber)
  TARGET_BRANCH: $[replace(variables['System.PullRequest.TargetBranch'],'refs/heads/','origin/')]
  BASE_SHA: $(git merge-base $(TARGET_BRANCH) HEAD)
  HEAD_SHA: $(git rev-parse HEAD)

steps:
  - checkout: self
    fetchDepth: 0

  - task: NodeTool@0
    inputs:
      versionSpec: "20.x"
    displayName: "Install Node.js"

  - task: npmAuthenticate@0
    inputs:
      workingFile: .npmrc

  - task: Cache@2
    inputs:
      key: 'pnpm | "$(Agent.OS)" | pnpm-lock.yaml'
      path: $(pnpm_config_cache)
    displayName: Cache pnpm

  - script: |
      corepack enable
      corepack prepare pnpm@9.14.2 --activate
      pnpm config set store-dir $(pnpm_config_cache)
    displayName: "Setup pnpm"

  # Set Azure Devops CLI default settings
  - bash: az devops configure --defaults organization=$(System.TeamFoundationCollectionUri) project=$(System.TeamProject)
    displayName: "Set default Azure DevOps organization and project"
  # Get last successful commit from Azure Devops CLI
  - bash: |
      LAST_SHA=$(az pipelines build list --branch $(Build.SourceBranchName) --definition-ids $(System.DefinitionId) --result succeeded --top 1 --query "[0].triggerInfo.\"ci.sourceSha\"")
      if [ -z "$LAST_SHA" ]
      then
        echo "Last successful commit not found. Using fallback 'HEAD~1': $BASE_SHA"
      else
        echo "Last successful commit SHA: $LAST_SHA"
        echo "##vso[task.setvariable variable=BASE_SHA]$LAST_SHA"
      fi
    displayName: "Get last successful commit SHA"
    condition: ne(variables['Build.Reason'], 'PullRequest')
    env:
      AZURE_DEVOPS_EXT_PAT: $(System.AccessToken)

  # Required for nx affected if we're on a branch
  - script: git branch --track main origin/main
  # This line enables distribution
  # The "--stop-agents-after" is optional, but allows idle agents to shut down once the "e2e-ci" targets have been requested
  # - script: npx nx-cloud start-ci-run --distribute-on="5 linux-medium-js" --stop-agents-after="e2e-ci"

  - script: pnpm install --frozen-lockfile
    displayName: "Install Dependencies"
    timeoutInMinutes: 10

  - script: npx nx prettier
    displayName: "Run Prettier Check"
    timeoutInMinutes: 5

  - script: npx nx lint
    displayName: "Run Lint Check"
    timeoutInMinutes: 5
  
  - script: npx nx test:ci
    displayName: "Run Tests"
    timeoutInMinutes: 20

    # publish the tests results
  - task: PublishTestResults@2
    displayName: 'Publish Unit Test Results'
    inputs:
      testResultsFiles: coverage/junit.xml
    condition: succeededOrFailed()

  # publish code coverage reports in cobertura format
  - task: PublishCodeCoverageResults@2
    displayName: 'Publish Code Coverage Results'
    inputs:
      codeCoverageTool: Cobertura
      summaryFileLocation: '$(System.DefaultWorkingDirectory)/coverage/cobertura-coverage.xml'
    condition: succeededOrFailed()

  - script: |
      export NODE_OPTIONS="--max-old-space-size=8192"
      npx nx affected --base=$(BASE_SHA) -t build
    displayName: "Run Build Packages Check"
    timeoutInMinutes: 20
