'use client';
import * as React from 'react';
import {
  EventHandlers,
  unstable_capitalize as capitalize,
  unstable_composeClasses as composeClasses,
} from '@mui/utils';
import useSlotProps from '@mui/utils/useSlotProps';
import { OverridableComponent } from '@mui/types';
import { styled } from '@pigment-css/react';
import { InputTypeMap, InputProps, InputOwnerState } from './Input.types';
import inputClasses, { getInputUtilityClass } from './Input.classes';
import FormControlContext from '../FormControl/FormControlContext';
import { useInput } from '../internal/hooks/useInput';
import ErrorIcon from '../internal/svg-icons/Error';
import { iconButtonClasses } from '../IconButton';
import { typographyClasses } from '../Typography';

const useUtilityClasses = (ownerState: InputOwnerState) => {
  const { disabled, fullWidth, size, focused, filled, error } = ownerState;

  const slots = {
    root: [
      'root',
      disabled && 'disabled',
      fullWidth && 'fullWidth',
      focused && 'focused',
      filled && 'filled',
      error && 'error',
      size && `size${capitalize(size)}`,
    ],
    wrapper: ['wrapper'],
    input: ['input', disabled && 'disabled', focused && 'focused'],
    startDecorator: ['startDecorator'],
    endDecorator: ['endDecorator'],
    errorStateIcon: ['errorStateIcon'],
  };

  return composeClasses(slots, getInputUtilityClass, {});
};

export const InputRoot = styled('div')<InputOwnerState>(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  flexWrap: 'wrap',
  border: '1px solid',
  borderColor: theme.vars.palette.outlineVariant,
  minHeight: '40px',
  boxSizing: 'border-box',
  borderRadius: '8px',
  gap: '4px',
  color: theme.vars.palette.onSurface,
  backgroundColor: theme.vars.palette.surfaceContainerHigh,
  '&:hover': {
    borderColor: theme.vars.palette.outline,
    backgroundColor: `color-mix(in srgb, ${theme.vars.palette.surfaceContainer}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.hoverOnSurface})`,
  },
  [`&.${inputClasses.focused}`]: {
    borderColor: theme.vars.palette.primary,
    backgroundColor: theme.vars.palette.surfaceContainerHigh,
  },
  variants: [
    {
      props: { filled: true, disabled: false, error: false },
      style: {
        borderColor: theme.vars.palette.outline,
        '&:hover': {
          borderColor: theme.vars.palette.outline,
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.surfaceContainer}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.hoverOnSurface})`,
        },
        [`&.${inputClasses.focused}`]: {
          borderColor: theme.vars.palette.primary,
          backgroundColor: theme.vars.palette.surfaceContainerHigh,
        },
      },
    },
    {
      props: { filled: false, disabled: false, error: false },
      style: {
        borderColor: theme.vars.palette.outlineVariant,
        '&:hover': {
          borderColor: theme.vars.palette.outline,
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.surfaceContainer}, ${theme.vars.palette.onSurfaceVariant} ${theme.vars.palette.stateLayers.hoverOnSurfaceVariant})`,
        },
        [`&.${inputClasses.focused}`]: {
          borderColor: theme.vars.palette.primary,
          backgroundColor: theme.vars.palette.surfaceContainerHigh,
        },
      },
    },
    {
      props: { error: true, disabled: false },
      style: {
        borderColor: theme.vars.palette.error,
        '&:hover': {
          borderColor: theme.vars.palette.error,
        },
        [`&.${inputClasses.focused}`]: {
          borderColor: theme.vars.palette.error,
        },
      },
    },
    {
      props: { disabled: true },
      style: {
        color: theme.vars.palette.onBackgroundDisabled,
        backgroundColor: `color-mix(in srgb, ${theme.vars.palette.backgroundDisabled}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.disabledOnSurface})`,
        border: 'unset',
        '&:hover': {
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.backgroundDisabled}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.disabledOnSurface})`,
        },
        '--nova-input-placeholderColor': theme.vars.palette.backgroundDisabled,
      },
    },
    {
      props: { readOnly: true },
      style: {
        color: theme.vars.palette.onSurfaceVariant,
        backgroundColor: theme.vars.palette.surfaceContainerHighest,
        border: theme.vars.palette.outlineVariant,
        '&:hover': {
          backgroundColor: theme.vars.palette.surfaceContainerHighest,
        },
      },
    },
    {
      props: { size: 'small' },
      style: { minHeight: '32px', paddingInline: '6px' },
    },
    {
      props: { size: 'medium' },
      style: { minHeight: '40px', paddingInline: '8px' },
    },
    {
      props: { size: 'large' },
      style: { minHeight: '48px', paddingInline: '10px' },
    },
  ],
}));

export const InputWrapper = styled('div')<InputOwnerState>(({ theme }) => ({
  flex: 1,
  minWidth: 0,
  display: 'flex',
  alignItems: 'center',
  flexWrap: 'wrap',
  columnGap: theme.vars.sys.size.spaceBetween.horizontal['2xs'].medium,
}));

export const InputInput = styled('input')<InputOwnerState>(({ theme }) => ({
  border: 'none',
  minWidth: 0,
  outline: 0,
  padding: 0,
  flex: 1,
  color: 'inherit',
  backgroundColor: 'transparent',
  fontFamily: 'inherit',
  fontStyle: 'inherit',
  fontWeight: 'inherit',
  textOverflow: 'ellipsis',
  '--nova-input-placeholderColor': theme.vars.palette.onSurfaceVariant,
  '&::placeholder': {
    color: 'var(--nova-input-placeholderColor)',
  },
  variants: [
    {
      props: { disabled: true },
      style: {
        color: 'inherit',
        '--nova-input-placeholderColor': theme.vars.palette.onBackgroundDisabled,
      },
    },
    {
      props: { size: 'small' },
      style: {
        fontSize: '14px',
        lineHeight: '18px',
      },
    },
    {
      props: { size: 'medium' },
      style: {
        fontSize: '16px',
        lineHeight: '20px',
      },
    },
    {
      props: { size: 'large' },
      style: {
        fontSize: '18px',
        lineHeight: '24px',
      },
    },
  ] as any,
}));

export const InputDecorator = styled('div')<InputOwnerState>(({ theme }) => ({
  display: 'inherit',
  alignItems: 'center',
  flexWrap: 'wrap',
  [`& .${iconButtonClasses.root}`]: {
    transform: 'scale(0.9)', // Against same IconButton height of Input
  },
  variants: [
    {
      props: { filled: true, disabled: false },
      style: {
        color: theme.vars.palette.onSurface,
        [`& .${iconButtonClasses.root}`]: {
          color: theme.vars.palette.onSurface,
        },
      },
    },
    {
      props: { filled: false, disabled: false },
      style: {
        color: theme.vars.palette.onSurfaceVariant,
        [`& .${iconButtonClasses.root}`]: {
          color: theme.vars.palette.onSurfaceVariant,
        },
      },
    },
    {
      props: { disabled: true },
      style: {
        color: theme.vars.palette.onBackgroundDisabled,
      },
    },
    {
      props: { size: 'small' },
      style: {
        fontSize: '20px',
        [`& .${typographyClasses.root}`]: {
          fontSize: '12px',
        },
      },
    },
    {
      props: { size: 'medium' },
      style: {
        fontSize: '24px',
        [`& .${typographyClasses.root}`]: {
          fontSize: '14px',
        },
      },
    },
    {
      props: { size: 'large' },
      style: {
        fontSize: '28px',
        [`& .${typographyClasses.root}`]: {
          fontSize: '16px',
        },
      },
    },
  ],
}));

export const ErrorStateIcon = styled('div')<InputOwnerState>(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  '& svg': {
    color: theme.vars.palette.error,
  },
  variants: [
    {
      props: { size: 'small' },
      style: {
        '& svg': {
          fontSize: '20px',
        },
      },
    },
    {
      props: { size: 'medium' },
      style: {
        '& svg': {
          fontSize: '24px',
        },
      },
    },
    {
      props: { size: 'large' },
      style: {
        '& svg': {
          fontSize: '28px',
        },
      },
    },
  ],
}));

// eslint-disable-next-line react/display-name
export const Input = React.forwardRef((props: InputProps, ref: React.ForwardedRef<Element>) => {
  const formControl = React.useContext(FormControlContext);
  const {
    'aria-describedby': ariaDescribedby,
    'aria-label': ariaLabel,
    'aria-labelledby': ariaLabelledby,
    autoComplete,
    autoFocus,
    className,
    defaultValue,
    disabled: disabledProp = false,
    error: errorProp = false,
    showErrorIcon = true,
    fullWidth: fullWidthProp = false,
    size: sizeProp = 'medium',
    id: idOverride,
    name,
    onClick,
    onChange,
    onKeyDown,
    onKeyUp,
    onFocus,
    onBlur,
    placeholder,
    readOnly,
    required,
    type,
    value,
    maxLength,
    startDecorator,
    endDecorator,
    children,
    component,
    slots = {},
    slotProps = {},
    ...rest
  } = props;

  const { getRootProps, getInputProps, focused, disabled, error } = useInput({
    disabled: props.disabled,
    defaultValue,
    error: props.error,
    onBlur,
    onClick,
    onChange,
    onFocus,
    required: props.required,
    value,
  });

  const id = idOverride || formControl?.htmlFor;

  const filled = formControl?.filled ?? (Boolean(value) || Boolean(defaultValue));
  const size = props.size ?? formControl?.size ?? sizeProp;
  const fullWidth = props.fullWidth ?? formControl?.fullWidth ?? fullWidthProp;

  const ownerState = {
    ...props,
    fullWidth,
    disabled,
    error,
    focused,
    size,
    filled,
  };

  const classes = useUtilityClasses(ownerState);

  const SlotRoot = slots.root ?? InputRoot;
  const SlotWrapper = slots.wrapper ?? InputWrapper;
  const SlotInput = slots.input ?? InputInput;
  const SlotStartDecorator = slots.startDecorator ?? InputDecorator;
  const SlotEndDecorator = slots.endDecorator ?? InputDecorator;
  const SlotErrorStateIcon = slots.errorStateIcon ?? ErrorStateIcon;

  const slotRootProps = useSlotProps({
    elementType: SlotRoot,
    externalSlotProps: slotProps.root,
    externalForwardedProps: rest,
    additionalProps: {
      as: component,
    },
    ownerState,
    getSlotProps: getRootProps,
    className: [classes.root, className],
  });

  const slotWrapperProps = useSlotProps({
    elementType: SlotWrapper,
    externalSlotProps: slotProps.wrapper,
    ownerState,
    className: classes.wrapper,
  });

  const slotInputProps = useSlotProps({
    elementType: SlotInput,
    externalSlotProps: slotProps.input,
    additionalProps: {
      'aria-label': ariaLabel,
      'aria-labelledby': ariaLabelledby,
      'aria-describedby': ariaDescribedby,
      autoComplete,
      autoFocus,
      disabled,
      onKeyDown,
      onKeyUp,
      name,
      maxLength,
      placeholder,
      readOnly,
      type,
      id,
      ref,
    },
    ownerState,
    getSlotProps: (otherHandlers: EventHandlers) => {
      return getInputProps({
        ...otherHandlers,
      });
    },
    className: [classes.input],
  });

  const slotStartDecoratorProps = useSlotProps({
    elementType: SlotStartDecorator,
    externalSlotProps: slotProps.startDecorator,
    ownerState,
    className: classes.startDecorator,
  });

  const slotEndDecoratorProps = useSlotProps({
    elementType: SlotEndDecorator,
    externalSlotProps: slotProps.endDecorator,
    ownerState,
    className: classes.endDecorator,
  });

  const slotErrorStateIconProps = useSlotProps({
    elementType: SlotErrorStateIcon,
    externalSlotProps: slotProps.errorStateIcon,
    ownerState,
    className: classes.errorStateIcon,
  });

  return (
    <SlotRoot {...slotRootProps}>
      {startDecorator && <SlotStartDecorator {...slotStartDecoratorProps}>{startDecorator}</SlotStartDecorator>}
      <SlotWrapper {...slotWrapperProps}>
        {children}
        <SlotInput {...slotInputProps} />
      </SlotWrapper>
      {endDecorator && <SlotEndDecorator {...slotEndDecoratorProps}>{endDecorator}</SlotEndDecorator>}
      {showErrorIcon && error && !(disabled || readOnly) && (
        <SlotErrorStateIcon {...slotErrorStateIconProps}>
          <ErrorIcon />
        </SlotErrorStateIcon>
      )}
    </SlotRoot>
  );
}) as OverridableComponent<InputTypeMap>;
