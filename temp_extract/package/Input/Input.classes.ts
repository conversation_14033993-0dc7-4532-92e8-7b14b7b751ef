import generateUtilityClasses from '@mui/utils/generateUtilityClasses';
import generateUtilityClass from '@mui/utils/generateUtilityClass';

export interface InputClasses {
  /** Class name applied to the root element. */
  root: string;
  /** Class name applied to the wrapper element. */
  wrapper: string;
  /** Class name applied to the input element. */
  input: string;
  /** Class name applied to the startDecorator element */
  startDecorator: string;
  /** Class name applied to the endDecorator element */
  endDecorator: string;

  /** Class name applied to the root element if the component is focused. */
  focused: string;
  /** Class name applied to the root element if the input has value */
  filled: string;
  /** Class name applied to the root element if `disabled={true}`. */
  disabled: string;
  /** State class applied to the root element if `error={true}`. */
  error: string;
  /** Class name applied to the root element if `fullWidth={true}`. */
  fullWidth: string;

  /** Styles applied to the root element if `size="small"`. */
  sizeSmall: string;
  /** Styles applied to the root element if `size="medium"`. */
  sizeMedium: string;
  /** Styles applied to the root element if `size="large"`. */
  sizeLarge: string;

  /** Class name applied to the error icon. */
  errorStateIcon: string;
}

export type InputClassKey = keyof InputClasses;

export function getInputUtilityClass(slot: string): string {
  return generateUtilityClass('NovaInput', slot);
}

const inputClasses: InputClasses = generateUtilityClasses('NovaInput', [
  'root',
  'wrapper',
  'input',
  'startDecorator',
  'endDecorator',

  'focused',
  'filled',
  'disabled',
  'error',
  'fullWidth',

  'sizeSmall',
  'sizeMedium',
  'sizeLarge',

  'errorStateIcon',
]);

export default inputClasses;
