import '@testing-library/jest-dom/vitest';
import { render, screen, cleanup, fireEvent } from '@testing-library/react';
import React from 'react';
import { describe, expect, it, afterEach, vi } from 'vitest';
import { Input } from './Input.tsx';

afterEach(() => {
  cleanup();
});

describe('Input', () => {
  it('should render normal', () => {
    render(<Input data-testid="NovaInput-root" />);
    expect(screen.getByTestId('NovaInput-root')).toHaveClass('NovaInput-root');
    expect(screen.getByTestId('NovaInput-root')).toHaveClass('NovaInput-sizeMedium');
    expect(screen.getByRole('textbox')).to.have.property('value', '');
  });

  it('should render error state', () => {
    render(<Input data-testid="NovaInput-root" error />);
    expect(screen.getByTestId('NovaInput-root')).toHaveClass('Mui-error');
  });

  it('should render disabled state', () => {
    render(<Input data-testid="NovaInput-root" disabled />);
    expect(screen.getByTestId('NovaInput-root')).toHaveClass('Mui-disabled');
  });

  it('should render fullWith state', () => {
    render(<Input data-testid="NovaInput-root" fullWidth />);
    expect(screen.getByTestId('NovaInput-root')).toHaveClass('NovaInput-fullWidth');
  });

  it('should render medium size', () => {
    render(<Input data-testid="NovaInput-root" size="medium" />);
    expect(screen.getByTestId('NovaInput-root')).toHaveClass('NovaInput-sizeMedium');
  });

  it('should render small size', () => {
    render(<Input data-testid="NovaInput-root" size="small" />);
    expect(screen.getByTestId('NovaInput-root')).toHaveClass('NovaInput-sizeSmall');
  });

  it('should render large size', () => {
    render(<Input data-testid="NovaInput-root" size="large" />);
    expect(screen.getByTestId('NovaInput-root')).toHaveClass('NovaInput-sizeLarge');
  });

  it('should default value work', () => {
    render(<Input data-testid="NovaInput-root" defaultValue={'xy'} />);
    expect(screen.getByRole('textbox')).to.have.property('value', 'xy');
    fireEvent.change(screen.getByRole('textbox'), { target: { value: 'abc' } });
    expect(screen.getByRole('textbox')).to.have.property('value', 'abc');
  });

  it('should value work', () => {
    render(<Input data-testid="NovaInput-root" value={'xy'} />);
    expect(screen.getByRole('textbox')).to.have.property('value', 'xy');
  });

  it('should onChange work', () => {
    const onChange = vi.fn();
    render(<Input data-testid="NovaInput-root" value={'xy'} onChange={onChange} />);
    fireEvent.change(screen.getByRole('textbox'), { target: { value: 'abc' } });
    expect(onChange).toHaveBeenCalled();
  });

  it('should startDecorator and endDecorator work', () => {
    render(<Input data-testid="NovaInput-root" startDecorator={<div>Prefix</div>} endDecorator={<div>Suffix</div>} />);
    expect(screen.getByText('Prefix')).toBeInTheDocument();
    expect(screen.getByText('Suffix')).toBeInTheDocument();
  });

  it('should slotProps work', () => {
    const onChange = vi.fn();
    render(
      <Input
        data-testid="NovaInput-root"
        startDecorator={<div>Prefix</div>}
        endDecorator={<div>Suffix</div>}
        slotProps={{
          input: { 'data-testid': 'NovaInput-input' },
          startDecorator: { 'data-testid': 'NovaInput-startDecorator' },
          endDecorator: { 'data-testid': 'NovaInput-endDecorator' },
        }}
        onChange={onChange}
      />,
    );
    expect(screen.getByTestId('NovaInput-input')).toHaveClass('NovaInput-input');
    expect(screen.getByTestId('NovaInput-startDecorator')).toHaveClass('NovaInput-startDecorator');
    expect(screen.getByTestId('NovaInput-endDecorator')).toHaveClass('NovaInput-endDecorator');
    fireEvent.change(screen.getByTestId('NovaInput-input'), { target: { value: 'abc' } });
    expect(onChange).toHaveBeenCalled();
  });
});
