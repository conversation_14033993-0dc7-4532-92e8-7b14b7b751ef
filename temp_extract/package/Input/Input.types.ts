import * as React from 'react';
import { OverrideProps } from '@mui/types';
import { CreateSlotsAndSlotProps, SlotProps } from '../types/slot';

export type InputSlot = 'root' | 'input' | 'startDecorator' | 'endDecorator';

export interface InputSlots {
  /**
   * The component that renders the root.
   * @default 'div'
   */
  root?: React.ElementType;
  /**
   * The component that renders the wrapper.
   * @default 'div'
   */
  wrapper?: React.ElementType;
  /**
   * The component that renders the input.
   * @default 'input'
   */
  input?: React.ElementType;
  /**
   * The component that renders the start decorator.
   * @default 'span'
   */
  startDecorator?: React.ElementType;
  /**
   * The component that renders the end decorator.
   * @default 'span'
   */
  endDecorator?: React.ElementType;
  /**
   * The component that renders the end decorator.
   * @default 'span'
   */
  errorStateIcon?: React.ElementType;
}

export interface InputPropsVariantOverrides {}
export interface InputPropsColorOverrides {}
export interface InputPropsSizeOverrides {}

export type InputSlotsAndSlotProps = CreateSlotsAndSlotProps<
  InputSlots,
  {
    root: SlotProps<'div', object, InputOwnerState>;
    wrapper: SlotProps<'div', object, InputOwnerState>;
    input: SlotProps<'input', object, InputOwnerState>;
    startDecorator: SlotProps<'span', object, InputOwnerState>;
    endDecorator: SlotProps<'span', object, InputOwnerState>;
    errorStateIcon: SlotProps<'span', object, InputOwnerState>;
  }
>;

export interface InputTypeMap<P = object, D extends React.ElementType = 'div'> {
  props: P &
    Pick<
      React.InputHTMLAttributes<HTMLInputElement>,
      | 'autoComplete'
      | 'autoFocus'
      | 'onClick'
      | 'onChange'
      | 'onKeyDown'
      | 'onKeyUp'
      | 'onFocus'
      | 'onBlur'
      | 'defaultValue'
      | 'value'
      | 'type'
      | 'placeholder'
      | 'readOnly'
      | 'required'
      | 'name'
      | 'id'
      | 'disabled'
      | 'maxLength'
    > & {
      /**
       * Trailing adornment for this input.
       */
      endDecorator?: React.ReactNode;
      /**
       * If `true`, the `input` will indicate an error.
       * The prop defaults to the value (`false`) inherited from the parent FormControl component.
       * @default false
       */
      error?: boolean;
      /**
       * Determines whether to display an error state icon at the end of `input` component.
       * This option only takes effect if the `error` property is set to `true`.
       * @default true
       */
      showErrorIcon?: boolean;
      /**
       * If `true`, the input will take up the full width of its container.
       * @default false
       */
      fullWidth?: boolean;
      /**
       * Leading adornment for this input.
       */
      startDecorator?: React.ReactNode;
      /**
       * The size of the component.
       * @default 'medium'
       */
      size?: 'small' | 'medium' | 'large';
    } & InputSlotsAndSlotProps;
  defaultComponent: D;
}

export type InputProps<
  D extends React.ElementType = InputTypeMap['defaultComponent'],
  P = {
    component?: React.ElementType;
  },
> = OverrideProps<InputTypeMap<P, D>, D>;

export default InputProps;

export interface InputOwnerState extends InputProps {
  /**
   * If `true`, the input is focused.
   */
  focused: boolean;
  /**
   * If `true`, the input has value.
   */
  filled: boolean;
}
