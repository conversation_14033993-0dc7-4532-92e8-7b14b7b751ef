import generateUtilityClasses from '@mui/utils/generateUtilityClasses';
import generateUtilityClass from '@mui/utils/generateUtilityClass';

export interface TextFieldClasses {
  /** Styles applied to the root element. */
  root: string;
  /** Styles applied to the supportTextRoot element. */
  supportTextRoot: string;
  /** Styles applied to the helperText element. */
  helperText: string;
  /** Styles applied to the counterText element. */
  counterText: string;
}

export type TextFieldClassKey = keyof TextFieldClasses;

export function getTextFieldUtilityClass(slot: string): string {
  return generateUtilityClass('NovaTextField', slot);
}

const textFieldClasses: TextFieldClasses = generateUtilityClasses('NovaTextField', [
  'root',
  'supportTextRoot',
  'helperText',
  'counterText',
]);

export default textFieldClasses;
