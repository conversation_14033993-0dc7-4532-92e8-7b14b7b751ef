import { render, screen, cleanup, fireEvent } from '@testing-library/react';
import React from 'react';
import { describe, expect, it, afterEach, vi } from 'vitest';
import { TextField } from './TextField.tsx';

afterEach(() => {
  cleanup();
});

describe('TextField', () => {
  it('should render normal', () => {
    render(<TextField data-testid="NovaTextField-root" />);
    expect(screen.getByTestId('NovaTextField-root')).toHaveClass('NovaTextField-root');
  });

  it('should render error state', () => {
    render(<TextField data-testid="NovaTextField-root" error />);
    expect(screen.getByTestId('NovaTextField-root')).toHaveClass('Mui-error');
  });

  it('should render disabled state', () => {
    render(<TextField data-testid="NovaTextField-root" disabled />);
    expect(screen.getByTestId('NovaTextField-root')).toHaveClass('Mui-disabled');
  });

  it('should label work', () => {
    render(<TextField data-testid="NovaTextField-root" label="Label A" />);
    expect(screen.getByText('Label A')).toBeInTheDocument();
  });

  it('should placeholder work', () => {
    render(<TextField data-testid="NovaTextField-root" placeholder="Name" />);
    expect(screen.getByPlaceholderText('Name')).toBeInTheDocument();
  });

  it('should helper text work', () => {
    render(<TextField data-testid="NovaTextField-root" helperText="Helper Text" />);
    expect(screen.getByText('Helper Text')).toBeInTheDocument();
  });

  it('should counter text work', () => {
    render(<TextField data-testid="NovaTextField-root" helperText="Helper Text" maxLength={20} />);
    expect(screen.getByText('Helper Text')).toBeInTheDocument();
    expect(screen.getByText('0/20')).toBeInTheDocument();
  });

  it('should render medium size', () => {
    render(
      <TextField
        data-testid="NovaTextField-root"
        size="medium"
        label="Label A"
        helperText="Helper Text"
        maxLength={20}
        slotProps={{
          label: { 'data-testid': 'NovaTextField-label' },
          input: { 'data-testid': 'NovaTextField-input' },
          helperText: { 'data-testid': 'NovaTextField-helperText' },
          counterText: { 'data-testid': 'NovaTextField-counterText' },
        }}
      />,
    );
    expect(screen.getByTestId('NovaTextField-label')).toHaveClass('NovaFormLabel-sizeMedium');
    expect(screen.getByTestId('NovaTextField-input')).toHaveClass('NovaInput-sizeMedium');
    expect(screen.getByTestId('NovaTextField-helperText')).toHaveClass('NovaFormHelperText-sizeMedium');
    expect(screen.getByTestId('NovaTextField-counterText')).toHaveClass('NovaFormHelperText-sizeMedium');
  });

  it('should render small size', () => {
    render(
      <TextField
        data-testid="NovaTextField-root"
        size="small"
        label="Label A"
        helperText="Helper Text"
        maxLength={20}
        slotProps={{
          label: { 'data-testid': 'NovaTextField-label' },
          input: { 'data-testid': 'NovaTextField-input' },
          helperText: { 'data-testid': 'NovaTextField-helperText' },
          counterText: { 'data-testid': 'NovaTextField-counterText' },
        }}
      />,
    );
    expect(screen.getByTestId('NovaTextField-label')).toHaveClass('NovaFormLabel-sizeSmall');
    expect(screen.getByTestId('NovaTextField-input')).toHaveClass('NovaInput-sizeSmall');
    expect(screen.getByTestId('NovaTextField-helperText')).toHaveClass('NovaFormHelperText-sizeSmall');
    expect(screen.getByTestId('NovaTextField-counterText')).toHaveClass('NovaFormHelperText-sizeSmall');
  });

  it('should render large size', () => {
    render(
      <TextField
        data-testid="NovaTextField-root"
        size="large"
        label="Label A"
        helperText="Helper Text"
        maxLength={20}
        slotProps={{
          label: { 'data-testid': 'NovaTextField-label' },
          input: { 'data-testid': 'NovaTextField-input' },
          helperText: { 'data-testid': 'NovaTextField-helperText' },
          counterText: { 'data-testid': 'NovaTextField-counterText' },
        }}
      />,
    );
    expect(screen.getByTestId('NovaTextField-label')).toHaveClass('NovaFormLabel-sizeLarge');
    expect(screen.getByTestId('NovaTextField-input')).toHaveClass('NovaInput-sizeLarge');
    expect(screen.getByTestId('NovaTextField-helperText')).toHaveClass('NovaFormHelperText-sizeLarge');
    expect(screen.getByTestId('NovaTextField-counterText')).toHaveClass('NovaFormHelperText-sizeLarge');
  });

  it('should default value work', () => {
    render(<TextField data-testid="NovaTextField-root" defaultValue={'xy'} />);
    expect(screen.getByRole('textbox')).to.have.property('value', 'xy');
    fireEvent.change(screen.getByRole('textbox'), { target: { value: 'abc' } });
    expect(screen.getByRole('textbox')).to.have.property('value', 'abc');
  });

  it('should value work', () => {
    render(<TextField data-testid="NovaTextField-root" value={'xy'} />);
    expect(screen.getByRole('textbox')).to.have.property('value', 'xy');
  });

  it('should onChange work', () => {
    const onChange = vi.fn();
    render(<TextField data-testid="NovaTextField-root" value={'xy'} onChange={onChange} />);
    fireEvent.change(screen.getByRole('textbox'), { target: { value: 'abc' } });
    expect(onChange).toHaveBeenCalled();
  });

  it('should startDecorator and endDecorator work', () => {
    render(
      <TextField
        data-testid="NovaTextField-root"
        startDecorator={<div>Prefix</div>}
        endDecorator={<div>Suffix</div>}
      />,
    );
    expect(screen.getByText('Prefix')).toBeInTheDocument();
    expect(screen.getByText('Suffix')).toBeInTheDocument();
  });

  it('should slotProps work', () => {
    const onChange = vi.fn();
    render(
      <TextField
        data-testid="NovaTextField-root"
        startDecorator={<div>Prefix</div>}
        endDecorator={<div>Suffix</div>}
        slotProps={{
          htmlInput: { 'data-testid': 'NovaTextField-input' },
        }}
        onChange={onChange}
      />,
    );
    fireEvent.change(screen.getByTestId('NovaTextField-input'), { target: { value: 'abc' } });
    expect(onChange).toHaveBeenCalled();
  });
});
