import * as React from 'react';
import { OverrideProps } from '@mui/types';
import { FormHelperTextProps } from '../FormHelperText';
import { InputProps } from '../Input';
import { CreateSlotsAndSlotProps, SlotProps } from '../types/slot';
import { FormLabelProps } from '../FormLabel/FormLabel.types';
import { FormControlProps } from '../FormControl';
import { InputOwnerState } from '../Input/Input.types';
import TextareaProps from '../Textarea/Textarea.types';

export interface TextFieldPropsColorOverrides {}

type InputRootKeys =
  | 'autoComplete'
  | 'autoFocus'
  | 'disabled'
  | 'error'
  | 'showErrorIcon'
  | 'required'
  | 'readOnly'
  | 'maxLength'
  | 'fullWidth'
  | 'placeholder'
  | 'defaultValue'
  | 'value'
  | 'type'
  | 'size'
  | 'startDecorator'
  | 'endDecorator';

type TextareaRootKeys =
  | 'autoComplete'
  | 'autoFocus'
  | 'disabled'
  | 'error'
  | 'required'
  | 'readOnly'
  | 'maxLength'
  | 'fullWidth'
  | 'placeholder'
  | 'defaultValue'
  | 'value'
  | 'maxRows'
  | 'minRows'
  | 'size'
  | 'startDecorator'
  | 'endDecorator';

export interface TextFieldSlots {
  /**
   * The component that renders the root.
   * @default 'div'
   */
  root?: React.ElementType;
  /**
   * The component that renders the label.
   */
  label?: React.ElementType;
  /**
   * The component that renders the input.
   */
  input?: React.ElementType;
  /**
   * The component that renders the inner html input.
   */
  htmlInput?: React.ElementType;
  /**
   * The component that renders the helper text.
   */
  helperText?: React.ElementType;
  /**
   * The component that renders the counter text.
   * @default 'span'
   */
  counterText?: React.ElementType;
}

export type TextFieldSlotsAndSlotProps = CreateSlotsAndSlotProps<
  TextFieldSlots,
  {
    root: FormControlProps;
    label: FormLabelProps;
    input: InputProps & TextareaProps;
    htmlInput: SlotProps<'input', object, InputOwnerState>;
    helperText: FormHelperTextProps;
    counterText: FormHelperTextProps;
  }
>;

export interface TextFieldTypeMap<P = object, D extends React.ElementType = 'div'> {
  props: P &
    TextFieldSlotsAndSlotProps &
    Pick<InputProps, InputRootKeys> &
    Pick<TextareaProps, TextareaRootKeys> & {
      /**
       * The id of the `input` element.
       * Use this prop to make `label` and `helperText` accessible for screen readers.
       */
      id?: string;
      /**
       * The label content.
       */
      label?: React.ReactNode;
      /**
       * Name attribute of the `input` element.
       */
      name?: string;
      /**
       * The helper text content.
       */
      helperText?: React.ReactNode;
      /**
       * If `true`, a `textarea` element is rendered instead of an `input`.
       * @default false
       */
      multiline?: boolean;
      /**
       * Callback fired when the `input` is blurred.
       *
       * Notice that the first argument (event) might be undefined.
       */
      onBlur?: React.FocusEventHandler<HTMLInputElement | HTMLTextAreaElement>;
      /**
       * Callback fired when the value is changed.
       *
       * @param {React.ChangeEvent<HTMLTextAreaElement | HTMLInputElement>} event The event source of the callback.
       * You can pull out the new value by accessing `event.target.value` (string).
       */
      onChange?: React.ChangeEventHandler<HTMLTextAreaElement | HTMLInputElement>;
      onFocus?: React.FocusEventHandler<HTMLInputElement | HTMLTextAreaElement>;
      onKeyDown?: React.KeyboardEventHandler<HTMLTextAreaElement | HTMLInputElement>;
      onKeyUp?: React.KeyboardEventHandler<HTMLTextAreaElement | HTMLInputElement>;
    };
  defaultComponent: D;
}

export type TextFieldProps<
  D extends React.ElementType = TextFieldTypeMap['defaultComponent'],
  P = {
    component?: React.ElementType;
  },
> = OverrideProps<TextFieldTypeMap<P, D>, D>;

export interface TextFieldOwnerState extends TextFieldProps {
  showSupportText?: boolean;
}
