'use client';
import * as React from 'react';
import { OverridableComponent } from '@mui/types';
import { unstable_useId as useId, unstable_composeClasses as composeClasses } from '@mui/utils';
import useSlotProps from '@mui/utils/useSlotProps';
import { styled } from '@pigment-css/react';
import { FormControl } from '../FormControl';
import { FormLabel } from '../FormLabel';
import { FormHelperText } from '../FormHelperText';
import { Input as InputBase } from '../Input';
import { Textarea } from '../Textarea';
import { TextFieldOwnerState, TextFieldProps, TextFieldTypeMap } from './TextField.types';
import { getTextFieldUtilityClass } from './TextField.classes';
import clsx from 'clsx';

const useUtilityClasses = () => {
  const slots = {
    root: ['root'],
    supportTextRoot: ['supportTextRoot'],
    helperText: ['helperText'],
    counterText: ['counterText'],
  };

  return composeClasses(slots, getTextFieldUtilityClass, {});
};

const SupportTextRoot = styled('div')<TextFieldOwnerState>(({ theme }) => ({
  display: 'flex',
  justifyContent: 'space-between',
  gap: 4,
  variants: [
    {
      props: { showSupportText: false },
      style: { justifyContent: 'flex-end' },
    },
  ],
}));

// eslint-disable-next-line react/display-name
export const TextField = React.forwardRef((props: TextFieldProps, ref: React.ForwardedRef<Element>) => {
  const {
    children,
    className,
    component,
    slots = {},
    slotProps = {},
    label,
    helperText,
    id: idOverride,
    autoComplete,
    autoFocus,
    placeholder,
    defaultValue,
    maxLength,
    value,
    name,
    onBlur,
    onChange,
    onFocus,
    onKeyDown,
    onKeyUp,
    disabled = false,
    error = false,
    showErrorIcon = true,
    required = false,
    readOnly = false,
    size = 'medium',
    fullWidth = false,
    type = 'text',
    multiline = false,
    minRows,
    maxRows,
    startDecorator,
    endDecorator,
    ...rest
  } = props;

  const [tmpValue, setTmpValue] = React.useState(defaultValue);
  const id = useId(idOverride);
  const helperTextId = helperText && id ? `${id}-helper-text` : undefined;
  const formLabelId = label && id ? `${id}-label` : undefined;
  const classes = useUtilityClasses();
  const Input = slots.input || InputBase;
  const handleOnChange: React.ChangeEventHandler<HTMLInputElement | HTMLTextAreaElement> = (event) => {
    setTmpValue(event.target.value);
    onChange?.(event);
  };

  const supportTextRootProps = useSlotProps({
    elementType: SupportTextRoot,
    externalSlotProps: {},
    ownerState: { ...props, showSupportText: Boolean(helperText) },
    className: classes.supportTextRoot,
  });

  return (
    <FormControl
      disabled={disabled}
      error={error}
      required={required}
      size={size}
      fullWidth={fullWidth}
      defaultValue={defaultValue}
      value={value}
      onChange={handleOnChange}
      className={clsx(classes.root, className)}
      {...(slots.root && {
        component: slots.root,
      })}
      {...rest}
      {...slotProps.root}
    >
      {label && (
        <FormLabel
          htmlFor={id}
          id={formLabelId}
          required={required}
          size={size}
          error={error}
          disabled={disabled}
          {...slotProps.label}
          {...(slots.label && {
            component: slots.label,
          })}
        >
          {label}
        </FormLabel>
      )}
      {multiline ? (
        <Textarea
          id={id}
          name={name}
          minRows={minRows}
          maxRows={maxRows}
          aria-describedby={helperTextId}
          autoComplete={autoComplete}
          autoFocus={autoFocus}
          placeholder={placeholder}
          readOnly={readOnly}
          size={size}
          fullWidth={fullWidth}
          maxLength={maxLength}
          onBlur={onBlur}
          onFocus={onFocus}
          onKeyDown={onKeyDown}
          onKeyUp={onKeyUp}
          startDecorator={startDecorator}
          endDecorator={endDecorator}
          slots={{
            input: slots.htmlInput,
          }}
          slotProps={{
            input: slotProps.htmlInput,
          }}
          {...slotProps.input}
          {...(slots.input && {
            component: slots.input,
          })}
        >
          {children}
        </Textarea>
      ) : (
        <Input
          ref={ref}
          id={id}
          name={name}
          type={type}
          aria-describedby={helperTextId}
          autoComplete={autoComplete}
          autoFocus={autoFocus}
          placeholder={placeholder}
          readOnly={readOnly}
          showErrorIcon={showErrorIcon}
          size={size}
          fullWidth={fullWidth}
          maxLength={maxLength}
          onBlur={onBlur}
          onFocus={onFocus}
          onKeyDown={onKeyDown}
          onKeyUp={onKeyUp}
          startDecorator={startDecorator}
          endDecorator={endDecorator}
          slots={{
            input: slots.htmlInput,
          }}
          slotProps={{
            input: slotProps.htmlInput,
          }}
          {...slotProps.input}
          {...(slots.input && {
            component: slots.input,
          })}
        >
          {children}
        </Input>
      )}

      <SupportTextRoot {...supportTextRootProps}>
        {helperText && (
          <FormHelperText
            id={helperTextId}
            size={size}
            error={error}
            disabled={disabled}
            className={classes.helperText}
            {...slotProps.helperText}
            {...(slots.helperText && {
              component: slots.helperText,
            })}
          >
            {helperText}
          </FormHelperText>
        )}
        {maxLength && (
          <FormHelperText
            size={size}
            disabled={disabled}
            error={false}
            className={classes.counterText}
            {...slotProps.counterText}
            {...(slots.counterText && {
              component: slots.counterText,
            })}
          >
            {`${Array.isArray(tmpValue) ? tmpValue.length : `${tmpValue || ''}`.length || 0}/${maxLength}`}
          </FormHelperText>
        )}
      </SupportTextRoot>
    </FormControl>
  );
}) as OverridableComponent<TextFieldTypeMap>;
