import { render, screen, cleanup, fireEvent } from '@testing-library/react';
import React from 'react';
import { describe, expect, it, afterEach, vi } from 'vitest';
import { Search } from './Search.tsx';

afterEach(() => {
  cleanup();
});

describe('Search', () => {
  it('should render normal', () => {
    render(<Search data-testid="NovaSearch-root" />);
    expect(screen.getByTestId('NovaSearch-root')).toHaveClass('NovaSearch-root');
  });

  it('should placeholder work', () => {
    render(<Search data-testid="NovaSearch-root" placeholder="Name" />);
    expect(screen.getByPlaceholderText('Name')).toBeInTheDocument();
  });

  it('should render medium size', () => {
    render(
      <Search
        size="medium"
        slotProps={{
          input: { 'data-testid': 'NovaSearch-input' },
        }}
      />,
    );
    expect(screen.getByTestId('NovaSearch-input')).toHaveClass('NovaInput-sizeMedium');
  });

  it('should render small size', () => {
    render(
      <Search
        size="small"
        slotProps={{
          input: { 'data-testid': 'NovaSearch-input' },
        }}
      />,
    );
    expect(screen.getByTestId('NovaSearch-input')).toHaveClass('NovaInput-sizeSmall');
  });

  it('should render large size', () => {
    render(
      <Search
        size="large"
        slotProps={{
          input: { 'data-testid': 'NovaSearch-input' },
        }}
      />,
    );
    expect(screen.getByTestId('NovaSearch-input')).toHaveClass('NovaInput-sizeLarge');
  });

  it('should render fullWidth', () => {
    render(
      <Search
        fullWidth
        placeholder="Name"
        slotProps={{
          input: { 'data-testid': 'NovaSearch-input' },
        }}
      />,
    );
    expect(screen.getByTestId('NovaSearch-input')).toHaveClass('NovaInput-fullWidth');
  });

  it('should default value work', () => {
    render(<Search defaultValue="default value" />);
    expect(screen.getByRole('textbox')).toHaveValue('default value');
  });

  it('should value work', () => {
    render(<Search value="value" />);
    expect(screen.getByRole('textbox')).toHaveValue('value');
  });

  it('should onChange work', () => {
    const handleChange = vi.fn();
    render(<Search value={'xy'} onChange={handleChange} />);
    fireEvent.change(screen.getByRole('textbox'), { target: { value: 'value' } });
    expect(handleChange).toHaveBeenCalled();
  });

  it('should startDecorator and endDecorator work', () => {
    render(
      <Search
        startDecorator={<div data-testid="startDecorator" />}
        endDecorator={<div data-testid="endDecorator" />}
      />,
    );
    expect(screen.getByTestId('startDecorator')).toBeInTheDocument();
    expect(screen.getByTestId('endDecorator')).toBeInTheDocument();
  });

  it('should slotProps work', () => {
    const handleChange = vi.fn();
    render(
      <Search
        slotProps={{
          htmlInput: { 'data-testid': 'NovaSearch-input' },
        }}
        onChange={handleChange}
      />,
    );
    fireEvent.change(screen.getByTestId('NovaSearch-input'), { target: { value: 'value' } });
    expect(handleChange).toHaveBeenCalled();
  });
});
