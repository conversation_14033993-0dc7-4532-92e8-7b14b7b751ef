'use client';

import * as React from 'react';
import { OverridableComponent } from '@mui/types';
import { styled } from '@pigment-css/react';
import { unstable_useId as useId, unstable_composeClasses as composeClasses } from '@mui/utils';
import { SearchProps, SearchOwnerState, SearchTypeMap } from './Search.types';
import { getSearchUtilityClass } from './Search.classes';
import { Input } from '../Input';
import { FormControl } from '../FormControl';
import clsx from 'clsx';

const useUtilityClasses = () => {
  const slots = {
    root: ['root'],
  };
  return composeClasses(slots, getSearchUtilityClass, {});
};

const SearchInput = styled(Input)<SearchOwnerState>(({ theme }) => ({
  borderRadius: '12px',
}));

// eslint-disable-next-line react/display-name
export const Search = React.forwardRef((props: SearchProps, ref: React.ForwardedRef<Element>) => {
  const {
    children,
    className,
    component,
    slots = {},
    slotProps = {},
    id: idOverride,
    autoComplete = 'off',
    autoFocus,
    placeholder,
    defaultValue,
    value,
    name,
    onBlur,
    onChange,
    onFocus,
    onKeyDown,
    onKeyUp,
    size = 'medium',
    fullWidth = false,
    type = 'text',
    startDecorator,
    endDecorator,
    ...rest
  } = props;

  const id = useId(idOverride);

  const classes = useUtilityClasses();
  const Input = slots.input || SearchInput;

  const handleOnChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    onChange?.(event);
  };

  return (
    <FormControl
      size={size}
      fullWidth={fullWidth}
      defaultValue={defaultValue}
      value={value}
      onChange={handleOnChange}
      className={clsx(classes.root, className)}
      {...(slots.root && {
        component: slots.root,
      })}
      {...rest}
      {...slotProps.root}
    >
      <Input
        id={id}
        ref={ref}
        name={name}
        type={type}
        autoComplete={autoComplete}
        autoFocus={autoFocus}
        placeholder={placeholder}
        size={size}
        fullWidth={fullWidth}
        onBlur={onBlur}
        onFocus={onFocus}
        onKeyDown={onKeyDown}
        onKeyUp={onKeyUp}
        startDecorator={startDecorator}
        endDecorator={endDecorator}
        slots={{
          input: slots.htmlInput,
        }}
        slotProps={{
          input: slotProps.htmlInput,
        }}
        {...slotProps.input}
        {...(slots.input && {
          component: slots.input,
        })}
      >
        {children}
      </Input>
    </FormControl>
  );
}) as OverridableComponent<SearchTypeMap>;
