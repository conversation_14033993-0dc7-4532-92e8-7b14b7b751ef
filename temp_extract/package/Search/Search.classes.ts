import {
  unstable_generateUtilityClasses as generateUtilityClasses,
  unstable_generateUtilityClass as generateUtilityClass,
} from '@mui/utils';

export interface SearchClasses {
  /** Styles applied to the root element. */
  root: string;
}

export type SearchClassKey = keyof SearchClasses;

export function getSearchUtilityClass(slot: string): string {
  return generateUtilityClass('NovaSearch', slot);
}

const SearchClasses: SearchClasses = generateUtilityClasses('NovaSearch', ['root']);

export default SearchClasses;
