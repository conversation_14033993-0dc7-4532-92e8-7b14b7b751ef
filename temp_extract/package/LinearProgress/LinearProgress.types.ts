import * as React from 'react';
import { OverridableStringUnion, OverrideProps } from '@mui/types';
import { CreateSlotsAndSlotProps, SlotProps } from '../types/slot';
import { SxProps } from '../types/theme';
import { ApplyColorInversion } from '../types/colorSystem';

export interface LinearProgressPropsColorOverrides {}

export interface LinearProgressSlots {
  /**
   * The component that renders the root.
   * @default 'div'
   */
  root?: React.ElementType;
}

export type LinearProgressSlotsAndSlotProps = CreateSlotsAndSlotProps<
  LinearProgressSlots,
  {
    root: SlotProps<'div', object, LinearProgressOwnerState>;
  }
>;

export interface LinearProgressTypeMap<P = object, D extends React.ElementType = 'div'> {
  props: P & {
    /**
     * The color of the component.
     * @default 'primary'
     */
    color?: OverridableStringUnion<
      'primary' | 'error' | 'info' | 'success' | 'warning' | 'inherit',
      LinearProgressPropsColorOverrides
    >;
    /**
     * The system prop that allows defining system overrides as well as additional CSS styles.
     */
    sx?: SxProps;
    /**
     * The thickness of the bar.
     * @default 4
     */
    thickness?: number;
    /**
     * The value of the progress indicator for the determinate and buffer variants.
     * Value between 0 and 100.
     * @default variant === 'determinate' ? 0 : 25
     */
    value?: number;
    /**
     * The variant to use.
     * Use indeterminate or query when there is no progress value.
     * @default 'indeterminate'
     */
    variant?: 'determinate' | 'indeterminate';
  } & LinearProgressSlotsAndSlotProps;
  defaultComponent: D;
}

export type LinearProgressProps<
  D extends React.ElementType = LinearProgressTypeMap['defaultComponent'],
  P = { component?: React.ElementType },
> = OverrideProps<LinearProgressTypeMap<P, D>, D>;

export interface LinearProgressOwnerState extends ApplyColorInversion<LinearProgressProps> {}
