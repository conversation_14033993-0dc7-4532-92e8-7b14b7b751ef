'use client';
import * as React from 'react';
import { unstable_capitalize as capitalize, unstable_composeClasses as composeClasses } from '@mui/utils';
import { keyframes, styled } from '@pigment-css/react';
import useSlotProps from '@mui/utils/useSlotProps';
import { getLinearProgressUtilityClass } from './LinearProgress.classes';
import { LinearProgressOwnerState, LinearProgressProps } from './LinearProgress.types';
import { ColorPaletteSystem } from '../types/colorSystem';
import { SystemColor } from '../Alert/Alert.types';

// **First progress bar animation**
const indeterminatePrimary = keyframes`
  0% {
    left: -35%;
    right: 100%;
  }

  60% {
    left: 100%;
    right: -60%;
  }

  70% {
    left: 100%;
    right: -70%;
  }

  100% {
    left: 100%;
    right: -90%;
  }
`;

const indeterminateSecondary = keyframes`
  0% {
    left: -100%;
    right: 150%;
  }

  60% {
    left: 107%;
    right: -17%;
  }

  70% {
    left: 107%;
    right: -7%;
  }

  100% {
   left: 100%;
    right: -90%;
  }
`;

const useUtilityClasses = (ownerState: LinearProgressOwnerState) => {
  const { variant, color } = ownerState;

  const slots = {
    root: ['root', color && `color${capitalize(color)}`, variant],
  };
  return composeClasses(slots, getLinearProgressUtilityClass, {});
};

const LinearProgressDot = styled('span')<LinearProgressProps>(({ theme }) => ({
  position: 'absolute',
  width: 'var(--nova-linearProgress-thickness)',
  height: 'var(--nova-linearProgress-thickness)',
  borderRadius: '50%',
  backgroundColor: 'var(--nova-linearProgress-indicatorColor)',
  right: 'calc(var(--nova-linearProgress-thickness) / 4)',
  zIndex: 1,
  variants: [
    {
      props: (props) => props.value === 100,
      style: {
        borderRadius: 'unset',
      },
    },
  ],
}));

export const LinearProgressRoot = styled('div')<LinearProgressProps>(({ theme }) => ({
  '--nova-linearProgress-trackColor': theme.vars.palette.secondaryContainer,
  '--nova-linearProgress-indicatorColor': theme.vars.palette.primary,
  '--nova-linearProgress-thickness': '3px',
  '--nova-linearProgress-radius': 'var(--nova-linearProgress-thickness)',
  '--nova-linearProgress-progressThickness': 'var(--nova-linearProgress-thickness)',
  '--nova-linearProgress-progressRadius':
    'max(var(--nova-linearProgress-radius) - var(--nova-linearProgress-padding), min(var(--nova-linearProgress-padding) / 2, var(--nova-linearProgress-radius) / 2))',
  minBlockSize: 'var(--nova-linearProgress-thickness)',
  boxSizing: 'border-box',
  borderRadius: 'var(--nova-linearProgress-radius)',
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  flex: 1,
  overflow: 'hidden',
  position: 'relative',
  color: 'var(--nova-linearProgress-indicatorColor)',
  '--nova-linearProgress-padding':
    'max((var(--nova-linearProgress-thickness) - 2 * var(--variant-borderWidth, 0px) - var(--nova-linearProgress-progressThickness)) / 2, 0px)',
  variants: [
    {
      props: { color: 'primary' },
      style: {
        '--nova-linearProgress-indicatorColor': theme.vars.palette.primary,
      },
    },
    {
      props: { color: 'inherit' },
      style: {
        '--nova-linearProgress-indicatorColor': 'currentColor',
        backgroundColor: 'none',
      },
    },
    {
      props: { color: 'error' },
      style: {
        '--nova-linearProgress-indicatorColor': theme.vars.palette.error,
      },
    },
    ...(['success', 'info', 'warning'] as SystemColor[]).map((color: SystemColor) => ({
      props: { color },
      style: {
        display: 'flex',
        '--nova-linearProgress-indicatorColor': theme.vars.palette.system[color as ColorPaletteSystem],
      },
    })),
    {
      props: { variant: 'indeterminate' },
      style: {
        backgroundColor: 'var(--nova-linearProgress-trackColor)',
        '--nova-linearProgress-progressMinWidth': 'calc(var(--nova-linearProgress-percent) * 1% / 2)',
        '--nova-linearProgress-progressMaxWidth': 'calc(var(--nova-linearProgress-percent) * 1%)',
        '--nova-linearProgress-progressLeft':
          'calc(100% - var(--nova-linearProgress-progressMinWidth) - var(--nova-linearProgress-progressInset))',
        '--nova-linearProgress-progressInset':
          'calc(var(--nova-linearProgress-thickness) / 2 - var(--nova-linearProgress-progressThickness) / 2)',
      },
    },
  ],
}));

const LinearProgressBar1 = styled('div')<LinearProgressProps>(({ theme }) => ({
  position: 'absolute',
  height: '100%',
  backgroundColor: 'var(--nova-linearProgress-indicatorColor)',
  blockSize: 'var(--nova-linearProgress-progressThickness)',
  borderRadius: 'var(--nova-linearProgress-progressRadius)',
  animation: `${indeterminatePrimary} 2.1s cubic-bezier(0.6, 0.85, 0.75, 0.4) infinite`,
  variants: [
    {
      props: { variant: 'determinate' },
      style: {
        animation: 'unset',
        left: 'var(--nova-linearProgress-padding)',
        transition: 'width 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
        transformOrigin: 'left',
        inlineSize: 'calc(var(--nova-linearProgress-percent) * 1% - 2 * var(--nova-linearProgress-padding) - 4px)',
        width: 'calc(var(--nova-linearProgress-percent) * 1% - 2 * var(--nova-linearProgress-padding) - 4px)',
      },
    },
    {
      props: (props) => props.value === 100,
      style: {
        width: '100%',
      },
    },
  ],
}));

const LinearProgressBar2 = styled('div')<LinearProgressProps>(({ theme }) => ({
  position: 'absolute',
  height: '100%',
  backgroundColor: 'var(--nova-linearProgress-indicatorColor)',
  borderRadius: 'var(--nova-linearProgress-progressRadius)',
  animation: `${indeterminateSecondary} 2.1s cubic-bezier(0.16, 0.8, 0.4, 1) 1.2s infinite`,
  variants: [
    {
      props: { variant: 'determinate' },
      style: {
        animation: 'unset',
        backgroundColor: 'var(--nova-linearProgress-trackColor)',
        transition: 'width 0.3s cubic-bezier(0.4, 0, 0.2, 1), left 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
        transformOrigin: 'right',
        width: 'fit-content',
        right: 'var(--nova-linearProgress-padding)',
        left: 'calc(var(--nova-linearProgress-percent) * 1% + 4px)',
        inlineSize:
          'calc((100% - var(--nova-linearProgress-percent) * 1%) - 2 * var(--nova-linearProgress-padding) - 4px)',
      },
    },
  ],
}));

export const LinearProgress = React.forwardRef(function LinearProgress(
  props: LinearProgressProps,
  ref: React.ForwardedRef<HTMLSpanElement>,
) {
  const {
    className,
    variant = 'indeterminate',
    color = 'primary',
    value: rawValue = variant === 'determinate' ? 0 : 25,
    thickness = 4,
    component = 'div',
    style,
    slots = {},
    slotProps = {},
    ...other
  } = props;

  // Clamp value between 0 and 100
  const value = Math.min(100, Math.max(0, rawValue));

  const ownerState = {
    ...props,
    color,
    value,
    variant,
    component,
  };

  const classes = useUtilityClasses(ownerState);
  const SlotRoot = slots.root ?? LinearProgressRoot;

  const rootSlotProps = useSlotProps({
    additionalProps: {
      ref,
      as: component,
      role: 'progressbar',
      style: {
        ...({
          '--nova-linearProgress-percent': value,
          '--nova-linearProgress-thickness': `${thickness}px`,
        } as React.CSSProperties),
        ...style,
      },
      ...(typeof value === 'number' &&
        variant === 'determinate' && {
          'aria-valuemin': 0,
          'aria-valuemax': 100,
          'aria-valuenow': Math.round(value),
        }),
    },
    className: [classes.root, className],
    elementType: LinearProgressRoot,
    externalSlotProps: slotProps.root,
    externalForwardedProps: other,
    ownerState,
  });

  return (
    <SlotRoot {...rootSlotProps}>
      <LinearProgressBar1 {...props} />
      <LinearProgressBar2 {...props} />
      {variant === 'determinate' && <LinearProgressDot {...props} />}
    </SlotRoot>
  );
});
