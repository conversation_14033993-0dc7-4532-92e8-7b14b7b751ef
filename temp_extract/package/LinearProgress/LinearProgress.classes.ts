import {
  unstable_generateUtilityClasses as generateUtilityClasses,
  unstable_generateUtilityClass as generateUtilityClass,
} from '@mui/utils';

export interface LinearProgressClasses {
  /** Styles applied to the root element. */
  root: string;
  /** Styles applied to the root element if `color="primary"`; */
  colorPrimary: string;
  /** Styles applied to the root element if `variant="determinate"`. */
  determinate: string;
  /** Styles applied to the root element if `variant="indeterminate"`. */
  indeterminate: string;
}

export function getLinearProgressUtilityClass(slot: string): string {
  return generateUtilityClass('NovaLinearProgress', slot);
}

const linearProgressClasses: LinearProgressClasses = generateUtilityClasses('NovaLinearProgress', [
  'root',
  'colorPrimary',
  'colorError',
  'colorInfo',
  'colorWarning',
  'colorSuccess',
  'determinate',
  'indeterminate',
]);

export default linearProgressClasses;
