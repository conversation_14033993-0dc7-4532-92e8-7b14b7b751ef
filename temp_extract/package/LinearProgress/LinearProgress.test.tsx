import React from 'react';
import '@testing-library/jest-dom/vitest';
import { screen, render, cleanup } from '@testing-library/react';
import { describe, expect, it, afterEach, vi } from 'vitest';
import { LinearProgress } from './LinearProgress';
import linearProgressClasses from './LinearProgress.classes';

vi.mock('@pigment-css/react', async () => {
  const actual = await import('@pigment-css/react');
  return {
    ...actual,
    keyframes: vi.fn(() => 'mocked-keyframe'),
  };
});

describe('<LinearProgress />', () => {
  afterEach(cleanup);

  it('should render indeterminate variant by default', () => {
    render(<LinearProgress />);
    const progressbar = screen.getByRole('progressbar');

    expect(progressbar).toHaveClass(linearProgressClasses.root);
    expect(progressbar).toHaveClass(linearProgressClasses.indeterminate);
  });

  it('should render for the primary color by default', () => {
    render(<LinearProgress />);
    const progressbar = screen.getByRole('progressbar');

    expect(progressbar).toHaveClass(linearProgressClasses.colorPrimary);
  });

  it('should render with determinate classes for the primary color by default', () => {
    render(<LinearProgress value={1} variant="determinate" />);
    const progressbar = screen.getByRole('progressbar');

    expect(progressbar).toHaveClass(linearProgressClasses.determinate);
    expect(progressbar).toHaveClass(linearProgressClasses.colorPrimary);
  });

  it('should render with determinate classes for the primary color', () => {
    render(<LinearProgress color="primary" value={1} variant="determinate" />);
    const progressbar = screen.getByRole('progressbar');

    expect(progressbar).toHaveClass(linearProgressClasses.determinate);
    expect(progressbar).toHaveClass(linearProgressClasses.colorPrimary);
  });

  it('exposes the current, min and max value to screen readers when determinate', () => {
    render(<LinearProgress variant="determinate" value={77} />);
    const progressbar = screen.getByRole('progressbar');

    expect(progressbar).toHaveAttribute('aria-valuenow', '77');
    expect(progressbar).toHaveAttribute('aria-valuemin', '0');
    expect(progressbar).toHaveAttribute('aria-valuemax', '100');
  });
});
