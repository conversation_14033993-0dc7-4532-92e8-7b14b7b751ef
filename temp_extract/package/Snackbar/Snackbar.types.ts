import * as React from 'react';
import { OverrideProps } from '@mui/types';
import { CreateSlotsAndSlotProps, SlotProps } from '../types/slot';
import { ClickAwayListenerProps } from '../ClickAwayListener';

export type SnackbarSlot = 'root' | 'clickAway';

export interface SnackbarSlots {
  /**
   * The component that renders the root.
   * @default 'div'
   */
  root?: React.ElementType;
  /**
   * The component that renders the click away.
   * @default ClickAwayListener
   */
  clickAway?: React.ElementType;
}

export type SnackbarSlotsAndSlotProps = CreateSlotsAndSlotProps<
  SnackbarSlots,
  {
    root: SlotProps<'div', object, SnackbarOwnerState>;
    clickAway: ClickAwayListenerProps | ((ownerState: SnackbarOwnerState) => ClickAwayListenerProps);
  }
>;

export interface SnackbarOrigin {
  vertical: 'top' | 'bottom';
  horizontal: 'left' | 'center' | 'right';
}

export type SnackbarCloseReason = 'timeout' | 'clickaway' | 'escapeKeyDown';

export interface SnackbarTypeMap<P = object, D extends React.ElementType = 'div'> {
  props: P &
    SnackbarSlotsAndSlotProps & {
      /**
       * The anchor of the `Snackbar`.
       * On smaller screens, the component grows to occupy all the available width,
       * the horizontal alignment is ignored.
       * @default { vertical: 'bottom', horizontal: 'center' }
       */
      anchorOrigin?: SnackbarOrigin;
      /**
       * The number of milliseconds to wait before automatically calling the
       * `onClose` function. `onClose` should then set the state of the `open`
       * prop to hide the Snackbar. This behavior is disabled by default with
       * the `null` value.
       * @default null
       */
      autoHideDuration?: number | null;

      /**
       * If `true`, the `autoHideDuration` timer will expire even if the window is not focused.
       * @default false
       */
      disableWindowBlurListener?: boolean;
      /**
       * Callback fired when the component requests to be closed.
       * Typically `onClose` is used to set state in the parent component,
       * which is used to control the `Snackbar` `open` prop.
       * The `reason` parameter can optionally be used to control the response to `onClose`,
       * for example ignoring `clickaway`.
       *
       * @param {React.SyntheticEvent<any> | Event} event The event source of the callback.
       * @param {string} reason Can be: `"timeout"` (`autoHideDuration` expired), `"clickaway"`, or `"escapeKeyDown"`.
       */
      onClose?: (event: React.SyntheticEvent<any> | Event, reason: SnackbarCloseReason) => void;
      /**
       * A callback fired when the component is about to be unmounted.
       */
      onUnmount?: () => void;
      /**
       * If `true`, the component is shown.
       */
      open?: boolean;
      /**
       * The number of milliseconds to wait before dismissing after user interaction.
       * If `autoHideDuration` prop isn't specified, it does nothing.
       * If `autoHideDuration` prop is specified but `resumeHideDuration` isn't,
       * we default to `autoHideDuration / 2` ms.
       */
      resumeHideDuration?: number;
      /**
       * The duration of the animation in milliseconds. This value is used to control
       * the length of time it takes for an animation to complete one cycle. It is also
       * utilized for delaying the unmount of the component.
       * Provide this value if you have your own animation so that we can precisely
       * time the component's unmount to match your custom animation.
       * @default 300
       */
      animationDuration?: number;
    };
  defaultComponent: D;
}

export type SnackbarProps<
  D extends React.ElementType = SnackbarTypeMap['defaultComponent'],
  P = { component?: React.ElementType },
> = OverrideProps<SnackbarTypeMap<P, D>, D>;

export interface SnackbarOwnerState extends SnackbarProps {}
