import React from 'react';
import { Snackbar } from './Snackbar';
import { fireEvent, render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, expect, it, vi } from 'vitest';

vi.mock('@pigment-css/react', async () => {
  const actual = await import('@pigment-css/react');
  return {
    ...actual,
    keyframes: vi.fn(() => 'mocked-keyframe'),
  };
});

describe('Snackbar', () => {
  it('should not render anything when closed', () => {
    render(<Snackbar open={false}>Message</Snackbar>);
    expect(screen.queryByText('Message')).toBeNull();
  });

  it('should be able show it after mounted', () => {
    const handleClose = vi.fn();
    const { rerender } = render(
      <Snackbar open={false} onClose={handleClose}>
        Message
      </Snackbar>,
    );

    rerender(
      <Snackbar open onClose={handleClose}>
        Message
      </Snackbar>,
    );

    expect(screen.getByText('Message')).toBeInTheDocument();
  });

  it('should be call when clicking away', async () => {
    const handleClose = vi.fn();
    render(
      <Snackbar open onClose={handleClose}>
        Message
      </Snackbar>,
    );

    await userEvent.click(document.body);
    expect(handleClose).toBeCalled();
  });

  it('should be called when pressing Escape', () => {
    const handleClose = vi.fn();
    render(
      <Snackbar open onClose={handleClose}>
        Message
      </Snackbar>,
    );

    fireEvent.keyDown(document.body, { key: 'Escape' });

    expect(handleClose).toBeCalled();
  });

  it('can limit which Snackbars are closed when pressing Escape', () => {
    const handleCloseA = vi.fn((e) => e.preventDefault());
    const handleCloseB = vi.fn();
    render(
      <React.Fragment>
        <Snackbar open onClose={handleCloseA}>
          Message A
        </Snackbar>
        <Snackbar open onClose={handleCloseB}>
          Message B
        </Snackbar>
      </React.Fragment>,
    );

    fireEvent.keyDown(document.body, { key: 'Escape' });

    expect(handleCloseA).toBeCalledTimes(1);
    expect(handleCloseB).toBeCalledTimes(0);
  });

  it('should call onClose when the timer is done', () => {
    vi.useFakeTimers();
    const handleClose = vi.fn();
    render(
      <Snackbar open autoHideDuration={1000} onClose={handleClose}>
        Message
      </Snackbar>,
    );

    vi.advanceTimersByTime(1000);

    expect(handleClose).toBeCalled();
  });

  it('should not call onClose when the autoHideDuration is reset', () => {
    const handleClose = vi.fn();
    const autoHideDuration = 2000;
    const { rerender } = render(
      <Snackbar open={false} onClose={handleClose} autoHideDuration={autoHideDuration}>
        Message
      </Snackbar>,
    );

    rerender(
      <Snackbar open onClose={handleClose} autoHideDuration={undefined}>
        Message
      </Snackbar>,
    );

    vi.advanceTimersByTime(autoHideDuration);

    expect(handleClose).toBeCalledTimes(0);
  });

  it('should not call onClose if autoHideDuration is undefined', () => {
    const handleClose = vi.fn();
    render(
      <Snackbar open onClose={handleClose} autoHideDuration={undefined}>
        Message
      </Snackbar>,
    );

    vi.advanceTimersByTime(3000);

    expect(handleClose).toBeCalledTimes(0);
  });

  it('should not call onClose if autoHideDuration is null', () => {
    const handleClose = vi.fn();
    render(
      <Snackbar open onClose={handleClose} autoHideDuration={null}>
        Message
      </Snackbar>,
    );

    vi.advanceTimersByTime(3000);

    expect(handleClose).toBeCalledTimes(0);
  });

  it('should not call onClose when closed', () => {
    const handleClose = vi.fn();
    render(
      <Snackbar open={false} onClose={handleClose}>
        Message
      </Snackbar>,
    );

    vi.advanceTimersByTime(3000);
    expect(handleClose).toBeCalledTimes(0);
  });

  it('should call onClose when timer done after user interaction', () => {
    const handleClose = vi.fn();
    const autoHideDuration = 2000;
    const resumeHideDuration = 3000;
    render(
      <Snackbar
        data-testid="snackbar"
        open
        onClose={handleClose}
        autoHideDuration={autoHideDuration}
        resumeHideDuration={resumeHideDuration}
      >
        Message
      </Snackbar>,
    );

    vi.advanceTimersByTime(autoHideDuration / 2);
    fireEvent.mouseEnter(screen.getByTestId('snackbar'));
    vi.advanceTimersByTime(autoHideDuration / 2);
    fireEvent.mouseLeave(screen.getByTestId('snackbar'));
    vi.advanceTimersByTime(resumeHideDuration);

    expect(handleClose).toBeCalledTimes(1);
  });

  it('should call onClose immediately after user interaction when 0', () => {
    const handleClose = vi.fn();
    const autoHideDuration = 6000;
    const resumeHideDuration = 0;
    render(
      <Snackbar
        data-testid="snackbar"
        open
        onClose={handleClose}
        autoHideDuration={autoHideDuration}
        resumeHideDuration={resumeHideDuration}
      >
        Message
      </Snackbar>,
    );

    fireEvent.mouseEnter(screen.getByTestId('snackbar'));
    vi.advanceTimersByTime(100);
    fireEvent.mouseLeave(screen.getByTestId('snackbar'));
    vi.advanceTimersByTime(resumeHideDuration);

    expect(handleClose).toBeCalledTimes(1);
  });

  it('should pause auto hide when not disabled and window lost focus', () => {
    const handleClose = vi.fn();
    const autoHideDuration = 2000;
    render(
      <Snackbar open onClose={handleClose} autoHideDuration={autoHideDuration} disableWindowBlurListener={false}>
        Message
      </Snackbar>,
    );

    fireEvent.blur(window);
    vi.advanceTimersByTime(autoHideDuration);
    fireEvent.focus(window);
    vi.advanceTimersByTime(autoHideDuration);

    expect(handleClose).toBeCalledTimes(1);
  });

  it('should not pause auto hide when disabled and window lost focus', () => {
    const handleClose = vi.fn();
    const autoHideDuration = 2000;
    render(
      <Snackbar open onClose={handleClose} autoHideDuration={autoHideDuration} disableWindowBlurListener>
        Message
      </Snackbar>,
    );

    fireEvent.blur(window);
    vi.advanceTimersByTime(autoHideDuration);

    expect(handleClose).toBeCalledTimes(1);
  });
});
