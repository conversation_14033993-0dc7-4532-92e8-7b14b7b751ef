'use client';
import * as React from 'react';
import { unstable_capitalize as capitalize, unstable_composeClasses as composeClasses } from '@mui/utils';
import { SnackbarOwnerState, SnackbarProps, SnackbarTypeMap } from './Snackbar.types';
import { getSnackbarUtilityClass } from './Snackbar.classes';
import { keyframes, styled } from '@pigment-css/react';
import { useSnackbar } from '../internal/hooks/useSnackbar/useSnackbar';
import { ClickAwayListener } from '../ClickAwayListener';
import useSlotProps from '@mui/utils/useSlotProps';
import { OverridableComponent } from '@mui/types';

const useUtilityClasses = (ownerState: SnackbarOwnerState) => {
  const { anchorOrigin } = ownerState;

  const slots = {
    root: ['root', `anchorOrigin${capitalize(anchorOrigin.vertical)}${capitalize(anchorOrigin.horizontal)}`],
  };

  return composeClasses(slots, getSnackbarUtilityClass, {});
};

const enterAnimation = keyframes`
  0% {
    transform: translateX(var(--nova-snackbar-translateX, 0px)) scale(0.75);
    opacity: 0;
  }
  100% {
    transform: translateX(var(--nova-snackbar-translateX, 0px)) scale(1);
    opacity: 1;
  }
`;

const exitAnimation = keyframes`
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
`;

const SnackbarRoot = styled('div', {
  name: 'NovaSnackbar',
  slot: 'Root',
})<SnackbarProps>(({ theme }) => ({
  position: 'fixed',
  zIndex: 1400,
  display: 'flex',
  left: 8,
  right: 8,
  justifyContent: 'center',
  alignItems: 'center',
  boxSizing: 'border-box',
  animation: `${enterAnimation} var(--nova-snackbar-animation-duration, 300ms) forwards`,

  variants: [
    {
      props: (ownerState) => ownerState.anchorOrigin.vertical === 'top',
      style: {
        top: 8,
      },
    },
    {
      props: (ownerState) => ownerState.anchorOrigin.vertical !== 'top',
      style: {
        bottom: 8,
        '@media (min-width: 600px)': {
          bottom: 24,
        },
      },
    },
    {
      props: (ownerState) => ownerState.anchorOrigin.horizontal === 'left',
      style: {
        justifyContent: 'flex-start',
        left: 24,
        right: 'auto',
      },
    },
    {
      props: (ownerState) => ownerState.anchorOrigin.horizontal === 'right',
      style: {
        justifyContent: 'flex-end',
        right: 24,
        left: 'auto',
      },
    },
    {
      props: (ownerState) => ownerState.anchorOrigin.horizontal === 'center',
      style: {
        '--nova-snackbar-translateX': '-50%',
        left: '50%',
        right: 'auto',
        transform: 'translateX(var(--nova-snackbar-translateX))',
      },
    },
    {
      props: { open: false },
      style: {
        animationName: exitAnimation,
      },
    },
  ],
}));

const defaultAnchorOrigin = { vertical: 'bottom', horizontal: 'center' } as const;

export const Snackbar = React.forwardRef(function Snackbar(props: SnackbarProps, ref: React.Ref<HTMLElement>) {
  const {
    anchorOrigin = defaultAnchorOrigin,
    animationDuration = 300,
    autoHideDuration = null,
    children,
    className,
    component,
    disableWindowBlurListener = false,
    onBlur,
    onClose,
    onFocus,
    onMouseEnter,
    onMouseLeave,
    onUnmount,
    open,
    resumeHideDuration,
    slots = {},
    slotProps = {},
    ...other
  } = props;

  // For animation
  const [exited, setExited] = React.useState(true);

  // `exiting` is a state for preventing click away event during exiting
  // because there is a case where the Snackbar is exiting and the user open a Snackbar again.
  // Without this state, the snack will open and close immediately since click away is called immediately after the click event.
  const [exiting, setExiting] = React.useState(false);

  // To call a function when the component is about to be unmounted.
  // Useful for preserving content in the Snackbar when undergoing exit animation.
  const unmountRef = React.useRef(onUnmount);
  unmountRef.current = onUnmount;

  React.useEffect(() => {
    if (open) {
      setExiting(false);
      setExited(false);
    } else {
      setExiting(true);
      const timer = setTimeout(() => {
        setExited(true);
        setExiting(false);
        unmountRef.current?.();
      }, animationDuration);
      return () => {
        clearTimeout(timer);
      };
    }
    return undefined;
  }, [open, animationDuration]);

  const ownerState = {
    ...props,
    anchorOrigin,
    autoHideDuration,
    animationDuration,
    disableWindowBlurListener,
  };
  delete ownerState.onUnmount; // `on*` are considered as event handler which does not work with ClickAwayListener

  const classes = useUtilityClasses(ownerState);

  const { getRootProps, onClickAway } = useSnackbar(ownerState);

  const handleClickAway = (event: React.SyntheticEvent<any> | Event | null) => {
    if (!exiting) {
      onClickAway(event);
    }
  };

  const externalForwardedProps = { ...other, component, slots, slotProps };

  const RootSlot = slots.root ?? SnackbarRoot;
  const ClickAwaySlot = slots.clickAway ?? ClickAwayListener;

  const rootSlotProps = useSlotProps({
    elementType: RootSlot,
    externalSlotProps: slotProps.root,
    externalForwardedProps,
    additionalProps: {
      ref,
      as: component,
      style: {
        '--nova-snackbar-animation-duration': `${ownerState.animationDuration}ms`,
      },
    },
    getSlotProps: getRootProps,
    ownerState,
    className: [classes.root, className],
  });

  // So we only render active snackbars.
  if (!open && exited) {
    return null;
  }

  return (
    <ClickAwaySlot
      onClickAway={handleClickAway}
      {...(typeof slotProps?.clickAway === 'function' ? slotProps.clickAway(ownerState) : slotProps?.clickAway)}
    >
      <RootSlot {...rootSlotProps}>{children}</RootSlot>
    </ClickAwaySlot>
  );
}) as OverridableComponent<SnackbarTypeMap>;
