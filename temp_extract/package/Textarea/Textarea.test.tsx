import '@testing-library/jest-dom/vitest';
import { render, screen, cleanup, fireEvent } from '@testing-library/react';
import React from 'react';
import { describe, expect, it, afterEach, vi } from 'vitest';
import { Textarea } from './Textarea.tsx';

afterEach(() => {
  cleanup();
});

describe('Textarea', () => {
  it('should render normal', () => {
    render(<Textarea data-testid="NovaTextarea-root" />);
    expect(screen.getByTestId('NovaTextarea-root')).toHaveClass('NovaTextarea-root');
    expect(screen.getByTestId('NovaTextarea-root')).toHaveClass('NovaTextarea-sizeMedium');
    expect(screen.getByRole('textbox')).to.have.property('value', '');
  });

  it('should render error state', () => {
    render(<Textarea data-testid="NovaTextarea-root" error />);
    expect(screen.getByTestId('NovaTextarea-root')).toHaveClass('Mui-error');
  });

  it('should render disabled state', () => {
    render(<Textarea data-testid="NovaTextarea-root" disabled />);
    expect(screen.getByTestId('NovaTextarea-root')).toHaveClass('Mui-disabled');
  });

  it('should render fullWith state', () => {
    render(<Textarea data-testid="NovaTextarea-root" fullWidth />);
    expect(screen.getByTestId('NovaTextarea-root')).toHaveClass('NovaTextarea-fullWidth');
  });

  it('should render medium size', () => {
    render(<Textarea data-testid="NovaTextarea-root" size="medium" />);
    expect(screen.getByTestId('NovaTextarea-root')).toHaveClass('NovaTextarea-sizeMedium');
  });

  it('should render small size', () => {
    render(<Textarea data-testid="NovaTextarea-root" size="small" />);
    expect(screen.getByTestId('NovaTextarea-root')).toHaveClass('NovaTextarea-sizeSmall');
  });

  it('should render large size', () => {
    render(<Textarea data-testid="NovaTextarea-root" size="large" />);
    expect(screen.getByTestId('NovaTextarea-root')).toHaveClass('NovaTextarea-sizeLarge');
  });

  it('should default value work', () => {
    render(<Textarea data-testid="NovaTextarea-root" defaultValue={'xy'} />);
    expect(screen.getByRole('textbox')).to.have.property('value', 'xy');
    fireEvent.change(screen.getByRole('textbox'), { target: { value: 'abc' } });
    expect(screen.getByRole('textbox')).to.have.property('value', 'abc');
  });

  it('should value work', () => {
    render(<Textarea data-testid="NovaTextarea-root" value={'xy'} />);
    expect(screen.getByRole('textbox')).to.have.property('value', 'xy');
  });

  it('should onChange work', () => {
    const onChange = vi.fn();
    render(<Textarea data-testid="NovaTextarea-root" value={'xy'} onChange={onChange} />);
    fireEvent.change(screen.getByRole('textbox'), { target: { value: 'abc' } });
    expect(onChange).toHaveBeenCalled();
  });

  it('should startDecorator and endDecorator work', () => {
    render(
      <Textarea data-testid="NovaTextarea-root" startDecorator={<div>Prefix</div>} endDecorator={<div>Suffix</div>} />,
    );
    expect(screen.getByText('Prefix')).toBeInTheDocument();
    expect(screen.getByText('Suffix')).toBeInTheDocument();
  });

  it('should slotProps work', () => {
    const onChange = vi.fn();
    render(
      <Textarea
        data-testid="NovaTextarea-root"
        startDecorator={<div>Prefix</div>}
        endDecorator={<div>Suffix</div>}
        slotProps={{
          textarea: { 'data-testid': 'NovaTextarea-textarea' },
          startDecorator: { 'data-testid': 'NovaTextarea-startDecorator' },
          endDecorator: { 'data-testid': 'NovaTextarea-endDecorator' },
        }}
        onChange={onChange}
      />,
    );
    expect(screen.getByTestId('NovaTextarea-textarea')).toHaveClass('NovaTextarea-textarea');
    expect(screen.getByTestId('NovaTextarea-startDecorator')).toHaveClass('NovaTextarea-startDecorator');
    expect(screen.getByTestId('NovaTextarea-endDecorator')).toHaveClass('NovaTextarea-endDecorator');
    fireEvent.change(screen.getByTestId('NovaTextarea-textarea'), { target: { value: 'abc' } });
    expect(onChange).toHaveBeenCalled();
  });
});
