import * as React from 'react';
import { OverrideProps } from '@mui/types';
import { SxProps } from '../types/theme';
import { CreateSlotsAndSlotProps, SlotProps } from '../types/slot';

export type TextareaSlot = 'root' | 'textarea' | 'startDecorator' | 'endDecorator';

export interface TextareaSlots {
  /**
   * The component that renders the root.
   * @default 'div'
   */
  root?: React.ElementType;
  /**
   * The component that renders the textarea.
   * @default 'textarea'
   */
  textarea?: React.ElementType;
  /**
   * The component that renders the start decorator.
   * @default 'div'
   */
  startDecorator?: React.ElementType;
  /**
   * The component that renders the end decorator.
   * @default 'div'
   */
  endDecorator?: React.ElementType;
}

export interface TextareaPropsVariantOverrides {}
export interface TextareaPropsColorOverrides {}
export interface TextareaPropsSizeOverrides {}

export type TextareaSlotsAndSlotProps = CreateSlotsAndSlotProps<
  TextareaSlots,
  {
    root: SlotProps<'div', object, TextareaOwnerState>;
    textarea: SlotProps<'textarea', object, TextareaOwnerState>;
    startDecorator: SlotProps<'span', object, TextareaOwnerState>;
    endDecorator: SlotProps<'span', object, TextareaOwnerState>;
  }
>;

export interface TextareaTypeMap<P = object, D extends React.ElementType = 'div'> {
  props: P &
    Pick<
      React.TextareaHTMLAttributes<HTMLTextAreaElement>,
      | 'autoComplete'
      | 'autoFocus'
      | 'onClick'
      | 'onChange'
      | 'onKeyDown'
      | 'onKeyUp'
      | 'onFocus'
      | 'onBlur'
      | 'defaultValue'
      | 'value'
      | 'placeholder'
      | 'readOnly'
      | 'required'
      | 'name'
      | 'id'
      | 'disabled'
      | 'maxLength'
    > & {
      /**
       * Maximum number of rows to display.
       */
      maxRows?: string | number;
      /**
       * Minimum number of rows to display.
       * @default 1
       */
      minRows?: string | number;
      /**
       * If `true`, the `textarea` will indicate an error.
       * The prop defaults to the value (`false`) inherited from the parent FormControl component.
       * @default false
       */
      error?: boolean;
      /**
       * If `true`, the textarea will take up the full width of its container.
       * @default false
       */
      fullWidth?: boolean;
      /**
       * Leading adornment for this textarea.
       */
      startDecorator?: React.ReactNode;
      /**
       * Trailing adornment for this textarea.
       */
      endDecorator?: React.ReactNode;
      /**
       * The size of the component.
       * @default 'medium'
       */
      size?: 'small' | 'medium' | 'large';
    } & TextareaSlotsAndSlotProps;
  defaultComponent: D;
}

export type TextareaProps<
  D extends React.ElementType = TextareaTypeMap['defaultComponent'],
  P = {
    component?: React.ElementType;
  },
> = OverrideProps<TextareaTypeMap<P, D>, D>;

export default TextareaProps;

export interface TextareaOwnerState extends TextareaProps {
  /**
   * If `true`, the textarea is focused.
   */
  focused: boolean;
  /**
   * If `true`, the textarea has value.
   */
  filled: boolean;
}
