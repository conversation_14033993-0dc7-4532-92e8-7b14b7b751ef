'use client';
import * as React from 'react';
import {
  EventHandlers,
  unstable_capitalize as capitalize,
  unstable_composeClasses as composeClasses,
} from '@mui/utils';
import useSlotProps from '@mui/utils/useSlotProps';
import { OverridableComponent } from '@mui/types';
import { styled } from '@pigment-css/react';
import { TextareaTypeMap, TextareaProps, TextareaOwnerState } from './Textarea.types';
import textareaClasses, { getTextareaUtilityClass } from './Textarea.classes';
import FormControlContext from '../FormControl/FormControlContext';
import { useInput } from '../internal/hooks/useInput';
import { TextareaAutosize } from '../internal/components/TextareaAutosize';
import { iconButtonClasses } from '../IconButton';
import { typographyClasses } from '../Typography';

const useUtilityClasses = (ownerState: TextareaOwnerState) => {
  const { disabled, fullWidth, size, focused, error } = ownerState;

  const slots = {
    root: [
      'root',
      disabled && 'disabled',
      fullWidth && 'fullWidth',
      focused && 'focused',
      error && 'error',
      size && `size${capitalize(size)}`,
    ],
    textarea: ['textarea', disabled && 'disabled', focused && 'focused'],
    startDecorator: ['startDecorator'],
    endDecorator: ['endDecorator'],
  };

  return composeClasses(slots, getTextareaUtilityClass, {});
};

export const TextareaRoot = styled('div')<TextareaOwnerState>(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  border: '1px solid',
  borderColor: theme.vars.palette.outlineVariant,
  boxSizing: 'border-box',
  borderRadius: '8px',
  gap: '4px',
  color: theme.vars.palette.onSurface,
  backgroundColor: theme.vars.palette.surfaceContainerHigh,
  '&:hover': {
    borderColor: theme.vars.palette.outline,
    backgroundColor: `color-mix(in srgb, ${theme.vars.palette.surfaceContainer}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.hoverOnSurface})`,
  },
  [`&.${textareaClasses.focused}`]: {
    borderColor: theme.vars.palette.primary,
    backgroundColor: theme.vars.palette.surfaceContainerHigh,
  },
  variants: [
    {
      props: { filled: true, disabled: false, error: false },
      style: {
        borderColor: theme.vars.palette.outline,
        '&:hover': {
          borderColor: theme.vars.palette.outline,
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.surfaceContainer}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.hoverOnSurface})`,
        },
        [`&.${textareaClasses.focused}`]: {
          borderColor: theme.vars.palette.primary,
          backgroundColor: theme.vars.palette.surfaceContainerHigh,
        },
      },
    },
    {
      props: { filled: false, disabled: false, error: false },
      style: {
        borderColor: theme.vars.palette.outlineVariant,
        '&:hover': {
          borderColor: theme.vars.palette.outline,
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.surfaceContainer}, ${theme.vars.palette.onSurfaceVariant} ${theme.vars.palette.stateLayers.hoverOnSurfaceVariant})`,
        },
        [`&.${textareaClasses.focused}`]: {
          borderColor: theme.vars.palette.primary,
          backgroundColor: theme.vars.palette.surfaceContainerHigh,
        },
      },
    },
    {
      props: { error: true, disabled: false },
      style: {
        borderColor: theme.vars.palette.error,
        '&:hover': {
          borderColor: theme.vars.palette.error,
        },
        [`&.${textareaClasses.focused}`]: {
          borderColor: theme.vars.palette.error,
        },
      },
    },
    {
      props: { disabled: true },
      style: {
        color: theme.vars.palette.onBackgroundDisabled,
        backgroundColor: `color-mix(in srgb, ${theme.vars.palette.backgroundDisabled}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.disabledOnSurface})`,
        border: 'unset',
        '&:hover': {
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.backgroundDisabled}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.disabledOnSurface})`,
        },
        '--nova-textarea-placeholderColor': theme.vars.palette.backgroundDisabled,
      },
    },
    {
      props: { readOnly: true },
      style: {
        color: theme.vars.palette.onSurfaceVariant,
        backgroundColor: theme.vars.palette.surfaceContainerHighest,
        border: theme.vars.palette.outlineVariant,
        '&:hover': {
          backgroundColor: theme.vars.palette.surfaceContainerHighest,
        },
      },
    },
    {
      props: { size: 'small' },
      style: { padding: '3px 6px' },
    },
    {
      props: { size: 'medium' },
      style: { padding: '7px 8px' },
    },
    {
      props: { size: 'large' },
      style: { padding: '11px 10px' },
    },
  ],
}));

export const TextareaTextarea = styled(TextareaAutosize)<TextareaOwnerState>(({ theme }) => ({
  resize: 'none',
  border: 'none',
  minWidth: 0,
  outline: 0,
  padding: 0,
  flex: 1,
  color: 'inherit',
  backgroundColor: 'transparent',
  fontFamily: 'inherit',
  fontStyle: 'inherit',
  fontWeight: 'inherit',
  textOverflow: 'ellipsis',
  ...theme.typography.labelMedium,
  '--nova-textarea-placeholderColor': theme.vars.palette.onSurfaceVariant,
  '&::placeholder': {
    color: 'var(--nova-textarea-placeholderColor)',
  },
  variants: [
    {
      props: { disabled: true },
      style: {
        color: 'inherit',
        '--nova-textarea-placeholderColor': theme.vars.palette.onBackgroundDisabled,
      },
    },
    {
      props: { size: 'small' },
      style: {
        fontSize: '14px',
        lineHeight: '18px',
      },
    },
    {
      props: { size: 'medium' },
      style: {
        fontSize: '16px',
        lineHeight: '20px',
      },
    },
    {
      props: { size: 'large' },
      style: {
        fontSize: '18px',
        lineHeight: '24px',
      },
    },
  ],
}));

export const TextareaDecorator = styled('div')<TextareaOwnerState>(({ theme }) => ({
  display: 'inherit',
  alignItems: 'center',
  flexWrap: 'wrap',
  [`& .${iconButtonClasses.root}`]: {
    transform: 'scale(0.9)', // Against same IconButton height of Input
  },
  variants: [
    {
      props: { filled: true, disabled: false },
      style: {
        color: theme.vars.palette.onSurface,
        [`& .${iconButtonClasses.root}`]: {
          color: theme.vars.palette.onSurface,
        },
      },
    },
    {
      props: { filled: false, disabled: false },
      style: {
        color: theme.vars.palette.onSurfaceVariant,
        [`& .${iconButtonClasses.root}`]: {
          color: theme.vars.palette.onSurfaceVariant,
        },
      },
    },
    {
      props: { disabled: true },
      style: {
        color: theme.vars.palette.onBackgroundDisabled,
      },
    },
    {
      props: { size: 'small' },
      style: {
        fontSize: '20px',
        [`& .${typographyClasses.root}`]: {
          fontSize: '12px',
        },
      },
    },
    {
      props: { size: 'medium' },
      style: {
        fontSize: '24px',
        [`& .${typographyClasses.root}`]: {
          fontSize: '14px',
        },
      },
    },
    {
      props: { size: 'large' },
      style: {
        fontSize: '28px',
        [`& .${typographyClasses.root}`]: {
          fontSize: '16px',
        },
      },
    },
  ],
}));

// eslint-disable-next-line react/display-name
export const Textarea = React.forwardRef((props: TextareaProps, ref: React.ForwardedRef<Element>) => {
  const formControl = React.useContext(FormControlContext);
  const {
    'aria-describedby': ariaDescribedby,
    'aria-label': ariaLabel,
    'aria-labelledby': ariaLabelledby,
    autoComplete,
    autoFocus,
    className,
    defaultValue,
    disabled: disabledProp = false,
    error: errorProp = false,
    fullWidth: fullWidthProp = false,
    size: sizeProp = 'medium',
    id: idOverride,
    name,
    onClick,
    onChange,
    onKeyDown,
    onKeyUp,
    onFocus,
    onBlur,
    placeholder,
    readOnly,
    required,
    value,
    maxLength,
    minRows,
    maxRows,
    startDecorator,
    endDecorator,
    component,
    slots = {},
    slotProps = {},
    ...rest
  } = props;

  const { getRootProps, getInputProps, focused, disabled, error } = useInput({
    disabled: props.disabled,
    defaultValue,
    error: props.error,
    onBlur,
    onClick,
    onChange,
    onFocus,
    required: props.required,
    value,
  });

  const id = idOverride || formControl?.htmlFor;

  const filled = formControl?.filled ?? (Boolean(value) || Boolean(defaultValue));
  const size = props.size ?? formControl?.size ?? sizeProp;
  const fullWidth = props.fullWidth ?? formControl?.fullWidth ?? fullWidthProp;

  const ownerState = {
    ...props,
    fullWidth,
    disabled,
    error,
    focused,
    size,
    filled,
  };

  const classes = useUtilityClasses(ownerState);

  const SlotRoot = slots.root ?? TextareaRoot;
  const SlotTextarea = slots.textarea ?? TextareaTextarea;
  const SlotStartDecorator = slots.startDecorator ?? TextareaDecorator;
  const SlotEndDecorator = slots.endDecorator ?? TextareaDecorator;

  const slotRootProps = useSlotProps({
    elementType: SlotRoot,
    externalSlotProps: slotProps.root,
    externalForwardedProps: rest,
    additionalProps: {
      ref,
      as: component,
    },
    ownerState,
    getSlotProps: getRootProps,
    className: [classes.root, className],
  });

  const slotTextareaProps = useSlotProps({
    elementType: SlotTextarea,
    externalSlotProps: slotProps.textarea,
    additionalProps: {
      'aria-label': ariaLabel,
      'aria-labelledby': ariaLabelledby,
      'aria-describedby': ariaDescribedby,
      autoComplete,
      autoFocus,
      disabled,
      onKeyDown,
      onKeyUp,
      name,
      maxLength,
      placeholder,
      readOnly,
      id,
      minRows,
      maxRows,
    },
    ownerState,
    getSlotProps: (otherHandlers: EventHandlers) => {
      return getInputProps({
        ...otherHandlers,
      });
    },
    className: [classes.textarea],
  });

  const slotStartDecoratorProps = useSlotProps({
    elementType: SlotStartDecorator,
    externalSlotProps: slotProps.startDecorator,
    ownerState,
    className: classes.startDecorator,
  });

  const slotEndDecoratorProps = useSlotProps({
    elementType: SlotEndDecorator,
    externalSlotProps: slotProps.endDecorator,
    ownerState,
    className: classes.endDecorator,
  });

  return (
    <SlotRoot {...slotRootProps}>
      {startDecorator && <SlotStartDecorator {...slotStartDecoratorProps}>{startDecorator}</SlotStartDecorator>}

      <SlotTextarea {...slotTextareaProps} />
      {endDecorator && <SlotEndDecorator {...slotEndDecoratorProps}>{endDecorator}</SlotEndDecorator>}
    </SlotRoot>
  );
}) as OverridableComponent<TextareaTypeMap>;
