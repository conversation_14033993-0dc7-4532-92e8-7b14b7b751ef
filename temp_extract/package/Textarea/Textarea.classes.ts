import generateUtilityClasses from '@mui/utils/generateUtilityClasses';
import generateUtilityClass from '@mui/utils/generateUtilityClass';

export interface TextareaClasses {
  /** Class name applied to the root element. */
  root: string;
  /** Class name applied to the textarea element. */
  textarea: string;
  /** Class name applied to the startDecorator element */
  startDecorator: string;
  /** Class name applied to the endDecorator element */
  endDecorator: string;

  /** Class name applied to the root element if the component is focused. */
  focused: string;
  /** Class name applied to the root element if `disabled={true}`. */
  disabled: string;
  /** State class applied to the root element if `error={true}`. */
  error: string;
  /** Class name applied to the root element if `fullWidth={true}`. */
  fullWidth: string;

  /** Styles applied to the root element if `size="small"`. */
  sizeSmall: string;
  /** Styles applied to the root element if `size="medium"`. */
  sizeMedium: string;
  /** Styles applied to the root element if `size="large"`. */
  sizeLarge: string;
}

export type TextareaClassKey = keyof TextareaClasses;

export function getTextareaUtilityClass(slot: string): string {
  return generateUtilityClass('NovaTextarea', slot);
}

const textareaClasses: TextareaClasses = generateUtilityClasses('NovaTextarea', [
  'root',
  'textarea',
  'startDecorator',
  'endDecorator',

  'focused',
  'disabled',
  'error',
  'fullWidth',

  'sizeSmall',
  'sizeMedium',
  'sizeLarge',
]);

export default textareaClasses;
