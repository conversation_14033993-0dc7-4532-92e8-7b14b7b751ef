/// <reference types="@testing-library/jest-dom" />
import React from 'react';
import { expect, test, describe } from 'vitest';
import { render, screen } from '@testing-library/react';
import { PaginationItem } from './PaginationItem';

describe('PaginationItem Component', () => {
  test('renders page type correctly', () => {
    render(<PaginationItem page={1} type="page" />);
    expect(screen.getByRole('button')).toHaveTextContent('1');
  });

  test('can pass data-testid', () => {
    render(<PaginationItem data-testid="NovaPaginationItem-root" page={1} />);
    expect(screen.getByTestId('NovaPaginationItem-root')).toBeDefined();
  });

  test('handles custom className', () => {
    const customClass = 'custom-pagination-item';
    render(<PaginationItem data-testid="NovaPaginationItem-root" className={customClass} page={1} />);
    const paginationItemRoot = screen.getByTestId('NovaPaginationItem-root');
    expect(paginationItemRoot).toHaveClass(customClass);
  });

  test('renders disabled state correctly', () => {
    render(<PaginationItem page={1} disabled />);
    expect(screen.getByRole('button')).toBeDisabled();
  });

  test('renders selected state correctly', () => {
    render(<PaginationItem page={1} selected />);
    const button = screen.getByRole('button');
    expect(button).toHaveClass('Mui-selected');
  });

  test('renders different sizes correctly', () => {
    const { rerender } = render(<PaginationItem page={1} size="small" />);
    expect(screen.getByRole('button')).toHaveClass('NovaPaginationItem-sizeSmall');

    rerender(<PaginationItem page={1} size="large" />);
    expect(screen.getByRole('button')).toHaveClass('NovaPaginationItem-sizeLarge');
  });

  test('renders navigation types correctly', () => {
    const { rerender } = render(<PaginationItem type="previous" />);
    expect(screen.getByRole('button')).toBeDefined();

    rerender(<PaginationItem type="next" />);
    expect(screen.getByRole('button')).toBeDefined();

    rerender(<PaginationItem type="first" />);
    expect(screen.getByRole('button')).toBeDefined();

    rerender(<PaginationItem type="last" />);
    expect(screen.getByRole('button')).toBeDefined();
  });

  test('renders ellipsis types correctly', () => {
    const { rerender } = render(<PaginationItem type="start-ellipsis" />);
    expect(screen.getByText('…')).toBeDefined();

    rerender(<PaginationItem type="end-ellipsis" />);
    expect(screen.getByText('…')).toBeDefined();
  });

  test('renders with custom slots', () => {
    const CustomIcon = () => <div data-testid="custom-icon">Custom</div>;
    render(
      <PaginationItem
        type="next"
        slots={{
          next: CustomIcon,
        }}
      />,
    );
    expect(screen.getByTestId('custom-icon')).toBeDefined();
  });
});
