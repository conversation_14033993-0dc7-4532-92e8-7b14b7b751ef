'use client';
import * as React from 'react';
import clsx from 'clsx';
import { unstable_capitalize as capitalize, unstable_composeClasses as composeClasses } from '@mui/utils';
import paginationItemClasses, { getPaginationItemUtilityClass } from './PaginationItem.classes';
import { ButtonBase } from '../ButtonBase';
import FirstPageIcon from '../internal/svg-icons/FirstPage';
import LastPageIcon from '../internal/svg-icons/LastPage';
import NavigateBeforeIcon from '../internal/svg-icons/NavigateBefore';
import NavigateNextIcon from '../internal/svg-icons/NavigateNext';
import { styled } from '@pigment-css/react';
import { PaginationItemOwnerState, PaginationItemProps, PaginationItemSlots } from './PaginationItem.types';
import useSlotProps from '@mui/utils/useSlotProps';

const overridesResolver = (props: PaginationItemOwnerState, styles: Record<string, any>) => {
  const { size, type } = props;

  return [
    styles.root,
    size && styles[`size${capitalize(size)}`],
    type === 'page' && styles.page,
    (type === 'start-ellipsis' || type === 'end-ellipsis') && styles.ellipsis,
    (type === 'previous' || type === 'next') && styles.previousNext,
    (type === 'first' || type === 'last') && styles.firstLast,
  ].filter(Boolean);
};

const useUtilityClasses = (props: PaginationItemOwnerState) => {
  const { classes, disabled, selected, size, type } = props;

  const slots = {
    root: [
      'root',
      size && `size${capitalize(size)}`,
      disabled && 'disabled',
      selected && 'selected',
      {
        page: 'page',
        first: 'firstLast',
        last: 'firstLast',
        'start-ellipsis': 'ellipsis',
        'end-ellipsis': 'ellipsis',
        previous: 'previousNext',
        next: 'previousNext',
      }[type as keyof typeof type],
    ],
    icon: ['icon'],
  };

  return composeClasses(slots, getPaginationItemUtilityClass, classes);
};

const baseStyles = ({ theme }: { theme: any }) => ({
  ...theme.typography.bodyMedium,
  borderRadius: 8,
  textAlign: 'center',
  boxSizing: 'border-box',
  minWidth: 25,
  height: 40,
  padding: '0 6px',
  margin: '0 3px',
  color: theme.vars.palette.onSurfaceVariant,
  fontSize: '16px',
});

const sizeVariants = [
  {
    props: { size: 'small' },
    style: {
      minWidth: 24,
      height: 28,
      margin: '0 2px',
      padding: '0 4px',
      fontSize: '14px',
    },
  },
  {
    props: { size: 'large' },
    style: {
      minWidth: 35,
      height: 51,
      margin: '0 4px',
      padding: '0 8px',
      fontSize: '18px',
    },
  },
];

const PaginationItemEllipsis = styled('div', {
  name: 'NovaPaginationItem',
  slot: 'Root',
  overridesResolver,
})<PaginationItemProps>(({ theme }) => ({
  ...baseStyles({ theme }),
  display: 'inline-flex',
  alignItems: 'center',
  justifyContent: 'center',
  [`&.${paginationItemClasses.disabled}`]: {
    color: theme.vars.palette.onBackgroundDisabled,
    pointerEvents: 'none',
  },
  variants: sizeVariants,
}));

const PaginationItemPage = styled(ButtonBase, {
  name: 'NovaPaginationItem',
  slot: 'Root',
  overridesResolver,
})<PaginationItemProps>(({ theme }) => ({
  ...baseStyles({ theme }),
  display: 'inline-flex',
  alignItems: 'center',
  justifyContent: 'center',
  '& > svg': {
    width: '24px',
    height: '24px',
  },

  // Focus state
  [`&.${paginationItemClasses.focusVisible}`]: {
    backgroundColor: `color-mix(in srgb, ${theme.vars.palette.backgroundStates}, ${theme.vars.palette.onSurfaceVariant} ${theme.vars.palette.stateLayers.focusOnSurface})`,
    outline: `2px solid ${theme.vars.palette.secondary}`,
    outlineOffset: '1px',
  },

  // Active state
  '&.Mui-active': {
    backgroundColor: `color-mix(in srgb, ${theme.vars.palette.backgroundStates}, ${theme.vars.palette.onSurfaceVariant} ${theme.vars.palette.stateLayers.pressOnSurface})`,
  },

  // Disabled state
  [`&.${paginationItemClasses.disabled}`]: {
    color: theme.vars.palette.onBackgroundDisabled,
    pointerEvents: 'none',
    '& svg': {
      fill: theme.vars.palette.onBackgroundDisabled,
    },
  },

  // Hover state
  '&:hover': {
    backgroundColor: `color-mix(in srgb, ${theme.vars.palette.backgroundStates}, ${theme.vars.palette.onSurfaceVariant} ${theme.vars.palette.stateLayers.hoverOnSurface})`,
    '@media (hover: none)': {
      backgroundColor: 'transparent',
    },
  },

  // Selected state
  [`&.${paginationItemClasses.selected}`]: {
    color: theme.vars.palette.onSecondaryContainer,
    backgroundColor: theme.vars.palette.secondaryContainer,

    '&:hover': {
      backgroundColor: `color-mix(in srgb, ${theme.vars.palette.secondaryContainer}, ${theme.vars.palette.onSecondaryContainer} ${theme.vars.palette.stateLayers.hoverSecondary})`,
      '@media (hover: none)': {
        backgroundColor: theme.vars.palette.secondary,
      },
    },

    [`&.${paginationItemClasses.focusVisible}`]: {
      backgroundColor: `color-mix(in srgb, ${theme.vars.palette.secondaryContainer}, ${theme.vars.palette.onSecondaryContainer} ${theme.vars.palette.stateLayers.focusSecondary})`,
    },

    [`&.${paginationItemClasses.disabled}`]: {
      backgroundColor: `color-mix(in srgb, ${theme.vars.palette.secondaryContainer}, ${theme.vars.palette.onBackgroundDisabled} ${theme.vars.palette.stateLayers.disabled})`,
      color: theme.vars.palette.onBackgroundDisabled,
      pointerEvents: 'none',
    },

    '&.Mui-active': {
      backgroundColor: `color-mix(in srgb, ${theme.vars.palette.secondaryContainer}, ${theme.vars.palette.onSecondaryContainer} ${theme.vars.palette.stateLayers.pressSecondary})`,
    },
  },

  variants: sizeVariants,
}));

const ICON_MAPPING = {
  previous: NavigateBeforeIcon,
  next: NavigateNextIcon,
  first: FirstPageIcon,
  last: LastPageIcon,
  'start-ellipsis': undefined,
  'end-ellipsis': undefined,
  page: undefined,
} as const;

const PaginationItemPageIcon = styled('div', {
  name: 'NovaPaginationItem',
  slot: 'Icon',
  overridesResolver: (props, styles) => styles.icon,
})<PaginationItemProps>(({ theme }) => ({
  ...theme.typography.bodyMedium,
  margin: '0 -8px',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  width: 26,
  height: 40,
  '& > svg': {
    width: '24px',
    height: '24px',
  },
  variants: [
    {
      props: { size: 'small' },
      style: {
        width: 25,
        height: 28,
        margin: '0 -6px',
      },
    },
    {
      props: { size: 'large' },
      style: {
        width: 35,
        height: 51,
        margin: '0 -10px',
      },
    },
  ],
}));

// eslint-disable-next-line react/display-name
export const PaginationItem = React.forwardRef<HTMLDivElement, PaginationItemProps>(
  function PaginationItem(props, ref) {
    const {
      className,
      component,
      disabled = false,
      page,
      selected = false,
      size = 'medium',
      slots = {} as PaginationItemSlots,
      slotProps = {},
      type = 'page',
      ...other
    } = props;

    const ownerState = {
      ...props,
      disabled,
      selected,
      size,
      type,
    };

    const classes = useUtilityClasses(ownerState);

    const iconSlot = (slots[type as keyof PaginationItemSlots] ?? ICON_MAPPING[type]) as React.ElementType | undefined;

    const iconSlotProps = useSlotProps({
      elementType: iconSlot,
      externalSlotProps: slotProps[type as keyof PaginationItemSlots] as any,
      externalForwardedProps: {},
      additionalProps: {
        ref,
        as: component,
      },
      ownerState,
    });

    // get aria labels based on type
    const getAriaLabel = () => {
      if (type === 'page') return `Go to page ${page}`;
      if (type === 'first') return 'Go to first page';
      if (type === 'last') return 'Go to last page';
      if (type === 'previous') return 'Go to previous page';
      if (type === 'next') return 'Go to next page';
      return undefined;
    };

    return type === 'start-ellipsis' || type === 'end-ellipsis' ? (
      <PaginationItemEllipsis ref={ref} className={clsx(classes.root, className)} size={size}>
        …
      </PaginationItemEllipsis>
    ) : (
      <PaginationItemPage
        ref={ref}
        component={component}
        disabled={disabled}
        className={clsx(classes.root, className)}
        size={size}
        aria-label={getAriaLabel()}
        {...other}
      >
        {type === 'page' && page}
        {iconSlot && <PaginationItemPageIcon {...iconSlotProps} className={classes.icon} as={iconSlot} size={size} />}
      </PaginationItemPage>
    );
  },
);
