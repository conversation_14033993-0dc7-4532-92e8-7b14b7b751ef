import * as React from 'react';
import { OverridableStringUnion } from '@mui/types';
import { SxProps } from '../types/theme';
import { OverrideProps } from '@mui/types';
import { PaginationItemClasses } from './PaginationItem.classes';
import { CreateSlotsAndSlotProps, SlotProps } from '../types/slot';
import { UsePaginationItem } from '../internal/hooks/usePagination/usePagination.types';

export interface PaginationItemPropsSizeOverrides {}

export interface PaginationItemSlots {
  first?: React.ElementType;
  last?: React.ElementType;
  next?: React.ElementType;
  previous?: React.ElementType;
}

export type PaginationItemSlotsAndSlotProps = CreateSlotsAndSlotProps<
  PaginationItemSlots,
  {
    first: SlotProps<React.ElementType<React.HTMLProps<HTMLElement>>, object, PaginationItemOwnerState>;
    last: SlotProps<React.ElementType<React.HTMLProps<HTMLElement>>, object, PaginationItemOwnerState>;
    next: SlotProps<React.ElementType<React.HTMLProps<HTMLElement>>, object, PaginationItemOwnerState>;
    previous: SlotProps<React.ElementType<React.HTMLProps<HTMLElement>>, object, PaginationItemOwnerState>;
  }
>;

export interface PaginationItemOwnerState extends PaginationItemProps {}

export interface PaginationItemOwnProps extends PaginationItemSlotsAndSlotProps {
  /**
   * Override or extend the styles applied to the component.
   */
  classes?: Partial<PaginationItemClasses>;
  /**
   * If `true`, the component is disabled.
   * @default false
   */
  disabled?: boolean;
  /**
   * The current page number.
   */
  page?: React.ReactNode;
  /**
   * If `true` the pagination item is selected.
   * @default false
   */
  selected?: boolean;
  /**
   * The size of the component.
   * @default 'medium'
   */
  size?: OverridableStringUnion<'small' | 'medium' | 'large', PaginationItemPropsSizeOverrides>;
  /**
   * The system prop that allows defining system overrides as well as additional CSS styles.
   */
  sx?: SxProps;
  /**
   * The type of pagination item.
   * @default 'page'
   */
  type?: UsePaginationItem['type'];
}

export interface PaginationItemTypeMap<AdditionalProps = object, RootComponent extends React.ElementType = 'div'> {
  props: AdditionalProps & PaginationItemOwnProps;
  defaultComponent: RootComponent;
}

export type PaginationItemProps<
  RootComponent extends React.ElementType = PaginationItemTypeMap['defaultComponent'],
  AdditionalProps = object,
> = OverrideProps<PaginationItemTypeMap<AdditionalProps, RootComponent>, RootComponent> & {
  component?: React.ElementType;
};
