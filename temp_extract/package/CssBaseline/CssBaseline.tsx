'use client';
import * as React from 'react';
import { globalCss } from '@pigment-css/react';

// Create global styles
globalCss`
  /* Reset box-sizing */
  *, *::before, *::after {
    box-sizing: border-box;
  }
  /* Reset margins and paddings */
  html, body {
    margin: 0;
    padding: 0;
    min-width: 100%;
    min-height: 100%;
  }
  /* Set base font */
  body {
    font-family: Hexagon <PERSON>, Roboto, Helvetica Neue, Arial, sans-serif;
    font-size: 1rem;
    line-height: 1.5rem;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    /* Fix font resize problem in iOS */
    -webkit-text-size-adjust: 100%;
    /* Use theme background and text colors */
    background-color: var(--palette-surface);
    color: var(--palette-onSurface);
  }
  /* Remove default margin */
  p, h1, h2, h3, h4, h5, h6 {
    margin: 0;
  }
  /* Remove default styles from lists */
  ul, ol {
    margin: 0;
    padding: 0;
    list-style: none;
  }
`;

export interface CssBaselineProps {
  children?: React.ReactNode;
}

// eslint-disable-next-line react/display-name
export const CssBaseline = React.forwardRef<HTMLDivElement, CssBaselineProps>((props, ref) => {
  const { children, ...other } = props;

  if (children) {
    return (
      <div ref={ref} {...other}>
        {children}
      </div>
    );
  }

  return <React.Fragment />;
});
