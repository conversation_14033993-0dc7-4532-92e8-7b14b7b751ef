import React from 'react';
import { render, cleanup } from '@testing-library/react';
import { expect, test, afterEach } from 'vitest';
import { IconButton } from './IconButton';

afterEach(() => {
  cleanup();
});

test('by default, should render with the root,  filled and sizeMedium classes', async () => {
  const { getByRole } = render(<IconButton>Icon</IconButton>);
  const button = getByRole('button');

  expect(button).toHaveClass('NovaIconButton-root');
  expect(button).toHaveClass('NovaIconButton-filled');
  expect(button).toHaveClass('NovaButtonBase-sizeMedium');
});

test('should render a filled button', () => {
  const { getByRole } = render(<IconButton variant="filled">Icon</IconButton>);
  const button = getByRole('button');

  expect(button).toHaveClass('NovaIconButton-filled');
});

test('should render an outlined button', () => {
  const { getByRole } = render(<IconButton variant="outlined">Icon</IconButton>);
  const button = getByRole('button');

  expect(button).toHaveClass('NovaIconButton-outlined');
});

test('should render a standard button', () => {
  const { getByRole } = render(<IconButton variant="standard">Icon</IconButton>);
  const button = getByRole('button');

  expect(button).toHaveClass('NovaIconButton-standard');
});

test('should render a neutral button', () => {
  const { getByRole } = render(<IconButton variant="neutral">Icon</IconButton>);
  const button = getByRole('button');

  expect(button).toHaveClass('NovaIconButton-neutral');
});

test('should render a small size button', () => {
  const { getByRole } = render(<IconButton size="small">Icon</IconButton>);
  const button = getByRole('button');

  expect(button).toHaveClass('NovaButtonBase-sizeSmall');
});

test('should render a medium size button', () => {
  const { getByRole } = render(<IconButton size="medium">Icon</IconButton>);
  const button = getByRole('button');

  expect(button).toHaveClass('NovaButtonBase-sizeMedium');
});

test('should render a large size button', () => {
  const { getByRole } = render(<IconButton size="large">Icon</IconButton>);
  const button = getByRole('button');

  expect(button).toHaveClass('NovaButtonBase-sizeLarge');
});

test('should render a disabled button', () => {
  const { getByRole } = render(<IconButton disabled>Icon</IconButton>);
  const button = getByRole('button');

  expect(button).toHaveProperty('disabled', true);
  expect(button).toHaveClass('Mui-disabled');
});

test('should render a button with a custom class', () => {
  const { getByRole } = render(<IconButton className="custom">Icon</IconButton>);
  const button = getByRole('button');

  expect(button).toHaveClass('custom');
});
