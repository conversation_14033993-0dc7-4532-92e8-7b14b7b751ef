import {
  unstable_generateUtilityClasses as generateUtilityClasses,
  unstable_generateUtilityClass as generateUtilityClass,
} from '@mui/utils';

export interface IconButtonClasses {
  /** Styles applied to the root element. */
  root: string;

  /** Styles applied to the IconButton if `variant="filled"`. */
  filled: string;
  /** Styles applied to the IconButton if `variant="outlined"`. */
  outlined: string;
  /** Styles applied to the IconButton if `variant="standard"`. */
  standard: string;
  /** Styles applied to the IconButton if `variant="neutral"`. */
  neutral: string;
}

export type IconButtonClassKey = keyof IconButtonClasses;

export function getIconButtonUtilityClass(slot: string): string {
  return generateUtilityClass('NovaIconButton', slot);
}

const iconButtonClasses: IconButtonClasses = generateUtilityClasses('NovaIconButton', [
  'root',
  'filled',
  'outlined',
  'standard',
  'neutral',
]);

export default iconButtonClasses;
