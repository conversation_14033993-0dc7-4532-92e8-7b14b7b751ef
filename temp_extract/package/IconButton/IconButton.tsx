'use client';
import React from 'react';
import { styled } from '@pigment-css/react';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import { IconButtonProps } from './IconButton.types';
import { getIconButtonUtilityClass } from './IconButton.classes';
import { ButtonBase } from '../ButtonBase';
import clsx from 'clsx';

const useUtilityClasses = (ownerState: IconButtonProps) => {
  const { variant } = ownerState;

  const slots = {
    root: ['root', variant],
  };

  return composeClasses(slots, getIconButtonUtilityClass, {});
};

const IconButtonBase = styled(ButtonBase)<IconButtonProps>(({ theme }) => ({
  justifyContent: 'center',
  fontSize: 24,
  width: '2.5rem',
  padding: 0,
  variants: [
    // Size Styles
    {
      props: { size: 'small' },
      style: {
        fontSize: 20,
        width: '2rem',
      },
    },
    {
      props: { size: 'medium' },
      style: {
        fontSize: 24,
        width: '2.5rem',
      },
    },
    {
      props: { size: 'large' },
      style: {
        fontSize: 28,
        width: '3rem',
      },
    },
    // Filled Styles
    {
      props: { variant: 'filled' },
      style: {
        color: theme.vars.palette.onPrimary,
        backgroundColor: theme.vars.palette.primary,
        '&:hover': {
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.primary}, ${theme.vars.palette.onPrimary} ${theme.vars.palette.stateLayers.hoverOnPrimary})`,
        },
        '&:focus-visible': {
          outline: `2px solid ${theme.vars.palette.primary}`,
          outlineOffset: 2,
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.primary}, ${theme.vars.palette.onPrimary} ${theme.vars.palette.stateLayers.focusOnPrimary})`,
        },
        '&:active': {
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.primary}, ${theme.vars.palette.onPrimary} ${theme.vars.palette.stateLayers.pressOnPrimary})`,
        },
      },
    },
    // Outlined Styles
    {
      props: { variant: 'outlined' },
      style: {
        borderWidth: 1,
        borderStyle: 'solid',
        borderColor: theme.vars.palette.primary,
        backgroundColor: 'transparent',
        color: theme.vars.palette.primary,
        '&:hover': {
          backgroundColor: `color-mix(in srgb, transparent, ${theme.vars.palette.primary} ${theme.vars.palette.stateLayers.hoverPrimary})`,
        },
        '&:focus-visible': {
          outline: `2px solid ${theme.vars.palette.primary}`,
          outlineOffset: 2,
          backgroundColor: `color-mix(in srgb, transparent, ${theme.vars.palette.primary} ${theme.vars.palette.stateLayers.focusPrimary})`,
        },
        '&:active': {
          backgroundColor: `color-mix(in srgb, transparent, ${theme.vars.palette.primary} ${theme.vars.palette.stateLayers.pressPrimary})`,
        },
      },
    },
    // Standard Styles
    {
      props: { variant: 'standard' },
      style: {
        color: theme.vars.palette.primary,
        '&:hover': {
          backgroundColor: `color-mix(in srgb, transparent, ${theme.vars.palette.primary} ${theme.vars.palette.stateLayers.hoverPrimary})`,
        },
        '&:focus-visible': {
          outline: `2px solid ${theme.vars.palette.primary}`,
          outlineOffset: 2,
          backgroundColor: `color-mix(in srgb, transparent, ${theme.vars.palette.primary} ${theme.vars.palette.stateLayers.focusPrimary})`,
        },
        '&:active': {
          backgroundColor: `color-mix(in srgb, transparent, ${theme.vars.palette.primary} ${theme.vars.palette.stateLayers.pressPrimary})`,
        },
      },
    },
    {
      props: { variant: 'neutral' },
      style: {
        color: theme.vars.palette.onSurface,
        '&:hover': {
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.backgroundStates}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.hoverOnSurface})`,
        },
        '&:focus-visible': {
          outline: `2px solid ${theme.vars.palette.onSurfaceVariant}`,
          outlineOffset: 2,
          backgroundColor: `color-mix(in srgb, transparent, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.focusOnSurface})`,
        },
        '&:active': {
          backgroundColor: `color-mix(in srgb, transparent, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.pressOnSurface})`,
        },
      },
    },
    // Disabled Styles
    {
      props: { disabled: true },
      style: {
        backgroundColor: `color-mix(in srgb, ${theme.vars.palette.backgroundDisabled}, ${theme.vars.palette.onBackgroundDisabled} ${theme.vars.palette.stateLayers.disabled})`,
        '--Icon-color': theme.vars.palette.onBackgroundDisabled,
        color: theme.vars.palette.onBackgroundDisabled,
        border: 'none',
      },
    },
    {
      props: { disabled: true, variant: 'outlined' },
      style: {
        backgroundColor: `color-mix(in srgb, transparent, ${theme.vars.palette.onBackgroundDisabled} ${theme.vars.palette.stateLayers.disabled})`,
        border: `1px solid ${theme.vars.palette.outlineDisabled}`,
      },
    },
    {
      props: { disabled: true, variant: 'standard' },
      style: {
        backgroundColor: 'transparent',
      },
    },
    {
      props: { disabled: true, variant: 'neutral' },
      style: {
        backgroundColor: 'transparent',
      },
    },
  ],
}));

export const IconButton = (props: IconButtonProps) => {
  const defaultProps: Partial<IconButtonProps> = {
    variant: 'filled',
  };

  const mergedProps = { ...defaultProps, ...props };
  const classes = useUtilityClasses(mergedProps);

  const { children, className, ...otherProps } = mergedProps;

  const childrenWidthFontSize = React.isValidElement(children)
    ? React.cloneElement(children as React.ReactElement, { fontSize: children.props.fontSize ?? 'inherit' })
    : children;

  return (
    <IconButtonBase className={clsx(classes.root, className)} {...otherProps}>
      {childrenWidthFontSize}
    </IconButtonBase>
  );
};
