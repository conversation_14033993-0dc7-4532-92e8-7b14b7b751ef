// Basic size variants
type SizeVariant = {
  medium: number;
  small: number;
  large: number;
};

export type SizeTokens = {
  icon: SizeVariant;
  padding: {
    topBottom: {
      '2xs': SizeVariant;
      xs: SizeVariant;
      sm?: SizeVariant;
      md: SizeVariant;
      lg: SizeVariant;
      xl?: SizeVariant;
      '2xl'?: SizeVariant;
    };
    leftRight: {
      '2xs': SizeVariant;
      xs: SizeVariant;
      sm?: SizeVariant;
      md: SizeVariant;
      lg: SizeVariant;
      xl?: SizeVariant;
      '2xl'?: SizeVariant;
    };
  };
  spaceBetween: {
    horizontal: {
      '2xs': SizeVariant;
      xs: SizeVariant;
      md: SizeVariant;
      lg: SizeVariant;
      '2xl'?: SizeVariant;
    };
    vertical: {
      '2xs': SizeVariant;
      xs: SizeVariant;
      md: SizeVariant;
      lg: SizeVariant;
      '2xl'?: SizeVariant;
    };
  };
  radius: {
    xs: SizeVariant;
    sm: SizeVariant;
    md: SizeVariant;
    fullyRounded: SizeVariant;
  };
  height: {
    md: SizeVariant;
    lg: SizeVariant;
    xl: SizeVariant;
    '2xl': SizeVariant;
  };
  width: {
    xl: SizeVariant;
    '2xl': SizeVariant;
  };
  typescale: {
    labelSmall: {
      size: SizeVariant;
      height: SizeVariant;
    };
    labelMedium: {
      size: SizeVariant;
      height: SizeVariant;
    };
    bodySmall: {
      size: SizeVariant;
      height: SizeVariant;
    };
    bodyMedium: {
      size: SizeVariant;
      height: SizeVariant;
    };
  };
  outline: {
    sm: number;
    md: number;
    lg: number;
    xl: number;
  };
  nestedLevel: {
    fixed: SizeVariant;
    lg: SizeVariant;
  };
};

// Define CSS variable type for clarity
type CSSVarToken = string;

// Define different size key sets based on actual usage
type BasicSizeKey = '2xs' | 'xs' | 'sm' | 'md' | 'lg' | 'xl';
type RadiusSizeKey = '2xs' | 'xs' | 'sm' | 'md';
type VerticalSpacingKeys = '2xs' | 'xs' | 'md' | 'lg' | 'xl';

// Export the main ViewportTokens interface
export interface ViewportTokens {
  spacing: {
    padding: {
      topBottom: Record<BasicSizeKey, CSSVarToken>;
      leftRight: Record<BasicSizeKey, CSSVarToken>;
    };
    spaceBetween: {
      horizontal: Record<BasicSizeKey, CSSVarToken>;
      vertical: Record<VerticalSpacingKeys, CSSVarToken>;
    };
  };
  radius: Record<RadiusSizeKey, CSSVarToken>;
  height: {
    md: CSSVarToken;
    lg: CSSVarToken;
    xl: CSSVarToken;
    '2xl': CSSVarToken;
    '3xl': CSSVarToken;
    '4xl': CSSVarToken;
    '5xl': CSSVarToken;
  };
  outline: {
    slight: CSSVarToken;
    regular: CSSVarToken;
    considerate: CSSVarToken;
    exaggerated: CSSVarToken;
  };
  components: {
    icons: {
      md: CSSVarToken;
      lg: CSSVarToken;
      xl: CSSVarToken;
    };
    sideNav: {
      width: CSSVarToken;
    };
    card: {
      width: {
        stacked: CSSVarToken;
        horizontal: CSSVarToken;
      };
      height: {
        vertical: CSSVarToken;
      };
    };
    topNavBar: {
      width: CSSVarToken;
      margin: CSSVarToken;
    };
    sideSheet: {
      radius: CSSVarToken;
    };
    navDrawer: {
      padding: CSSVarToken;
    };
  };
}
