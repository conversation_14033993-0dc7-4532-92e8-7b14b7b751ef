import * as CSS from 'csstype';
import { Breakpoints } from '@mui/system';
import { CSSProperties } from '@pigment-css/react';
import type { ExtendTheme } from '@pigment-css/react/theme';
import { SizeTokens, ViewportTokens } from './sysTokensType';

/** This is duplicated from the definition in the pigment themes package — alternatively, we could add the theme package
 * as a dependency, but that presents some awkwardness for teams who may want to use a custom theme
 * We'll have to think about the best way to share the standard theme structure definition
 * */
export type NovaPigmentTheme = {
  palette: {
    primary: string;
    onPrimary: string;
    primaryContainer: string;
    onPrimaryContainer: string;
    inversePrimary: string;

    secondary: string;
    onSecondary: string;
    secondaryContainer: string;
    onSecondaryContainer: string;

    tertiary: string;
    onTertiary: string;
    tertiaryContainer: string;
    onTertiaryContainer: string;

    error: string;
    onError: string;
    errorContainer: string;
    onErrorContainer: string;

    surface: string;
    onSurface: string;
    surfaceContainer: string;
    inverseSurface: string;
    inverseOnSurface: string;
    onSurfaceVariant: string;
    surfaceContainerHigh: string;
    surfaceContainerHighest: string;
    surfaceBright: string;
    surfaceContainerLow: string;
    surfaceContainerLowest: string;
    surfaceDim: string;
    surfaceVariant: string;

    outline: string;
    outlineVariant: string;
    outlineDisabled: string;

    scrim: string;
    backgroundStates: string;
    shadow: string;

    backgroundDisabled: string;
    onBackgroundDisabled: string;

    system: {
      warning: string;
      onWarning: string;
      warningContainer: string;
      onWarningContainer: string;
      info: string;
      onInfo: string;
      infoContainer: string;
      onInfoContainer: string;
      success: string;
      onSuccess: string;
      successContainer: string;
      onSuccessContainer: string;
    };

    stateLayers: {
      hoverOnSurface: string;
      focusOnSurface: string;
      pressOnSurface: string;
      disabledOnSurface: string;
      dragOnSurface: string;
      extraHeavyOnSurface: string;
      hoverOnSurfaceVariant: string;
      focusOnSurfaceVariant: string;
      pressOnSurfaceVariant: string;
      hoverSecondary: string;
      focusSecondary: string;
      pressSecondary: string;
      hoverOnSecondaryContainer: string;
      focusOnSecondaryContainer: string;
      pressOnSecondaryContainer: string;
      hoverPrimary: string;
      focusPrimary: string;
      pressPrimary: string;
      disabled: string;
      hoverOnPrimary: string;
      focusOnPrimary: string;
      pressOnPrimary: string;
      hoverError: string;
      focusError: string;
      pressError: string;
    };

    addOn: {
      onSecondaryFixed: string;
      primaryFixed: string;
      primaryFixedDim: string;
    };
  };
  shadows: ['none', string, string, string, string, string];
  spacing: number;
  typography: {
    fontFamily: string;
    displayLarge: CSSProperties;
    displayMedium: CSSProperties;
    displaySmall: CSSProperties;
    headlineLarge: CSSProperties;
    headlineMedium: CSSProperties;
    headlineSmall: CSSProperties;
    titleLarge: CSSProperties;
    titleMedium: CSSProperties;
    titleSmall: CSSProperties;
    bodyLarge: CSSProperties;
    bodyMedium: CSSProperties;
    bodySmall: CSSProperties;
    labelLarge: CSSProperties;
    labelMedium: CSSProperties;
    labelSmall: CSSProperties;
  };
  sys: {
    size: SizeTokens;
    viewport: ViewportTokens;
  };
  breakpoints: Breakpoints;
};

/**
 * The `SxProps` can be either object or function
 */
type Theme = ExtendTheme<{
  colorScheme: 'light' | 'dark';
  tokens: NovaPigmentTheme;
}>;

export interface CSSSelectorObjectOrCssVariables<Theme extends object> {
  [cssSelectorOrVariable: string]:
    | ((theme: Theme) => SystemStyleObject<Theme> | string | number)
    | SystemStyleObject<Theme>
    | string
    | number;
}

export type CSSPseudoSelectorProps<Theme extends object> = {
  [K in CSS.Pseudos]?: ((theme: Theme) => SystemStyleObject<Theme>) | SystemStyleObject<Theme>;
};

export type SystemStyleObject<Theme extends object> =
  | React.CSSProperties
  | CSSPseudoSelectorProps<Theme>
  | CSSSelectorObjectOrCssVariables<Theme>
  | null;

type SystemSxProps<Theme extends object> =
  | SystemStyleObject<Theme>
  | ((theme: Theme) => SystemStyleObject<Theme>)
  | ReadonlyArray<boolean | SystemStyleObject<Theme> | ((theme: Theme) => SystemStyleObject<Theme>)>;

export type SxProps = SystemSxProps<Theme>;
