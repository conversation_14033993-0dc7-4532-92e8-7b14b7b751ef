import { OverridableStringUnion, Simplify } from '@mui/types';
import { NovaPigmentTheme } from './theme';

export type DefaultColorPalette = 'primary' | 'info' | 'warning' | 'success' | 'error';

export interface ColorPalettePropOverrides {}

export type ColorPaletteProp = OverridableStringUnion<DefaultColorPalette, ColorPalettePropOverrides>;

export type ApplyColorInversion<T extends { color?: ColorPaletteProp | 'inherit' }> = Simplify<T>;

export type ColorPaletteSystem = keyof NovaPigmentTheme['palette']['system'];
