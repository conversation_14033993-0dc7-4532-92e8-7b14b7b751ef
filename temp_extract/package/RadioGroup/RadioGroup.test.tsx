import React from 'react';
import { expect, test, describe, vi } from 'vitest';
import { render, screen, fireEvent, act } from '@testing-library/react';
import { RadioGroup } from './RadioGroup';
import { Radio } from '../Radio/Radio';

describe('<RadioGroup />', () => {
  test('renders basic radiogroup correctly', () => {
    render(<RadioGroup />);
    expect(screen.getByRole('radiogroup')).toBeTruthy();
  });

  test('renders a radiogroup with the data-testid property', () => {
    render(<RadioGroup data-testid="NovaRadio-root" />);
    expect(screen.getByTestId('NovaRadio-root')).toBeDefined();
  });

  test('should have `orientation` class', () => {
    render(<RadioGroup value="" orientation="horizontal" />);
    expect(screen.getByRole('radiogroup')).toHaveClass('NovaRadioGroup-horizontal');
  });

  test('the root component has the radiogroup role', () => {
    const { container } = render(<RadioGroup value="" />);
    expect(container.firstChild).toHaveAttribute('role', 'radiogroup');
  });

  test('should fire the onBlur callback', () => {
    const handleBlur = vi.fn();
    const { container } = render(<RadioGroup value="" onBlur={handleBlur} />);
    fireEvent.blur(container.firstChild as ChildNode);
    expect(handleBlur).toHaveBeenCalled();
  });

  test('should fire the onKeyDown callback', () => {
    const handleKeyDown = vi.fn();
    render(<RadioGroup tabIndex={-1} value="" onKeyDown={handleKeyDown} />);
    const radiogroup = screen.getByRole('radiogroup');
    act(() => {
      radiogroup.focus();
    });
    fireEvent.keyDown(radiogroup);
    expect(handleKeyDown).toHaveBeenCalled();
  });

  test('should support uncontrolled mode', () => {
    render(
      <RadioGroup name="group">
        <Radio value="one" />
      </RadioGroup>,
    );
    const radio = screen.getByRole('radio') as HTMLInputElement;
    fireEvent.click(radio);
    expect(radio.checked).to.equal(true);
  });

  test('should support default value in uncontrolled mode', () => {
    render(
      <RadioGroup name="group" defaultValue="zero">
        <Radio value="zero" />
        <Radio value="one" />
      </RadioGroup>,
    );
    const radios = screen.getAllByRole('radio') as Array<HTMLInputElement>;
    expect(radios[0].checked).to.equal(true);
    fireEvent.click(radios[1]);
    expect(radios[1].checked).to.equal(true);
  });

  test('should have a default name', () => {
    render(
      <RadioGroup>
        <Radio value="zero" />
        <Radio value="one" />
      </RadioGroup>,
    );
    const [arbitraryRadio, ...radios] = screen.getAllByRole('radio') as Array<HTMLInputElement>;
    expect(arbitraryRadio.name).not.to.equal('');
    expect(new Set(radios.map((radio) => radio.name))).to.have.length(1);
  });

  test('should support number value', () => {
    render(
      <RadioGroup name="group" defaultValue={1}>
        <Radio value={1} />
        <Radio value={2} />
      </RadioGroup>,
    );
    const radios = screen.getAllByRole('radio') as Array<HTMLInputElement>;
    expect(radios[0]).toHaveAttribute('value', '1');
    expect(radios[0].checked).to.equal(true);
    expect(radios[1].checked).to.equal(false);
    fireEvent.click(radios[1]);
    expect(radios[0].checked).to.equal(false);
    expect(radios[1].checked).to.equal(true);
  });

  describe('prop: onChange', () => {
    test('should fire onChange', () => {
      const handleChange = vi.fn();
      render(
        <RadioGroup value="" onChange={handleChange}>
          <Radio value="woofRadioGroup" />
          <Radio />
        </RadioGroup>,
      );
      const radios = screen.getAllByRole('radio');
      fireEvent.click(radios[0]);
      expect(handleChange).toHaveBeenCalled();
    });
  });

  test('should have configurable size', () => {
    const { container, rerender } = render(<RadioGroup />);
    expect(container.firstChild).toHaveClass('NovaRadioGroup-sizeMedium');
    rerender(<RadioGroup size="small" />);
    expect(container.firstChild).toHaveClass('NovaRadioGroup-sizeSmall');
    rerender(<RadioGroup size="large" />);
    expect(container.firstChild).toHaveClass('NovaRadioGroup-sizeLarge');
  });
});
