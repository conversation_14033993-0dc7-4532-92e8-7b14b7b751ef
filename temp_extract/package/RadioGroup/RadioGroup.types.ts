import * as React from 'react';
import { RadioProps } from '../Radio/Radio.types';
import { SxProps } from '../types/theme';
import { CreateSlotsAndSlotProps, SlotProps } from '../types/slot';

export type RadioGroupSlot = 'root';

export interface RadioGroupSlots {
  /**
   * The component that renders the root.
   * @default 'div'
   */
  root?: React.ElementType;
}

export type RadioGroupSlotsAndSlotProps = CreateSlotsAndSlotProps<
  RadioGroupSlots,
  {
    root: SlotProps<'div', object, RadioGroupOwnerState>;
  }
>;

export interface RadioGroupProps extends Omit<React.HTMLAttributes<HTMLDivElement>, 'sx'>, RadioGroupSlotsAndSlotProps {
  /**
   * Class name applied to the root element.
   */
  className?: string;
  /**
   * The component used for the Root slot.
   * Either a string to use a HTML element or a component.
   */
  component?: React.ElementType;
  /**
   * The default value. Use when the component is not controlled.
   */
  defaultValue?: any;
  /**
   * The radio's `disabledIcon` prop. If specified, the value is passed down to every radios under this element.
   * @default false
   */
  disableIcon?: boolean;
  /**
   * The name used to reference the value of the control.
   * If you don't provide this prop, it falls back to a randomly generated name.
   */
  name?: string;
  /**
   * The radio's `overlay` prop. If specified, the value is passed down to every radios under this element.
   * @default false
   */
  overlay?: boolean;
  /**
   * Callback fired when a radio button is selected.
   *
   * @param {React.ChangeEvent<HTMLInputElement>} event The event source of the callback.
   * You can pull out the new value by accessing `event.target.value` (string).
   */
  onChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
  /**
   * The component orientation.
   * @default 'vertical'
   */
  orientation?: 'horizontal' | 'vertical';
  /**
   * The size of the component.
   * @default 'medium'
   */
  size?: RadioProps['size'];
  /**
   * The system prop that allows defining system overrides as well as additional CSS styles.
   */
  sx?: SxProps;
  /**
   * Value of the selected radio button. The DOM API casts this to a string.
   */
  value?: any;
}

export interface RadioGroupOwnerState extends RadioGroupProps {}
