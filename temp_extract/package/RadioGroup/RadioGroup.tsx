'use client';

import * as React from 'react';
import {
  unstable_capitalize as capitalize,
  unstable_useControlled as useControlled,
  unstable_useId as useId,
  unstable_composeClasses as composeClasses,
} from '@mui/utils';
import useSlotProps from '@mui/utils/useSlotProps';
import { styled } from '@pigment-css/react';
import { getRadioGroupUtilityClass } from './RadioGroup.classes';
import { RadioGroupProps, RadioGroupOwnerState } from './RadioGroup.types';
import RadioGroupContext from './RadioGroupContext';
import FormControlContext from '../FormControl/FormControlContext';

const useUtilityClasses = (ownerState: RadioGroupOwnerState) => {
  const { orientation, size } = ownerState;
  const slots = {
    root: ['root', orientation, size && `size${capitalize(size)}`],
  };

  return composeClasses(slots, getRadioGroupUtilityClass, {});
};

const RadioGroupRoot = styled('div')<RadioGroupOwnerState>(({ theme }) => ({
  variants: [
    {
      props: { size: 'medium' },
      style: {
        '--nova-radiogroup-gap': '0.875rem',
      },
    },
    {
      props: { size: 'small' },
      style: {
        '--nova-radiogroup-gap': '0.625rem',
      },
    },
    {
      props: { size: 'large' },
      style: {
        '--nova-radiogroup-gap': '1.25rem',
      },
    },
    {
      props: { orientation: 'horizontal' },
      style: {
        flexDirection: 'row',
      },
    },
    {
      props: { orientation: 'vertical' },
      style: {
        flexDirection: 'column',
      },
    },
  ],
  display: 'flex',
  margin: '0.5rem',
  borderRadius: '8px',
}));

// eslint-disable-next-line react/display-name
export const RadioGroup = React.forwardRef((props: RadioGroupProps, ref: React.ForwardedRef<Element>) => {
  const {
    className,
    children,
    name: nameProp,
    defaultValue,
    disableIcon = false,
    component,
    overlay,
    value: valueProp,
    onChange,
    size: sizeProp = 'medium',
    orientation = 'vertical',
    role = 'radiogroup',
    slots = {},
    slotProps = {},
    ...other
  } = props;

  const formControl = React.useContext(FormControlContext);

  const [value, setValueState] = useControlled({
    controlled: valueProp,
    default: defaultValue,
    name: 'RadioGroup',
  });

  const size = props.size ?? formControl?.size ?? sizeProp;

  const ownerState = {
    orientation,
    size,
    role,
    ...props,
  };

  const classes = useUtilityClasses(ownerState);
  const name = useId(nameProp);

  const contextValue = React.useMemo(
    () => ({
      disableIcon,
      overlay,
      orientation,
      size,
      name,
      value,
      onChange: (event: React.ChangeEvent<HTMLInputElement>) => {
        setValueState(event.target.value);

        if (onChange) {
          onChange(event);
        }
      },
    }),
    [disableIcon, name, onChange, overlay, orientation, setValueState, size, value],
  );

  const SlotRoot = slots.root ?? RadioGroupRoot;
  const rootProps = useSlotProps({
    elementType: RadioGroupRoot,
    externalSlotProps: slotProps.root,
    externalForwardedProps: other,
    additionalProps: {
      ref,
      role,
      as: component,
    },
    ownerState,
    className: [classes.root, className],
  });

  return (
    <RadioGroupContext.Provider value={contextValue}>
      <SlotRoot {...rootProps}>
        {React.Children.map(children, (child, index) =>
          React.isValidElement(child)
            ? React.cloneElement(child, {
                // to let Radio knows when to apply margin(Inline|Block)Start
                ...(index === 0 && { 'data-first-child': '' }),
                ...(index === React.Children.count(children) - 1 && { 'data-last-child': '' }),
                'data-parent': 'RadioGroup',
                'data-size': size, // Add size property to child elements
              } as Record<string, string>)
            : child,
        )}
      </SlotRoot>
    </RadioGroupContext.Provider>
  );
});
