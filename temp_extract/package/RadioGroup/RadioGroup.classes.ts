import {
  unstable_generateUtilityClasses as generateUtilityClasses,
  unstable_generateUtilityClass as generateUtilityClass,
} from '@mui/utils';

export interface RadioGroupClasses {
  /** Class name applied to the root element. */
  root: string;
  /** Styles applied to the root element if `size="small"`. */
  sizeSmall: string;
  /** Styles applied to the root element if `size="medium"`. */
  sizeMedium: string;
  /** Styles applied to the root element if `size="large"`. */
  sizeLarge: string;
  /** Class name applied to the root element if `orientation="horizontal"`. */
  horizontal: string;
  /** Class name applied to the root element if `orientation="vertical"`. */
  vertical: string;
}

export function getRadioGroupUtilityClass(slot: string): string {
  return generateUtilityClass('NovaRadioGroup', slot);
}

const radioGroupClasses: RadioGroupClasses = generateUtilityClasses('NovaRadioGroup', [
  'root',
  'sizeSmall',
  'sizeMedium',
  'sizeLarge',
  'horizontal',
  'vertical',
]);

export default radioGroupClasses;
