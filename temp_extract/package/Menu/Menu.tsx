'use client';
import * as React from 'react';
import { styled } from '@pigment-css/react';
import { unstable_composeClasses as composeClasses, unstable_capitalize as capitalize } from '@mui/utils';
import { Popper } from '../Popper';
import useSlotProps from '@mui/utils/useSlotProps';
import { MenuOwnerState, MenuProps } from './Menu.types';
import { getMenuUtilityClass } from './Menu.classes';
import { useMenu, MenuProvider } from '../internal/hooks/useMenu';
import { DropdownContext, useDropdown } from '../internal/hooks/useDropdown';
import ListProvider from '../List/ListProvider';
import GroupListContext from '../List/GroupListContext';
import { ListRoot as StyledList } from '../List/List';
import { ClickAwayListener } from '../ClickAwayListener';
import { DropdownActionTypes } from '../internal/hooks/useDropdown';

const useUtilityClasses = (ownerState: MenuOwnerState) => {
  const { open, density } = ownerState;
  const slots = {
    root: ['root', open && 'expanded'],
    listbox: ['listbox', open && 'expanded', density && `density${capitalize(density)}`],
  };

  const composedClasses = composeClasses(slots, getMenuUtilityClass, {});
  return composedClasses;
};

const MenuRoot = styled(Popper)<MenuProps>(({ theme }) => ({
  zIndex: 1200,
}));

export const MenuListRoot = styled(StyledList)<MenuProps>(({ theme }) => ({
  borderRadius: '8px',
  border: `1px solid ${theme.vars.palette.outlineVariant}`,
  background: theme.vars.palette.surfaceContainer,
  overflow: 'auto',
  '--nova-listItem-color': theme.vars.palette.onSurface,
  '--nova-listItem-secondaryColor': theme.vars.palette.onSurfaceVariant,
  '--nova-listItem-paddingY': '8px',
  '--nova-listItem-paddingX': '16px',
  '--nova-listItem-minHeight': '56px',
  '--nova-listItem-gap': '8px',
  '--nova-listItem-paddingLeft': 'var(--nova-listItem-paddingX)',
  '--nova-listItem-paddingRight': 'var(--nova-listItem-paddingX)',
  '--nova-listDivider-gap': '8px',
  '--nova-list-paddingBlock': '8px',
  paddingBlock: 'var(--nova-list-paddingBlock)',
  paddingInline: 'var(--nova-list-paddingInline)',
  '--nova-listItem-stickyBackground': theme.vars.palette.surfaceContainer,
  '--nova-listItem-stickyTop': 'calc(var(--nova-list-padding, var(--nova-listDivider-gap)) * -1)', // negative amount of the List's padding block  ...scopedVariables,
  variants: [
    {
      props: { density: 'compact' },
      style: {
        '--nova-listItem-paddingY': '4px',
        '--nova-listItem-paddingX': '16px',
        '--nova-listItem-minHeight': '48px',
      },
    },
    {
      props: { density: 'standard' },
      style: {
        '--nova-listItem-paddingY': '8px',
        '--nova-listItem-paddingX': '16px',
        '--nova-listItem-minHeight': '56px',
      },
    },
    {
      props: { density: 'comfortable' },
      style: {
        fontFamily: theme.typography.fontFamily,
        '--nova-listItem-paddingY': '12px',
        '--nova-listItem-paddingX': '24px',
        '--nova-listItem-minHeight': '64px',
      },
    },
  ],
}));

const MenuInner = React.forwardRef(function Menu(props: MenuProps, ref: React.ForwardedRef<Element>) {
  const {
    anchorEl: anchorElProp,
    children,
    className,
    component,
    disablePortal = false,
    keepMounted = false,
    id,
    onItemsChange,
    onClose,
    modifiers: modifiersProp,
    density = 'standard',
    slots = {},
    slotProps = {},
    ...other
  } = props;

  const listRef = React.useRef<HTMLElement>(null);

  const dropdownContext = React.useContext(DropdownContext);

  const { contextValue, getListboxProps, open, triggerElement } = useMenu({
    onItemsChange,
    id,
    listboxRef: listRef,
  });
  const anchorEl = anchorElProp ?? triggerElement;

  const ownerState = {
    ...props,
    disablePortal,
    density,
    open,
  };

  const classes = useUtilityClasses(ownerState);

  const modifiers = React.useMemo(
    () => [
      {
        name: 'offset',
        options: {
          offset: [0, 4],
        },
      },
      ...(modifiersProp || []),
    ],
    [modifiersProp],
  );

  const Root = slots.root ?? MenuRoot;
  const rootProps = useSlotProps({
    additionalProps: {
      as: component,
      open: open && triggerElement !== null,
      anchorEl,
      disablePortal,
      keepMounted,
      modifiers,
    },
    elementType: Root,
    externalSlotProps: slotProps.root,
    externalForwardedProps: other,
    ownerState,
    className: [classes.root, className],
  });

  const MenuList = slots.listbox ?? MenuListRoot;
  const menuListProps = useSlotProps({
    additionalProps: {
      ref,
    },
    elementType: MenuList,
    getSlotProps: getListboxProps,
    externalSlotProps: slotProps.listbox,
    className: classes.listbox,
    ownerState,
  });

  const handleClickAway = (event: MouseEvent | TouchEvent) => {
    if (dropdownContext) {
      dropdownContext.dispatch({
        type: DropdownActionTypes.blur,
        event: event as any,
      });
    } else if (onClose) {
      onClose();
    }
  };

  const menuList = (
    <MenuList {...menuListProps}>
      <MenuProvider value={contextValue}>
        <GroupListContext.Provider value="menu">
          <ListProvider nested>{children} </ListProvider>
        </GroupListContext.Provider>
      </MenuProvider>
    </MenuList>
  );

  if (open === true && anchorEl == null) {
    return menuList;
  }

  return (
    <Root {...rootProps}>
      <ClickAwayListener onClickAway={handleClickAway}>{menuList}</ClickAwayListener>
    </Root>
  );
});

export const Menu = React.forwardRef(function Menu(props: MenuProps, ref: React.ForwardedRef<Element>) {
  const { open, onClose } = props;
  const upperDropdownContext = React.useContext(DropdownContext);

  // Always call hooks at the top level
  const { contextValue: dropdownContextValue } = useDropdown({
    open,
    onOpenChange: (event, isOpen) => {
      if (!isOpen && onClose) {
        onClose();
      }
    },
    componentName: 'Menu',
  });

  // If we're already in a dropdown context, don't create a new one
  if (upperDropdownContext) {
    return <MenuInner ref={ref} {...props} />;
  }

  // Create a new dropdown context for the menu
  return (
    <DropdownContext.Provider value={dropdownContextValue}>
      <MenuInner ref={ref} {...props} />
    </DropdownContext.Provider>
  );
});
