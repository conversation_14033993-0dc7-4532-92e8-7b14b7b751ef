import React from 'react';
import '@testing-library/jest-dom/vitest';
import { screen, render, fireEvent, waitFor } from '@testing-library/react';
import { expect, describe, it } from 'vitest';
import { unstable_capitalize as capitalize } from '@mui/utils';
import classes, { MenuClassKey } from './Menu.classes';
import { Menu } from './Menu';
import { MenuItem } from '../MenuItem';

describe('<Menu />', () => {
  it('should open menu when button clicked', async () => {
    render(
      <Menu open anchorEl={document.createElement('div')}>
        <MenuItem data-testid="root" />
      </Menu>,
    );

    expect(screen.getByRole('menu')).toBeInTheDocument();
    expect(screen.getByRole('menuitem')).toBeInTheDocument();
  });

  describe('prop: density', () => {
    it('standard by default', () => {
      render(
        <Menu open>
          <MenuItem />
        </Menu>,
      );
      expect(screen.getByRole('menu')).toHaveClass(classes.densityStandard);
    });
    (['standard', 'compact', 'comfortable'] as const).forEach((size) => {
      it(`should render ${size}`, () => {
        render(
          <Menu open density={size}>
            <MenuItem />
          </Menu>,
        );
        expect(screen.getByRole('menu')).toHaveClass(classes[`density${capitalize(size)}` as MenuClassKey]);
      });
    });
  });
});
