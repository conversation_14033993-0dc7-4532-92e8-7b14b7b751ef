import { CreateSlotsAndSlotProps, SlotProps } from '../types/slot';
import { OverrideProps } from '@mui/types';
import { SxProps } from '../types/theme';
import { PopperProps } from '../Popper';

export type MenuSlot = 'root';

export interface MenuSlots {
  /**
   * The component that renders the root.
   * @default 'div'
   */
  root?: React.ElementType;
  /**
   * The component that renders the root.
   * @default 'ul'
   */
  listbox?: React.ElementType;
}

export type MenuSlotsAndSlotProps = CreateSlotsAndSlotProps<
  MenuSlots,
  {
    root: SlotProps<'div', object, MenuOwnerState>;
    listbox: SlotProps<'ul', object, MenuOwnerState>;
  }
>;

export interface MenuTypeMap<P = object, D extends React.ElementType = 'div'> {
  props: P &
    MenuSlotsAndSlotProps &
    Omit<PopperProps, 'children' | 'open'> & {
      /**
       * Whether the menu is currently open.
       */
      open?: boolean;
      /**
       * The `density` attribute for the MenuItem
       * @default 'standard'
       */
      density?: 'standard' | 'compact' | 'comfortable';
      /**
       * The system prop that allows defining system overrides as well as additional CSS styles.
       */
      sx?: SxProps;
      /**
       * Triggered when focus leaves the menu and the menu should close.
       */
      onClose?: () => void;
      /**
       * Function called when the items displayed in the menu change.
       */
      onItemsChange?: (items: string[]) => void;
    };
  defaultComponent: D;
}

export type MenuProps<
  D extends React.ElementType = MenuTypeMap['defaultComponent'],
  P = {
    component?: React.ElementType;
  },
> = OverrideProps<MenuTypeMap<P, D>, D>;

export interface MenuOwnerState extends MenuProps {}
