import {
  unstable_generateUtilityClasses as generateUtilityClasses,
  unstable_generateUtilityClass as generateUtilityClass,
} from '@mui/utils';

export interface MenuClasses {
  /** Styles applied to the root element. */
  root: string;
  /** Styles applied to the root element if `density="standard"`. */
  densityStandard: string;
  /** Styles applied to the root element if `density="compact"`. */
  densityCompact: string;
  /** Styles applied to the root element if `density="comfortable"`. */
  densityComfortable: string;
}

export type MenuClassKey = keyof MenuClasses;

export function getMenuUtilityClass(slot: string): string {
  return generateUtilityClass('NovaMenu', slot);
}

const menuClasses: MenuClasses = generateUtilityClasses('NovaMenu', [
  'root',
  'densityStandard',
  'densityCompact',
  'densityComfortable',
]);

export default menuClasses;
