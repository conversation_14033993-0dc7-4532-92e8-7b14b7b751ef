import {
  unstable_generateUtilityClasses as generateUtilityClasses,
  unstable_generateUtilityClass as generateUtilityClass,
} from '@mui/utils';

export interface ListClasses {
  /** Styles applied to the root element. */
  root: string;
  /** Styles applied to the root element if `density="standard"`. */
  densityStandard: string;
  /** Styles applied to the root element if `density="compact"`. */
  densityCompact: string;
  /** Styles applied to the root element if `density="comfortable"`. */
  densityComfortable: string;
}

export type ListClassKey = keyof ListClasses;

export function getListUtilityClass(slot: string): string {
  return generateUtilityClass('NovaList', slot);
}

const ListClasses: ListClasses = generateUtilityClasses('NovaList', [
  'root',
  'densityStandard',
  'densityCompact',
  'densityComfortable',
]);

export default ListClasses;
