import { CreateSlotsAndSlotProps, SlotProps } from '../types/slot';
import { OverridableStringUnion, OverrideProps } from '@mui/types';
import { SxProps } from '../types/theme';

export type ListSlot = 'root';

export interface ListSlots {
  /**
   * The component that renders the root.
   * @default 'ul'
   */
  root?: React.ElementType;
}

export type ListSlotsAndSlotProps = CreateSlotsAndSlotProps<
  ListSlots,
  {
    root: SlotProps<'ul', object, ListOwnerState>;
  }
>;

export interface ListPropsSizeOverrides {}

export interface ListTypeMap<P = object, D extends React.ElementType = 'div'> {
  props: P &
    ListSlotsAndSlotProps & {
      /**
       * The content of the component.
       */
      children?: React.ReactNode;
      /**
       * The `density` attribute for the ListItem
       * @default 'standard'
       */
      density?: OverridableStringUnion<'standard' | 'compact' | 'comfortable', ListPropsSizeOverrides>;
      /**
       * The component orientation.
       * @default 'vertical'
       */
      orientation?: 'horizontal' | 'vertical';
      /**
       * Only for horizontal list.
       * If `true`, the list sets the flex-wrap to "wrap" and adjust margin to have gap-like behavior (will move to `gap` in the future).
       *
       * @default false
       */
      wrap?: boolean;
      /**
       * The system prop that allows defining system overrides as well as additional CSS styles.
       */
      sx?: SxProps;
    };
  defaultComponent: D;
}

export type ListProps<
  D extends React.ElementType = ListTypeMap['defaultComponent'],
  P = {
    component?: React.ElementType;
  },
> = OverrideProps<ListTypeMap<P, D>, D>;

export interface ListOwnerState extends ListProps {
  /**
   * @internal
   * If `true`, the element is rendered in a nested list item.
   */
  nesting?: boolean | string;
}
