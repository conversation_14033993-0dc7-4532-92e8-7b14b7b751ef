'use client';
import * as React from 'react';
import { styled } from '@pigment-css/react';
import { unstable_composeClasses as composeClasses, unstable_capitalize as capitalize } from '@mui/utils';
import useSlotProps from '@mui/utils/useSlotProps';
import { ListOwnerState, ListProps } from './List.types';
import { getListUtilityClass } from './List.classes';
import NestedListContext from './NestedListContext';
import ComponentListContext from './ComponentListContext';
import GroupListContext from './GroupListContext';
import ListProvider from './ListProvider';

const useUtilityClasses = (ownerState: ListOwnerState) => {
  const { density } = ownerState;
  const slots = {
    root: ['root', density && `density${capitalize(density)}`],
  };

  const composedClasses = composeClasses(slots, getListUtilityClass, {});
  return composedClasses;
};

export const ListRoot = styled('ul')<ListProps>(({ theme }) => ({
  margin: 0,
  padding: 0,
  outline: 0,
  boxSizing: 'border-box',
  listStyle: 'none',
  display: 'flex',
  flexDirection: 'column',
  flexGrow: 1,
  position: 'relative', // for sticky ListItem
  '--nova-listItem-color': theme.vars.palette.onSurface,
  '--nova-listItem-secondaryColor': theme.vars.palette.onSurfaceVariant,
  '--nova-listItem-paddingY': '16px',
  '--nova-listItem-paddingX': '16px',
  '--nova-listItem-minHeight': '36px',
  '--nova-listItem-gap': '16px',
  '--nova-listItemDecorator-gap': '8px',
  '--nova-listItem-paddingLeft': 'var(--nova-listItem-paddingX)',
  '--nova-listItem-paddingRight': 'var(--nova-listItem-paddingX)',
  '--nova-listDivider-gap': '8px',
  '--nova-list-gap': '0px',
  '--nova-list-nestedInsetStart': '0px',
  '--nova-listItemDecorator-size': 'var(--nova-avatar-size-standard, 2rem)',
  variants: [
    {
      props: { density: 'compact' },
      style: {
        fontFamily: theme.typography.fontFamily,
        '--nova-listItem-paddingY': '8px',
        '--nova-listItem-paddingX': '12px',
        '--nova-listItem-minHeight': '32px',
        '--nova-listItem-gap': '12px',
        '--nova-listItemDecorator-gap': '8px',
        '--nova-listDivider-gap': '4px',
        '--nova-listItemContent-gap': '0px',
        '--nova-listItemDecorator-size': 'var(--nova-avatar-size-standard, 1.5rem)',
      },
    },
    {
      props: { density: 'standard' },
      style: {
        fontFamily: theme.typography.fontFamily,
        '--nova-listItem-paddingY': '16px',
        '--nova-listItem-paddingX': '16px',
        '--nova-listItem-minHeight': '36px',
        '--nova-listItem-gap': '16px',
        '--nova-listItemDecorator-gap': '8px',
        '--nova-listDivider-gap': '8px',
        '--nova-listItemContent-gap': '0px',
        '--nova-listItemDecorator-size': 'var(--nova-avatar-size-standard, 2rem)',
      },
    },
    {
      props: { density: 'comfortable' },
      style: {
        fontFamily: theme.typography.fontFamily,
        '--nova-listItem-paddingY': '24px',
        '--nova-listItem-paddingX': '24px',
        '--nova-listItem-minHeight': '44px',
        '--nova-listItem-gap': '24px',
        '--nova-listItemDecorator-gap': '12px',
        '--nova-listDivider-gap': '12px',
        '--nova-listItemContent-gap': '4px',
        '--nova-listItemDecorator-size': 'var(--nova-avatar-size-standard, 2.5rem)',
      },
    },

    {
      props: { nesting: true },
      style: {
        '--nova-listItem-paddingRight': 'var(--nova-listItem-paddingX)',
        '--nova-listItem-paddingLeft': 'var(--nova-nestedListItem-paddingLeft)',
        // reset ListItem, ListItemButton negative margin (caused by NestedListItem)
        '--nova-listItemButton-marginBlock': '0px',
        '--nova-listItemButton-marginInline': '0px',
        '--nova-listItem-marginBlock': '0px',
        '--nova-listItem-marginInline': '0px',
        padding: 0,
        marginInlineStart: 'var(--nova-nestedList-marginLeft)',
        marginInlineEnd: 'var(--nova-nestedList-marginRight)',
        marginBlockStart: 'var(--nova-list-gap)',
        marginBlockEnd: 'initial', // reset user agent stylesheet.
      },
    },
    {
      props: { orientation: 'horizontal' },
      style: {
        flexDirection: 'row',
        paddingInline: 'var(--nova-list-padding)',
        paddingBlock: 'var(--nova-list-padding)',
      },
    },
    {
      props: { wrap: true },
      style: {
        flexWrap: 'wrap',
      },
    },
    {
      props: { orientation: 'horizontal', wrap: true },
      style: {
        padding: 'var(--nova-list-padding)',
        marginInlineStart: 'calc(-1 * var(--nova-list-gap))',
        marginBlockStart: 'calc(-1 * var(--nova-list-gap))',
      },
    },
  ],
}));

export const List = React.forwardRef(function List(props: ListProps, ref: React.ForwardedRef<Element>) {
  const nesting = React.useContext(NestedListContext);
  const group = React.useContext(GroupListContext);

  const {
    children,
    component,
    density = 'standard',
    orientation = 'vertical',
    wrap = false,
    role: roleProp,
    slots = {},
    slotProps = {},
    ...other
  } = props;

  const role = group ? 'group' : roleProp;

  const ownerState = {
    ...props,
    density,
    nesting,
    orientation,
    wrap,
    role,
  };

  const classes = useUtilityClasses(ownerState);
  const SlotRoot = slots.root ?? ListRoot;
  const rootProps = useSlotProps({
    additionalProps: {
      ref,
      as: component,
      role,
      'aria-labelledby': typeof nesting === 'string' ? nesting : undefined,
    },
    className: classes.root,
    elementType: ListRoot,
    externalSlotProps: slotProps.root,
    externalForwardedProps: other,
    ownerState,
  });
  return (
    <SlotRoot {...rootProps}>
      <ComponentListContext.Provider value={`${typeof component === 'string' ? component : ''}:${role || ''}`}>
        <ListProvider row={orientation === 'horizontal'} wrap={wrap}>
          {children}
        </ListProvider>
      </ComponentListContext.Provider>
    </SlotRoot>
  );
});
