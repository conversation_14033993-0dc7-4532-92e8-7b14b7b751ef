import * as React from 'react';
import { OverrideProps } from '@mui/types';
import { SlotProps, CreateSlotsAndSlotProps } from '../types/slot';

export type FormHelperTextSlot = 'root';

export interface FormHelperTextSlots {
  /**
   * The component that renders the root.
   * @default 'p'
   */
  root?: React.ElementType;
}

export type FormHelperTextSlotsAndSlotProps = CreateSlotsAndSlotProps<
  FormHelperTextSlots,
  {
    root: SlotProps<'p', object, FormHelperTextOwnerState>;
  }
>;

export interface FormHelperTextTypeMap<P = object, D extends React.ElementType = 'p'> {
  props: P & {
    /**
     * The content of the component.
     */
    children?: React.ReactNode;
    /**
     * If `true`, the helper text should be displayed in a disabled state.
     */
    disabled?: boolean;
    /**
     * If `true`, the helper text is displayed in an error state.
     */
    error?: boolean;
    /**
     * The size of the component.
     * @default 'medium'
     */
    size?: 'small' | 'medium' | 'large';
  } & FormHelperTextSlotsAndSlotProps;
  defaultComponent: D;
}

export type FormHelperTextProps<
  D extends React.ElementType = FormHelperTextTypeMap['defaultComponent'],
  P = {
    component?: React.ElementType;
  },
> = OverrideProps<FormHelperTextTypeMap<P, D>, D>;

export interface FormHelperTextOwnerState extends FormHelperTextProps {}
