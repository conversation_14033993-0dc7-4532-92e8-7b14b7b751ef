'use client';
import * as React from 'react';
import { OverridableComponent } from '@mui/types';
import {
  unstable_composeClasses as composeClasses,
  unstable_capitalize as capitalize,
  unstable_useForkRef as useForkRef,
} from '@mui/utils';
import useSlotProps from '@mui/utils/useSlotProps';
import { styled } from '@pigment-css/react';
import { FormHelperTextOwnerState, FormHelperTextProps, FormHelperTextTypeMap } from './FormHelperText.types';
import { getFormHelperTextUtilityClass } from './FormHelperText.classes';
import FormControlContext from '../FormControl/FormControlContext';

const useUtilityClasses = (ownerState: FormHelperTextOwnerState) => {
  const { disabled, error, size } = ownerState;
  const slots = {
    root: ['root', disabled && 'disabled', error && 'error', size && `size${capitalize(size)}`],
  };

  return composeClasses(slots, getFormHelperTextUtilityClass, {});
};

const FormHelperTextRoot = styled('div')<FormHelperTextProps>(({ theme }) => ({
  color: theme.vars.palette.onSurfaceVariant,
  variants: [
    {
      props: { disabled: true },
      style: {
        color: theme.vars.palette.onBackgroundDisabled,
      },
    },
    {
      props: { error: true, disabled: false },
      style: {
        color: theme.vars.palette.error,
      },
    },
    {
      props: { size: 'small' },
      style: {
        fontSize: '12px',
        lineHeight: '16px',
      },
    },
    {
      props: { size: 'medium' },
      style: {
        fontSize: '14px',
        lineHeight: '19px',
      },
    },
    {
      props: { size: 'large' },
      style: {
        fontSize: '16px',
        lineHeight: '20px',
      },
    },
  ],
}));

// eslint-disable-next-line react/display-name
export const FormHelperText = React.forwardRef((props: FormHelperTextProps, ref: React.ForwardedRef<Element>) => {
  const {
    disabled: disabledProp = false,
    error: errorProp = false,
    size: sizeProp = 'medium',
    children,
    component,
    slots = {},
    slotProps = {},
    ...rest
  } = props;
  const rootRef = React.useRef<HTMLElement>(null);
  const handleRef = useForkRef(rootRef, ref);
  const formControl = React.useContext(FormControlContext);

  const disabled = props.disabled ?? formControl?.disabled ?? disabledProp;
  const error = props.error ?? formControl?.error ?? errorProp;
  const size = props.size ?? formControl?.size ?? sizeProp;

  const ownerState = {
    ...props,
    disabled,
    error,
    size,
  };

  const classes = useUtilityClasses(ownerState);

  const SlotRoot = slots.root ?? FormHelperTextRoot;

  const slotRootProps = useSlotProps({
    elementType: SlotRoot,
    externalSlotProps: slotProps.root,
    externalForwardedProps: rest,
    additionalProps: {
      ref: handleRef,
      as: component,
    },
    ownerState: ownerState,
    className: classes.root,
  });

  return <SlotRoot {...slotRootProps}>{children}</SlotRoot>;
}) as OverridableComponent<FormHelperTextTypeMap>;
