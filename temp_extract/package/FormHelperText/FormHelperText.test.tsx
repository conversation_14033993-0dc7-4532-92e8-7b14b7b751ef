import '@testing-library/jest-dom/vitest';
import { render, screen, cleanup } from '@testing-library/react';
import React from 'react';
import { describe, expect, it, afterEach } from 'vitest';
import { FormHelperText } from './FormHelperText';

afterEach(() => {
  cleanup();
});

describe('FormHelperText', () => {
  it('should render normal', () => {
    render(<FormHelperText data-testid="NovaFormHelperText-root" />);
    expect(screen.getByTestId('NovaFormHelperText-root')).toHaveClass('NovaFormHelperText-root');
    expect(screen.getByTestId('NovaFormHelperText-root')).toHaveClass('NovaFormHelperText-sizeMedium');
  });

  it('should render error state', () => {
    render(<FormHelperText data-testid="NovaFormHelperText-root" error />);
    expect(screen.getByTestId('NovaFormHelperText-root')).toHaveClass('Mui-error');
  });

  it('should render disabled state', () => {
    render(<FormHelperText data-testid="NovaFormHelperText-root" disabled />);
    expect(screen.getByTestId('NovaFormHelperText-root')).toHaveClass('Mui-disabled');
  });

  it('should render medium size', () => {
    render(<FormHelperText data-testid="NovaFormHelperText-root" size="medium" />);
    expect(screen.getByTestId('NovaFormHelperText-root')).toHaveClass('NovaFormHelperText-sizeMedium');
  });

  it('should render small size', () => {
    render(<FormHelperText data-testid="NovaFormHelperText-root" size="small" />);
    expect(screen.getByTestId('NovaFormHelperText-root')).toHaveClass('NovaFormHelperText-sizeSmall');
  });

  it('should render large size', () => {
    render(<FormHelperText data-testid="NovaFormHelperText-root" size="large" />);
    expect(screen.getByTestId('NovaFormHelperText-root')).toHaveClass('NovaFormHelperText-sizeLarge');
  });
});
