import {
  unstable_generateUtilityClasses as generateUtilityClasses,
  unstable_generateUtilityClass as generateUtilityClass,
} from '@mui/utils';

export interface RadioClasses {
  /** Class name applied to the root element. */
  root: string;
  /** Class name applied to the container element. */
  container: string;
  /** Class name applied to the radio element. */
  radio: string;
  /** Class name applied to the icon element. */
  icon: string;
  /** Class name applied to the action element. */
  action: string;
  /** Class name applied to the input element. */
  input: string;
  /** Class name applied to the label element. */
  label: string;
  /** State class applied to the root, action slots if `checked`. */
  checked: string;
  /** State class applied to the root, action slots if `disabled`. */
  disabled: string;
  /** Class name applied to the root element if the switch has visible focus */
  focusVisible: string;
  /** Styles applied to the root element if `size="small"`. */
  sizeSmall: string;
  /** Styles applied to the root element if `size="medium"`. */
  sizeMedium: string;
  /** Styles applied to the root element if `size="large"`. */
  sizeLarge: string;
}

export function getRadioUtilityClass(slot: string): string {
  return generateUtilityClass('NovaRadio', slot);
}

const radioClasses: RadioClasses = generateUtilityClasses('NovaRadio', [
  'root',
  'container',
  'radio',
  'icon',
  'action',
  'input',
  'label',
  'checked',
  'disabled',
  'focusVisible',
  'sizeSmall',
  'sizeMedium',
  'sizeLarge',
]);

export default radioClasses;
