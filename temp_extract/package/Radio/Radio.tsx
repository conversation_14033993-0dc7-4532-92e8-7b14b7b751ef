'use client';

import * as React from 'react';
import {
  unstable_composeClasses as composeClasses,
  unstable_capitalize as capitalize,
  EventHandlers,
} from '@mui/utils';
import useSlotProps from '@mui/utils/useSlotProps';
import { styled } from '@pigment-css/react';
import radioClasses, { getRadioUtilityClass } from './Radio.classes';
import { RadioOwnerState, RadioProps } from './Radio.types';
import RadioGroupContext from '../RadioGroup/RadioGroupContext';
import { useSwitch } from '../internal/hooks/useSwitch';
import FormControlContext from '../FormControl/FormControlContext';

const useUtilityClasses = (ownerState: RadioOwnerState) => {
  const { checked, disabled, disableIcon, focusVisible, size } = ownerState;

  const slots = {
    root: [
      'root',
      checked && 'checked',
      disabled && 'disabled',
      focusVisible && 'focusVisible',
      size && `size${capitalize(size)}`,
    ],
    container: ['container'],
    radio: ['radio', checked && 'checked', disabled && 'disabled'],
    icon: ['icon', disableIcon && 'disabled'],
    action: ['action', checked && 'checked', disableIcon && disabled && 'disabled', focusVisible && 'focusVisible'],
    input: ['input'],
    label: ['label'],
  };

  return composeClasses(slots, getRadioUtilityClass, {});
};

function areEqualValues(a: unknown, b: unknown) {
  if (typeof b === 'object' && b !== null) {
    return a === b;
  }
  return String(a) === String(b);
}

const RadioRoot = styled('span')<RadioOwnerState>(({ theme }) => ({
  '--nova-radiogroup-gap': '0.5rem',
  padding: 0,
  ...theme.typography.bodyMedium,
  variants: [
    {
      props: { size: 'medium' },
      style: {
        '--nova-radio-size': '19px',
        '--nova-radio-container-size': '40px',
        gap: '4px',
      },
    },
    {
      props: { size: 'small' },
      style: {
        '--nova-radio-size': '16px',
        '--nova-radio-container-size': '32px',
        gap: '4px',
      },
    },
    {
      props: { size: 'large' },
      style: {
        '--nova-radio-size': '22px',
        '--nova-radio-container-size': '48px',
        gap: '8px',
      },
    },
    {
      props: { 'data-parent': 'RadioGroup', 'data-first-child': undefined, orientation: 'horizontal' },
      style: {
        marginInlineStart: 'var(--nova-radiogroup-gap)',
        marginBlockStart: undefined,
      },
    },
    {
      props: { 'data-parent': 'RadioGroup', 'data-first-child': undefined, orientation: 'vertical' },
      style: {
        marginInlineStart: undefined,
        marginBlockStart: 'var(--nova-radiogroup-gap)',
      },
    },
    {
      props: { overlay: true },
      style: {
        position: 'initial',
      },
    },
    {
      props: { overlay: false },
      style: {
        position: 'relative',
      },
    },
  ],
  display: 'inline-flex',
  alignItems: 'center',
  boxSizing: 'border-box',
  minWidth: 0,
  fontFamily: theme.typography.fontFamily,
  color: theme.vars.palette.onSurface,
  cursor: 'pointer',
  [`&.${radioClasses.disabled}`]: {
    opacity: 0.4,
    cursor: 'default',
    pointerEvents: 'none',
  },
}));

const RadioContainer = styled('span')<RadioOwnerState>(() => ({
  display: 'inline-flex',
  justifyContent: 'center',
  alignItems: 'center',
  width: 'var(--nova-radio-container-size)',
  height: 'var(--nova-radio-container-size)',
  flexShrink: 0,
  position: 'relative',
}));

const RadioRadio = styled('span')<RadioOwnerState>(({ theme }) => ({
  color: theme.vars.palette.primary,
  margin: 0,
  boxSizing: 'border-box',
  width: 'var(--nova-radio-size)',
  height: 'var(--nova-radio-size)',
  border: '2px solid',
  borderRadius: 'var(--nova-radio-size)',
  display: 'inline-flex',
  justifyContent: 'center',
  alignItems: 'center',
  flexShrink: 0,
  position: 'relative',
  [`${RadioRoot}.${radioClasses.focusVisible} &`]: {
    outline: `1px solid ${theme.vars.palette.primary}`,
    outlineOffset: '1px',
    borderColor: theme.vars.palette.addOn.primaryFixedDim,
    backgroundColor: `color-mix(in srgb, transparent, ${theme.vars.palette.primary} ${theme.vars.palette.stateLayers.focusPrimary})`,
    '& > div': {
      color: theme.vars.palette.addOn.primaryFixedDim,
    },
  },
  '&:hover': {
    '@media (hover: hover)': {
      '& > div': {
        color: theme.vars.palette.addOn.primaryFixedDim,
      },
      backgroundColor: `color-mix(in srgb, transparent, ${theme.vars.palette.primary} ${theme.vars.palette.stateLayers.hoverPrimary})`,
      borderColor: theme.vars.palette.addOn.primaryFixedDim,
    },
  },
  '&:active': {
    backgroundColor: `color-mix(in srgb, transparent, ${theme.vars.palette.primary} ${theme.vars.palette.stateLayers.pressPrimary})`,
  },
  variants: [
    {
      props: { disableIcon: true },
      style: {
        display: 'contents',
        borderColor: theme.vars.palette.onBackgroundDisabled,
      },
    },
    {
      props: { disableIcon: false },
      style: {
        borderColor: theme.vars.palette.primary,
        backgroundColor: theme.vars.palette.backgroundDisabled,
      },
    },
  ],
  [`&.${radioClasses.disabled}`]: {
    backgroundColor: theme.vars.palette.backgroundDisabled,
    cursor: 'default',
    pointerEvents: 'none',
    userSelect: 'none',
    '& > div': {
      color: theme.vars.palette.onBackgroundDisabled,
    },
    borderColor: theme.vars.palette.onBackgroundDisabled,
  },
}));

const RadioAction = styled('span')<RadioOwnerState>(({ theme }) => ({
  position: 'absolute',
  textAlign: 'left',
  top: 0,
  left: 0,
  width: 'var(--nova-radio-size)',
  height: 'var(--nova-radio-size)',
  zIndex: 1,
}));

const RadioInput = styled('input')(() => ({
  margin: 0,
  opacity: 0,
  position: 'absolute',
  height: '100%',
  width: '100%',
  cursor: 'pointer',
  [`${RadioRoot}[data-disable-icon="true"] &`]: {
    cursor: 'default',
  },
  [`${RadioRoot}.${radioClasses.disabled} &`]: {
    cursor: 'default',
  },
  '&[readonly]': {
    pointerEvents: 'none',
    cursor: 'default',
  },
}));

const RadioLabel = styled('label')<RadioOwnerState>(({ theme }) => ({
  flex: 1,
  minWidth: 0,
  cursor: 'pointer',
  display: 'inline-flex',
  alignItems: 'center',
  variants: [
    {
      props: { disableIcon: true },
      style: {
        zIndex: 1,
        pointerEvents: 'none',
      },
    },
    {
      props: { disableIcon: false },
      style: {
        pointerEvents: 'auto',
      },
    },
    {
      props: { disabled: true },
      style: {
        cursor: 'default',
        pointerEvents: 'none',
      },
    },
    {
      props: { size: 'small' },
      style: {
        fontSize: '14px',
        lineHeight: '18px',
      },
    },
    {
      props: { size: 'medium' },
      style: {
        fontSize: '16px',
        lineHeight: '20px',
      },
    },
    {
      props: { size: 'large' },
      style: {
        fontSize: '18px',
        lineHeight: '24px',
      },
    },
  ],
}));

const RadioIcon = styled('span')<RadioOwnerState>(() => ({
  width: 'calc((var(--nova-radio-size) - 4px) / 2)',
  height: 'calc((var(--nova-radio-size) - 4px) / 2)',
  borderRadius: '50%',
  color: 'inherit',
  backgroundColor: 'currentColor',
  transition: 'transform 0.15s ease-in-out',
  position: 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%) scale(0)',
  variants: [
    {
      props: { checked: true },
      style: {
        transform: 'translate(-50%, -50%) scale(1)',
      },
    },
    {
      props: { checked: false },
      style: {
        transform: 'translate(-50%, -50%) scale(0)',
      },
    },
  ],
}));

// eslint-disable-next-line react/display-name
export const Radio = React.forwardRef((props: RadioProps, ref: React.ForwardedRef<Element>) => {
  const {
    className,
    checked: checkedProp,
    checkedIcon,
    defaultChecked,
    disabled: disabledProp,
    disableIcon: disableIconProp = false,
    overlay: overlayProp = false,
    component,
    label,
    name: nameProp,
    id: idOverride,
    onBlur,
    onChange,
    onFocus,
    onFocusVisible,
    readOnly,
    required: requiredProps,
    size: sizeProp = 'medium',
    uncheckedIcon,
    value,
    slots = {},
    slotProps = {},
    ...other
  } = props;

  const radioGroup = React.useContext(RadioGroupContext);
  const formControl = React.useContext(FormControlContext);

  const generatedId = React.useId();
  const id = idOverride || formControl?.htmlFor || generatedId;

  // The component's own size is used first. If not, the RadioGroup's size is used.
  const size = props.size ?? radioGroup?.size ?? formControl?.size ?? sizeProp;
  const name = props.name ?? radioGroup?.name ?? nameProp;
  const disableIcon = props.disableIcon ?? radioGroup?.disableIcon ?? disableIconProp;
  const overlay = props.overlay ?? radioGroup?.overlay ?? overlayProp;
  const inRequired = props.required ?? formControl?.required ?? requiredProps;
  const inDisabled = props.disabled ?? formControl?.disabled ?? disabledProp;

  const radioChecked =
    typeof checkedProp === 'undefined' && value != null ? areEqualValues(radioGroup?.value, value) : checkedProp;

  // Create a ref for the input element
  const inputRef = React.useRef<HTMLInputElement>(null);

  // Direct handler for label clicks
  const handleLabelClick = React.useCallback(
    (event: React.MouseEvent) => {
      if (inDisabled || readOnly) {
        return;
      }

      // Programmatically trigger the input click directly
      if (inputRef.current) {
        inputRef.current.click();

        // Create a synthetic change event to match the expected shape
        const createSyntheticChangeEvent = () => {
          const syntheticEvent = new Event('change', { bubbles: true }) as any;

          // Add properties that would be available on a real change event
          syntheticEvent.target = {
            value,
            checked: true,
            name,
            type: 'radio',
            nodeName: 'INPUT',
          };

          return syntheticEvent;
        };

        // Only manually dispatch events for controlled components
        // For uncontrolled components, the native click will handle state
        if (!radioGroup && onChange) {
          // For standalone Radio with onChange
          try {
            onChange(createSyntheticChangeEvent());
          } catch (error) {
            console.error('Error in Radio onChange handler:', error);
          }
        } else if (radioGroup?.onChange && radioGroup.value !== undefined) {
          // For controlled RadioGroup
          try {
            radioGroup.onChange(createSyntheticChangeEvent());
          } catch (error) {
            console.error('Error in RadioGroup onChange handler:', error);
          }
        }
      }
    },
    [inDisabled, name, onChange, radioGroup, readOnly, value],
  );

  const handleClick = React.useCallback(
    (event: React.MouseEvent<HTMLInputElement>) => {
      if (readOnly) {
        event.preventDefault();
        return;
      }
    },
    [readOnly],
  );

  const useRadioProps = {
    checked: radioChecked,
    defaultChecked,
    disabled: inDisabled,
    required: inRequired,
    readOnly,
    onBlur: formControl?.onBlur,
    onChange: formControl?.onChange,
    onFocus: formControl?.onFocus,
    onFocusVisible,
  };

  const { getInputProps, checked, disabled, focusVisible } = useSwitch(useRadioProps);

  const ownerState = {
    ...props,
    checked,
    disabled,
    focusVisible,
    size,
    disableIcon,
    overlay,
    orientation: radioGroup?.orientation,
  };

  const classes = useUtilityClasses(ownerState);

  const SlotRoot = slots.root ?? RadioRoot;
  const rootProps = useSlotProps({
    elementType: RadioRoot,
    externalSlotProps: slotProps.root,
    externalForwardedProps: other,
    additionalProps: {
      ref,
      'data-disable-icon': disableIcon,
      as: component,
    },
    ownerState,
    className: [classes.root, className],
  });

  const SlotContainer = slots.container ?? RadioContainer;
  const containerProps = useSlotProps({
    elementType: RadioContainer,
    externalSlotProps: slotProps.container,
    ownerState,
    className: classes.container,
  });

  const SlotRadio = slots.radio ?? RadioRadio;
  const radioProps = useSlotProps({
    elementType: RadioRadio,
    externalSlotProps: slotProps.radio,
    ownerState,
    className: classes.radio,
  });

  const SlotIcon = slots.icon ?? RadioIcon;
  const iconProps = useSlotProps({
    elementType: RadioIcon,
    externalSlotProps: slotProps.icon,
    ownerState,
    className: classes.icon,
  });

  const SlotAction = slots.action ?? RadioAction;
  const actionProps = useSlotProps({
    elementType: RadioAction,
    externalSlotProps: slotProps.action,
    ownerState,
    className: classes.action,
  });

  const SlotInput = slots.input ?? RadioInput;
  const inputProps = useSlotProps({
    elementType: RadioInput,
    externalSlotProps: slotProps.input,
    additionalProps: {
      id: id,
      type: 'radio',
      role: undefined,
      name,
      value: String(value),
      onClick: handleClick,
      'aria-checked': checked,
      'aria-disabled': disabled,
      ref: inputRef,
      onKeyDown: (event: React.KeyboardEvent) => {
        // Trigger click on Space key for better accessibility
        if (event.key === ' ' && !disabled && !readOnly) {
          event.preventDefault();
          inputRef.current?.click();
        }
      },
    },
    getSlotProps: (eventHandlers: EventHandlers) => {
      return getInputProps({
        ...eventHandlers,
        onChange: onChange || radioGroup?.onChange,
        onFocus,
        onBlur,
      });
    },
    ownerState,
    className: classes.input,
  });

  const SlotLabel = slots.label ?? RadioLabel;
  const labelProps = useSlotProps({
    elementType: RadioLabel,
    externalSlotProps: slotProps.label,
    additionalProps: {
      id: `${id}-label`,
      htmlFor: id,
      onClick: handleLabelClick,
    },
    ownerState,
    className: classes.label,
  });

  // Create a wrapper for the label text that also responds to clicks
  const LabelText = React.useMemo(() => {
    return label ? (
      <span onClick={handleLabelClick} style={{ cursor: disabled ? 'default' : 'pointer' }}>
        {label}
      </span>
    ) : null;
  }, [label, handleLabelClick, disabled]);

  return (
    <SlotRoot {...rootProps}>
      <SlotContainer {...containerProps}>
        <SlotRadio {...radioProps}>
          {checked && !disableIcon && checkedIcon}
          {!checked && !disableIcon && uncheckedIcon}
          {!checkedIcon && !uncheckedIcon && !disableIcon && <SlotIcon {...iconProps} />}
          <SlotAction {...actionProps}>
            <SlotInput {...inputProps} />
          </SlotAction>
        </SlotRadio>
      </SlotContainer>
      <SlotLabel {...labelProps}>{LabelText}</SlotLabel>
    </SlotRoot>
  );
});
