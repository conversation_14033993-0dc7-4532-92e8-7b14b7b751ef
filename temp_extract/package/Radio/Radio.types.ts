import * as React from 'react';
import { OverridableStringUnion } from '@mui/types';
import { SxProps } from '../types/theme';
import { CreateSlotsAndSlotProps, SlotProps } from '../types/slot';
import { UseSwitchParameters } from '../internal/hooks/useSwitch';

export type RadioSlot = 'root' | 'container' | 'radio' | 'icon' | 'action' | 'input' | 'label';

export interface RadioSlots {
  /**
   * The component that renders the root.
   * @default 'span'
   */
  root?: React.ElementType;
  /**
   * The component that renders the container.
   * @default 'span'
   */
  container?: React.ElementType;
  /**
   * The component that renders the radio.
   * @default 'span'
   */
  radio?: React.ElementType;
  /**
   * The component that renders the icon.
   * @default 'span'
   */
  icon?: React.ElementType;
  /**
   * The component that renders the action.
   * @default 'span'
   */
  action?: React.ElementType;
  /**
   * The component that renders the input.
   * @default 'input'
   */
  input?: React.ElementType;
  /**
   * The component that renders the label.
   * @default 'label'
   */
  label?: React.ElementType;
}

export interface RadioPropsSizeOverrides {}

export type RadioSlotsAndSlotProps = CreateSlotsAndSlotProps<
  RadioSlots,
  {
    root: SlotProps<'span', object, RadioOwnerState>;
    container: SlotProps<'span', object, RadioOwnerState>;
    radio: SlotProps<'span', object, RadioOwnerState>;
    icon: SlotProps<'span', object, RadioOwnerState>;
    action: SlotProps<'span', object, RadioOwnerState>;
    input: SlotProps<'input', object, RadioOwnerState>;
    label: SlotProps<'label', object, RadioOwnerState>;
  }
>;

export interface RadioProps extends UseSwitchParameters, RadioSlotsAndSlotProps {
  /**
   * The id of the `input` element.
   */
  id?: string;
  /**
   * The icon to display when the component is checked.
   */
  checkedIcon?: React.ReactNode;
  /**
   * The icon to display when the component is not checked.
   */
  uncheckedIcon?: React.ReactNode;
  /**
   * Class name applied to the root element.
   */
  className?: string;
  /**
   * The component used for the Root slot.
   * Either a string to use a HTML element or a component.
   */
  component?: React.ElementType;
  /**
   * If `true`, the checked icon is removed and the selected variant is applied on the `action` element instead.
   * @default false
   */
  disableIcon?: boolean;
  /**
   * The label element at the end the radio.
   */
  label?: React.ReactNode;
  /**
   * The `name` attribute of the input.
   */
  name?: string;
  /**
   * If `true`, the root element's position is set to initial which allows the action area to fill the nearest positioned parent.
   * This prop is useful for composing Radio with ListItem component.
   * @default false
   */
  overlay?: boolean;
  /**
   * The size of the component.
   * @default 'medium'
   */
  size?: OverridableStringUnion<'small' | 'medium' | 'large', RadioPropsSizeOverrides>;
  /**
   * The system prop that allows defining system overrides as well as additional CSS styles.
   */
  sx?: SxProps;
  /**
   * The value of the component. The DOM API casts this to a string.
   */
  value?: unknown;
}

export interface RadioOwnerState extends RadioProps {
  /**
   * If `true`, the element's focus is visible.
   */
  focusVisible?: boolean;
  /**
   * @internal
   * The value from the RadioGroup component.
   */
  orientation?: 'horizontal' | 'vertical';
  /**
   * @internal
   * The internal prop for controlling CSS margin of the element.
   */
  'data-first-child'?: string;
  /**
   * @internal
   * The internal prop for controlling CSS margin of the element.
   */
  'data-parent'?: string;
}
