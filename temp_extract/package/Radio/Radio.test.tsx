/// <reference types="@testing-library/jest-dom" />
import React from 'react';
import { expect, test, describe, vi } from 'vitest';
import { render, screen, fireEvent, act } from '@testing-library/react';
import { Radio } from './Radio';
import { RadioGroup } from '../RadioGroup/RadioGroup';

describe('<Radio />', () => {
  test('renders basic radio correctly', () => {
    const view = render(<Radio />);
    expect(view).toBeTruthy();
    expect(screen.getByRole('radio')).not.toBeChecked();
  });

  test('renders a radio with the Checked state when checked', () => {
    render(<Radio defaultChecked />);
    expect(screen.getByRole('radio')).toBeChecked();
  });

  test('renders a radio with the data-testid property', () => {
    render(<Radio data-testid="NovaRadio-root" />);
    expect(screen.getByTestId('NovaRadio-root')).toBeDefined();
  });

  test('renders a `role="radio"` with the name', () => {
    render(<Radio name="bar" />);
    expect(screen.getByRole('radio')).to.have.property('name', 'bar');
  });

  test('renders a `role="radio"` with the required attribute', () => {
    render(<Radio name="bar" required />);
    expect(screen.getByRole('radio')).toHaveAttribute('required');
  });

  test('renders a `role="radio"` with the readOnly attribute', () => {
    render(<Radio name="bar" readOnly />);
    expect(screen.getByRole('radio')).toHaveAttribute('readonly');
  });

  test('the radio can be disabled', () => {
    render(<Radio disabled />);
    expect(screen.getByRole('radio')).to.have.property('disabled', true);
  });

  test('should have configurable size', () => {
    const { container, rerender } = render(<Radio />);
    expect(container.firstChild).toHaveClass('NovaRadio-sizeMedium');
    rerender(<Radio size="small" />);
    expect(container.firstChild).toHaveClass('NovaRadio-sizeSmall');
    rerender(<Radio size="large" />);
    expect(container.firstChild).toHaveClass('NovaRadio-sizeLarge');
  });

  test('handles click events', () => {
    const handleChange = vi.fn();
    render(<Radio onChange={handleChange} />);
    const radioElement = screen.getByRole('radio');
    fireEvent.click(radioElement);
    expect(handleChange).toHaveBeenCalled();
    expect(radioElement as HTMLInputElement).toBeChecked();
  });

  test('the Checked state changes after change events', () => {
    render(<Radio defaultChecked />);
    act(() => {
      screen.getByRole('radio').click();
      fireEvent.change(screen.getByRole('radio'), { target: { checked: '' } });
    });
    expect(screen.getByRole('radio')).not.toBeChecked();
  });

  test('should be checked when it is selected in the radio group', () => {
    render(
      <RadioGroup defaultValue="1">
        <Radio value="1" />
        <Radio value="2" />
      </RadioGroup>,
    );
    expect(screen.getByRole('radio', { checked: true })).to.have.property('value', '1');
  });

  test('should be checked when changing the value', () => {
    render(
      <RadioGroup defaultValue={1}>
        <Radio name="0" value={0} />
        <Radio name="1" value={1} />
      </RadioGroup>,
    );
    expect(screen.getByRole('radio', { checked: true })).to.have.property('value', '1');
    act(() => {
      screen.getByRole('radio', { checked: false }).click();
    });
    expect(screen.getByRole('radio', { checked: true })).to.have.property('value', '0');
    act(() => {
      screen.getByRole('radio', { checked: false }).click();
    });
    expect(screen.getByRole('radio', { checked: true })).to.have.property('value', '1');
  });

  test('should pass `slotProps` down to slots', () => {
    const { container } = render(
      <Radio
        data-testid="root-radio"
        label="Label"
        slotProps={{
          radio: { className: 'custom-radio' },
          icon: { className: 'custom-icon' },
          action: { className: 'custom-action' },
          input: { className: 'custom-input' },
          label: { className: 'custom-label' },
        }}
      />,
    );

    expect(screen.getByTestId('root-radio')).toBeVisible();
    expect(container.querySelector('.custom-radio')).toHaveClass('NovaRadio-radio');
    expect(container.querySelector('.custom-icon')).toHaveClass('NovaRadio-icon');
    expect(container.querySelector('.custom-action')).toHaveClass('NovaRadio-action');
    expect(container.querySelector('.custom-input')).toHaveClass('NovaRadio-input');
    expect(container.querySelector('.custom-input')).toHaveClass('NovaRadio-input');
  });
});
