import generateUtilityClasses from '@mui/utils/generateUtilityClasses';
import generateUtilityClass from '@mui/utils/generateUtilityClass';

export interface PaginationClasses {
  /** Styles applied to the root element. */
  root: string;
  /** Styles applied to the ul element. */
  ul: string;
}

export type PaginationClassKey = keyof PaginationClasses;

export function getPaginationUtilityClass(slot: string): string {
  return generateUtilityClass('NovaPagination', slot);
}

const paginationClasses: PaginationClasses = generateUtilityClasses('NovaPagination', ['root', 'ul']);

export default paginationClasses;
