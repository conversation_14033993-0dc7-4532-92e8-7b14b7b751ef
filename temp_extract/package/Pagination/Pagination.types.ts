import * as React from 'react';
import { OverridableStringUnion } from '@mui/types';
import { SxProps } from '../types/theme';
import { PaginationClasses } from './Pagination.classes';
import { UsePaginationItem, UsePaginationProps } from '../internal/hooks/usePagination/usePagination.types';

export interface PaginationRenderItemParams extends UsePaginationItem {
  size: PaginationProps['size'];
}

export interface PaginationPropsSizeOverrides {}

export interface PaginationLocale {
  /**
   * Accepts a function which returns a string value that provides a user-friendly name for the current page.
   * This is important for screen reader users.
   *
   * For localization purposes, you can use the provided [translations](https://mui.com/material-ui/guides/localization/).
   * @param {string} type The link or button type to format ('page' | 'first' | 'last' | 'next' | 'previous' | 'start-ellipsis' | 'end-ellipsis'). Defaults to 'page'.
   * @param {number | null} page The page number to format.
   * @param {boolean} selected If true, the current page is selected.
   * @returns {string}
   */
  getItemAriaLabel?: (
    type: UsePaginationItem['type'],
    page: UsePaginationItem['page'],
    selected: UsePaginationItem['selected'],
  ) => string;
}

export interface PaginationProps
  extends UsePaginationProps,
    PaginationLocale,
    Omit<React.HTMLAttributes<HTMLElement>, 'children' | 'onChange'> {
  /**
   * Override or extend the styles applied to the component.
   */
  classes?: Partial<PaginationClasses>;

  /**
   * Render the item.
   * @param {PaginationRenderItemParams} params The props to spread on a PaginationItem.
   * @returns {ReactNode}
   * @default (item) => <PaginationItem {...item} />
   */
  renderItem?: (params: PaginationRenderItemParams) => React.ReactNode;
  /**
   * The size of the component.
   * @default 'medium'
   */
  size?: OverridableStringUnion<'small' | 'medium' | 'large', PaginationPropsSizeOverrides>;
  /**
   * The system prop that allows defining system overrides as well as additional CSS styles.
   */
  sx?: SxProps;
}
