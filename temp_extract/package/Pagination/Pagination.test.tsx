/// <reference types="@testing-library/jest-dom" />
import React from 'react';
import { expect, test, describe, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { Pagination } from './Pagination';

describe('Pagination Component', () => {
  test('renders basic pagination correctly', () => {
    render(<Pagination count={10} />);
    const navigation = screen.getByRole('navigation');
    expect(navigation).toBeDefined();
  });

  test('can pass data-testid', () => {
    render(<Pagination data-testid="NovaPagination-root" count={10} />);
    expect(screen.getByTestId('NovaPagination-root')).toBeDefined();
  });

  test('handles custom className', () => {
    const customClass = 'custom-pagination';
    render(<Pagination data-testid="NovaPagination-root" className={customClass} count={10} />);
    const paginationRoot = screen.getByTestId('NovaPagination-root');
    expect(paginationRoot).toHaveClass(customClass);
  });

  test('renders correct number of pages', () => {
    render(<Pagination count={5} />);
    const pageButtons = screen
      .getAllByRole('button')
      .filter((button) => /page \d/.test(button.getAttribute('aria-label') || ''));
    expect(pageButtons).toHaveLength(5);
  });

  test('handles page changes', () => {
    const handleChange = vi.fn();
    render(<Pagination count={5} onChange={handleChange} />);

    fireEvent.click(screen.getByRole('button', { name: /page 3/i }));
    expect(handleChange).toHaveBeenCalledWith(expect.anything(), 3);
  });

  test('can be disabled', () => {
    render(<Pagination count={5} disabled />);
    const buttons = screen.getAllByRole('button');
    buttons.forEach((button) => {
      expect(button).to.have.property('disabled', true);
    });
  });

  test('shows navigation buttons correctly', () => {
    render(<Pagination count={10} showFirstButton showLastButton />);

    expect(screen.getByRole('button', { name: /first/i })).toBeDefined();
    expect(screen.getByRole('button', { name: /previous/i })).toBeDefined();
    expect(screen.getByRole('button', { name: /next/i })).toBeDefined();
    expect(screen.getByRole('button', { name: /last/i })).toBeDefined();
  });

  test('can hide previous and next buttons', () => {
    render(<Pagination count={10} hidePrevButton hideNextButton />);

    expect(screen.queryByRole('button', { name: /previous/i })).toBeNull();
    expect(screen.queryByRole('button', { name: /next/i })).toBeNull();
  });

  test('renders with different sizes', () => {
    const { rerender } = render(<Pagination count={5} size="small" />);
    expect(screen.getByRole('navigation')).toBeDefined();

    rerender(<Pagination count={5} size="large" />);
    expect(screen.getByRole('navigation')).toBeDefined();
  });

  test('custom renderItem function works', () => {
    const customText = 'Custom Page';
    render(
      <Pagination
        count={3}
        renderItem={(item) => (
          <button {...item} type="button" aria-label={`${customText} ${item.page}`}>
            {item.page}
          </button>
        )}
      />,
    );

    expect(screen.getByRole('button', { name: `${customText} 1` })).toBeDefined();
  });
});
