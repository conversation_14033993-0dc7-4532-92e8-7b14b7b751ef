'use client';
import * as React from 'react';
import clsx from 'clsx';
import composeClasses from '@mui/utils/composeClasses';
import { PaginationItem } from '../PaginationItem';
import { styled } from '@pigment-css/react';
import usePagination from '../internal/hooks/usePagination/usePagination';
import { getPaginationUtilityClass } from './Pagination.classes';
import { PaginationProps, PaginationRenderItemParams } from './Pagination.types';
import { UsePaginationItem } from '../internal/hooks/usePagination/usePagination.types';
import { useLocale } from '../Locale';
import enUS from '../Locale/en_US';

const useUtilityClasses = (props: PaginationProps) => {
  const { classes } = props;

  const slots = {
    root: ['root'],
    ul: ['ul'],
  };

  return composeClasses(slots, getPaginationUtilityClass, classes);
};

const PaginationRoot = styled('nav', {
  name: 'NovaPagination',
  slot: 'Root',
  overridesResolver: (props, styles) => [styles.root],
})({});

const PaginationUl = styled('ul', {
  name: 'NovaPagination',
  slot: 'Ul',
  overridesResolver: (props, styles) => styles.ul,
})({
  display: 'flex',
  flexWrap: 'wrap',
  alignItems: 'center',
  padding: 0,
  margin: 0,
  listStyle: 'none',
});

// eslint-disable-next-line react/display-name
export const Pagination = React.forwardRef(function Pagination(props: PaginationProps, ref: React.Ref<HTMLElement>) {
  const paginationLocale = useLocale('Pagination');
  const {
    boundaryCount = 1,
    className,
    count = 1,
    defaultPage = 1,
    disabled = false,
    getItemAriaLabel = enUS.Pagination.getItemAriaLabel,
    hideNextButton = false,
    hidePrevButton = false,
    onChange,
    page,
    renderItem = (item: PaginationRenderItemParams) => <PaginationItem {...item} />,
    showFirstButton = false,
    showLastButton = false,
    siblingCount = 1,
    size = 'medium',
    ...other
  } = { ...paginationLocale, ...props };

  const { items } = usePagination({ ...props, componentName: 'Pagination' });

  const ownerState = {
    ...props,
    boundaryCount,
    count,
    defaultPage,
    disabled,
    getItemAriaLabel,
    hideNextButton,
    hidePrevButton,
    renderItem,
    showFirstButton,
    showLastButton,
    siblingCount,
    size,
  };

  const classes = useUtilityClasses(ownerState);

  const renderPaginationItem = (item: UsePaginationItem, index: number) => {
    const itemProps = {
      ...item,
      'aria-label': getItemAriaLabel(item.type, item.page, item.selected),
      size,
    };

    return <li key={index}>{renderItem(itemProps)}</li>;
  };

  return (
    <PaginationRoot aria-label="pagination navigation" className={clsx(classes.root, className)} ref={ref} {...other}>
      <PaginationUl className={classes.ul}>{items.map(renderPaginationItem)}</PaginationUl>
    </PaginationRoot>
  );
});
