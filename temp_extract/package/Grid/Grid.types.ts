import * as React from 'react';
import { OverrideProps } from '@mui/types';
import { Breakpoint } from '@pigment-css/react';
import { SxProps } from '../types/theme';

type ResponsiveStyleValue<T> = T | Array<T | null> | { [key in Breakpoint]?: T | null };

export type GridDirection = 'row' | 'row-reverse' | 'column' | 'column-reverse';

export type GridSpacing = number | string;

export type GridWrap = 'nowrap' | 'wrap' | 'wrap-reverse';

export type GridSize = 'auto' | 'grow' | number;

export interface GridTypeMap<P = object, D extends React.ElementType = 'div'> {
  props: P & {
    /**
     * The number of columns.
     * @default 12
     */
    columns?: ResponsiveStyleValue<number>;
    /**
     * Defines the horizontal space between the type `item` components.
     * It overrides the value of the `spacing` prop.
     */
    columnSpacing?: ResponsiveStyleValue<GridSpacing>;
    /**
     * If `true`, the component will have the flex *container* behavior.
     * You should be wrapping *items* with a *container*.
     * @default false
     */
    container?: boolean;
    /**
     * Defines the `flex-direction` style property.
     * It is applied for all screen sizes.
     * @default 'row'
     */
    direction?: ResponsiveStyleValue<GridDirection>;
    /**
     * Defines the offset of the grid.
     */
    offset?: ResponsiveStyleValue<number> | undefined;
    /**
     * Defines the vertical space between the type `item` components.
     * It overrides the value of the `spacing` prop.
     */
    rowSpacing?: ResponsiveStyleValue<GridSpacing>;
    /**
     * Defines the space between the type `item` components.
     * It can only be used on a type `container` component.
     * @default 0
     */
    spacing?: ResponsiveStyleValue<GridSpacing> | undefined;
    /**
     * Defines the column size of the grid.
     */
    size?: ResponsiveStyleValue<GridSize> | undefined;
    /**
     * Defines the `flex-wrap` style property.
     * It's applied for all screen sizes.
     * @default 'wrap'
     */
    wrap?: GridWrap;
    /**
     * The system prop that allows defining system overrides as well as additional CSS styles.
     */
    sx?: SxProps;
  };
  defaultComponent: D;
}

export type GridProps<
  D extends React.ElementType = GridTypeMap['defaultComponent'],
  P = { component?: React.ElementType },
> = OverrideProps<GridTypeMap<P, D>, D>;

export interface GridOwnerState extends GridProps {}
