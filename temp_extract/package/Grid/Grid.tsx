import * as React from 'react';
import { OverridableComponent } from '@mui/types';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import clsx from 'clsx';
import GridRoot from '@pigment-css/react/Grid';
import { GridOwnerState, GridProps, GridTypeMap } from './Grid.types';
import { getGridUtilityClass } from './Grid.classes';

const useUtilityClasses = (ownerState: GridOwnerState) => {
  const { container } = ownerState;
  const slots = {
    root: ['root', container && 'container'],
  };
  return composeClasses(slots, getGridUtilityClass, {});
};

// eslint-disable-next-line react/display-name
export const Grid = React.forwardRef((props: GridProps, ref: React.ForwardedRef<Element>) => {
  const { className, ...rest } = props;

  const classes = useUtilityClasses(rest);

  return <GridRoot ref={ref} className={clsx(classes.root, className)} {...(rest as any)} />;
}) as OverridableComponent<GridTypeMap>;

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-expect-error
Grid.muiName = 'Grid';
