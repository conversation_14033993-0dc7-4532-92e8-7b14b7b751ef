'use client';
import * as React from 'react';
import { OverridableComponent } from '@mui/types';
import { unstable_composeClasses as composeClasses, unstable_useForkRef as useForkRef } from '@mui/utils';
import useSlotProps from '@mui/utils/useSlotProps';
import { styled } from '@pigment-css/react';
import { MonthView } from './components/MonthView';
import { MonthListView } from './components/MonthListView';
import { useUtils } from '../hooks/useUtils';
import { MonthCalendarOwnerState, MonthCalendarProps, MonthCalendarTypeMap } from './MonthCalendar.types';
import { getMonthCalendarUtilityClass } from './MonthCalendar.classes';

const useUtilityClasses = (ownerState: MonthCalendarOwnerState) => {
  const { disabled } = ownerState;

  const slots = {
    root: ['root'],
    disabled: disabled ? ['disabled'] : [],
  };

  return composeClasses(slots, getMonthCalendarUtilityClass, {});
};

const Root = styled('div')({
  display: 'flex',
  flexDirection: 'column',
  minWidth: '328px',
});

export const MonthCalendar = React.forwardRef(function MonthCalendar(
  props: MonthCalendarProps,
  ref: React.ForwardedRef<HTMLDivElement>,
) {
  const { viewStyle = 'grid', component, slots = {}, slotProps = {}, ...other } = props;

  const handleRef = useForkRef(ref, null);
  const ownerState = {
    ...props,
  };

  const classes = useUtilityClasses(ownerState);
  const SlotRoot = slots.root ?? Root;

  const rootProps = useSlotProps({
    elementType: Root,
    externalSlotProps: slotProps.root,
    externalForwardedProps: other,
    additionalProps: {
      ref: handleRef,
      as: component,
    },
    ownerState,
    className: classes.root,
  });

  return (
    <SlotRoot {...rootProps}>{viewStyle === 'list' ? <MonthListView {...other} /> : <MonthView {...other} />}</SlotRoot>
  );
}) as OverridableComponent<MonthCalendarTypeMap>;
