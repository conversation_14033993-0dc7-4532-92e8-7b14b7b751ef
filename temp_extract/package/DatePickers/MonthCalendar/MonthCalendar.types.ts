import * as React from 'react';
import { OverrideProps } from '@mui/types';
import { SlotComponentProps } from '@mui/utils';
import { ViewStyle } from '../types';
import { PickerDateType } from '../models/pickers';
import { BaseDateValidationProps, MonthValidationProps } from '../models/validation';
import { FormProps } from '../models/formProps';
import { SxProps } from '../../types/theme';

export interface MonthCalendarComponentProps extends MonthValidationProps, BaseDateValidationProps, FormProps {
  /**
   * The currently selected date.
   */
  date: PickerDateType;

  /**
   * The year to display in the calendar.
   */
  viewDate: PickerDateType;

  /**
   * Callback for when a month is selected.
   */
  onChange: (month: number) => void;

  /**
   * Optional callback for when the year changes.
   */
  onYearChange?: (year: number) => void;

  /**
   * The style of the month view.
   * @default 'grid'
   */
  viewStyle?: ViewStyle;
  /**
   * The system prop that allows defining system overrides as well as additional CSS styles.
   */
  sx?: SxProps;
}

export interface MonthCalendarSlots {
  /**
   * The component used for the root element.
   * @default 'div'
   */
  root?: React.ElementType;
}

export interface MonthCalendarSlotProps {
  root?: SlotComponentProps<'div', object, MonthCalendarOwnerState>;
}

export interface MonthCalendarTypeMap<P = object, D extends React.ElementType = 'div'> {
  props: P &
    MonthCalendarComponentProps & {
      /**
       * The slots for customizing the component appearance.
       */
      slots?: MonthCalendarSlots;

      /**
       * The props used for each slot.
       */
      slotProps?: MonthCalendarSlotProps;
    };
  defaultComponent: D;
}

export type MonthCalendarProps<D extends React.ElementType = MonthCalendarTypeMap['defaultComponent']> = OverrideProps<
  MonthCalendarTypeMap<object, D>,
  D
> & {
  component?: D;
};

export type MonthCalendarOwnerState = MonthCalendarProps;
