import * as React from 'react';
import { styled } from '@pigment-css/react';
import clsx from 'clsx';
import { modalDatePickerClasses } from '../../ModalDatePicker/ModalDatePicker.classes';
import { MonthCalendarComponentProps } from '../MonthCalendar.types';
import { useNow, useUtils } from '../../hooks/useUtils';
import { PickerDateType } from '../../models/pickers';
import { getMonthsInYear } from '../../utils/dateUtils';

const MonthGrid = styled('div')({
  display: 'grid',
  justifyItems: 'center',
  gridTemplateColumns: 'repeat(3, 1fr)',
  gap: '8px',
});

const MonthButton = styled('button')(({ theme }) => ({
  padding: '8px',
  border: 'none',
  width: '72px',
  borderRadius: '20px',
  cursor: 'pointer',
  backgroundColor: 'transparent',
  color: theme.vars.palette.onSurface,
  ...theme.typography.bodySmall,
  '&:hover': {
    color: theme.vars.palette.onSurface,
    backgroundColor: `color-mix(in srgb, ${theme.vars.palette.backgroundStates}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.hoverOnSurface})`,
  },
  '&.selected': {
    backgroundColor: theme.vars.palette.primary,
    color: theme.vars.palette.onPrimary,
  },
  '&:disabled': {
    color: theme.vars.palette.onBackgroundDisabled,
    cursor: 'default',
  },
  '&:focus-visible': {
    outline: `1px solid ${theme.vars.palette.primary}`,
    outlineOffset: '2px',
  },
}));

export type MonthViewProps = Omit<MonthCalendarComponentProps, 'viewStyle'>;
export const MonthView: React.FC<MonthViewProps> = ({
  date,
  viewDate,
  onChange,
  onYearChange,
  minDate,
  maxDate,
  disablePast,
  disableFuture,
  disabled = false,
  readOnly = false,
  shouldDisableMonth,
}) => {
  const now = useNow();
  const utils = useUtils();

  const isMonthDisabled = React.useCallback(
    (dateToValidate: PickerDateType) => {
      const firstEnabledMonth = utils.startOfMonth(disablePast && utils.isAfter(now, minDate) ? now : minDate);

      const lastEnabledMonth = utils.startOfMonth(disableFuture && utils.isBefore(now, maxDate) ? now : maxDate);

      const monthToValidate = utils.startOfMonth(dateToValidate);

      if (utils.isBefore(monthToValidate, firstEnabledMonth)) {
        return true;
      }

      if (utils.isAfter(monthToValidate, lastEnabledMonth)) {
        return true;
      }

      if (!shouldDisableMonth) {
        return false;
      }

      return shouldDisableMonth(monthToValidate);
    },
    [disableFuture, disablePast, maxDate, minDate, now, shouldDisableMonth, utils],
  );

  // Handle keyboard navigation
  const handleKeyDown = React.useCallback(
    (event: React.KeyboardEvent) => {
      const { key } = event;

      // Prevent default for arrow keys to avoid scrolling the page
      if (['ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown'].includes(key)) {
        event.preventDefault();
      }

      const currentMonth = date ? date.month() : now.month();
      let newMonth = currentMonth;
      let yearChanged = false;

      switch (key) {
        case 'ArrowLeft':
          // Move one month to the left
          newMonth = Math.max(0, currentMonth - 1);
          break;
        case 'ArrowRight':
          // Move one month to the right
          newMonth = Math.min(11, currentMonth + 1);
          break;
        case 'ArrowUp':
          // Move up one row (3 months)
          newMonth = Math.max(0, currentMonth - 3);
          break;
        case 'ArrowDown':
          // Move down one row (3 months)
          newMonth = Math.min(11, currentMonth + 3);
          break;
        case 'Home':
          // Move to the first month
          newMonth = 0;
          break;
        case 'End':
          // Move to the last month
          newMonth = 11;
          break;
        case 'PageUp': {
          // Move to the same month in the previous year
          const prevYear = viewDate.year() - 1;
          // Check if the year is disabled
          const prevYearDate = viewDate.year(prevYear);
          const isPrevYearDisabled = minDate && utils.isBeforeYear(prevYearDate, minDate);

          if (!isPrevYearDisabled && onYearChange) {
            yearChanged = true;
            onYearChange(prevYear);
          }
          break;
        }
        case 'PageDown': {
          // Move to the same month in the next year
          const nextYear = viewDate.year() + 1;
          // Check if the year is disabled
          const nextYearDate = viewDate.year(nextYear);
          const isNextYearDisabled = maxDate && utils.isBeforeYear(nextYearDate, maxDate);

          if (!isNextYearDisabled && onYearChange) {
            yearChanged = true;
            onYearChange(nextYear);
          }
          break;
        }
        default:
          return;
      }

      // Check if month is changing
      if (newMonth !== currentMonth && !yearChanged) {
        // Check if month is disabled
        const newMonthDate = viewDate.month(newMonth);
        const isDisabled = isMonthDisabled(newMonthDate);
        if (!isDisabled && !disabled && !readOnly) {
          onChange(newMonth);
        }
      }
    },
    [date, now, viewDate, minDate, utils, onYearChange, maxDate, isMonthDisabled, disabled, readOnly, onChange],
  );

  return (
    <MonthGrid className={modalDatePickerClasses.monthView}>
      {getMonthsInYear(utils, viewDate).map((month, index) => {
        const monthName = utils.format(month, 'monthShort');
        const monthLabel = utils.format(month, 'month');
        const monthNumber = utils.getMonth(month);
        const isSelected = date && utils.getMonth(date) === monthNumber;
        const isDisabled = isMonthDisabled(month);
        return (
          <MonthButton
            key={index}
            onClick={() => onChange(index)}
            className={clsx(
              modalDatePickerClasses.monthButton,
              isSelected && ['selected', modalDatePickerClasses.selected],
              isDisabled && modalDatePickerClasses.disabled,
            )}
            disabled={isDisabled || disabled || readOnly}
            type="button"
            aria-label={monthLabel}
            aria-selected={isSelected}
            onKeyDown={handleKeyDown}
            tabIndex={isSelected ? 0 : -1}
          >
            {monthName}
          </MonthButton>
        );
      })}
    </MonthGrid>
  );
};
