import {
  unstable_generateUtilityClasses as generateUtilityClasses,
  unstable_generateUtilityClass as generateUtilityClass,
} from '@mui/utils';

export interface CalendarHeaderClasses {
  /** Styles applied to the root element. */
  root: string;
  /** Styles applied to the container element if `variant="docked"`. */
  variantDocked: string;
  /** Styles applied to the container element if `variant="modal"`. */
  variantModal: string;
  /** Styles applied to the month year section element. */
  monthYearSection: string;
  /** Styles applied to the selector button element. */
  selectorButton: string;
  /** Styles applied to the selector button when expanded. */
  expanded: string;
  /** Styles applied to the selector button when disabled. */
  disabled: string;
}

export type CalendarHeaderClassKey = keyof CalendarHeaderClasses;

export function getCalendarHeaderUtilityClass(slot: string): string {
  return generateUtilityClass('NovaCalendarHeader', slot);
}

const calendarHeaderClasses: CalendarHeaderClasses = generateUtilityClasses('NovaCalendarHeader', [
  'root',
  'variantDocked',
  'variantModal',
  'monthYearSection',
  'selectorButton',
  'expanded',
  'disabled',
]);

export default calendarHeaderClasses;
