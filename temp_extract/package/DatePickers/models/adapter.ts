import { PickerDateType } from './pickers';

export interface AdapterFormats {
  // Token formats
  /**
   * The 4-digit year.
   * @example "2019"
   */
  year: string;
  /**
   * The full month name.
   * @example "January"
   */
  month: string;
  /**
   * The abbreviated month name.
   * @example "Jan"
   */
  monthShort: string;
  /**
   * The day of the month.
   * @example "1"
   */
  dayOfMonth: string;
  /**
   * The day of the month with letters.
   * @example "2nd"
   */
  dayOfMonthFull: string;
  /**
   * The name of the day of the week.
   * @example "Wednesday"
   */
  weekday: string;
  /**
   * The abbreviated name of the day of the week.
   * @example "Wed"
   * */
  weekdayShort: string;
  /**
   * The hours, 24-hour clock.
   * @example "23"
   */
  hours24h: string;
  /**
   * The hours, 12-hour clock.
   * @example "11"
   */
  hours12h: string;
  /**
   * The meridiem.
   * @example "AM"
   */
  meridiem: string;
  /**
   * The minutes.
   * @example "44"
   */
  minutes: string;
  /**
   * The seconds.
   * @example "00"
   */
  seconds: string;

  // Date formats
  /** The localized full date.
   * Used for the aria-label of the opening button of the `DatePicker`.
   * @example "Jan 1, 2019"
   */
  fullDate: string;
  /**
   * A keyboard input friendly date format.
   * Used in the date fields.
   * @example "02/13/2020"
   */
  keyboardDate: string;
  /**
   * The abbreviated month name and the day of the month.
   * Used in the `DateTimePicker` and `DateRangePicker` toolbars.
   * @example "Jan 1"
   */
  shortDate: string;
  /**
   * The month name and the day of the month.
   * Used in the `DatePicker` toolbar for non-english locales.
   * @example "1 January"
   */
  normalDate: string;
  /**
   * The month name, the day of the week and the day of the month.
   * Used in the `DatePicker` toolbar for english locales.
   * @example "Sun, Jan 1"
   */
  normalDateWithWeekday: string;

  // Time formats
  /**
   * The hours with the meridiem and minutes.
   * @example "11:44 PM"
   */
  fullTime12h: string;
  /**
   * The hours without the meridiem and minutes.
   * @example "23:44"
   */
  fullTime24h: string;

  // Date & Time formats
  /**
   * A keyboard input friendly time format for 12-hour clock.
   * Used in the date-time fields.
   * @example "02/13/2020 11:44 PM"
   */
  keyboardDateTime12h: string;
  /**
   * A keyboard input friendly time format for 24-hour clock.
   * Used in the date-time fields.
   * @example "02/13/2020 23:44"
   */
  keyboardDateTime24h: string;
}

export type DateBuilderReturnType<T extends string | null | undefined> = [T] extends [null] ? null : PickerDateType;

// Define the base adapter interface
export interface DateAdapter<TLocale = any> {
  locale?: string;
  formats: AdapterFormats;
  // Formatting
  format: (value: PickerDateType, formatKey: keyof AdapterFormats) => string;
  formatByString: (value: PickerDateType, formatString: string) => string;

  // Date manipulation
  /**
   * Create a date in the date library format.
   * If no `value` parameter is provided, creates a date with the current timestamp.
   * If a `value` parameter is provided, pass it to the date library to try to parse it.
   * @param {string | null | undefined} value The optional value to parse.
   * @returns {PickerValidDate | null} The parsed date.
   */
  date<T extends string | null | undefined>(value?: T): DateBuilderReturnType<T>;
  toJsDate: (value: PickerDateType) => Date;
  parse: (value: string, format: string) => PickerDateType | null;

  // Date info
  getYear: (value: PickerDateType) => number;
  getMonth: (value: PickerDateType) => number;

  getHours: (value: PickerDateType) => number;
  getMinutes: (value: PickerDateType) => number;
  getSeconds: (value: PickerDateType) => number;
  getMilliseconds: (value: PickerDateType) => number;
  getDate: (value: PickerDateType) => number;
  getDaysInMonth: (value: PickerDateType) => number;

  // Date checking
  isValid: (value: any) => boolean;
  isSameDay: (value: PickerDateType, comparing: PickerDateType) => boolean;
  isSameMonth: (value: PickerDateType, comparing: PickerDateType) => boolean;
  isSameYear: (value: PickerDateType, comparing: PickerDateType) => boolean;
  isBefore: (value: PickerDateType, comparing: PickerDateType) => boolean;
  isAfter: (value: PickerDateType, comparing: PickerDateType) => boolean;
  isBeforeDay: (value: PickerDateType, comparing: PickerDateType) => boolean;
  isAfterDay: (value: PickerDateType, comparing: PickerDateType) => boolean;
  isBeforeYear: (value: PickerDateType, comparing: PickerDateType) => boolean;
  isAfterYear: (value: PickerDateType, comparing: PickerDateType) => boolean;

  // Date math
  add: (value: PickerDateType, amount: number, unit: string) => PickerDateType;
  subtract: (value: PickerDateType, amount: number, unit: string) => PickerDateType;
  addDays: (value: PickerDateType, amount: number) => PickerDateType;
  addMonths: (value: PickerDateType, amount: number) => PickerDateType;
  addYears: (value: PickerDateType, amount: number) => PickerDateType;

  // Date setters
  setYear: (value: PickerDateType, year: number) => PickerDateType;
  setMonth: (value: PickerDateType, month: number) => PickerDateType;
  setDate: (value: PickerDateType, date: number) => PickerDateType;
  setHours(value: PickerDateType, hours: number): PickerDateType;
  setMinutes(value: PickerDateType, minutes: number): PickerDateType;
  setSeconds(value: PickerDateType, seconds: number): PickerDateType;
  setMilliseconds(value: PickerDateType, milliseconds: number): PickerDateType;
  // Defaults
  startOf: (value: PickerDateType, unit: string) => PickerDateType;
  endOf: (value: PickerDateType, unit: string) => PickerDateType;
  startOfDay: (value: PickerDateType) => PickerDateType;
  endOfDay: (value: PickerDateType) => PickerDateType;
  startOfMonth: (value: PickerDateType) => PickerDateType;
  endOfMonth: (value: PickerDateType) => PickerDateType;
  startOfWeek: (value: PickerDateType) => PickerDateType;
  endOfWeek: (value: PickerDateType) => PickerDateType;
  startOfYear: (value: PickerDateType) => PickerDateType;
  endOfYear: (value: PickerDateType) => PickerDateType;

  // Locale related
  getCurrentLocaleCode: () => string;
  is12HourCycleInCurrentLocale: () => boolean;

  // Calendar helpers
  getWeekArray: (value: PickerDateType) => PickerDateType[][];
  getWeekNumber: (value: PickerDateType) => number;
  getDayOfWeek: (date: PickerDateType) => number;
}
