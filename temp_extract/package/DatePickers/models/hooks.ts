import { FieldSectionData, FieldSectionType } from './dateSection';

/**
 * Possible operations that can be performed on date field sections
 */
export type FieldSectionOperation = 'input' | 'paste' | 'clear' | 'update';

/**
 * Callback for section value changes
 */
export type SectionChangeCallback = (sections: FieldSectionData[], sectionIndex: number) => void;

/**
 * Function to update a specific section's value
 */
export type UpdateSectionsCallback = (sectionIndex: number, newValue: string, moveToNextSection?: boolean) => void;

/**
 * Function to combine section values into a date string
 */
export type CombineDateSections = (
  sections: FieldSectionData[],
  operation: FieldSectionOperation,
  activeIndex: number | null,
) => {
  value: string;
  sections: FieldSectionData[];
};
