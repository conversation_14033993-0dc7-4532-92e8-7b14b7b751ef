'use client';
import React, { useCallback, useMemo } from 'react';
import { styled } from '@pigment-css/react';
import { Typography } from '../../Typography';
import { IconButton } from '../../IconButton';
import { ArrowLeftIcon, ArrowRightIcon } from '../icons';
import { useNow, useUtils } from '../hooks';
import { RangeCalendarHeaderProps } from './RangeCalendarHeader.types';

const RootContainer = styled('div')<{ variant: 'modal' | 'docked' }>({
  display: 'flex',
  paddingBlock: '8px',
  paddingInline: '16px',
  variants: [
    {
      props: { variant: 'modal' },
      style: {
        paddingInline: '32px',
        paddingBlock: '0px',
        paddingBlockStart: '8px',
      },
    },
  ],
});

const MonthYearSection = styled('div')<{ left: boolean }>(({ theme }) => ({
  display: 'flex',
  flex: 1,
  alignItems: 'center',
  justifyContent: 'flex-start',
  color: theme.vars.palette.onSurfaceVariant,
  variants: [
    {
      props: { left: true },
      style: {
        justifyContent: 'flex-end',
      },
    },
  ],
}));

export const RangeCalendarHeader = React.forwardRef(function CalendarHeader(
  props: RangeCalendarHeaderProps,
  ref: React.ForwardedRef<Element>,
) {
  const {
    calendars,
    month,
    monthIndex,

    onMonthChange,

    variant,
    disabled = false,
    readOnly = false,
    disablePast = false,
    disableFuture = false,
  } = props;

  const utils = useUtils();
  const today = useNow();

  const SlotRoot = RootContainer;
  const SlotMonthYearSection = MonthYearSection;

  // Create a helper function for determining disabled states
  const isNavigationDisabled = useCallback(
    (direction: 'previous' | 'next'): boolean => {
      if (disabled || readOnly) return true;

      const amount = direction === 'previous' ? -1 : 1;
      const newDate = utils.addMonths(month, amount);

      return direction === 'previous'
        ? disablePast && utils.isBefore(newDate, today)
        : disableFuture && utils.isAfter(newDate, today);
    },
    [month, disableFuture, disablePast, disabled, readOnly, today, utils],
  );

  // Create navigation handlers with integrated disable state
  const createNavHandler = useCallback(
    (direction: 'previous' | 'next') => {
      const isDisabled = isNavigationDisabled(direction);

      const handler = () => {
        const amount = direction === 'previous' ? -1 : 1;

        const newDate = utils.addMonths(month, amount);

        onMonthChange(newDate);
      };

      return { handler, isDisabled };
    },
    [month, isNavigationDisabled, onMonthChange, utils],
  );

  const { handler: handlePreviousMonth, isDisabled: disablePreviousMonth } = useMemo(
    () => createNavHandler('previous'),
    [createNavHandler],
  );

  const { handler: handleNextMonth, isDisabled: disableNextMonth } = useMemo(
    () => createNavHandler('next'),
    [createNavHandler],
  );

  return (
    <SlotRoot ref={ref} variant={variant}>
      <SlotMonthYearSection left={monthIndex !== 0 || variant === 'modal'}>
        <IconButton
          onClick={handlePreviousMonth}
          disabled={disablePreviousMonth}
          style={{
            visibility: monthIndex !== 0 || variant === 'modal' ? 'hidden' : 'visible',
            display: variant === 'modal' ? 'none' : 'flex',
          }}
          variant="standard"
          size="medium"
          aria-label="Previous month"
        >
          <ArrowLeftIcon />
        </IconButton>
        <Typography>{month.format('MMMM') + ' ' + month.year()}</Typography>
        <IconButton
          onClick={handleNextMonth}
          disabled={disableNextMonth}
          style={{ visibility: monthIndex !== calendars - 1 || variant === 'modal' ? 'hidden' : 'visible' }}
          variant="standard"
          size="medium"
          aria-label="Next month"
        >
          <ArrowRightIcon />
        </IconButton>
      </SlotMonthYearSection>
    </SlotRoot>
  );
});
