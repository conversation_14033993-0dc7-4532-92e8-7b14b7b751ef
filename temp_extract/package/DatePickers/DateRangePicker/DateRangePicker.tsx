'use client';
import * as React from 'react';
import { DateRangePickerProps } from './DateRangePicker.types';
import { DockedDateRangePicker } from '../DockedDateRangePicker';
import { ModalDateRangePicker } from '../ModalDateRangePicker';
import { unstable_createUseMediaQuery as createUseMediaQuery } from '@mui/system/useMediaQuery';

const useMediaQuery = createUseMediaQuery();

/**
 * The DateRangePicker component allows selecting a range of dates.
 * It automatically chooses between the docked (desktop) and modal (mobile) variants based on screen size.
 */
export const DateRangePicker = React.forwardRef<HTMLDivElement, DateRangePickerProps>((props, ref) => {
  const {
    desktopModeMediaQuery = '(pointer: fine)',
    // Common props
    slots,
    slotProps,
    ...others
  } = props;

  // Detect if we're on mobile based on media query or breakpoint
  const prefersDocked = useMediaQuery(desktopModeMediaQuery, { noSsr: true, defaultMatches: true });

  // Render the appropriate variant
  if (prefersDocked) {
    return <DockedDateRangePicker ref={ref} slots={slots} slotProps={slotProps} {...others} />;
  }

  return <ModalDateRangePicker ref={ref} slots={slots} slotProps={slotProps} {...others} />;
});
