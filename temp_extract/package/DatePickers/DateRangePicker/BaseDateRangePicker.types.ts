import * as React from 'react';
import { SlotComponentProps } from '@mui/utils';
import { SxProps } from '../../types/theme';
import { DateRangeCalendarProps } from '../DateRangeCalendar/DateRangeCalendar.types';
import { DateRangeFieldProps } from '../DateRangeField/DateRangeField.types';
import { PickerDateType } from '../models/pickers';
import { PickerRangeValue } from '../utils/dateRangeUtils';
import { DatePickerView } from '../types';

/**
 * Base DateRangePicker component props interface that all variants extend
 */
export interface BaseDateRangePickerProps
  extends Omit<DateRangeCalendarProps, 'component' | 'slots' | 'slotProps' | 'value' | 'onChange'>,
    Pick<DateRangeFieldProps, 'startLabel' | 'endLabel' | 'separator'> {
  /**
   * The selected range.
   */
  value?: PickerRangeValue;

  /**
   * The default selected range.
   */
  defaultValue?: PickerRangeValue;

  /**
   * <PERSON><PERSON> fired when the range changes.
   * @param {PickerRangeValue} range The new range value.
   */
  onChange?: (range: PickerRangeValue) => void;

  /**
   * If `true`, the component is disabled.
   * @default false
   */
  disabled?: boolean;

  /**
   * If `true`, the component is read-only.
   * @default false
   */
  readOnly?: boolean;

  /**
   * The label text displayed in the picker dialog.
   * @default 'Depart - return dates'
   */
  label?: React.ReactNode;

  /**
   * If `true`, the picker closes after a date is selected.
   * @default false
   */
  closeOnSelect?: boolean;

  /**
   * Callback fired when the popup is opened.
   */
  onOpen?: () => void;

  /**
   * Callback fired when the popup is closed.
   */
  onClose?: () => void;

  /**
   * Controls whether the popup is open.
   * @default false
   */
  open?: boolean;

  /**
   * Format of the date when rendered in the input(s).
   * @default 'MM/DD/YYYY'
   */
  format?: string;

  /**
   * If `true`, the text field indicates an error.
   * @default false
   */
  error?: boolean;

  /**
   * Text shown below the input field for additional information.
   */
  helperText?: React.ReactNode;

  /**
   * If `true`, the input will take up the full width of its container.
   * @default false
   */
  fullWidth?: boolean;

  /**
   * If `true`, the label is displayed as required and the input will be required.
   * @default false
   */
  required?: boolean;

  /**
   * The placeholder text for the inputs.
   */
  placeholder?: string;

  /**
   * Function that returns a boolean if the given date should be disabled.
   */
  shouldDisableDate?: (date: PickerDateType, position?: 'start' | 'end') => boolean;

  /**
   * Text used for the clear button.
   * @default 'Clear'
   */
  clearText?: string;

  /**
   * Text used for the today button.
   * @default 'Today'
   */
  todayText?: string;

  /**
   * Text used for the apply/ok button.
   * @default 'OK'
   */
  okText?: string;

  /**
   * Text used for the cancel button.
   * @default 'Cancel'
   */
  cancelText?: string;

  /**
   * If `true`, focuses the calendar on open.
   * @default false
   */
  autoFocus?: boolean;

  /**
   * Available views for the date picker.
   * @default ['day', 'month', 'year']
   */
  views?: DatePickerView[];

  /**
   * Callback when view changes.
   * @param {DatePickerView} view - The new active view
   */
  onViewChange?: (view: DatePickerView) => void;

  /**
   * The system prop that allows defining system overrides as well as additional CSS styles.
   */
  sx?: SxProps;
}

/**
 * Base slots available in all DateRangePicker variants
 */
export interface BaseDateRangePickerSlots {
  /**
   * Custom component for the field input.
   * @default DateRangeField
   */
  field?: React.ElementType;

  /**
   * Custom component for the range calendar.
   * @default DateRangeCalendar
   */
  calendar?: React.ElementType;
}

/**
 * Base slot props for all DateRangePicker variants
 */
export interface BaseDateRangePickerSlotProps {
  field?: SlotComponentProps<React.ElementType, Record<string, unknown>, BaseDateRangePickerProps>;
  calendar?: SlotComponentProps<React.ElementType, Record<string, unknown>, BaseDateRangePickerProps>;
}
