import * as React from 'react';
import { OverrideProps } from '@mui/types';
import { SlotComponentProps } from '@mui/utils';
import { DockedDateRangePickerProps } from '../DockedDateRangePicker/DockedDateRangePicker.types';
import { ModalDateRangePickerProps } from '../ModalDateRangePicker/ModalDateRangePicker.types';

/**
 * Component slots for the DateRangePicker
 */
export interface DateRangePickerSlots {
  /**
   * Custom component for the field input.
   * @default DateRangeField
   */
  field?: React.ElementType;

  /**
   * Custom component for the calendar.
   * @default DateRangeCalendar
   */
  calendar?: React.ElementType;
}

/**
 * Props for the DateRangePicker slots
 */
export interface DateRangePickerSlotProps {
  /**
   * Props for the root element.
   */
  root?: SlotComponentProps<'div', Record<string, unknown>, DateRangePickerComponentProps>;

  /**
   * Props for the field component.
   */
  field?: SlotComponentProps<React.ElementType, Record<string, unknown>, any>;

  /**
   * Props for the calendar component.
   */
  calendar?: SlotComponentProps<React.ElementType, Record<string, unknown>, any>;
}

/**
 * Component props for the DateRangePicker
 */
export interface DateRangePickerComponentProps
  extends Omit<DockedDateRangePickerProps, 'slots' | 'slotProps'>,
    Omit<ModalDateRangePickerProps, 'slots' | 'slotProps'> {
  /**
   * CSS media query when `Mobile` mode will be changed to `Desktop`.
   * @default '@media (pointer: fine)'
   * @example '@media (min-width: 720px)' or theme.breakpoints.up("sm")
   */
  desktopModeMediaQuery?: string;

  /**
   * Overridable component slots.
   * @default {}
   */
  slots?: DateRangePickerSlots;

  /**
   * The props used for each component slot.
   * @default {}
   */
  slotProps?: DateRangePickerSlotProps;
}

export interface DateRangePickerTypeMap {
  props: DateRangePickerComponentProps;
  defaultComponent: React.ElementType;
}

/**
 * DateRangePicker component props
 */
export type DateRangePickerProps<D extends React.ElementType = DateRangePickerTypeMap['defaultComponent']> =
  OverrideProps<DateRangePickerTypeMap, D>;

/**
 * Component prop types for DateRangePicker root
 */
export type DateRangePickerRootSlotProps = {
  root?: SlotComponentProps<'div', Record<string, unknown>, DateRangePickerComponentProps>;
};
