import {
  unstable_generateUtilityClass as generateUtilityClass,
  unstable_generateUtilityClasses as generateUtilityClasses,
} from '@mui/utils';

export interface DateRangePickerClasses {
  /** Styles applied to the root element. */
  root: string;
  /** Styles applied to the popup element. */
  popup: string;
  /** Styles applied to the field element. */
  field: string;
  /** Styles applied to the calendar element. */
  calendar: string;
}

export type DateRangePickerClassKey = keyof DateRangePickerClasses;

export function getDateRangePickerUtilityClass(slot: string): string {
  return generateUtilityClass('NovaDateRangePicker', slot);
}

export const dateRangePickerClasses: DateRangePickerClasses = generateUtilityClasses('NovaDateRangePicker', [
  'root',
  'popup',
  'field',
  'calendar',
]);
