'use client';
import * as React from 'react';
import { styled } from '@pigment-css/react';
import clsx from 'clsx';
import { unstable_composeClasses as composeClasses, unstable_useForkRef as useForkRef } from '@mui/utils';
import { DateRangeField } from '../DateRangeField/DateRangeField';
import { DateRangeCalendar } from '../DateRangeCalendar/DateRangeCalendar';
import { DockedDateRangePickerProps } from './DockedDateRangePicker.types';
import { ClickAwayListener } from '../../ClickAwayListener';
import { Popper } from '../../Popper';
import { useDateRangePicker } from '../hooks/useDateRangePicker';
import { PickerViewFooter } from '../PickerViewFooter';
import { getDockedDateRangePickerUtilityClass } from './DockedDateRangePicker.classes';
import { useDefaultDates } from '../hooks';
import { IconButton } from '../../IconButton';
import { CalendarIcon } from '../icons';

const useUtilityClasses = (ownerState: { classes?: Record<string, string> }) => {
  const { classes } = ownerState;

  const slots = {
    root: ['root'],
    popper: ['popper'],
  };

  return composeClasses(slots, getDockedDateRangePickerUtilityClass, classes);
};

const DockedDateRangePickerRoot = styled('div', {
  name: 'NovaDockedDateRangePicker',
  slot: 'Root',
})({
  display: 'inline-flex',
  flexDirection: 'column',
  position: 'relative',
});

const DockedDateRangePickerPopper = styled(Popper, {
  name: 'NovaDockedDateRangePicker',
  slot: 'Popper',
})({
  zIndex: 1400,
});

const DockedDateRangePickerPaper = styled('div', {
  name: 'NovaDockedDateRangePicker',
  slot: 'Paper',
})(({ theme }) => ({
  backgroundColor: theme.vars.palette.surfaceContainerHigh,
  boxShadow: theme.vars.shadows[4],
  borderRadius: '12px',
  overflow: 'hidden',
}));

/**
 * Docked version of DateRangePicker component
 */
export const DockedDateRangePicker = React.forwardRef<HTMLDivElement, DockedDateRangePickerProps>((props, ref) => {
  const defaultDate = useDefaultDates();
  const {
    className,
    component = 'div',
    value,
    defaultValue,
    onChange,
    disabled = false,
    readOnly = false,
    calendars,
    endDecorator,
    onClose,
    open: openProp,
    slotProps,
    slots,
    format = 'MM/DD/YYYY',
    label,
    startLabel,
    endLabel,
    separator,
    minDate = defaultDate.minDate,
    maxDate = defaultDate.maxDate,
    disableFuture,
    disablePast,
    shouldDisableDate,
    closeOnSelect = false,
    clearText = 'Clear',
    todayText = 'Today',
    okText = 'OK',
    cancelText = 'Cancel',
    placement = 'bottom-start',
    disablePortal = false,
    container,
    views = ['day', 'month', 'year'],
    autoFocus = false,
    ...other
  } = props;

  // Use the date range picker hook
  const {
    selectedRange,
    updateRange,
    rangePosition,
    setRangePosition,
    open,
    handleOpen,
    handleClose,
    anchorEl,
    currentView,
    setCurrentView,
    availableViews,
    handleAccept,
    handleCancel,
    handleClear,
    validateDate,
  } = useDateRangePicker({
    value,
    defaultValue,
    onChange,
    open: openProp,
    onClose,
    disabled,
    readOnly,
    minDate,
    maxDate,
    disableFuture,
    disablePast,
    shouldDisableDate,
    format,
    closeOnSelect,
    autoFocus,
    views,
  });

  // Get utility classes
  const ownerState = { ...props };
  const classes = useUtilityClasses(ownerState);

  // Ref for click away listener
  const rootRef = React.useRef<HTMLDivElement>(null);
  const handleRef = useForkRef(rootRef, ref);

  const openPickerButton = (
    <IconButton
      className="DateField-calendarIcon"
      variant="standard"
      onClick={handleOpen}
      disabled={disabled || readOnly}
    >
      {endDecorator || <CalendarIcon />}
    </IconButton>
  );

  // Memoize the field component
  const Field = slots?.field || DateRangeField;
  const fieldProps = {
    ...slotProps?.field,
    value: selectedRange,
    onChange: updateRange,
    format,
    disabled,
    readOnly,
    label,
    startLabel,
    endLabel,
    separator,
    error: false,
    onClick: handleOpen,
    endDecorator: openPickerButton,
  };

  // Memoize the calendar component
  const Calendar = slots?.calendar || DateRangeCalendar;
  const calendarProps = {
    ...slotProps?.calendar,
    value: selectedRange,
    onChange: updateRange,
    rangePosition,
    onRangePositionChange: setRangePosition,
    view: currentView,
    calendars,
    availableViews,
    disabled,
    readOnly,
    disableFuture,
    disablePast,
    minDate,
    maxDate,
  };

  return (
    <DockedDateRangePickerRoot ref={handleRef} className={clsx(classes.root, className)} as={component} {...other}>
      <Field style={{ minWidth: '260px' }} {...fieldProps} />
      {open && (
        <ClickAwayListener onClickAway={handleClose}>
          <DockedDateRangePickerPopper
            role="dialog"
            open={open}
            anchorEl={anchorEl}
            placement={placement}
            disablePortal={disablePortal}
            container={container}
            className={classes.popper}
          >
            <DockedDateRangePickerPaper>
              <Calendar {...calendarProps} variant={'docked'} viewStyle={'list'} />
              {currentView === 'day' && (
                <PickerViewFooter
                  component="div"
                  onCancel={handleCancel}
                  onClear={handleClear}
                  onAccept={handleAccept}
                  clearText={clearText}
                  cancelText={cancelText}
                  okText={okText}
                />
              )}
            </DockedDateRangePickerPaper>
          </DockedDateRangePickerPopper>
        </ClickAwayListener>
      )}
    </DockedDateRangePickerRoot>
  );
});
