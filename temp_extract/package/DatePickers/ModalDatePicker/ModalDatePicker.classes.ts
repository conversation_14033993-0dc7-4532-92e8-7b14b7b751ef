import {
  unstable_generateUtilityClasses as generateUtilityClasses,
  unstable_generateUtilityClass as generateUtilityClass,
} from '@mui/utils';

/**
 * Interface for DatePicker CSS classes
 */
export interface ModalDatePickerClasses {
  /** Styles applied to the root element. */
  root: string;

  /** Styles applied to the root element when disabled. */
  disabled: string;

  /** Styles applied to the root element when error. */
  error: string;

  /** Styles applied to the calendar container element. */
  calendarContainer: string;

  /** Styles applied to the calendar header element. */
  calendarHeader: string;

  /** Styles applied to the text input field. */
  inputField: string;

  /** Styles applied to the weekday header element. */
  weekDayHeader: string;

  /** Styles applied to the calendar grid element. */
  calendarGrid: string;

  /** Styles applied to the day button elements. */
  dayButton: string;

  /** Styles applied to the selected day/month/year button. */
  selected: string;

  /** Styles applied to the today's day button. */
  today: string;

  /** Styles applied to the month view container. */
  monthView: string;

  /** Styles applied to the month button elements. */
  monthButton: string;

  /** Styles applied to the year view container. */
  yearView: string;

  /** Styles applied to the year button elements. */
  yearButton: string;

  /** Styles applied to the view switcher button. */
  viewButton: string;

  /** Styles applied to the active view button. */
  activeViewButton: string;

  /** Styles applied to the footer. */
  footer: string;

  /** Styles applied to the footer actions container. */
  actions: string;

  /** Styles applied to action buttons (Clear, Today, OK, Cancel). */
  actionButton: string;

  /** Styles applied to the month selector. */
  monthSelector: string;

  /** Styles applied to the year selector. */
  yearSelector: string;
}

/**
 * Type for DatePicker class keys
 */
export type ModalDatePickerClassKey = keyof ModalDatePickerClasses;

/**
 * Generates a utility class for the DatePicker component
 * @param {string} slot - The class slot name
 * @returns {string} The generated class name
 */
export function getModalDatePickerUtilityClass(slot: string): string {
  return generateUtilityClass('NovaDatePicker', slot);
}

/**
 * Generated utility classes for the DatePicker component
 */
export const modalDatePickerClasses: ModalDatePickerClasses = generateUtilityClasses('NovaModalDatePicker', [
  // Layout classes
  'root',
  'disabled',
  'error',
  'calendarContainer',
  'calendarHeader',
  'weekDayHeader',
  'calendarGrid',
  'inputField',

  // Button element classes
  'dayButton',
  'selected',
  'today',
  'monthView',
  'monthButton',
  'yearView',
  'yearButton',

  // Component part classes
  'monthSelector',
  'yearSelector',
  'viewButton',
  'activeViewButton',
  'footer',
  'actions',
  'actionButton',
]);
