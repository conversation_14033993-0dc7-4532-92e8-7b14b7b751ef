import * as React from 'react';
import { OverrideProps } from '@mui/types';
import { SlotComponentProps } from '@mui/utils';
import { ViewStyle } from '../types';
import { PickerDateType } from '../models/pickers';
import { BaseDateValidationProps, YearValidationProps } from '../models/validation';
import { FormProps } from '../models/formProps';
import { SxProps } from '../../types/theme';

export interface YearCalendarComponentProps extends YearValidationProps, BaseDateValidationProps, FormProps {
  /**
   * The currently selected date.
   */
  date: PickerDateType;

  /**
   * The date to display in the calendar.
   */
  viewDate: PickerDateType;

  /**
   * Callback for when a year is selected.
   */
  onChange: (year: number) => void;

  /**
   * Callback for when the year range changes (typically for pagination).
   */
  onYearRangeChange: (date: PickerDateType) => void;

  /**
   * The style of the year view.
   * @default 'grid'
   */
  viewStyle?: ViewStyle;
  /**
   * The system prop that allows defining system overrides as well as additional CSS styles.
   */
  sx?: SxProps;
}

export interface YearCalendarSlots {
  /**
   * The component used for the root element.
   * @default 'div'
   */
  root?: React.ElementType;
}

export interface YearCalendarSlotProps {
  root?: SlotComponentProps<'div', object, YearCalendarOwnerState>;
}

export interface YearCalendarTypeMap<P = object, D extends React.ElementType = 'div'> {
  props: P &
    YearCalendarComponentProps & {
      /**
       * The slots for customizing the component appearance.
       */
      slots?: YearCalendarSlots;

      /**
       * The props used for each slot.
       */
      slotProps?: YearCalendarSlotProps;
    };
  defaultComponent: D;
}

export type YearCalendarProps<D extends React.ElementType = YearCalendarTypeMap['defaultComponent']> = OverrideProps<
  YearCalendarTypeMap<object, D>,
  D
> & {
  component?: D;
};

export type YearCalendarOwnerState = YearCalendarProps;
