'use client';
import * as React from 'react';
import { OverridableComponent } from '@mui/types';
import { unstable_composeClasses as composeClasses, unstable_useForkRef as useForkRef } from '@mui/utils';
import useSlotProps from '@mui/utils/useSlotProps';
import { styled } from '@pigment-css/react';
import { YearView } from './components/YearView';
import { YearListView } from './components/YearListView';
import { YearCalendarOwnerState, YearCalendarProps, YearCalendarTypeMap } from './YearCalendar.types';
import { getYearCalendarUtilityClass } from './YearCalendar.classes';

const useUtilityClasses = (ownerState: YearCalendarOwnerState) => {
  const { disabled } = ownerState;

  const slots = {
    root: ['root'],
    disabled: disabled ? ['disabled'] : [],
  };

  return composeClasses(slots, getYearCalendarUtilityClass, {});
};

const Root = styled('div')({
  display: 'flex',
  flexDirection: 'column',
  minWidth: '328px',
});

export const YearCalendar = React.forwardRef(function YearCalendar(
  props: YearCalendarProps,
  ref: React.ForwardedRef<HTMLDivElement>,
) {
  const { viewStyle = 'grid', component, slots = {}, slotProps = {}, ...other } = props;

  const handleRef = useForkRef(ref, null);
  const ownerState = {
    ...props,
  };

  const classes = useUtilityClasses(ownerState);
  const SlotRoot = slots.root ?? Root;

  const rootProps = useSlotProps({
    elementType: Root,
    externalSlotProps: slotProps.root,
    externalForwardedProps: other,
    additionalProps: {
      ref: handleRef,
      as: component,
    },
    ownerState,
    className: classes.root,
  });

  return (
    <SlotRoot {...rootProps}>{viewStyle === 'grid' ? <YearView {...other} /> : <YearListView {...other} />}</SlotRoot>
  );
}) as OverridableComponent<YearCalendarTypeMap>;
