import {
  unstable_generateUtilityClass as generateUtilityClass,
  unstable_generateUtilityClasses as generateUtilityClasses,
} from '@mui/utils';

export interface DateFieldClasses {
  /** Styles applied to the root element */
  root: string;
  /** Styles applied to the input element */
  input: string;
  /** Styles applied to the error state */
  error: string;
  /** Styles applied to the disabled state */
  disabled: string;
}

export type DateFieldClassKey = keyof DateFieldClasses;

export function getDateFieldUtilityClass(slot: string): string {
  return generateUtilityClass('NovaDateField', slot);
}

const dateFieldClasses: DateFieldClasses = generateUtilityClasses('NovaDateField', [
  'root',
  'input',
  'error',
  'disabled',
]);
export default dateFieldClasses;
