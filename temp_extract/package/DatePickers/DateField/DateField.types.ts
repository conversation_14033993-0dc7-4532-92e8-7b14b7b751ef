import React, { ElementType } from 'react';
import { OverrideProps } from '@mui/types';
import { TextFieldProps } from '../../TextField';
import { PickerDateType } from '../models/pickers';
import { CreateSlotsAndSlotProps } from '../../types/slot';

import { BaseDateValidationProps, DayValidationProps } from '../models/validation';

export interface DateFieldPropsColorOverrides {}

export interface DateFieldSlots {
  /**
   * The component that renders the root.
   * @default TextField
   */
  root?: React.ElementType;
  /**
   * The component that renders the input.
   */
  input?: React.ElementType;
}

export type DateFieldSlotsAndSlotProps = CreateSlotsAndSlotProps<
  DateFieldSlots,
  {
    root: Omit<TextFieldProps, 'onChange' | 'value'>;
    input: React.InputHTMLAttributes<HTMLInputElement>;
  }
>;

export interface DateFieldOwnerState {
  disabled?: boolean;
  error?: boolean;
}

export interface DateFieldOwnProps extends BaseDateValidationProps, DayValidationProps {
  /**
   * The date value of the field.
   */
  value?: PickerDateType | null;

  /**
   * The default value. Use when the component is not controlled.
   */
  defaultValue?: PickerDateType | null;

  /**
   * Callback when date changes.
   */
  onChange?: (value: PickerDateType | null) => void;

  /**
   * The format the date is displayed in.
   * @default 'MM/DD/YYYY'
   */
  format?: string;

  /**
   * If true, a clear button will be displayed when a date is selected.
   * @default true
   */
  clearable?: boolean;
}

export interface DateFieldTypeMap<P = Record<string, never>, D extends ElementType = 'div'> {
  props: P & DateFieldOwnProps & Omit<TextFieldProps, 'value' | 'defaultValue' | 'onChange' | 'autoComplete'>;
  defaultComponent: D;
}

export type DateFieldProps<D extends ElementType = DateFieldTypeMap['defaultComponent']> = OverrideProps<
  DateFieldTypeMap<object, D>,
  D
>;
