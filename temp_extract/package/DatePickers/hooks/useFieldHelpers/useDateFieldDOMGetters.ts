import React, { useCallback, useMemo } from 'react';
import { FieldSectionData } from '../../models/dateSection';
import { useFieldSelection } from './useFieldSelection';

/**
 * Interface defining the DOM getters for DateField components
 * Enhanced to match MUI's UseFieldDOMGetters pattern more closely
 */
export interface DateFieldDOMGetters {
  isReady: () => boolean;
  getRoot: () => HTMLElement;
  getInput: () => HTMLInputElement | null;
  getSectionContent: (sectionIndex: number) => HTMLElement;
  getSectionContainer: (sectionIndex: number) => HTMLElement;
  getSectionFromPosition: (position: number) => number | null;
  getSectionIndexFromDOMElement: (element: Element | null | undefined) => number | null;
  getSectionDOMInfo: (sectionIndex: number) => {
    startInInput: number;
    endInInput: number;
  } | null;
}

/**
 * Hook to create DOM getters for the DateField component
 * Enhanced to more closely follow MUI's pattern of abstracting DOM interactions
 */
export function useDateFieldDOMGetters(
  rootRef: React.RefObject<HTMLDivElement>,
  inputRef: React.RefObject<HTMLInputElement>,
  actualInputRef: React.MutableRefObject<HTMLInputElement | null>,
  sections: FieldSectionData[],
): DateFieldDOMGetters {
  const { findActiveSectionByPosition } = useFieldSelection();

  // Get the input element
  const getInput = useCallback((): HTMLInputElement | null => {
    return actualInputRef.current || inputRef.current;
  }, [actualInputRef, inputRef]);

  // Check if the DOM references are ready for use
  const isReady = useCallback((): boolean => {
    return rootRef.current !== null && getInput() !== null && sections.length > 0;
  }, [rootRef, getInput, sections]);

  // Get root element
  const getRoot = useCallback(() => {
    if (!rootRef.current) {
      throw new Error('Root element is not available');
    }
    return rootRef.current;
  }, [rootRef]);

  // Get section content element - for now, returns the input since we don't have separate section DOM elements
  // This could be enhanced when implementing a fully accessible DOM structure like MUI's V7 approach
  const getSectionContent = useCallback(
    (sectionIndex: number): HTMLElement => {
      // In current implementation, we don't have separate DOM elements
      // This is a placeholder for future improvements
      return getInput() as HTMLElement;
    },
    [getInput],
  );

  // Get section container - for now, returns the input since we don't have separate section containers
  const getSectionContainer = useCallback(
    (sectionIndex: number): HTMLElement => {
      // In current implementation, we don't have separate DOM elements
      // This is a placeholder for future improvements
      return getInput() as HTMLElement;
    },
    [getInput],
  );

  // Get section DOM information
  const getSectionDOMInfo = useCallback(
    (sectionIndex: number) => {
      if (!isReady() || sectionIndex < 0 || sectionIndex >= sections.length) {
        return null;
      }

      const section = sections[sectionIndex];
      if ('startInInput' in section && 'endInInput' in section) {
        return {
          startInInput: (section as any).startInInput,
          endInInput: (section as any).endInInput,
        };
      }

      return null;
    },
    [isReady, sections],
  );

  // Get section from cursor position using the consistent field selection hook
  const getSectionFromPosition = useCallback(
    (position: number): number | null => {
      if (!isReady()) {
        return null;
      }

      return findActiveSectionByPosition(sections, position);
    },
    [isReady, sections, findActiveSectionByPosition],
  );

  // Get section index from a DOM element (MUI style)
  const getSectionIndexFromDOMElement = useCallback(
    (element: Element | null | undefined): number | null => {
      if (!element || !sections?.length) {
        return null;
      }

      // In current implementation, we don't have separate section elements
      // This is a placeholder for future improvements
      // When real section elements are implemented, we would check data attributes

      // For now, if the element is an input or contains the input, process selection
      const input = actualInputRef.current || inputRef.current;
      if (input && (element === input || element.contains(input) || input.contains(element))) {
        const selectionStart = input.selectionStart ?? 0;
        return getSectionFromPosition(selectionStart);
      }

      return null;
    },
    [sections, actualInputRef, inputRef, getSectionFromPosition],
  );

  // Return the getters as a stable object
  return useMemo(
    () => ({
      isReady,
      getRoot,
      getInput,
      getSectionContent,
      getSectionContainer,
      getSectionFromPosition,
      getSectionIndexFromDOMElement,
      getSectionDOMInfo,
    }),
    [
      isReady,
      getRoot,
      getInput,
      getSectionContent,
      getSectionContainer,
      getSectionFromPosition,
      getSectionIndexFromDOMElement,
      getSectionDOMInfo,
    ],
  );
}

export default useDateFieldDOMGetters;
