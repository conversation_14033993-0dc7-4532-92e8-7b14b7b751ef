import { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { parseDateSections } from '../../utils/sectionUtils';
import { FieldSectionData, FieldSectionType, FieldValueType, DisplayedSection } from '../../models/dateSection';
import { useCharacterEditing } from './useCharacterEditing';
import { useClipboardPaste } from './useClipboardPaste';
import { useUtils } from '../useUtils';
import {
  CombineDateSections,
  FieldSectionOperation,
  SectionChangeCallback,
  UpdateSectionsCallback,
} from '../../models/hooks';

// Character query type (similar to MUI's CharacterEditingQuery)
export interface CharacterQuery {
  value: string;
  sectionIndex: number;
  sectionType: FieldSectionType;
  lastUpdate: number;
}

// Extended version of FieldSectionData that includes startSeparator property
interface ExtendedFieldSectionData extends FieldSectionData {
  startSeparator?: string;
}

export interface UseDateSectionsProps {
  value: string;
  onChange?: (value: string, reason?: FieldSectionOperation, activeSection?: number) => void;
  valueFormat?: string;
  format?: string; // Support both valueFormat and format (for compatibility with useFieldIntegration)
  valueType?: FieldValueType;
  sections?: DisplayedSection[];
  onSectionChange?: SectionChangeCallback;
  shouldDisableDate?: (date: Date) => boolean;
  isRtl?: boolean;
  formatDensity?: 'dense' | 'spacious';
}

/**
 * Hook to manage date input sections in text fields
 */
export const useDateSections = ({
  value,
  onChange,
  valueFormat,
  format,
  valueType = 'date',
  sections: displayedSections = [],
  onSectionChange,
  shouldDisableDate,
  isRtl = false,
  formatDensity = 'dense',
}: UseDateSectionsProps) => {
  const utils = useUtils();
  const lastSeparator = useRef('');
  const [activeSection, setActiveSection] = useState<number | null>(null);
  const [characterQuery, setCharacterQuery] = useState<{
    value: string;
    sectionIndex: number;
    sectionType: FieldSectionType;
    lastUpdate: number;
  } | null>(null);

  // Use the provided format or valueFormat
  const effectiveFormat = format || valueFormat || 'MM/DD/YYYY';

  // Derive sections from value, but store in internal state
  const currentSections = useMemo<FieldSectionData[]>(() => {
    if (!effectiveFormat) {
      return [];
    }

    // Always extract sections from the input value string or empty string if no value
    // This ensures we have section placeholders even when the field is empty
    const extractedSections = parseDateSections(effectiveFormat, value || '', { isRtl, formatDensity });

    // If no displayed sections are provided, use the extracted ones
    if (!displayedSections || displayedSections.length === 0) {
      return extractedSections;
    }

    // Map each displayed section to its current value and additional metadata
    return displayedSections.map((section) => {
      const matchedSection = extractedSections.find((s) => s.type === section.type);
      const sectionValue = matchedSection?.value || '';

      let contentType: 'letter' | 'digit' | 'digit-with-letter' = 'digit';
      if (section.type === 'month' && (section.format?.includes('MMM') || section.format?.includes('MMMM'))) {
        contentType = 'letter';
      } else if (section.type === 'weekDay') {
        contentType = 'letter';
      } else if (section.type === 'meridiem') {
        contentType = 'digit-with-letter'; // Special case for AM/PM
      }

      // Create section with all required properties of FieldSectionData
      return {
        ...section,
        value: sectionValue,
        displayValue: sectionValue || section.placeholder || '',
        contentType,
        separator: matchedSection?.separator || '',
        token: section.format || matchedSection?.token || '',
        startIndex: matchedSection?.startIndex || 0,
        endIndex: matchedSection?.endIndex || 0,
        invalid: matchedSection?.invalid || false,
        placeholder: section.placeholder || matchedSection?.placeholder || '',
        maxLength: section.maxLength || matchedSection?.maxLength || 2,
        modified: matchedSection?.modified || false,
      };
    });
  }, [value, effectiveFormat, displayedSections, isRtl, formatDensity]);

  // Add internal state to track sections
  const [internalSections, setInternalSections] = useState<FieldSectionData[]>(currentSections);

  // Helper to combine sectional values into a full date string
  const createDateFromSectionValues = useCallback<CombineDateSections>(
    (sections, operation, activeIndex) => {
      if (!sections || sections.length === 0) {
        return { value: '', sections: [] };
      }

      // Extract all section values
      const extractedValues: Record<FieldSectionType, string | null> = {
        year: null,
        month: null,
        day: null,
        weekDay: null,
        hours: null,
        minutes: null,
        seconds: null,
        meridiem: null,
        empty: null,
      };

      // Record all separators
      const separators: string[] = [];
      let lastType: FieldSectionType | null = null;

      // Using type assertion to treat sections as having startSeparator property
      (sections as ExtendedFieldSectionData[]).forEach((section) => {
        extractedValues[section.type] = section.value || null;

        // Handle separators for proper date string formatting
        if (lastType && section.startSeparator) {
          separators.push(section.startSeparator);
          lastSeparator.current = section.startSeparator;
        }
        lastType = section.type;
      });

      // Format date string from sections
      let dateString = '';
      let placeholder = false;

      // Date components
      if (extractedValues.year !== null && extractedValues.month !== null && extractedValues.day !== null) {
        // Create proper date format as per value type (ISO, date object)
        if (valueType === 'date') {
          // YYYY-MM-DD
          dateString = `${extractedValues.year}-${extractedValues.month}-${extractedValues.day}`;
        } else {
          // Regional date format
          const formatSeparator = separators[0] || '/';
          const monthIndex = effectiveFormat.indexOf('M') < effectiveFormat.indexOf('D') ? 0 : 1;

          if (monthIndex === 0) {
            dateString = `${extractedValues.month}${formatSeparator}${extractedValues.day}${formatSeparator}${extractedValues.year}`;
          } else {
            dateString = `${extractedValues.day}${formatSeparator}${extractedValues.month}${formatSeparator}${extractedValues.year}`;
          }
        }
      } else {
        placeholder = true;
      }

      // Time components
      if (extractedValues.hours !== null) {
        if (dateString && !placeholder) {
          dateString += 'T'; // ISO format separator
        }

        dateString += `${extractedValues.hours}:${extractedValues.minutes || '00'}`;

        if (extractedValues.seconds !== null) {
          dateString += `:${extractedValues.seconds}`;
        }

        if (extractedValues.meridiem !== null) {
          dateString += ` ${extractedValues.meridiem}`;
        }
      }

      // Return combined value and updated sections
      return {
        value: dateString,
        sections: sections.map((section) => ({
          ...section,
          modified: false, // Reset modification flag
        })),
      };
    },
    [valueType, effectiveFormat],
  );

  // Update all sections at once (for compatibility with useFieldIntegration)
  const setSections = useCallback(
    (newSections: FieldSectionData[]) => {
      // Update internal sections state
      setInternalSections(newSections);

      // Combine sections into a date string
      const result = createDateFromSectionValues(newSections, 'update', null);

      // Notify parent component
      onChange?.(result.value, 'update');
      onSectionChange?.(newSections, -1);
    },
    [onChange, onSectionChange, createDateFromSectionValues],
  );

  // Update a specific section value
  const updateSectionValue = useCallback<UpdateSectionsCallback>(
    (sectionIndex, newValue, moveToNextSection = false) => {
      if (sectionIndex < 0 || sectionIndex >= internalSections.length) {
        return;
      }
      // Create a copy of sections array with the updated value
      const updatedSections = internalSections.map((section, idx) =>
        idx === sectionIndex ? { ...section, value: newValue, modified: true } : section,
      );

      // Update internal sections state
      setInternalSections(updatedSections);

      // Keep a reference to the active section
      const activeSection = updatedSections[sectionIndex];

      // Update character query to track the section edit
      setCharacterQuery({
        value: newValue,
        sectionIndex,
        sectionType: activeSection.type,
        lastUpdate: Date.now(),
      });

      // 2. Try to build an actual valid date from the sections
      // This is for validation only - need all required parts
      const hasAllDateParts =
        updatedSections.some((s) => s.type === 'year' && s.value) &&
        updatedSections.some((s) => s.type === 'month' && s.value) &&
        updatedSections.some((s) => s.type === 'day' && s.value);

      // Get the actual date string (empty if incomplete)
      const result = createDateFromSectionValues(updatedSections, 'input', sectionIndex);

      // 3. Validate the date if we have all required parts
      let isValidDate = hasAllDateParts && result.value !== '';
      // Only perform validation against shouldDisableDate if we have a complete date
      if (isValidDate && shouldDisableDate && valueType !== 'time') {
        try {
          // Convert to a standard JS Date for validation
          let date: Date;
          if (valueType === 'date') {
            date = new Date(result.value);
          } else {
            const parsedDate = utils.parse(result.value, effectiveFormat);
            date = utils.toJsDate ? utils.toJsDate(parsedDate) : (parsedDate as unknown as Date);
          }

          if (shouldDisableDate(date)) {
            isValidDate = false;
          }
        } catch (error) {
          isValidDate = false;
        }
      }

      // For the onChange handler:
      // - If we have a valid date, pass the full date string
      // - If we have an incomplete date, pass an empty string for the actual value
      if (isValidDate) {
        // We have a complete, valid date - pass the actual date string
        onChange?.(result.value, 'input', sectionIndex);
      } else {
        // Incomplete or invalid date - pass empty string as value
        // This follows MUI's pattern where incomplete dates yield null/empty
        onChange?.('', 'input', sectionIndex);
      }

      // Always update the sections so UI reflects what's being edited
      onSectionChange?.(updatedSections, sectionIndex);

      // 6. Move to next section if requested
      if (moveToNextSection && sectionIndex < internalSections.length - 1) {
        setActiveSection(sectionIndex + 1);
      }
    },
    [
      internalSections,
      createDateFromSectionValues,
      onChange,
      onSectionChange,
      shouldDisableDate,
      utils,
      effectiveFormat,
      valueType,
      setCharacterQuery,
    ],
  );

  // Move to the previous section
  const moveToPreviousSection = useCallback(() => {
    if (activeSection === null) {
      setActiveSection(0);
    } else if (activeSection > 0) {
      setActiveSection(activeSection - 1);
    }
  }, [activeSection]);

  // Move to the next section
  const moveToNextSection = useCallback(() => {
    if (activeSection === null) {
      setActiveSection(0);
    } else if (activeSection < internalSections.length - 1) {
      setActiveSection(activeSection + 1);
    }
  }, [activeSection, internalSections.length]);

  // Character editing capabilities
  const { processCharacterInput: processSectionInput, handleSectionPaste } = useCharacterEditing({
    activeSection,
    sections: internalSections,
    characterQuery,
    setCharacterQuery,
    updateSectionValue,
  });

  // Wrap the original function to add logging
  const processCharacterInput = useCallback(
    (keyPressed: string) => {
      return processSectionInput(keyPressed);
    },
    [processSectionInput],
  );

  // Wrap setActiveSection to add logging
  const originalSetActiveSection = setActiveSection;
  const wrappedSetActiveSection = useCallback(
    (sectionIndex: number | null) => {
      originalSetActiveSection(sectionIndex);
    },
    [originalSetActiveSection],
  );

  // Handle clipboard paste for the entire field or sections
  const adaptedHandleSectionPaste = useCallback(
    (text: string): boolean => {
      if (!activeSection || activeSection >= internalSections.length) {
        return false;
      }

      const section = internalSections[activeSection];
      const result = handleSectionPaste(section, text, true);
      if (result.newValue) {
        updateSectionValue(activeSection, result.newValue, result.shouldGoToNextSection);
        return true;
      }
      return false;
    },
    [activeSection, internalSections, handleSectionPaste, updateSectionValue],
  );

  // Handle clipboard paste for the entire field or sections
  const { handleClipboardPaste } = useClipboardPaste({
    activeSection,
    sections: internalSections,
    format: effectiveFormat,
    onChange: (value) => onChange?.(value, 'paste'),
    setCharacterQuery,
    updateSectionValue,
    handleSectionPaste: adaptedHandleSectionPaste,
  });

  // Return necessary data and functions with the interface expected by useFieldIntegration
  return {
    sections: internalSections,
    setSections,
    activeSection,
    setActiveSection: wrappedSetActiveSection,
    moveToPreviousSection,
    moveToNextSection,
    processCharacterInput,
    handleClipboardPaste,
    // Keep the original functions for backward compatibility
    handleSectionSelect: wrappedSetActiveSection,
    handleSectionClear: useCallback(() => wrappedSetActiveSection(null), [wrappedSetActiveSection]),
    updateSectionValue,
  };
};
