import { useMemo } from 'react';
import { FieldSectionData, FieldSectionType } from '../../models/dateSection';

/**
 * Hook to generate proper ARIA attributes for DateField components
 * Based on MUI's accessibility approach in useFieldSectionContentProps
 */
export function useDateFieldAccessibility(
  error: boolean,
  disabled: boolean,
  readOnly: boolean,
  required: boolean,
  activeSection: number | null,
) {
  /**
   * Get ARIA attributes for the root element
   */
  const rootAriaAttributes = useMemo(
    () => ({
      role: 'group',
      'aria-label': 'Date input field',
      'aria-invalid': error,
      'aria-disabled': disabled,
      'aria-readonly': readOnly,
      'aria-required': required,
    }),
    [error, disabled, readOnly, required],
  );

  /**
   * Get section specific ARIA attributes
   * Similar to MUI's getSectionValueText and getSectionValueNow functions
   */
  const getSectionAriaAttributes = useMemo(() => {
    // Return a function that can be called with a section
    return (section: FieldSectionData, index: number) => {
      const type = section.type;

      // Define section labels (similar to MUI's translations)
      const sectionLabels: Record<FieldSectionType, string> = {
        year: 'Year',
        month: 'Month',
        day: 'Day',
        weekDay: 'Day of week',
        hours: 'Hours',
        minutes: 'Minutes',
        seconds: 'Seconds',
        meridiem: 'AM/PM',
        empty: 'Empty',
      };

      // Define boundaries for each section type
      let min: number;
      let max: number;

      switch (type) {
        case 'year':
          min = 0;
          max = 9999;
          break;
        case 'month':
          min = 1;
          max = 12;
          break;
        case 'day':
          min = 1;
          max = 31;
          break;
        case 'hours':
          min = 0;
          max = 23;
          break;
        case 'minutes':
        case 'seconds':
          min = 0;
          max = 59;
          break;
        case 'meridiem':
          min = 0;
          max = 1; // AM/PM
          break;
        default:
          min = 0;
          max = 0;
      }

      // Current value (only for numeric sections)
      let current: number | undefined;
      if (section.value && type !== 'meridiem' && /^\d+$/.test(section.value)) {
        current = parseInt(section.value, 10);
      }

      // Get textual representation for screen readers
      let valueText: string | undefined;
      if (section.value) {
        valueText = section.value;

        // Special handling for AM/PM
        if (type === 'meridiem') {
          valueText = section.value.toUpperCase() === 'AM' ? 'AM' : 'PM';
        }
        // Special handling for months with names
        else if (type === 'month' && isNaN(Number(section.value))) {
          valueText = section.value;
        }
      }

      return {
        'aria-label': sectionLabels[type] || type,
        ...(current !== undefined && { 'aria-valuenow': current }),
        ...(valueText && { 'aria-valuetext': valueText }),
        'aria-valuemin': min,
        'aria-valuemax': max,
        'aria-disabled': disabled,
        'aria-readonly': readOnly,
        role: 'spinbutton',
        tabIndex: index === activeSection ? 0 : -1,
      };
    };
  }, [activeSection, disabled, readOnly]);

  return {
    rootAriaAttributes,
    getSectionAriaAttributes,
  };
}

export default useDateFieldAccessibility;
