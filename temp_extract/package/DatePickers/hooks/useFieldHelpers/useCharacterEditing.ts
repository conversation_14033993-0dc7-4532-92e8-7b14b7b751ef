import { useCallback } from 'react';
import { useUtils } from '../useUtils';
import { FieldSectionData, FieldSectionType } from '../../models/dateSection';
import { cleanDigitValue } from '../../utils/sectionUtils';
import { useSectionBoundaries } from './useSectionBoundaries';

interface CharacterQuery {
  value: string;
  sectionIndex: number;
  sectionType: FieldSectionType;
  lastUpdate: number;
}

interface UseCharacterEditingProps {
  activeSection: number | null;
  sections: FieldSectionData[];
  characterQuery: CharacterQuery | null;
  setCharacterQuery: (query: CharacterQuery | null) => void;
  updateSectionValue: (sectionIndex: number, value: string, moveToNextSection: boolean) => void;
}

/**
 * Return type for the useCharacterEditing hook
 */
export interface UseCharacterEditingReturnValue {
  handleSectionPaste: (
    section: FieldSectionData,
    text: string,
    shouldGoToNextSection?: boolean,
  ) => {
    newValue: string | null;
    shouldGoToNextSection: boolean;
  };
  applyCharacterEditing: (
    section: FieldSectionData,
    keyboardCharacter: string,
  ) => {
    newValue: string | null;
    shouldGoToNextSection: boolean;
  };
  processCharacterInput: (keyPressed: string) => boolean;
}

/**
 * Check if a character is valid for a section type
 * Based on MUI's approach of validating input characters
 */
const isCharacterValidForSection = (
  sectionType: FieldSectionType,
  contentType: 'digit' | 'letter' | 'digit-with-letter',
  character: string,
): boolean => {
  // Digit sections (year, month, day, hours, minutes, seconds)
  if (contentType === 'digit') {
    return /^[0-9]$/.test(character);
  }

  // Letter sections (month names, weekdays)
  if (contentType === 'letter') {
    return /^[a-zA-Z]$/.test(character);
  }

  // Meridiem (AM/PM)
  if (sectionType === 'meridiem') {
    return /^[aApP]$/.test(character);
  }

  // Default fallback
  return false;
};

/**
 * Enhanced hook for character editing, inspired by MUI's useFieldCharacterEditing
 * but adapted for our implementation and V6 DOM structure
 */
export function useCharacterEditing({
  activeSection,
  sections,
  characterQuery,
  setCharacterQuery,
  updateSectionValue,
}: UseCharacterEditingProps): UseCharacterEditingReturnValue {
  const utils = useUtils();
  // Use the centralized section boundaries hook instead of local implementation
  const { getSectionBoundaries } = useSectionBoundaries();

  /**
   * Get letter editing options for a specific section type
   * Supports month names, weekday names, and AM/PM
   */
  const getLetterEditingOptions = useCallback(
    (sectionType: FieldSectionType, format: string): string[] => {
      const now = utils.date();

      switch (sectionType) {
        case 'month': {
          // Get full list of month names
          return Array.from({ length: 12 }, (_, i) => {
            const date = utils.setMonth(now, i);
            return utils.formatByString(date, format);
          });
        }

        case 'weekDay': {
          // Get full list of weekday names
          return Array.from({ length: 7 }, (_, i) => {
            const date = utils.setDate(now, i + 1);
            return utils.formatByString(date, format);
          });
        }

        case 'meridiem': {
          // AM/PM values
          const amDate = utils.startOfDay(now);
          const pmDate = utils.endOfDay(now);
          return [utils.formatByString(amDate, format), utils.formatByString(pmDate, format)];
        }

        default:
          return [];
      }
    },
    [utils],
  );

  /**
   * Find matching option from available options based on query
   */
  const findMatchingOption = useCallback(
    (options: string[], queryValue: string): { value: string; exact: boolean } | null => {
      // Make search case insensitive
      const lowerQuery = queryValue.toLowerCase();

      // First check for exact matches (for single character inputs)
      const exactMatch = options.find((option) => option.toLowerCase() === lowerQuery);

      if (exactMatch) {
        return { value: exactMatch, exact: true };
      }

      // Then check for prefix matches
      const prefixMatches = options.filter((option) => option.toLowerCase().startsWith(lowerQuery));

      if (prefixMatches.length === 1) {
        // Only one match, return it
        return { value: prefixMatches[0], exact: false };
      } else if (prefixMatches.length > 1) {
        // Multiple matches, return the shortest one
        // This is similar to MUI's behavior
        const shortestMatch = prefixMatches.reduce(
          (shortest, current) => (current.length < shortest.length ? current : shortest),
          prefixMatches[0],
        );
        return { value: shortestMatch, exact: false };
      }

      return null;
    },
    [],
  );

  /**
   * Main function to process character input
   */
  const processCharacterInput = useCallback(
    (keyPressed: string) => {
      if (activeSection === null || activeSection >= sections.length) {
        return false;
      }

      const section = sections[activeSection];
      if (!section) return false;

      // Clean and normalize the input
      const cleanKey = keyPressed.trim();
      if (!cleanKey) return false;

      const now = Date.now();
      let queryValue = cleanKey;

      // If we have a recent query for this section, append to it
      if (
        characterQuery &&
        characterQuery.sectionIndex === activeSection &&
        characterQuery.sectionType === section.type &&
        now - characterQuery.lastUpdate < 1000
      ) {
        queryValue = characterQuery.value + cleanKey;
      }

      // Create/update the character query
      const newQuery: CharacterQuery = {
        value: queryValue,
        sectionIndex: activeSection,
        sectionType: section.type,
        lastUpdate: now,
      };

      setCharacterQuery(newQuery);

      // Process based on content type
      if (section.contentType === 'letter' || section.type === 'meridiem') {
        // Handle letter-based inputs (month names, weekdays, AM/PM)
        const options = getLetterEditingOptions(section.type, section.format || '');
        const match = findMatchingOption(options, queryValue);

        if (match) {
          updateSectionValue(activeSection, match.value, match.exact);
          return true;
        }
      } else if (section.contentType === 'digit' || section.contentType === 'digit-with-letter') {
        // Handle numeric input with enhanced validation
        // Check if the input is a number
        if (!/^\d+$/.test(queryValue)) {
          return false;
        }

        const numValue = parseInt(queryValue, 10);
        const boundaries = getSectionBoundaries(section.type, section.format || '', null);

        // Handle leading zeroes and validation
        if (numValue > boundaries.maximum) {
          // For single digits that could be part of a valid entry, keep the query
          if (queryValue.length === 1 && parseInt(queryValue, 10) <= parseInt(String(boundaries.maximum)[0], 10)) {
            return false; // Keep the query but don't update value yet
          }
          return false; // Invalid input, ignore
        }

        if (numValue < boundaries.minimum) {
          // Special handling for sections that allow values like 01, 02, etc.
          if (
            queryValue.length === 1 &&
            ['month', 'day', 'hours', 'minutes', 'seconds', 'year'].includes(section.type)
          ) {
            if (boundaries.minimum > 0) {
              // For months and days that start from 1, prepend a 0
              updateSectionValue(activeSection, queryValue.padStart(section.maxLength || 2, '0'), false);
            } else {
              updateSectionValue(activeSection, queryValue, false);
            }
            return true;
          }
          return false; // Invalid input, ignore
        }

        // Format the value appropriately
        let formattedValue = String(numValue);

        // Add leading zeroes for appropriate section types
        if (['month', 'day', 'hours', 'minutes', 'seconds', 'year'].includes(section.type)) {
          const maxLength = section.type === 'year' ? 4 : 2;

          // For YYYY format, pad to 4 digits
          if (section.type === 'year' && (section.format?.includes('YYYY') || section.maxLength === 4)) {
            formattedValue = formattedValue.padStart(4, '0');
          }
          // For other sections or YY format, pad to 2 digits
          else if (formattedValue.length < (section.maxLength || 2)) {
            formattedValue = formattedValue.padStart(section.maxLength || 2, '0');
          }
        }

        // Determine if we should go to next section
        const shouldGoToNext =
          // Only move to next section if:
          // 1. Adding another digit would exceed the maximum (e.g., for month, if '1' is typed, 1*10 is not > 12, so don't advance)
          // 2. OR if we've already reached the full maximum length of digits
          numValue * 10 > boundaries.maximum ||
          // For sections with fixed length (month:2, day:2, year:4), only advance when we've typed all digits
          queryValue.length >=
            (section.type === 'year' && section.maxLength === 4 ? 4 : String(boundaries.maximum).length);

        // Verify we have a valid formatted value before updating
        if (formattedValue) {
          // Add additional validation check - only update with non-empty valid values
          updateSectionValue(activeSection, formattedValue, shouldGoToNext);

          // We successfully processed this character
          return true;
        }

        return false;
      }

      return false;
    },
    [
      activeSection,
      sections,
      characterQuery,
      setCharacterQuery,
      updateSectionValue,
      getLetterEditingOptions,
      findMatchingOption,
      getSectionBoundaries,
    ],
  );

  /**
   * Handle section-specific paste operations
   * Renamed from handlePaste to better distinguish from global paste handling
   */
  const handleSectionPaste = useCallback(
    (
      section: FieldSectionData,
      text: string,
      shouldGoToNextSection: boolean = true,
    ): {
      newValue: string | null;
      shouldGoToNextSection: boolean;
    } => {
      if (!section || !text) {
        return { newValue: null, shouldGoToNextSection: false };
      }

      // Handle digit sections
      if (section.contentType === 'digit') {
        // Only allow digits
        const digitsOnly = text.replace(/\D/g, '');
        if (!digitsOnly) {
          return { newValue: null, shouldGoToNextSection: false };
        }

        const maxSectionLength = section.maxLength || 2;

        // Limit to max length for the section
        // For MM/DD, this is 2; for YYYY, this is 4
        const truncatedValue = digitsOnly.substring(0, maxSectionLength);

        // Clean the value to ensure it's valid for the section type
        const cleanedValue = cleanDigitValue(section.type, truncatedValue);

        // Only accept valid numbers
        if (!cleanedValue) {
          return { newValue: null, shouldGoToNextSection: false };
        }

        // If we have the max digits for this section, move to next section
        const goToNext = shouldGoToNextSection && cleanedValue.length >= maxSectionLength;

        return { newValue: cleanedValue, shouldGoToNextSection: goToNext };
      }

      // Handle letter-based sections (month names, weekday names)
      if (section.contentType === 'letter') {
        const lettersOnly = text.replace(/[^a-zA-Z]/g, '');
        if (!lettersOnly) {
          return { newValue: null, shouldGoToNextSection: false };
        }

        // For letters, we usually complete the word in a more natural way
        // This is a simplification; a full implementation would handle
        // matching month/day names based on the locale
        return { newValue: lettersOnly, shouldGoToNextSection };
      }

      // Handle meridiem (AM/PM)
      if (section.type === 'meridiem') {
        const upperText = text.toUpperCase();
        if (upperText.includes('A')) {
          return { newValue: 'AM', shouldGoToNextSection };
        }
        if (upperText.includes('P')) {
          return { newValue: 'PM', shouldGoToNextSection };
        }
      }

      return { newValue: null, shouldGoToNextSection: false };
    },
    [],
  );

  /**
   * Apply character editing to a section
   * Based on MUI's handling of typing characters
   */
  const applyCharacterEditing = useCallback(
    (
      section: FieldSectionData,
      keyboardCharacter: string,
    ): {
      newValue: string | null;
      shouldGoToNextSection: boolean;
    } => {
      if (!section || !keyboardCharacter) {
        return { newValue: null, shouldGoToNextSection: false };
      }

      // Check if the character is valid for this section
      if (!isCharacterValidForSection(section.type, section.contentType, keyboardCharacter)) {
        return { newValue: null, shouldGoToNextSection: false };
      }

      // For digit sections (year, month, day, hours, minutes, seconds)
      if (section.contentType === 'digit') {
        const maxSectionLength = section.maxLength || (section.type === 'year' ? 4 : 2);
        const currentValue = section.value || '';

        // Similar to MUI, we handle different scenarios based on current value
        // When the section is empty or already filled to max length, replace with new digit
        if (currentValue === '' || currentValue.length >= maxSectionLength) {
          // Start with just the typed character
          let newValue = keyboardCharacter;

          // Special case for day and month to ensure valid ranges
          if ((section.type === 'day' || section.type === 'month') && keyboardCharacter > '3') {
            // If typing a digit > 3 in an empty day/month field, prefix with 0
            newValue = `0${keyboardCharacter}`;
          }

          // Make sure to properly format the new value
          const cleanedValue = cleanDigitValue(section.type, newValue);

          // If the value is valid but needs leading zeros, add them
          if (cleanedValue && ['month', 'day', 'hours', 'minutes', 'seconds', 'year'].includes(section.type)) {
            // For YYYY format in year section
            if (section.type === 'year' && (section.format?.includes('YYYY') || maxSectionLength === 4)) {
              const formattedValue = cleanedValue.padStart(4, '0');

              // Update the shouldGoToNext logic
              const numValue = parseInt(formattedValue, 10);
              const shouldGoToNext =
                // Move to next section when:
                // 1. We've entered a complete valid value that matches the expected length
                formattedValue.length >= maxSectionLength ||
                // 2. The entered value is already at its maximum (can't add more digits)
                (numValue > 0 && numValue * 10 > 9999) ||
                // 3. For single-digit entries, move if the digit guarantees we're done
                (formattedValue.length === 1 && parseInt(formattedValue, 10) * 10 + 9 > 9999);

              // Only advance to next section if we have a valid value
              const shouldGoToNextValid = shouldGoToNext && formattedValue !== '';

              return { newValue: formattedValue, shouldGoToNextSection: shouldGoToNextValid };
            }
            // For other sections that need padding to 2 digits
            else if (cleanedValue.length === 1) {
              const formattedValue = cleanedValue.padStart(maxSectionLength, '0');

              // Update the shouldGoToNext logic
              const numValue = parseInt(formattedValue, 10);
              const maxValue = section.type === 'year' ? 9999 : 99;
              const shouldGoToNext =
                // Move to next section when:
                // 1. We've entered a complete valid value that matches the expected length
                formattedValue.length >= maxSectionLength ||
                // 2. The entered value is already at its maximum (can't add more digits)
                (numValue > 0 && numValue * 10 > maxValue) ||
                // 3. For single-digit entries, move if the digit guarantees we're done
                (formattedValue.length === 1 && parseInt(formattedValue, 10) * 10 + 9 > maxValue);

              // Only advance to next section if we have a valid value
              const shouldGoToNextValid = shouldGoToNext && formattedValue !== '';

              return { newValue: formattedValue, shouldGoToNextSection: shouldGoToNextValid };
            }
          }

          // For other values, proceed as before
          // Update the shouldGoToNext logic
          const numValue = parseInt(cleanedValue, 10);
          const maxValue = section.type === 'year' ? 9999 : 99;
          const shouldGoToNext =
            // Move to next section when:
            // 1. We've entered a complete valid value that matches the expected length
            cleanedValue.length >= maxSectionLength ||
            // 2. The entered value is already at its maximum (can't add more digits)
            (numValue > 0 && numValue * 10 > maxValue) ||
            // 3. For single-digit entries, move if the digit guarantees we're done
            (cleanedValue.length === 1 && parseInt(cleanedValue, 10) * 10 + 9 > maxValue);

          // Only advance to next section if we have a valid value
          const shouldGoToNextValid = shouldGoToNext && cleanedValue !== '';

          return { newValue: cleanedValue, shouldGoToNextSection: shouldGoToNextValid };
        }
        // When section has a value but not at max length, append the new digit
        else {
          // Append the typed character
          const newValue = `${currentValue}${keyboardCharacter}`;

          // Clean to ensure it's valid for this section type
          const cleanedValue = cleanDigitValue(section.type, newValue);

          // Make sure to properly format the value
          let formattedValue = cleanedValue;
          if (
            formattedValue &&
            ['month', 'day', 'hours', 'minutes', 'seconds', 'year'].includes(section.type) &&
            formattedValue.length < maxSectionLength
          ) {
            formattedValue = formattedValue.padStart(maxSectionLength, '0');
          }

          // Update the shouldGoToNext logic
          const numValue = parseInt(formattedValue, 10);
          const maxValue = section.type === 'year' ? 9999 : 99;
          const shouldGoToNext =
            // Move to next section when:
            // 1. We've entered a complete valid value that matches the expected length
            formattedValue.length >= maxSectionLength ||
            // 2. The entered value is already at its maximum (can't add more digits)
            (numValue > 0 && numValue * 10 > maxValue) ||
            // 3. For single-digit entries, move if the digit guarantees we're done
            (formattedValue.length === 1 && parseInt(formattedValue, 10) * 10 + 9 > maxValue);

          // Only advance to next section if we have a valid value
          const shouldGoToNextValid = shouldGoToNext && formattedValue !== '';

          return { newValue: formattedValue, shouldGoToNextSection: shouldGoToNextValid };
        }
      }

      // For meridiem (AM/PM)
      if (section.type === 'meridiem') {
        if (keyboardCharacter.toLowerCase() === 'a') {
          return { newValue: 'AM', shouldGoToNextSection: true };
        }
        if (keyboardCharacter.toLowerCase() === 'p') {
          return { newValue: 'PM', shouldGoToNextSection: true };
        }
      }

      // Letter-based sections (more complex in real implementation)
      if (section.contentType === 'letter') {
        // This is a simplification; full implementations would handle
        // matching month/day names based on the locale and comparing with available options
        return { newValue: keyboardCharacter.toUpperCase(), shouldGoToNextSection: false };
      }

      return { newValue: null, shouldGoToNextSection: false };
    },
    [],
  );

  return {
    handleSectionPaste,
    applyCharacterEditing,
    processCharacterInput,
  };
}
