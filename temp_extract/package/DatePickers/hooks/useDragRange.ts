import * as React from 'react';
import { unstable_useEventCallback as useEventCallback } from '@mui/utils';
import { DateAdapter } from '../models/adapter';
import { PickerDateType } from '../models/pickers';
import { PickerRangeValue, RangePosition } from '../utils/dateRangeUtils';

interface UseDragRangeProps {
  /** The current date range value */
  value: PickerRangeValue;
  /** The position in the range being selected */
  rangePosition: RangePosition;
  /** Callback fired when the range changes */
  onChange: (newValue: PickerRangeValue) => void;
  /** <PERSON><PERSON> fired when the range position changes */
  onRangePositionChange: (position: RangePosition) => void;
  /** Date adapter for date operations */
  utils: DateAdapter;
  /** Whether the component is disabled */
  disabled?: boolean;
  /** Whether the component is read only */
  readOnly?: boolean;
  /** Whether dragging to edit the range is disabled */
  disableDragEditing?: boolean;
}

/**
 * Custom hook for implementing drag-and-drop range selection
 */
export const useDragRange = (props: UseDragRangeProps) => {
  const { value, rangePosition, onChange, onRangePositionChange, utils, disabled, readOnly, disableDragEditing } =
    props;

  // Store drag state
  const [isDragging, setIsDragging] = React.useState(false);
  const [dragDay, setDragDay] = React.useState<PickerDateType | null>(null);
  const [dragPosition, setDragPosition] = React.useState<RangePosition | null>(null);

  // Calculate dragging range (this is the preview during dragging)
  const getDraggingRange = React.useCallback(() => {
    if (!dragDay || !dragPosition || !value[0] || !value[1]) {
      return [null, null] as PickerRangeValue;
    }

    // Create a preview range based on which end is being dragged
    if (dragPosition === 'start') {
      return [dragDay, value[1]] as PickerRangeValue;
    } else {
      return [value[0], dragDay] as PickerRangeValue;
    }
  }, [dragDay, dragPosition, value]);

  // Handle drag start by capturing the date and position
  const handleDragStart = useEventCallback((event: React.DragEvent<HTMLElement>) => {
    if (disabled || readOnly || disableDragEditing) {
      event.preventDefault();
      return;
    }

    // Prevent drag for days that aren't start or end dates
    const position = event.currentTarget.getAttribute('data-position');
    if (!position) {
      event.preventDefault();
      return;
    }

    // Get the timestamp from the dragged element
    const timestamp = event.currentTarget.getAttribute('data-timestamp');
    if (!timestamp) {
      event.preventDefault();
      return;
    }

    // Start dragging with the position and date
    const date = utils.date(timestamp);
    setIsDragging(true);
    setDragDay(date);
    setDragPosition(position as RangePosition);

    // Set drag-specific data
    event.dataTransfer.effectAllowed = 'move';
    try {
      event.dataTransfer.setData('text/plain', timestamp);
    } catch (error) {
      // Some browsers don't support setData, we need to at least set something
      event.dataTransfer.setData('text', timestamp);
    }

    // Hide the drag image, as we'll show a preview via highlighting
    const img = new Image();
    img.src = 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7'; // 1px transparent image
    event.dataTransfer.setDragImage(img, 0, 0);
  });

  // Handle drag over to update preview
  const handleDragOver = useEventCallback((event: React.DragEvent<HTMLElement>) => {
    if (!isDragging || disabled || readOnly) return;
    event.preventDefault();

    // Get the date from the element being dragged over
    const timestamp = event.currentTarget.getAttribute('data-timestamp');
    if (!timestamp) return;

    const newDay = utils.date(timestamp);

    // Update drag day for preview
    if (dragDay && !utils.isSameDay(dragDay, newDay)) {
      setDragDay(newDay);
    } else if (!dragDay) {
      setDragDay(newDay);
    }
  });

  // Handle drag end (cancel)
  const handleDragEnd = useEventCallback((event: React.DragEvent<HTMLElement>) => {
    event.preventDefault();
    setIsDragging(false);
    setDragDay(null);
    setDragPosition(null);
  });

  // Handle drop (confirm)
  const handleDrop = useEventCallback((event: React.DragEvent<HTMLElement>) => {
    event.preventDefault();

    if (!isDragging || !dragDay || !dragPosition || disabled || readOnly) {
      setIsDragging(false);
      setDragDay(null);
      setDragPosition(null);
      return;
    }

    // Update the range with the dragged day at the dragged position
    let newValue = [...value] as PickerRangeValue;
    let finalPosition = dragPosition;

    if (dragPosition === 'start') {
      // If dragging the start date and it's after the end date, swap them
      if (value[1] && utils.isAfter(dragDay, value[1])) {
        newValue = [value[1], dragDay];
        finalPosition = 'end';
      } else {
        newValue[0] = dragDay;
      }
    } else {
      // If dragging the end date and it's before the start date, swap them
      if (value[0] && utils.isBefore(dragDay, value[0])) {
        newValue = [dragDay, value[0]];
        finalPosition = 'start';
      } else {
        newValue[1] = dragDay;
      }
    }

    // Update the range and reset drag state
    onChange(newValue);
    onRangePositionChange(finalPosition);

    // Reset drag state
    setIsDragging(false);
    setDragDay(null);
    setDragPosition(null);
  });

  // Clean up drag state if component unmounts during drag
  React.useEffect(() => {
    return () => {
      if (isDragging) {
        setIsDragging(false);
        setDragDay(null);
        setDragPosition(null);
      }
    };
  }, [isDragging]);

  return {
    isDragging,
    dragDay,
    dragPosition,
    handleDragStart,
    handleDragOver,
    handleDragEnd,
    handleDrop,
    getDraggingRange,
    setIsDragging,
    setDragDay,
    setDragPosition,
  };
};
