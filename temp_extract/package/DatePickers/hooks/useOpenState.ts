import * as React from 'react';
import { unstable_useEventCallback as useEventCallback } from '@mui/utils';
import { useControlledValue } from './useControlledValue';

export interface UseOpenStateParams {
  /**
   * Controls whether the picker is open.
   */
  open?: boolean;

  /**
   * <PERSON><PERSON> fired when the picker is opened.
   */
  onOpen?: () => void;

  /**
   * Call<PERSON> fired when the picker is closed.
   */
  onClose?: () => void;

  /**
   * If `true`, the component is disabled and can't be opened.
   */
  disabled?: boolean;

  /**
   * If `true`, the component is read-only and can't be opened.
   */
  readOnly?: boolean;

  /**
   * If `true`, focuses the input when the picker opens.
   */
  autoFocus?: boolean;
}

export interface UseOpenStateResult {
  /**
   * Current open state of the picker.
   */
  open: boolean;

  /**
   * Element that triggered the picker to open.
   */
  anchorEl: HTMLElement | null;

  /**
   * Handle opening the picker.
   */
  handleOpen: (event?: React.MouseEvent<HTMLElement>) => void;

  /**
   * <PERSON>le closing the picker.
   */
  handleClose: () => void;
}

/**
 * Hook to manage the open state of a picker.
 *
 * @param params Open state params
 * @returns Open state and handlers
 */
export function useOpenState(params: UseOpenStateParams): UseOpenStateResult {
  const { open: openProp, onOpen, onClose, disabled = false, readOnly = false, autoFocus = false } = params;

  // Store anchor element for positioning
  const [anchorEl, setAnchorEl] = React.useState<HTMLElement | null>(null);

  // Use controlled/uncontrolled state
  const [open, setOpen] = useControlledValue<boolean>(openProp, false);

  // Handle opening the picker
  const handleOpen = useEventCallback((event?: React.MouseEvent<HTMLElement>) => {
    if (disabled || readOnly) {
      return;
    }

    // Store the element that triggered the open
    if (event?.currentTarget) {
      setAnchorEl(event.currentTarget);
    }

    setOpen(true);
    onOpen?.();
  });

  // Handle closing the picker
  const handleClose = useEventCallback(() => {
    setOpen(false);
    onClose?.();
  });

  return {
    open: !!open,
    anchorEl,
    handleOpen,
    handleClose,
  };
}
