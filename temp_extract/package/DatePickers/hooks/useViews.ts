'use client';
import * as React from 'react';
import { unstable_useEventCallback as useEventCallback } from '@mui/utils';
import { DatePickerView } from '../types';

export interface UseViewsParams {
  /**
   * The view to open by default.
   * @default 'day'
   */
  openTo?: DatePickerView;

  /**
   * Available views.
   * @default ['day', 'month', 'year']
   */
  views?: readonly DatePickerView[] | DatePickerView[];

  /**
   * Callback fired when the view changes.
   */
  onViewChange?: (view: DatePickerView) => void;
}

export interface UseViewsResult {
  /**
   * Current active view.
   */
  view: DatePickerView;

  /**
   * Set the current view.
   */
  setView: (view: DatePickerView) => void;

  /**
   * List of available views.
   */
  views: readonly DatePickerView[];

  /**
   * Go to next view in the sequence.
   */
  nextView: () => void;

  /**
   * Go to previous view in the sequence.
   */
  previousView: () => void;
}

const DEFAULT_VIEWS: readonly DatePickerView[] = ['day', 'month', 'year'];

/**
 * Hook to manage date picker views (day, month, year).
 *
 * @param params Views parameters
 * @returns View state and handlers
 */
export function useViews(params: UseViewsParams): UseViewsResult {
  const { openTo = 'day' as DatePickerView, views = DEFAULT_VIEWS, onViewChange } = params;

  // Ensure we have at least one view
  const availableViews = views.length > 0 ? views : DEFAULT_VIEWS;

  // Make sure openTo view is included in the list of available views
  const defaultView = availableViews.includes(openTo) ? openTo : availableViews[0];

  // Use controlled state for view
  const [view, setViewState] = React.useState<DatePickerView>(defaultView);

  // Update view with callback notification
  const setView = useEventCallback((newView: DatePickerView) => {
    if (availableViews.includes(newView)) {
      setViewState(newView);
      onViewChange?.(newView);
    }
  });

  // Navigate to next view in sequence
  const nextView = useEventCallback(() => {
    const currentIndex = availableViews.indexOf(view);
    const nextIndex = (currentIndex + 1) % availableViews.length;
    setView(availableViews[nextIndex]);
  });

  // Navigate to previous view in sequence
  const previousView = useEventCallback(() => {
    const currentIndex = availableViews.indexOf(view);
    const prevIndex = (currentIndex - 1 + availableViews.length) % availableViews.length;
    setView(availableViews[prevIndex]);
  });

  return {
    view,
    setView,
    views: availableViews,
    nextView,
    previousView,
  };
}
