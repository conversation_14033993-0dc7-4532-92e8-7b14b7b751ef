import * as React from 'react';
import { unstable_useEventCallback as useEventCallback } from '@mui/utils';
import { PickerRangeValue, RangePosition } from '../utils/dateRangeUtils';
import { useRangePosition } from './useRangePosition';
import { useDateValidation } from './useDateValidation';
import { useControlledValue } from './useControlledValue';
import { useOpenState } from './useOpenState';
import { useViews } from './useViews';
import { DatePickerView } from '../types';

export interface UseDateRangePickerParams {
  /**
   * The selected range.
   */
  value?: PickerRangeValue;

  /**
   * The default selected range.
   */
  defaultValue?: PickerRangeValue;

  /**
   * Callback fired when the range changes.
   */
  onChange?: (range: PickerRangeValue) => void;

  /**
   * Controls whether the popup is open.
   */
  open?: boolean;

  /**
   * <PERSON><PERSON> fired when the popup is opened.
   */
  onOpen?: () => void;

  /**
   * <PERSON><PERSON> fired when the popup is closed.
   */
  onClose?: () => void;

  /**
   * If `true`, the component is disabled.
   */
  disabled?: boolean;

  /**
   * If `true`, the component is read-only.
   */
  readOnly?: boolean;

  /**
   * Minimum selectable date.
   */
  minDate?: any;

  /**
   * Maximum selectable date.
   */
  maxDate?: any;

  /**
   * If `true`, disable dates after the current date.
   */
  disableFuture?: boolean;

  /**
   * If `true`, disable dates before the current date.
   */
  disablePast?: boolean;

  /**
   * Function that returns a boolean if the given date should be disabled.
   */
  shouldDisableDate?: (date: any) => boolean;

  /**
   * Format of the date when rendered in the input.
   */
  format?: string;

  /**
   * If `true`, the picker closes after a date is selected.
   */
  closeOnSelect?: boolean;

  /**
   * The position in the currently edited date range.
   */
  rangePosition?: RangePosition;

  /**
   * The default position in the range.
   */
  defaultRangePosition?: RangePosition;

  /**
   * Callback fired when the range position changes.
   */
  onRangePositionChange?: (position: RangePosition) => void;

  /**
   * If `true`, focuses the calendar on open.
   */
  autoFocus?: boolean;

  /**
   * Available views for the date picker.
   */
  views?: readonly DatePickerView[] | DatePickerView[];

  /**
   * Callback fired when the view changes.
   */
  onViewChange?: (view: DatePickerView) => void;
}

export interface UseDateRangePickerResult {
  // Range state
  selectedRange: PickerRangeValue;
  previewRange: PickerRangeValue;
  updateRange: (newRange: PickerRangeValue) => void;

  // Range position state
  rangePosition: RangePosition;
  setRangePosition: (position: RangePosition) => void;

  // Open state
  open: boolean;
  handleOpen: (event?: React.MouseEvent<HTMLElement>) => void;
  handleClose: () => void;
  anchorEl: HTMLElement | null;

  // View state
  currentView: DatePickerView;
  setCurrentView: (view: DatePickerView) => void;
  availableViews: readonly DatePickerView[];

  // Options
  disabled: boolean;
  readOnly: boolean;
  closeOnSelect: boolean;

  // Validation
  validateDate: (date: any) => boolean;

  // Handlers for accepting/canceling/clearing
  handleAccept: () => void;
  handleCancel: () => void;
  handleClear: () => void;
}

// Default views
const DEFAULT_VIEWS: readonly DatePickerView[] = ['day', 'month', 'year'];

/**
 * Hook to manage DateRangePicker state and logic.
 *
 * @param props DateRangePicker props
 * @returns State and handlers for the range picker
 */
export function useDateRangePicker(props: UseDateRangePickerParams): UseDateRangePickerResult {
  const {
    value: valueProp,
    defaultValue,
    onChange,
    open: openProp,
    onOpen,
    onClose,
    disabled = false,
    readOnly = false,
    minDate,
    maxDate,
    disableFuture = false,
    disablePast = false,
    shouldDisableDate,
    format = 'MM/DD/YYYY',
    closeOnSelect = true,
    rangePosition: rangePositionProp,
    defaultRangePosition = 'start',
    onRangePositionChange,
    autoFocus = false,
    views = DEFAULT_VIEWS,
    onViewChange,
  } = props;

  // Use the controlled value hook for the date range
  const [selectedRange, setSelectedRange] = useControlledValue<PickerRangeValue>(valueProp, defaultValue);

  // State for preview range (used during selection)
  const [previewRange, setPreviewRange] = React.useState<PickerRangeValue>([null, null]);

  // Use the range position hook
  const { rangePosition, setRangePosition } = useRangePosition({
    rangePosition: rangePositionProp,
    defaultRangePosition,
    onRangePositionChange,
  });

  // Use the open state hook
  const { open, anchorEl, handleOpen, handleClose } = useOpenState({
    open: openProp,
    onOpen,
    onClose,
    disabled,
    readOnly,
    autoFocus,
  });

  // Use the view management hook
  const {
    view: currentView,
    setView: setCurrentView,
    views: availableViews,
  } = useViews({
    openTo: 'day',
    views,
    onViewChange,
  });

  // Use the date validation hook
  const { validateDate } = useDateValidation({
    minDate,
    maxDate,
    disableFuture,
    disablePast,
    shouldDisableDate,
    format,
  });

  // Handle updating the range with validation
  const updateRange = useEventCallback((newRange: PickerRangeValue) => {
    if ((!newRange[0] || validateDate(newRange[0])) && (!newRange[1] || validateDate(newRange[1]))) {
      setSelectedRange(newRange);
      onChange?.(newRange);

      // Reset preview range
      setPreviewRange([null, null]);

      // Close picker on complete selection if configured
      if (newRange[0] && newRange[1] && closeOnSelect) {
        handleClose();
      }
    }
  });

  // Handle accepting the selected range
  const handleAccept = useEventCallback(() => {
    handleClose();
  });

  // Handle canceling the selection
  const handleCancel = useEventCallback(() => {
    handleClose();
  });

  // Handle clearing the selection
  const handleClear = useEventCallback(() => {
    updateRange([null, null]);
  });

  // Update preview range when opening picker
  React.useEffect(() => {
    if (open) {
      setPreviewRange([null, null]);

      // Auto focus the calendar when opening
      if (autoFocus) {
        setCurrentView('day');
      }
    }
  }, [open, autoFocus, setCurrentView]);

  return {
    selectedRange,
    previewRange,
    updateRange,
    rangePosition,
    setRangePosition,
    open,
    handleOpen,
    handleClose,
    anchorEl,
    currentView,
    setCurrentView,
    availableViews,
    disabled,
    readOnly,
    closeOnSelect,
    validateDate,
    handleAccept,
    handleCancel,
    handleClear,
  };
}
