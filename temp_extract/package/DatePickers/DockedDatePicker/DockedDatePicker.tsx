'use client';
import React, { useCallback } from 'react';
import { styled } from '@pigment-css/react';
import clsx from 'clsx';
import { ClickAwayListener } from '../../ClickAwayListener';
import { DockedDatePickerProps } from './DockedDatePicker.types';
import { DateCalendar } from '../DateCalendar';
import { PickerViewFooter } from '../PickerViewFooter';
import { Popper } from '../../Popper';
import { useDefaultDates } from '../hooks/useUtils';
import { DateField } from '../DateField';
import { CalendarIcon } from '../icons';
import { IconButton } from '../../IconButton';
import dockedDatePickerClasses from './DockedDatePicker.classes';
import { useDatePicker } from '../hooks/useDatePicker';

const CalendarContainer = styled('div')(({ theme }) => ({
  backgroundColor: theme.vars.palette.surfaceContainerHigh,
  border: `1px solid ${theme.vars.palette.outlineVariant}`,
  borderRadius: '12px',
}));

export const DockedDatePicker = React.forwardRef<HTMLDivElement, DockedDatePickerProps>((props, ref) => {
  const defaultDate = useDefaultDates();

  const {
    value,
    defaultValue,
    onChange,
    onClose,
    label,
    placeholder,
    disabled = false,
    error = false,
    helperText,
    format = 'DD/MM/YYYY',
    fullWidth = false,
    required = false,
    readOnly = false,
    size = 'medium',
    minDate = defaultDate.minDate,
    maxDate = defaultDate.maxDate,
    disableFuture = false,
    disablePast = false,
    shouldDisableDate,
    views = ['day', 'month', 'year'],
    firstDayOfWeek = 0,
    onViewChange,
    placement = 'bottom-start',
    todayText = 'Today',
    clearText = 'Clear',
    className,
    autoFocus = false,
    closeOnSelect = false,
    name,
    onOpen,
    inputRef: inputRefProp,
    endDecorator,
    ...textFieldProps
  } = props;

  // Use the date picker hook
  const {
    dateState,
    open,
    anchorEl,
    handleOpen,
    currentView,
    handleViewDateChange,
    handleDateChange,
    handleInputChange,
    handleViewChange,
    handleAccept,
    handleCancel,
    handleClear,
  } = useDatePicker({
    value,
    defaultValue,
    onChange,
    onOpen,
    onClose,
    views,
    minDate,
    maxDate,
    disableFuture,
    disablePast,
    shouldDisableDate,
    format,
    disabled,
    readOnly,
    autoFocus,
    closeOnSelect,
  });

  const handlePickerIconClicked = useCallback(() => {
    handleOpen();
  }, [handleOpen]);

  const openPickerButton = (
    <IconButton
      className="DateField-calendarIcon"
      variant="standard"
      onClick={handlePickerIconClicked}
      disabled={disabled || readOnly}
    >
      {endDecorator || <CalendarIcon />}
    </IconButton>
  );

  const baseFieldProps = {
    value: dateState.tempDate,
    onChange: handleInputChange,
    label,
    placeholder,
    format,
    disabled,
    fullWidth,
    required,
    size,
    readOnly,
    helperText,
    disableFuture,
    disablePast,
    maxDate,
    minDate,
    clearable: false,
  };

  const fieldProps = {
    onClick: handleOpen,
    className: dockedDatePickerClasses.inputField,
    endDecorator: openPickerButton,
    ...baseFieldProps,
    ...textFieldProps,
  };

  return (
    <React.Fragment>
      <div
        ref={ref}
        className={clsx(
          dockedDatePickerClasses.root,
          className,
          disabled && dockedDatePickerClasses.disabled,
          error && dockedDatePickerClasses.error,
        )}
      >
        <DateField {...fieldProps} />
      </div>
      <Popper
        className={dockedDatePickerClasses.popper}
        open={open}
        anchorEl={anchorEl}
        placement={placement}
        disablePortal={true}
      >
        <ClickAwayListener onClickAway={handleCancel}>
          <CalendarContainer>
            <DateCalendar
              className={dockedDatePickerClasses.calendarContainer}
              view={currentView}
              date={dateState.tempDate}
              viewDate={dateState.selectedDate}
              onViewDateChange={handleViewDateChange}
              onDateChange={handleDateChange}
              onViewChange={handleViewChange}
              disableFuture={disableFuture}
              disablePast={disablePast}
              shouldDisableDate={shouldDisableDate}
              minDate={minDate}
              maxDate={maxDate}
              disabled={disabled}
              readOnly={readOnly}
              variant={'docked'}
              viewStyle={'list'}
              views={views}
            />
            {currentView === 'day' && (
              <PickerViewFooter
                className={dockedDatePickerClasses.footer}
                onAccept={handleAccept}
                onCancel={handleCancel}
                onClear={handleClear}
                clearText={clearText}
                disabled={disabled || readOnly}
              />
            )}
          </CalendarContainer>
        </ClickAwayListener>
      </Popper>
    </React.Fragment>
  );
});
