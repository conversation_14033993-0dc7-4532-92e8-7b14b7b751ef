import { OverrideProps } from '@mui/types';
import { CreateSlotsAndSlotProps, SlotProps } from '../../types/slot';
import { SxProps } from '../../types/theme';

export type PickerViewFooterSlot = 'root';

export interface PickerViewFooterSlots {
  /**
   * The component that renders the root.
   * @default 'div'
   */
  root?: React.ElementType;
}

export type PickerViewFooterSlotsAndSlotProps = CreateSlotsAndSlotProps<
  PickerViewFooterSlots,
  {
    root: SlotProps<'div', object, PickerViewFooterOwnerState>;
  }
>;

export interface PickerViewFooterTypeMap<P = object, D extends React.ElementType = 'div'> {
  props: P &
    PickerViewFooterSlotsAndSlotProps & {
      /**
       * Callback fired when the clear button is clicked.
       */
      onClear?: () => void;
      /**
       * Callback fired when the cancel button is clicked.
       */
      onCancel?: () => void;
      /**
       * Callback fired when the accept/OK button is clicked.
       */
      onAccept?: () => void;
      /**
       * If `true`, the component is disabled.
       * @default false
       */
      disabled?: boolean;
      /**
       * Text to display on the clear button.
       * @default 'Clear'
       */
      clearText?: string;
      /**
       * Text to display on the cancel button.
       * @default 'Cancel'
       */
      cancelText?: string;
      /**
       * Text to display on the OK button.
       * @default 'OK'
       */
      okText?: string;
      /**
       * If `true`, the component is in a fullScreen mode.
       * @default false
       */
      fullScreen?: boolean;
      /**
       * The system prop that allows defining system overrides as well as additional CSS styles.
       */
      sx?: SxProps;
    };
  defaultComponent: D;
}

export type PickerViewFooterProps<
  D extends React.ElementType = PickerViewFooterTypeMap['defaultComponent'],
  P = { component?: React.ElementType },
> = OverrideProps<PickerViewFooterTypeMap<P, D>, D>;

export interface PickerViewFooterOwnerState extends PickerViewFooterProps {}
