import * as React from 'react';
import '@testing-library/jest-dom/vitest';
import { render, screen, cleanup, fireEvent } from '@testing-library/react';
import { describe, expect, it, afterEach, vi } from 'vitest';
import { PickerViewFooter } from './PickerViewFooter';
import pickerViewFooterClasses from './PickerViewFooter.classes';

afterEach(() => {
  cleanup();
});

describe('<PickerViewFooter />', () => {
  const defaultProps = {
    onClear: vi.fn(),
    onCancel: vi.fn(),
    onAccept: vi.fn(),
  };

  describe('renders correctly', () => {
    it('should render with default values', () => {
      const { container } = render(<PickerViewFooter {...defaultProps} />);
      expect(container.firstChild).toBeInTheDocument();

      // Check for buttons
      expect(screen.getByText('Clear')).toBeInTheDocument();
      expect(screen.getByText('Cancel')).toBeInTheDocument();
      expect(screen.getByText('OK')).toBeInTheDocument();
    });

    it('should render with custom button texts', () => {
      const { container } = render(
        <PickerViewFooter {...defaultProps} clearText="Reset" cancelText="Go back" okText="Confirm" />,
      );

      expect(screen.getByText('Reset')).toBeInTheDocument();
      expect(screen.getByText('Go back')).toBeInTheDocument();
      expect(screen.getByText('Confirm')).toBeInTheDocument();
    });

    it('should not render clear and cancel buttons if callbacks are not provided', () => {
      const { container } = render(<PickerViewFooter onAccept={defaultProps.onAccept} />);

      // OK button should always be rendered
      expect(screen.getByText('OK')).toBeInTheDocument();

      // Clear and Cancel buttons should not be rendered
      expect(screen.queryByText('Clear')).not.toBeInTheDocument();
      expect(screen.queryByText('Cancel')).not.toBeInTheDocument();
    });
  });

  describe('callbacks', () => {
    it('should call onClear when clear button is clicked', () => {
      render(<PickerViewFooter {...defaultProps} />);
      const clearButton = screen.getByText('Clear');
      fireEvent.click(clearButton);
      expect(defaultProps.onClear).toHaveBeenCalledTimes(1);
    });

    it('should call onCancel when cancel button is clicked', () => {
      render(<PickerViewFooter {...defaultProps} />);
      const cancelButton = screen.getByText('Cancel');
      fireEvent.click(cancelButton);
      expect(defaultProps.onCancel).toHaveBeenCalledTimes(1);
    });

    it('should call onAccept when OK button is clicked', () => {
      render(<PickerViewFooter {...defaultProps} />);
      const okButton = screen.getByText('OK');
      fireEvent.click(okButton);
      expect(defaultProps.onAccept).toHaveBeenCalledTimes(1);
    });
  });

  describe('disabled state', () => {
    it('should disable all buttons when disabled prop is true', () => {
      render(<PickerViewFooter {...defaultProps} disabled />);

      const buttons = screen.getAllByRole('button');
      buttons.forEach((button) => {
        expect(button).toBeDisabled();
      });
    });
  });
});
