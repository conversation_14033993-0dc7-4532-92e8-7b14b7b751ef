import {
  unstable_generateUtilityClass as generateUtilityClass,
  unstable_generateUtilityClasses as generateUtilityClasses,
} from '@mui/utils';

export interface DateRangeFieldClasses {
  /** Styles applied to the root element. */
  root: string;
  /** Styles applied to the input element. */
  input: string;
  /** Styles applied to the component when disabled. */
  disabled: string;
  /** Styles applied to the component when it has an error. */
  error: string;
}

export type DateRangeFieldClassKey = keyof DateRangeFieldClasses;

/**
 * Generates a utility class for the DateRangeField component
 * @param {string} slot - The class slot name
 * @returns {string} The generated class name
 */
export function getDateRangeFieldUtilityClass(slot: string): string {
  return generateUtilityClass('NovaDateRangeField', slot);
}

/**
 * Generated utility classes for the DateRangeField component
 */
const dateRangeFieldClasses: DateRangeFieldClasses = generateUtilityClasses('NovaDateRangeField', [
  'root',
  'input',
  'disabled',
  'error',
]);

export default dateRangeFieldClasses;
