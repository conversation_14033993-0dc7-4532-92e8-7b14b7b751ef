import {
  unstable_generateUtilityClasses as generateUtilityClasses,
  unstable_generateUtilityClass as generateUtilityClass,
} from '@mui/utils';

export interface DayCalendarClasses {
  /** Styles applied to the root element. */
  root: string;
  /** Styles applied to the component when disabled. */
  disabled: string;
}

export type DayCalendarClassKey = keyof DayCalendarClasses;

export function getDayCalendarUtilityClass(slot: string): string {
  return generateUtilityClass('NovaDayCalendar', slot);
}

export const dayCalendarClasses: DayCalendarClasses = generateUtilityClasses('NovaDayCalendar', ['root', 'disabled']);
