'use client';
import * as React from 'react';
import { OverridableComponent } from '@mui/types';
import { unstable_composeClasses as composeClasses, unstable_useForkRef as useForkRef } from '@mui/utils';
import useSlotProps from '@mui/utils/useSlotProps';
import { styled } from '@pigment-css/react';
import { findClosestEnabledDate } from '../utils/dateUtils';
import { DayCalendarOwnerState, DayCalendarProps, DayCalendarTypeMap } from './DayCalendar.types';
import { getDayCalendarUtilityClass } from './DayCalendar.classes';
import { PickerDateType } from '../models/pickers';
import { useNow, useUtils } from '../hooks/useUtils';
import { DatePickerDay } from '../DatePickerDay';

const useUtilityClasses = (ownerState: DayCalendarOwnerState) => {
  const { disabled } = ownerState;

  const slots = {
    root: ['root'],
    disabled: disabled ? ['disabled'] : [],
  };

  return composeClasses(slots, getDayCalendarUtilityClass, {});
};

const Root = styled('div')({
  display: 'flex',
  flexDirection: 'column',
  width: '100%',
  boxSizing: 'border-box',
  alignItems: 'center',
});

const WeekdayHeader = styled('div')(({ theme }) => ({
  display: 'grid',
  gridTemplateColumns: 'repeat(7, 1fr)',
  ...theme.typography.bodySmall,
  color: theme.vars.palette.onSurface,
  textAlign: 'center',
  height: '40px',
  alignItems: 'center',
  paddingInline: '24px',
  width: '100%',
  boxSizing: 'border-box',
}));

const CalendarGrid = styled('div')({
  display: 'grid',
  gridTemplateColumns: 'repeat(7, 1fr)',
  paddingInline: '24px',
  paddingBlock: '8px',
  width: '100%',
  boxSizing: 'border-box',
  minWidth: '328px',
});

// Create a separate WrappedDay component to handle day-specific props
function WrappedDay({
  day,
  slotProps,
  slots,
  isSelected,
  isToday,
  isDisabled,
  handleCalendarKeyDown,
  handleDateChange,
  selectedDayRef,
  children,
  outsideCurrentMonth = false,
  ownerState,
  ...other
}: {
  day: PickerDateType;
  slotProps: any;
  slots: any;
  isSelected: boolean;
  isToday: boolean;
  isDisabled: boolean;
  handleCalendarKeyDown: (event: React.KeyboardEvent) => void;
  handleDateChange: (date: PickerDateType) => void;
  selectedDayRef: React.RefObject<HTMLButtonElement>;
  children?: React.ReactNode;
  outsideCurrentMonth?: boolean;
  ownerState?: any;
}) {
  const SlotDay = slots.day ?? DatePickerDay;

  // Now we can safely use useSlotProps here since we're in a proper React component
  const dayProps = useSlotProps({
    elementType: SlotDay,
    externalSlotProps: slotProps.day,
    additionalProps: {
      ...other,
      day,
      selected: isSelected,
      today: isToday,
      disabled: isDisabled,
      onKeyDown: handleCalendarKeyDown,
      onDaySelect: handleDateChange,
      ref: isSelected ? selectedDayRef : undefined,
      'aria-selected': isSelected,
      'aria-current': isToday ? 'date' : undefined,
      tabIndex: isSelected ? 0 : -1,
      outsideCurrentMonth,
    },
    ownerState: {
      ...ownerState,
      day,
      selected: isSelected,
      today: isToday,
      disabled: isDisabled,
      outsideCurrentMonth,
    },
  });

  return <SlotDay {...dayProps}>{children}</SlotDay>;
}

export const CalendarWeekDay = ({ utils, now }) => {
  // Render weekdays
  const renderWeekDays = () => {
    const weekdays = [];
    const firstDayOfWeek = utils.getDayOfWeek?.(utils.startOfWeek(now)) || 0;

    for (let i = 0; i < 7; i++) {
      const day = (i + firstDayOfWeek) % 7;
      const dayDate = utils.addDays(utils.startOfWeek(now), i);
      const weekLabel = utils.format(dayDate, 'weekdayShort')[0];
      const weekName = utils.format(dayDate, 'weekday');
      weekdays.push(
        <div
          key={day}
          role="columnheader"
          aria-label={weekName}
          style={{
            width: 'auto',
            height: 'auto',
            aspectRatio: '1/1',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            boxSizing: 'border-box',
          }}
        >
          {weekLabel}
        </div>,
      );
    }

    return weekdays;
  };

  return <WeekdayHeader role="row">{renderWeekDays()}</WeekdayHeader>;
};

export const DayCalendar = React.forwardRef((props: DayCalendarProps, ref: React.ForwardedRef<HTMLDivElement>) => {
  const {
    date,
    viewDate,
    handleDateChange,
    disableFuture,
    disablePast,
    minDate,
    maxDate,
    shouldDisableDate,
    disabled = false,
    readOnly = false,
    displayWeekNumber = true,
    component,
    slots = {},
    slotProps = {},
    ...other
  } = props;

  const utils = useUtils();
  const now = useNow();
  const selectedDayRef = React.useRef<HTMLButtonElement>(null);
  const [isKeyboardNavigation, setIsKeyboardNavigation] = React.useState(false);

  const handleRef = useForkRef(ref, null);
  const ownerState = {
    ...props,
  };

  const classes = useUtilityClasses(ownerState);
  const SlotRoot = slots.root ?? Root;

  const rootProps = useSlotProps({
    elementType: SlotRoot,
    externalSlotProps: slotProps.root,
    externalForwardedProps: other,
    additionalProps: {
      ref: handleRef,
      as: component,
    },
    ownerState,
    className: classes.root,
  });

  // Focus the selected day button when it changes due to keyboard navigation
  React.useEffect(() => {
    if (isKeyboardNavigation && selectedDayRef.current) {
      selectedDayRef.current.focus();
      setIsKeyboardNavigation(false);
    }
  }, [date, isKeyboardNavigation]);

  // Keyboard navigation
  const handleCalendarKeyDown = React.useCallback(
    (event: React.KeyboardEvent) => {
      const { key } = event;

      const currentDate = date || now;
      let newDate: PickerDateType | null = null;

      switch (key) {
        case 'ArrowLeft': {
          // Move to previous day, crossing month boundary if needed
          const prevDay = utils.addDays(currentDate, -1);
          // If we're crossing to the previous month
          if (utils.getMonth(prevDay) !== utils.getMonth(currentDate)) {
            // Find the closest enabled date between the direct prev day and prev month end
            newDate = findClosestEnabledDate({
              date: prevDay,
              disableFuture,
              disablePast,
              minDate,
              maxDate,
              isDateDisabled: (date) =>
                (disablePast && utils.isBefore(date, now)) || (disableFuture && utils.isAfter(date, now)),
              utils,
            });
          } else {
            newDate = prevDay;
          }
          event.preventDefault();
          break;
        }
        case 'ArrowRight': {
          // Move to next day, crossing month boundary if needed
          const nextDay = utils.addDays(currentDate, 1);
          // If we're crossing to the next month
          if (utils.getMonth(nextDay) !== utils.getMonth(currentDate)) {
            // Find the closest enabled date between the direct next day and next month start
            newDate = findClosestEnabledDate({
              date: nextDay,
              disableFuture,
              disablePast,
              minDate,
              maxDate,
              isDateDisabled: (date) =>
                (disablePast && utils.isBefore(date, now)) || (disableFuture && utils.isAfter(date, now)),
              utils,
            });
          } else {
            newDate = nextDay;
          }
          event.preventDefault();
          break;
        }
        case 'ArrowUp': {
          // Move up a week, crossing month boundary if needed
          const prevWeek = utils.addDays(currentDate, -7);
          newDate = prevWeek;
          event.preventDefault();
          break;
        }
        case 'ArrowDown': {
          // Move down a week, crossing month boundary if needed
          const nextWeek = utils.addDays(currentDate, 7);
          newDate = nextWeek;
          event.preventDefault();
          break;
        }
        case 'Home':
          // Move to start of week
          newDate = utils.startOfWeek(currentDate);
          event.preventDefault();
          break;
        case 'End':
          // Move to end of week
          newDate = utils.endOfWeek(currentDate);
          event.preventDefault();
          break;
        case 'PageUp':
          // Move to the same day in the previous month
          newDate = utils.addMonths(currentDate, -1);
          event.preventDefault();
          break;
        case 'PageDown':
          // Move to the same day in the next month
          newDate = utils.addMonths(currentDate, 1);
          event.preventDefault();
          break;
        default:
          return;
      }

      if (!newDate) {
        return;
      }

      // Mark that this change was from keyboard navigation
      setIsKeyboardNavigation(true);
      // Update the date
      handleDateChange(newDate);
    },
    [date, handleDateChange, utils, disableFuture, disablePast, minDate, maxDate, now],
  );

  // Render days of the month
  const renderDays = (month: PickerDateType) => {
    // Get a properly structured array of weeks for the month
    const weeksOfMonth = utils.getWeekArray(month);
    const days = [];

    // For each week
    weeksOfMonth.forEach((week, weekIndex) => {
      // For each day in the week
      week.forEach((day, dayIndex) => {
        const dayInCurrentMonth = utils.isSameMonth(day, month);

        if (dayInCurrentMonth) {
          // Current month day - render day button
          const dayNumber = utils.getDate(day);

          const isSelected =
            props.selectedDays?.some((selectedDay) => selectedDay !== null && utils.isSameDay(selectedDay, day)) ||
            (date !== null && utils.isSameDay(date, day)) ||
            false;

          const isToday = utils.isSameDay(day, now);
          const isDisabled =
            (minDate && utils.isBefore(day, minDate)) ||
            (maxDate && utils.isAfter(day, maxDate)) ||
            (disablePast && utils.isBeforeDay(day, now)) ||
            (disableFuture && utils.isAfterDay(day, now)) ||
            (shouldDisableDate && shouldDisableDate(day));

          days.push(
            <WrappedDay
              key={`day-${weekIndex}-${dayIndex}`}
              day={day}
              slotProps={slotProps}
              slots={slots}
              isSelected={isSelected}
              isToday={isToday}
              isDisabled={isDisabled || disabled || readOnly}
              handleCalendarKeyDown={handleCalendarKeyDown}
              handleDateChange={handleDateChange}
              selectedDayRef={selectedDayRef}
              outsideCurrentMonth={false}
              ownerState={ownerState}
            >
              {dayNumber}
            </WrappedDay>,
          );
        } else {
          // Day outside current month - render empty cell
          days.push(
            <div
              key={`empty-${weekIndex}-${dayIndex}`}
              style={{
                width: 'auto',
                height: 'auto',
                aspectRatio: '1/1',
                boxSizing: 'border-box',
              }}
            />,
          );
        }
      });
    });

    return days;
  };

  return (
    <SlotRoot {...rootProps}>
      {/* Weekday header */}
      {displayWeekNumber && <CalendarWeekDay utils={utils} now={now} />}

      {/* Calendar grid */}
      <CalendarGrid role="grid">{renderDays(viewDate)}</CalendarGrid>
    </SlotRoot>
  );
}) as OverridableComponent<DayCalendarTypeMap>;
