import { DateAdapter } from '../models/adapter';
import { FieldFormatTokenInfo, FieldFormatTokenMap, FieldSectionData, FieldSectionType } from '../models/dateSection';
import dayjs from 'dayjs/esm';

// Helper interface to track section positions
export interface SectionWithPosition extends FieldSectionData {
  startIndex: number;
  endIndex: number;
  // Input positions account for invisible unicode characters
  startInInput: number;
  endInInput: number;
  separatorStart?: number;
  separatorEnd?: number;
  startSeparator?: string;
}

// CharacterQuery interface for accumulating keystrokes
export type CharacterQuery = string;

// Add cleanString helper similar to MUI's implementation
export const cleanString = (dirtyString: string): string =>
  dirtyString.replace(/[\u2066\u2067\u2068\u2069\u200e]/g, '');

// Comprehensive format token map similar to MUI's approach
const formatTokenMap: FieldFormatTokenMap = {
  // Year
  YY: 'year',
  YYYY: { type: 'year', contentType: 'digit', maxLength: 4 },

  // Month
  M: { type: 'month', contentType: 'digit', maxLength: 2 },
  MM: 'month',
  MMM: { type: 'month', contentType: 'letter' },
  MMMM: { type: 'month', contentType: 'letter' },

  // Day of the month
  D: { type: 'day', contentType: 'digit', maxLength: 2 },
  DD: 'day',

  // Day of the week (not fully implemented in our simple version)
  d: { type: 'weekDay', contentType: 'digit', maxLength: 1 },
  dd: { type: 'weekDay', contentType: 'letter' },
  ddd: { type: 'weekDay', contentType: 'letter' },
  dddd: { type: 'weekDay', contentType: 'letter' },

  // Hours
  H: { type: 'hours', contentType: 'digit', maxLength: 2 },
  HH: 'hours',
  h: { type: 'hours', contentType: 'digit', maxLength: 2 },
  hh: 'hours',

  // Minutes
  m: { type: 'minutes', contentType: 'digit', maxLength: 2 },
  mm: 'minutes',

  // Seconds
  s: { type: 'seconds', contentType: 'digit', maxLength: 2 },
  ss: 'seconds',

  // Meridiem
  A: 'meridiem',
  a: 'meridiem',
};

// Get token config information with proper defaults
export const getTokenConfig = (token: string): FieldFormatTokenInfo => {
  const config = formatTokenMap[token];

  if (!config) {
    // Default to treating unknown tokens as separators
    return { type: 'day', contentType: 'digit', maxLength: 2 };
  }

  if (typeof config === 'string') {
    // If the config is just a string (section type), provide defaults
    return {
      type: config,
      contentType: ['hours', 'minutes', 'seconds', 'day', 'month', 'year'].includes(config) ? 'digit' : 'letter',
      maxLength: ['hours', 'minutes', 'seconds', 'day', 'month'].includes(config) ? 2 : 4,
    };
  }

  return config;
};

// Enhanced version of findFormatTokens following MUI's approach to handle escaped parts
export const findFormatTokens = (format: string): Array<{ token: string; index: number }> => {
  const tokenList = Object.keys(formatTokenMap).sort((a, b) => b.length - a.length); // Sort by length (longer first)

  const result: Array<{ token: string; index: number }> = [];

  // Handle escaped characters first (anything inside single quotes)
  const escapedParts: Array<{ start: number; end: number }> = [];
  const escapeRegExp = /'([^']*?)'/g;
  let escapeMatch: RegExpExecArray | null;

  // Find all escaped parts
  while ((escapeMatch = escapeRegExp.exec(format))) {
    escapedParts.push({ start: escapeMatch.index, end: escapeMatch.index + escapeMatch[0].length - 1 });
  }

  // Helper function to check if a position is within escaped parts
  const isEscaped = (position: number): boolean => {
    return escapedParts.some((part) => position >= part.start && position <= part.end);
  };

  // Find all tokens
  let i = 0;
  while (i < format.length) {
    // Skip escaped parts
    if (isEscaped(i)) {
      i++;
      continue;
    }

    // Try to match a token at the current position
    let matched = false;
    for (const token of tokenList) {
      if (format.substring(i, i + token.length) === token) {
        result.push({ token, index: i });
        i += token.length;
        matched = true;
        break;
      }
    }

    // If no token matched, move to the next character
    if (!matched) {
      i++;
    }
  }

  // Sort tokens by their position in the format
  return result.sort((a, b) => a.index - b.index);
};

/**
 * Clean separator for display by removing unwanted characters
 */
export const cleanSeparator = (separator: string): string => {
  // Remove any single quotes (used for escaping in format)
  return separator.replace(/'/g, '');
};

/**
 * Extract sections and separators from a format string
 */
export const parseFormatIntoSections = (
  format: string,
): Array<{
  token: string;
  type: FieldSectionType;
  separator: string;
  index: number;
  trailingSeparator?: string;
}> => {
  const tokens = findFormatTokens(format);
  const sections: Array<{
    token: string;
    type: FieldSectionType;
    separator: string;
    index: number;
    trailingSeparator?: string;
  }> = [];

  tokens.forEach((tokenInfo, index) => {
    const { token, index: tokenIndex } = tokenInfo;
    const config = getTokenConfig(token);

    // Calculate the separator (text between this token and the previous one)
    let separator = '';
    if (index === 0) {
      // First token - separator is everything before it
      separator = format.substring(0, tokenIndex);
    } else {
      const prevTokenEnd = tokens[index - 1].index + tokens[index - 1].token.length;
      separator = format.substring(prevTokenEnd, tokenIndex);
    }

    // Ensure separator is properly identified and separated
    sections.push({
      token,
      type: config.type,
      separator: cleanSeparator(separator), // Clean any quotes from the separator
      index: tokenIndex,
    });
  });

  // Add trailing separator if there is any content after the last token
  if (tokens.length > 0) {
    const lastToken = tokens[tokens.length - 1];
    const lastTokenEnd = lastToken.index + lastToken.token.length;

    if (lastTokenEnd < format.length) {
      // There's content after the last token - add it as trailing separator to the last section
      const trailingSeparator = format.substring(lastTokenEnd);
      if (sections.length > 0) {
        // Separators should always be before a section, not after
        // Store this for potential future use but don't add it to the section
        sections[sections.length - 1].trailingSeparator = cleanSeparator(trailingSeparator);
      }
    }
  }

  return sections;
};

/**
 * Expands format shortcuts into full format strings
 * Similar to MUI's expandFormat
 */
export const expandDateFormat = (utils: any, format: string): string => {
  // Expansion safety counter
  let formatExpansionOverflow = 10;
  let prevFormat = format;
  let nextFormat = utils.expandFormat ? utils.expandFormat(format) : format;

  // Keep expanding until the format doesn't change anymore
  while (nextFormat !== prevFormat) {
    prevFormat = nextFormat;
    nextFormat = utils.expandFormat ? utils.expandFormat(prevFormat) : prevFormat;
    formatExpansionOverflow -= 1;
    if (formatExpansionOverflow < 0) {
      console.warn('Format expansion seems to be in an infinite loop', format);
      return prevFormat;
    }
  }

  return nextFormat;
};

/**
 * Finds parts of the format string that are escaped
 * Based on MUI's implementation
 */
export const getEscapedPartsFromFormat = (
  utils: any,
  expandedFormat: string,
): Array<{ start: number; end: number }> => {
  const escapedParts: Array<{ start: number; end: number }> = [];

  // If the utils doesn't define escaped characters, use defaults
  const startChar = utils.escapedCharacters?.start || "'";
  const endChar = utils.escapedCharacters?.end || "'";

  // Create a regex to find all escaped sequences
  const regExp = new RegExp(`(\\${startChar}[^\\${endChar}]*\\${endChar})+`, 'g');

  let match: RegExpExecArray | null = null;
  // Find all matches in the format string
  while ((match = regExp.exec(expandedFormat)) !== null) {
    escapedParts.push({ start: match.index, end: regExp.lastIndex - 1 });
  }

  return escapedParts;
};

/**
 * Gets section type and configuration from a format token
 * Similar to MUI's getDateSectionConfigFromFormatToken
 */
export const getSectionTypeFromFormatToken = (
  token: string,
): {
  type: FieldSectionType;
  contentType: 'digit' | 'letter' | 'digit-with-letter';
} => {
  // Year tokens
  if (['YYYY', 'YY', 'Y'].includes(token)) {
    return { type: 'year', contentType: 'digit' };
  }

  // Month tokens
  if (['MMMM', 'MMM'].includes(token)) {
    return { type: 'month', contentType: 'letter' };
  }
  if (['MM', 'M'].includes(token)) {
    return { type: 'month', contentType: 'digit' };
  }

  // Day tokens
  if (['DD', 'D'].includes(token)) {
    return { type: 'day', contentType: 'digit' };
  }

  // Weekday tokens
  if (['dddd', 'ddd', 'dd', 'd'].includes(token)) {
    return { type: 'weekDay', contentType: 'letter' };
  }

  // Hour tokens (24-hour)
  if (['HH', 'H'].includes(token)) {
    return { type: 'hours', contentType: 'digit' };
  }

  // Hour tokens (12-hour)
  if (['hh', 'h'].includes(token)) {
    return { type: 'hours', contentType: 'digit' };
  }

  // Minute tokens
  if (['mm', 'm'].includes(token)) {
    return { type: 'minutes', contentType: 'digit' };
  }

  // Second tokens
  if (['ss', 's'].includes(token)) {
    return { type: 'seconds', contentType: 'digit' };
  }

  // AM/PM tokens
  if (['A', 'a'].includes(token)) {
    return { type: 'meridiem', contentType: 'digit-with-letter' };
  }

  // Default for unrecognized tokens
  return { type: 'empty', contentType: 'digit' };
};

/**
 * Get maximum length for a section based on its type
 */
export const getSectionMaxLength = (type: FieldSectionType, token: string): number => {
  switch (type) {
    case 'year':
      return token === 'YYYY' ? 4 : 2;
    case 'month':
      return token.length > 2 ? 9 : 2; // Text months can be longer
    case 'day':
      return 2;
    case 'weekDay':
      return 10; // Full weekday names can be long
    case 'hours':
      return 2;
    case 'minutes':
      return 2;
    case 'seconds':
      return 2;
    case 'meridiem':
      return 2;
    default:
      return 2;
  }
};

// Create an internal extended version of FieldSectionData for our processing
interface ExtendedFieldSectionData extends FieldSectionData {
  startSeparator?: string; // Add this for leading separators
}

/**
 * Post-process sections to add input positions and handle formatDensity
 * Following MUI's approach to accurately track positions
 */
export const addPositionPropertiesToSections = (
  sections: ExtendedFieldSectionData[],
  options: {
    isRtl?: boolean;
    formatDensity?: 'dense' | 'spacious';
  } = {},
): SectionWithPosition[] => {
  const { formatDensity = 'dense', isRtl = false } = options;

  const sectionsWithPositions: SectionWithPosition[] = sections.map((section) => ({
    ...section,
    startInInput: 0,
    endInInput: 0,
    startIndex: 0,
    endIndex: 0,
  }));

  // Now calculate positions - this is critical for correct section selection
  let currentPosition = 0;

  sectionsWithPositions.forEach((section, index) => {
    // Start by handling any leading separator for the first section
    if (index === 0 && section.startSeparator) {
      currentPosition += section.startSeparator.length;
    }

    // Start position of this section's content
    section.startInInput = currentPosition;

    // Get the displayed value (actual value or placeholder)
    const displayValue = section.value || section.placeholder || '';

    // End position is start + length - 1 (for correct indexing)
    section.endInInput = section.startInInput + displayValue.length - 1;

    // Move current position past this section's content
    currentPosition = section.endInInput + 1;

    // If this section has a separator after it, track it and update position
    if (section.separator) {
      section.separatorStart = currentPosition;

      // Apply format density to separators if needed
      let sectionSeparator = section.separator;
      if (formatDensity === 'spacious' && ['/', '.', '-'].includes(sectionSeparator)) {
        sectionSeparator = ` ${sectionSeparator} `;
        section.separator = sectionSeparator; // Save this modified separator
      }

      // For RTL handling
      if (isRtl && sectionSeparator) {
        sectionSeparator = `\u2069${sectionSeparator}\u2066`;
        section.separator = sectionSeparator; // Save this modified separator
      }

      // Update position after adding separator
      currentPosition += sectionSeparator.length;
      section.separatorEnd = currentPosition - 1;
    }
  });

  return sectionsWithPositions;
};

/**
 * Enhanced implementation of parseDateSections, based on MUI's buildSectionsFromFormat
 * This handles format expansion, better token extraction, and improved section positioning
 */
export const parseDateSections = (
  format: string,
  value: string,
  options: {
    isRtl?: boolean;
    formatDensity?: 'dense' | 'spacious';
  } = {},
): FieldSectionData[] => {
  // Default options
  const { formatDensity = 'dense', isRtl = false } = options;
  const utils = getUtils();

  // Step 1: Expand the format
  const expandedFormat = expandDateFormat(utils, format);

  // Step 2: Find escaped parts in format
  const escapedParts = getEscapedPartsFromFormat(utils, expandedFormat);

  // Step 3: Build sections from the expanded format
  const sections: ExtendedFieldSectionData[] = [];
  let startSeparator = '';

  // Get potential date values from input value string
  let dateValues: Record<FieldSectionType, string | null> = {
    year: null,
    month: null,
    day: null,
    weekDay: null,
    hours: null,
    minutes: null,
    seconds: null,
    meridiem: null,
    empty: null,
  };

  if (value) {
    try {
      // Try to extract values from input using the format
      const date = utils.parse(value, format);
      if (utils.isValid(date)) {
        dateValues = {
          year: utils.getYear(date).toString(),
          month: (utils.getMonth(date) + 1).toString().padStart(2, '0'),
          day: utils.getDate(date).toString().padStart(2, '0'),
          weekDay: null, // Usually derived from other values
          hours: utils.getHours(date).toString().padStart(2, '0'),
          minutes: utils.getMinutes(date).toString().padStart(2, '0'),
          seconds: utils.getSeconds(date).toString().padStart(2, '0'),
          meridiem: utils.getHours(date) >= 12 ? 'PM' : 'AM',
          empty: null,
        };
      } else {
        // Try extractSectionsFromValueString if available
        if (typeof extractSectionsFromValueString === 'function') {
          const extractedSections = extractSectionsFromValueString(value, format, utils);
          // Merge extracted sections with our dateValues
          Object.keys(extractedSections).forEach((key) => {
            if (extractedSections[key as FieldSectionType] !== null) {
              dateValues[key as FieldSectionType] = extractedSections[key as FieldSectionType];
            }
          });
        } else {
          // Fallback to regex extraction if parsing fails
          // Try to extract using common format patterns
          let match = value.match(/(\d{4})[-./](\d{1,2})[-./](\d{1,2})/);
          if (match) {
            dateValues.year = match[1];
            dateValues.month = match[2].padStart(2, '0');
            dateValues.day = match[3].padStart(2, '0');
          }

          // MM/DD/YYYY or DD/MM/YYYY
          if (!match) {
            match = value.match(/(\d{1,2})[-./](\d{1,2})[-./](\d{4})/);
            if (match) {
              // Use format to determine if it's MM/DD or DD/MM
              if (format.indexOf('M') < format.indexOf('D')) {
                dateValues.month = match[1].padStart(2, '0');
                dateValues.day = match[2].padStart(2, '0');
              } else {
                dateValues.day = match[1].padStart(2, '0');
                dateValues.month = match[2].padStart(2, '0');
              }
              dateValues.year = match[3];
            }
          }

          // Extract time part HH:MM:SS AM/PM
          match = value.match(/(\d{1,2}):(\d{1,2})(?::(\d{1,2}))?(?:\s*([aApP][mM])?)/);
          if (match) {
            dateValues.hours = match[1].padStart(2, '0');
            dateValues.minutes = match[2].padStart(2, '0');
            if (match[3]) dateValues.seconds = match[3].padStart(2, '0');
            if (match[4]) dateValues.meridiem = match[4].toUpperCase();
          }
        }
      }
    } catch (error) {
      console.error('Error parsing date value:', error);
    }
  }

  // Get all possible tokens from format
  const validTokens = [
    'YYYY',
    'YY',
    'Y',
    'MMMM',
    'MMM',
    'MM',
    'M',
    'DD',
    'D',
    'dddd',
    'ddd',
    'dd',
    'd',
    'HH',
    'H',
    'hh',
    'h',
    'mm',
    'm',
    'ss',
    's',
    'A',
    'a',
  ].sort((a, b) => b.length - a.length); // Sort by length to match longer tokens first

  const regExpFirstWordInFormat = /^([a-zA-Z]+)/;
  const regExpWordOnlyComposedOfTokens = new RegExp(`^(${validTokens.join('|')})*$`);
  const regExpFirstTokenInWord = new RegExp(`^(${validTokens.join('|')})`);

  // Helper to check if a character is part of an escaped sequence
  const isCharEscaped = (charIndex: number): boolean => {
    return escapedParts.some((part) => part.start <= charIndex && part.end >= charIndex);
  };

  // Process the expanded format character by character
  let i = 0;
  let sectionIndex = 0;
  while (i < expandedFormat.length) {
    // Skip escaped characters
    if (isCharEscaped(i)) {
      i++;
      continue;
    }

    // Try to match a token at the current position
    const remainder = expandedFormat.slice(i);
    const firstWordInFormat = regExpFirstWordInFormat.exec(remainder)?.[1];

    // If we have a word and it's composed entirely of tokens, extract those tokens
    if (firstWordInFormat && regExpWordOnlyComposedOfTokens.test(firstWordInFormat)) {
      let word = firstWordInFormat;

      while (word.length > 0) {
        // Extract the first token in the word
        const firstToken = regExpFirstTokenInWord.exec(word)![1];
        word = word.slice(firstToken.length);

        // Get the section type from the token
        const sectionType = getSectionTypeFromFormatToken(firstToken).type;

        // Get the value for this section type from dateValues
        const sectionValue = dateValues[sectionType] || '';

        // Create and add the section with careful separator handling
        const section = createSectionFromToken(utils, firstToken, sectionValue, startSeparator, sectionIndex);

        // Reset the start separator after adding it to a section
        section.startSeparator = startSeparator;
        startSeparator = '';

        sections.push(section);
        sectionIndex++;
      }

      i += firstWordInFormat.length;
    }
    // If not a token, collect characters as separators
    else {
      const char = expandedFormat[i];

      if (sections.length === 0) {
        // If no sections yet, this is a leading separator
        startSeparator += char;
      } else {
        // Add as end separator to the last section
        // This ensures separators belong to the previous section, not the next one
        const lastSection = sections[sections.length - 1];
        lastSection.separator = (lastSection.separator || '') + char;
      }

      i++;
    }
  }

  // Handle case where we only have separators and no sections
  if (sections.length === 0 && startSeparator.length > 0) {
    sections.push({
      type: 'empty',
      contentType: 'letter',
      value: '',
      displayValue: startSeparator,
      placeholder: startSeparator,
      maxLength: null,
      separator: '',
      token: '',
      startIndex: 0,
      endIndex: 0,
      invalid: false,
      modified: false,
    });
  }

  // Get positioned sections using the dedicated helper
  return addPositionPropertiesToSections(sections, options);
};

/**
 * Convert sections back to date string with improved handling
 */
export const sectionsToDateString = (
  sections: FieldSectionData[],
  format: string,
  options: {
    isRtl?: boolean;
    formatDensity?: 'dense' | 'spacious';
  } = {},
): string => {
  // Validity check - we need at least some sections with values
  if (!sections || sections.length === 0) {
    return '';
  }

  // Check if we have enough sections with values to create a valid date
  const hasValidSections = sections.some((section) => !!section.value);
  if (!hasValidSections) {
    return '';
  }

  // For all formats, use the generic implementation
  const formatSections = parseFormatIntoSections(format);
  let result = '';

  // Helper to clean RTL markers from text
  const cleanRtlMarkers = (text: string): string => {
    return text.replace(/\u2066|\u2067\u2068|\u2069/g, '');
  };

  // Map our sections to the format sections
  const sectionMap = new Map();
  sections.forEach((section) => {
    const formatSection = formatSections.find((fs) => fs.type === section.type);
    if (formatSection) {
      sectionMap.set(formatSection.type, section);
    }
  });

  // First, handle any text that appears before the first format token
  if (formatSections.length > 0 && formatSections[0].separator) {
    result += formatSections[0].separator;
  }

  // Build the result by following the exact format with its separators
  formatSections.forEach((formatSection, index) => {
    // Get the section data matching this format section
    const section = sectionMap.get(formatSection.type);

    if (section) {
      // Add the section value or placeholder if it has a value
      if (section.value) {
        result += section.value;
      } else {
        // For empty sections, use the format token to maintain consistency
        result += formatSection.token;
      }
    } else {
      // If no matching section found, use the format token
      result += formatSection.token;
    }

    // Add separator after this section (if not the last section)
    if (index < formatSections.length - 1) {
      const nextFormatSection = formatSections[index + 1];
      result += nextFormatSection.separator;
    } else if (formatSection.trailingSeparator) {
      // Add any trailing separator
      result += formatSection.trailingSeparator;
    }
  });

  return cleanRtlMarkers(result);
};

/**
 * Clean a digit value based on section boundaries
 * Ensures the value is within valid range and properly formatted
 */
export const cleanDigitValue = (type: FieldSectionType, value: string, options: { pad?: boolean } = {}): string => {
  // If empty value, return empty string
  if (!value) return '';

  // Convert value to number for validation
  const numericValue = parseInt(value, 10);

  // If not a valid number, return empty string
  if (isNaN(numericValue)) {
    return '';
  }

  // Create default minimum and maximum based on section type
  let minimum = 0;
  let maximum = 99;

  // Set appropriate min/max based on section type
  switch (type) {
    case 'year':
      minimum = 0;
      maximum = 9999;
      break;
    case 'month':
      minimum = 1;
      maximum = 12;
      break;
    case 'day':
      minimum = 1;
      maximum = 31;
      break;
    case 'hours':
      minimum = 0;
      maximum = 23;
      break;
    case 'minutes':
    case 'seconds':
      minimum = 0;
      maximum = 59;
      break;
    case 'meridiem':
      minimum = 0;
      maximum = 1;
      break;
    case 'weekDay':
      minimum = 0;
      maximum = 6;
      break;
  }

  // Clamp value to valid range
  const clampedValue = Math.max(minimum, Math.min(maximum, numericValue));

  // Format result based on section type
  if (type === 'meridiem') {
    return clampedValue === 0 ? 'AM' : 'PM';
  }

  // For other sections, optionally pad to 2 digits
  let result = clampedValue.toString();

  // Add leading zero for month, day, hours, minutes, seconds if needed
  const needsPadding = ['month', 'day', 'hours', 'minutes', 'seconds'].includes(type);
  if ((options.pad || needsPadding) && result.length === 1) {
    result = `0${result}`;
  }

  return result;
};

/**
 * Function to build the visible string value in the input field from sections
 */
export const getInputValueFromSections = (
  sectionsWithPositions: SectionWithPosition[],
  options: { includeRTLCharacters?: boolean; cleanStr?: boolean } = {},
): string => {
  // Default the options
  const { includeRTLCharacters = true, cleanStr = false } = options;

  let result = '';

  // Build input value from sections
  sectionsWithPositions.forEach((section, index) => {
    // Add separator before section (if not the first section)
    if (index > 0 && sectionsWithPositions[index - 1].separator) {
      result += sectionsWithPositions[index - 1].separator;
    }

    // Determine the value to display (actual value or placeholder)
    const displayValue = section.value || section.placeholder || '';

    // MUI-compatible handling of single digit values (no auto-padding)
    const isSingleDigit =
      section.value &&
      section.value.length === 1 &&
      section.type !== 'year' &&
      ['day', 'month', 'hours', 'minutes', 'seconds'].includes(section.type);

    // MUI handles format padding differently - it doesn't automatically add padding
    // We'll add invisible space only if needed for selection handling
    if (isSingleDigit) {
      result += `${displayValue}\u200e`; // Add invisible marker for selection handling
    } else {
      result += displayValue;
    }
  });

  // Optionally wrap with RTL isolation characters (for proper display of some locales)
  if (includeRTLCharacters) {
    result = `\u2066${result}\u2069`;
  }

  // Optionally clean the string
  if (cleanStr) {
    result = cleanString(result);
  }

  return result;
};

/**
 * Similar to MUI's mergeDateIntoReferenceDate but as a standalone utility
 */
export const mergeParsedDateWithReference = (
  utils: any,
  parsedDate: any,
  referenceDate: any,
  sectionTypes: string[],
) => {
  if (!parsedDate || !utils.isValid(parsedDate)) return referenceDate;
  if (!referenceDate || !utils.isValid(referenceDate)) return parsedDate;

  // Prioritize sections in the correct order
  const sectionOrder = {
    year: 1,
    month: 2,
    day: 3,
    weekDay: 4,
    hours: 5,
    minutes: 6,
    seconds: 7,
    meridiem: 8,
  };

  // Sort the section types by their priority
  const sortedSectionTypes = [...sectionTypes].sort((a, b) => (sectionOrder[a] || 99) - (sectionOrder[b] || 99));

  // Apply each date component in order
  return sortedSectionTypes.reduce((mergedDate, sectionType) => {
    switch (sectionType) {
      case 'year':
        return utils.setYear(mergedDate, utils.getYear(parsedDate));
      case 'month':
        return utils.setMonth(mergedDate, utils.getMonth(parsedDate));
      case 'day':
        return utils.setDate(mergedDate, utils.getDate(parsedDate));
      case 'hours':
        return utils.setHours(mergedDate, utils.getHours(parsedDate));
      case 'minutes':
        return utils.setMinutes(mergedDate, utils.getMinutes(parsedDate));
      case 'seconds':
        return utils.setSeconds(mergedDate, utils.getSeconds(parsedDate));
      case 'meridiem': {
        const isPM = utils.getHours(parsedDate) >= 12;
        const currentHours = utils.getHours(mergedDate);
        const is12HoursFormat = currentHours >= 12;

        if (isPM && !is12HoursFormat) {
          return utils.setHours(mergedDate, currentHours + 12);
        }
        if (!isPM && is12HoursFormat) {
          return utils.setHours(mergedDate, currentHours - 12);
        }
        return mergedDate;
      }
      default:
        return mergedDate;
    }
  }, referenceDate);
};

// Add this function to determine which sections are in a given format
export const getSectionsFromFormat = (format: string) => {
  const sections: string[] = [];

  // Common format tokens
  const tokens = {
    year: ['YYYY', 'YY'],
    month: ['MM', 'M', 'MMMM', 'MMM'],
    day: ['DD', 'D', 'Do'],
    weekDay: ['dddd', 'ddd', 'dd', 'd'],
    hours: ['HH', 'H', 'hh', 'h'],
    minutes: ['mm', 'm'],
    seconds: ['ss', 's'],
    meridiem: ['A', 'a'],
  };

  // Check each token type
  Object.entries(tokens).forEach(([sectionType, formatTokens]) => {
    for (const token of formatTokens) {
      if (format.includes(token)) {
        sections.push(sectionType);
        break;
      }
    }
  });

  return sections;
};

// Add specialized format detection for better character editing
export const getSectionFormatPatterns = (): Record<FieldSectionType, string[]> => {
  return {
    year: ['YYYY', 'YY', 'yyyy', 'yy'],
    month: ['MM', 'M', 'MMMM', 'MMM'],
    day: ['DD', 'D', 'dd', 'd'],
    weekDay: ['dddd', 'ddd', 'dd', 'd', 'EEEE', 'EEE', 'E'],
    hours: ['HH', 'H', 'hh', 'h'],
    minutes: ['mm', 'm'],
    seconds: ['ss', 's'],
    meridiem: ['A', 'a', 'AM/PM', 'am/pm'],
    empty: [],
  };
};

// Add function to detect if a format string contains a specific section type
export const formatContainsSection = (format: string, sectionType: FieldSectionType): boolean => {
  const patterns = getSectionFormatPatterns()[sectionType];

  // Check if any pattern appears in the format
  return patterns.some((pattern) => format.includes(pattern));
};

// Add function to extract multiple sections from a value string based on format
export const extractSectionsFromValueString = (
  valueStr: string,
  format: string,
  utils: any,
): Record<FieldSectionType, string | null> => {
  // Initialize empty result
  const result: Record<FieldSectionType, string | null> = {
    year: null,
    month: null,
    day: null,
    weekDay: null,
    hours: null,
    minutes: null,
    seconds: null,
    meridiem: null,
    empty: null,
  };

  // If the string matches the format exactly, parse it using the adapter
  try {
    const parsedDate = utils.parse(valueStr, format);

    if (parsedDate && utils.isValid(parsedDate)) {
      // Extract values from the parsed date based on format
      if (formatContainsSection(format, 'year')) {
        result.year = utils.getYear(parsedDate).toString();
      }

      if (formatContainsSection(format, 'month')) {
        // Convert from 0-based to 1-based for month
        result.month = (utils.getMonth(parsedDate) + 1).toString().padStart(2, '0');
      }

      if (formatContainsSection(format, 'day')) {
        result.day = utils.getDate(parsedDate).toString().padStart(2, '0');
      }

      if (formatContainsSection(format, 'hours')) {
        const hours = utils.getHours(parsedDate);
        // Check if format uses 12-hour clock
        const is12Hour = format.includes('h') || format.includes('a') || format.includes('A');

        if (is12Hour) {
          const hours12 = hours % 12 === 0 ? 12 : hours % 12;
          result.hours = hours12.toString().padStart(2, '0');
          result.meridiem = hours >= 12 ? 'PM' : 'AM';
        } else {
          result.hours = hours.toString().padStart(2, '0');
        }
      }

      if (formatContainsSection(format, 'minutes')) {
        result.minutes = utils.getMinutes(parsedDate).toString().padStart(2, '0');
      }

      if (formatContainsSection(format, 'seconds')) {
        result.seconds = utils.getSeconds(parsedDate).toString().padStart(2, '0');
      }

      return result;
    }
  } catch (error) {
    // If parsing fails, continue with manual extraction
  }

  // Try to manually extract sections using regex patterns

  // Match for YYYY-MM-DD pattern
  let match = valueStr.match(/(\d{4})[-./](\d{1,2})[-./](\d{1,2})/);
  if (match) {
    result.year = match[1];
    result.month = match[2].padStart(2, '0');
    result.day = match[3].padStart(2, '0');
  }

  // Match for MM/DD/YYYY pattern
  if (!match) {
    match = valueStr.match(/(\d{1,2})[-./](\d{1,2})[-./](\d{4})/);
    if (match) {
      // This is ambiguous - could be MM/DD/YYYY or DD/MM/YYYY
      // Use the format to determine the order
      if (format.startsWith('M') || format.startsWith('MM')) {
        result.month = match[1].padStart(2, '0');
        result.day = match[2].padStart(2, '0');
      } else {
        result.day = match[1].padStart(2, '0');
        result.month = match[2].padStart(2, '0');
      }
      result.year = match[3];
    }
  }

  // Match for time pattern HH:MM or HH:MM:SS
  match = valueStr.match(/(\d{1,2}):(\d{1,2})(?::(\d{1,2}))?(?:\s*([aApP][mM])?)/);
  if (match) {
    let hours = parseInt(match[1], 10);
    const minutes = match[2];
    const seconds = match[3] || null;
    const meridiem = match[4]?.toUpperCase() || null;

    // Handle 12-hour format
    if (meridiem) {
      result.meridiem = meridiem.toUpperCase();

      // Convert to 24-hour if needed
      if (meridiem.toUpperCase() === 'PM' && hours < 12) {
        hours += 12;
      } else if (meridiem.toUpperCase() === 'AM' && hours === 12) {
        hours = 0;
      }
    }

    result.hours = hours.toString().padStart(2, '0');
    result.minutes = minutes.padStart(2, '0');
    if (seconds) {
      result.seconds = seconds.padStart(2, '0');
    }
  }

  return result;
};

/**
 * Returns a date utilities object with common date manipulation methods
 * This is a fallback when a utils instance is not provided
 */
export const getUtils = () => {
  // Default implementation using dayjs
  return {
    date: (value?: any) => (value ? dayjs(value) : dayjs()),
    parse: (value: string, format: string) => {
      try {
        return dayjs(value, format);
      } catch (e) {
        return null;
      }
    },
    isValid: (date: any) => date && dayjs(date).isValid(),
    formatByString: (date: any, format: string) => dayjs(date).format(format),
    getYear: (date: any) => dayjs(date).year(),
    getMonth: (date: any) => dayjs(date).month(), // 0-based month
    getDate: (date: any) => dayjs(date).date(),
    getHours: (date: any) => dayjs(date).hour(),
    getMinutes: (date: any) => dayjs(date).minute(),
    getSeconds: (date: any) => dayjs(date).second(),
    setYear: (date: any, year: number) => dayjs(date).year(year),
    setMonth: (date: any, month: number) => dayjs(date).month(month),
    setDate: (date: any, day: number) => dayjs(date).date(day),
    setHours: (date: any, hours: number) => dayjs(date).hour(hours),
    setMinutes: (date: any, minutes: number) => dayjs(date).minute(minutes),
    setSeconds: (date: any, seconds: number) => dayjs(date).second(seconds),
    addYears: (date: any, amount: number) => dayjs(date).add(amount, 'year'),
    addMonths: (date: any, amount: number) => dayjs(date).add(amount, 'month'),
    addDays: (date: any, amount: number) => dayjs(date).add(amount, 'day'),
    startOfDay: (date: any) => dayjs(date).startOf('day'),
    endOfDay: (date: any) => dayjs(date).endOf('day'),
    expandFormat: (format: string) => format, // Simple pass-through for now
  };
};

/**
 * Creates a section data object from a format token
 * Based on MUI's approach
 */
export const createSectionFromToken = (
  utils: any,
  token: string,
  value: string,
  startSeparator: string = '',
  sectionIndex: number = 0,
): ExtendedFieldSectionData => {
  const sectionConfig = getSectionTypeFromFormatToken(token);
  let maxLength: number | null = null;

  // Set maxLength based on token type, following MUI's pattern
  if (sectionConfig.type === 'year') {
    maxLength = token === 'YYYY' ? 4 : 2;
  } else if (sectionConfig.type === 'month') {
    maxLength = sectionConfig.contentType === 'digit' ? 2 : 9; // Text months can be longer
  } else if (sectionConfig.type === 'day') {
    maxLength = 2;
  } else if (sectionConfig.type === 'hours' || sectionConfig.type === 'minutes' || sectionConfig.type === 'seconds') {
    maxLength = 2;
  } else if (sectionConfig.type === 'meridiem') {
    maxLength = 2;
  } else if (sectionConfig.type === 'weekDay') {
    maxLength = 10;
  }

  // Set placeholder based on the section type
  let placeholder = token;
  if (sectionConfig.type === 'month') placeholder = 'MM';
  if (sectionConfig.type === 'day') placeholder = 'DD';
  if (sectionConfig.type === 'year') placeholder = token === 'YYYY' ? 'YYYY' : 'YY';
  if (sectionConfig.type === 'hours') placeholder = 'HH';
  if (sectionConfig.type === 'minutes') placeholder = 'MM';
  if (sectionConfig.type === 'seconds') placeholder = 'SS';
  if (sectionConfig.type === 'meridiem') placeholder = 'AM/PM';
  if (sectionConfig.type === 'weekDay') placeholder = 'Day';

  // Validate the section value if present
  let isInvalid = false;
  if (value) {
    if (sectionConfig.type === 'month' && sectionConfig.contentType === 'digit') {
      const num = parseInt(value, 10);
      isInvalid = isNaN(num) || num < 1 || num > 12;
    } else if (sectionConfig.type === 'day') {
      const num = parseInt(value, 10);
      isInvalid = isNaN(num) || num < 1 || num > 31;
    } else if (sectionConfig.type === 'year') {
      const num = parseInt(value, 10);
      isInvalid = isNaN(num) || num < 0;
    } else if (['hours', 'minutes', 'seconds'].includes(sectionConfig.type)) {
      const num = parseInt(value, 10);
      const max = sectionConfig.type === 'hours' ? 23 : 59;
      isInvalid = isNaN(num) || num < 0 || num > max;
    } else if (sectionConfig.type === 'meridiem') {
      isInvalid = !['AM', 'PM', 'am', 'pm'].includes(value);
    }
  }

  return {
    ...sectionConfig,
    value: value,
    displayValue: value || placeholder,
    placeholder,
    maxLength,
    separator: '', // Will be updated in post-processing
    token: token,
    invalid: isInvalid,
    modified: !!value,
    startIndex: sectionIndex,
    endIndex: sectionIndex,
    startSeparator, // Include the startSeparator property
  };
};
