import { DateAdapter } from '../models/adapter';
import { PickerDateType } from '../models/pickers';
import { DateOrTimeViewWithMeridiem, DatePickerView } from '../types';

export const mergeDateAndTime = (
  utils: DateAdapter,
  dateParam: PickerDateType,
  timeParam: PickerDateType,
): PickerDateType => {
  let mergedDate = dateParam;
  mergedDate = utils.setHours(mergedDate, utils.getHours(timeParam));
  mergedDate = utils.setMinutes(mergedDate, utils.getMinutes(timeParam));
  mergedDate = utils.setSeconds(mergedDate, utils.getSeconds(timeParam));
  mergedDate = utils.setMilliseconds(mergedDate, utils.getMilliseconds(timeParam));

  return mergedDate;
};

export const findClosestEnabledDate = ({
  date,
  disableFuture,
  disablePast,
  minDate,
  maxDate,
  isDateDisabled,
  utils,
}: {
  date: PickerDateType;
  disableFuture?: boolean;
  disablePast?: boolean;
  minDate?: PickerDateType;
  maxDate?: PickerDateType;
  isDateDisabled: (date: PickerDateType) => boolean;
  utils: DateAdapter;
}): PickerDateType | null => {
  const today = mergeDateAndTime(utils, utils.date(undefined), date);
  if (disablePast && utils.isBefore(minDate!, today)) {
    minDate = today;
  }

  if (disableFuture && utils.isAfter(maxDate, today)) {
    maxDate = today;
  }

  let forward: PickerDateType | null = date;
  let backward: PickerDateType | null = date;

  // Check min/max constraints
  if (minDate && utils.isBefore(date, minDate)) {
    forward = minDate;
    backward = null;
  }

  if (maxDate && utils.isAfter(date, maxDate)) {
    if (backward) {
      backward = maxDate;
    }
    forward = null;
  }

  // Try to find the closest enabled date by checking forward and backward from the date
  while (forward || backward) {
    // Check if we've gone beyond our boundaries
    if (forward && maxDate && utils.isAfter(forward, maxDate)) {
      forward = null;
    }
    if (backward && minDate && utils.isBefore(backward, minDate)) {
      backward = null;
    }

    // Check forward direction
    if (forward) {
      if (!isDateDisabled(forward)) {
        return forward;
      }
      forward = utils.addDays(forward, 1);
    }

    // Check backward direction
    if (backward) {
      if (!isDateDisabled(backward)) {
        return backward;
      }
      backward = utils.addDays(backward, 1);
    }
  }

  return null;
};

export const getMonthsInYear = (utils: DateAdapter, year: PickerDateType): PickerDateType[] => {
  const firstMonth = utils.startOfYear(year);
  const months = [firstMonth];

  while (months.length < 12) {
    const prevMonth = months[months.length - 1];
    months.push(utils.addMonths(prevMonth, 1));
  }

  return months;
};

export const areViewsEqual = <TView extends DateOrTimeViewWithMeridiem>(
  views: ReadonlyArray<DateOrTimeViewWithMeridiem>,
  expectedViews: TView[],
): views is ReadonlyArray<TView> => {
  if (views.length !== expectedViews.length) {
    return false;
  }

  return expectedViews.every((expectedView) => views.includes(expectedView));
};

export const resolveDateFormat = (
  utils: DateAdapter,
  { format, views }: { format?: string; views: readonly DatePickerView[] },
  isInToolbar: boolean,
) => {
  if (format != null) {
    return format;
  }

  const formats = utils.formats;
  if (areViewsEqual(views, ['year'])) {
    return formats.year;
  }

  if (areViewsEqual(views, ['month'])) {
    return formats.month;
  }

  if (areViewsEqual(views, ['day'])) {
    return formats.dayOfMonth;
  }

  if (areViewsEqual(views, ['month', 'year'])) {
    return `${formats.month} ${formats.year}`;
  }

  if (areViewsEqual(views, ['day', 'month'])) {
    return `${formats.month} ${formats.dayOfMonth}`;
  }

  if (isInToolbar) {
    // Little localization hack (Google is doing the same for android native pickers):
    // For english localization it is convenient to include weekday into the date "Mon, Jun 1".
    // For other locales using strings like "June 1", without weekday.
    return /en/.test(utils.getCurrentLocaleCode()) ? formats.normalDateWithWeekday : formats.normalDate;
  }

  return formats.keyboardDate;
};
