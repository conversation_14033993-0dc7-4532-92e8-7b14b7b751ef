import * as React from 'react';
import { FieldSectionType } from '../models/dateSection';

// Add improved useEnhancedEffect for better DOM synchronization
const useEnhancedEffect = typeof window !== 'undefined' ? React.useLayoutEffect : React.useEffect;

// Helper functions that were previously imported
const getActiveElement = (doc: Document): Element | null => doc.activeElement;

/**
 * Get boundary values for date sections
 * This is a simplified version of the MUI date boundaries logic
 */
export const getSectionBoundaries = (utils: any) => {
  return {
    year: (options: { currentDate?: any } = {}) => ({
      minimum: 0,
      maximum: 9999,
    }),
    month: () => ({
      minimum: 1,
      maximum: 12,
    }),
    day: (options: { currentDate?: any } = {}) => {
      // If a reference date is provided, get the actual days in that month
      if (options.currentDate && utils?.getDaysInMonth) {
        return {
          minimum: 1,
          maximum: utils.getDaysInMonth(options.currentDate),
        };
      }

      // Default to 31 days if no reference date
      return {
        minimum: 1,
        maximum: 31,
      };
    },
    weekDay: () => ({
      minimum: 0,
      maximum: 6,
    }),
    hours: (options: { format?: string } = {}) => {
      // Check if it's a 12-hour format
      const is12Hour = options.format ? /h|a|A/i.test(options.format) : false;

      return {
        minimum: is12Hour ? 1 : 0,
        maximum: is12Hour ? 12 : 23,
      };
    },
    minutes: () => ({
      minimum: 0,
      maximum: 59,
    }),
    seconds: () => ({
      minimum: 0,
      maximum: 59,
    }),
    meridiem: () => ({
      minimum: 0, // 0 = AM
      maximum: 1, // 1 = PM
    }),
  };
};

export { useEnhancedEffect, getActiveElement };
