import { SlotComponentProps } from '@mui/utils/types';
import { DockedDatePickerProps } from '../DockedDatePicker/DockedDatePicker.types';
import { ModalDatePickerProps } from '../ModalDatePicker/ModalDatePicker.types';
import { OverrideProps } from '@mui/types';
import { DockedDateRangePickerSlotProps } from '../DockedDateRangePicker';

/**
 * Component slots for the DatePicker
 */
export interface DatePickerSlots {
  /**
   * Custom component for the field input.
   * @default DateField
   */
  field?: React.ElementType;

  /**
   * Custom component for the calendar.
   * @default DateCalendar
   */
  calendar?: React.ElementType;
}

/**
 * Props for the DatePicker slots
 */
export interface DatePickerSlotProps {
  /**
   * Props for the root element.
   */
  root?: SlotComponentProps<'div', Record<string, unknown>, DatePickerComponentProps>;

  /**
   * Props for the field component.
   */
  field?: SlotComponentProps<React.ElementType, Record<string, unknown>, any>;

  /**
   * Props for the calendar component.
   */
  calendar?: SlotComponentProps<React.ElementType, Record<string, unknown>, any>;

  /**
   * Props for the dialog component (mobile only).
   */
  dialog?: SlotComponentProps<React.ElementType, Record<string, unknown>, any>;
}

export interface DatePickerComponentProps
  extends Omit<ModalDatePickerProps, 'slots' | 'slotProps'>,
    Omit<DockedDatePickerProps, 'slots' | 'slotProps'> {
  /**
   * CSS media query when `Mobile` mode will be changed to `Desktop`.
   * @default '@media (pointer: fine)'
   * @example '@media (min-width: 720px)' or theme.breakpoints.up("sm")
   */
  desktopModeMediaQuery?: string;

  /**
   * Overridable component slots.
   * @default {}
   */
  slots?: DatePickerSlots;

  /**
   * The props used for each component slot.
   * @default {}
   */
  slotProps?: DatePickerSlotProps;
}

export interface DatePickerTypeMap {
  props: DatePickerComponentProps;
  defaultComponent: React.ElementType;
}

/**
 * DatePicker component props
 */
export type DatePickerProps<D extends React.ElementType = DatePickerTypeMap['defaultComponent']> = OverrideProps<
  DatePickerTypeMap,
  D
>;
