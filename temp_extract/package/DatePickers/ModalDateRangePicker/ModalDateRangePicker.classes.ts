import {
  unstable_generateUtilityClass as generateUtilityClass,
  unstable_generateUtilityClasses as generateUtilityClasses,
} from '@mui/utils';

export interface ModalDateRangePickerClasses {
  /** Styles applied to the root element. */
  root: string;
  /** Styles applied to the dialog element. */
  dialog: string;
  /** Styles applied to the content element. */
  content: string;
  /** Styles applied to the field element. */
  field: string;
  /** Styles applied to the calendar element. */
  calendar: string;
}

export type ModalDateRangePickerClassKey = keyof ModalDateRangePickerClasses;

export function getModalDateRangePickerUtilityClass(slot: string): string {
  return generateUtilityClass('NovaModalDateRangePicker', slot);
}

export const modalDateRangePickerClasses: ModalDateRangePickerClasses = generateUtilityClasses(
  'NovaModalDateRangePicker',
  ['root', 'dialog', 'content', 'field', 'calendar'],
);
