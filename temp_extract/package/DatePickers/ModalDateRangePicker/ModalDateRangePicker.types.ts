import * as React from 'react';
import { OverrideProps } from '@mui/types';
import { SlotComponentProps } from '@mui/utils';
import {
  BaseDateRangePickerProps,
  BaseDateRangePickerSlots,
  BaseDateRangePickerSlotProps,
} from '../DateRangePicker/BaseDateRangePicker.types';

/**
 * Modal-specific DateRangePicker props interface
 */
export interface ModalDateRangePickerComponentProps extends BaseDateRangePickerProps {
  /**
   * If `true`, the dialog will be open
   * @default false
   */
  open?: boolean;

  /**
   * Component slots and their props
   */
  slots?: BaseDateRangePickerSlots & {
    /**
     * Custom component for the dialog
     * @default Dialog
     */
    dialog?: React.ElementType;
  };

  /**
   * Props applied to slot components
   */
  slotProps?: BaseDateRangePickerSlotProps & {
    dialog?: SlotComponentProps<React.ElementType, Record<string, unknown>, ModalDateRangePickerComponentProps>;
  };
}

export interface ModalDateRangePickerTypeMap {
  props: ModalDateRangePickerComponentProps;
  defaultComponent: React.ElementType;
}

/**
 * Modal DateRangePicker component props
 */
export type ModalDateRangePickerProps<D extends React.ElementType = ModalDateRangePickerTypeMap['defaultComponent']> =
  OverrideProps<ModalDateRangePickerTypeMap, D>;

/**
 * Component prop types for Modal DateRangePicker slots
 */
export type ModalDateRangePickerSlotProps = {
  root?: SlotComponentProps<'div', Record<string, unknown>, ModalDateRangePickerComponentProps>;
} & BaseDateRangePickerSlotProps;
