'use client';
import React, { useCallback, useState } from 'react';
import { styled } from '@pigment-css/react';
import clsx from 'clsx';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import { Dialog } from '../../Dialog';
import { DateRangeField } from '../DateRangeField/DateRangeField';
import { DateRangeCalendar } from '../DateRangeCalendar/DateRangeCalendar';
import { ModalDateRangePickerProps } from './ModalDateRangePicker.types';
import { getModalDateRangePickerUtilityClass } from './ModalDateRangePicker.classes';
import { Divider } from '../../Divider';
import { DatePickerView, PickerViewHeader } from '../PickerViewHeader';
import { PickerViewFooter } from '../PickerViewFooter';
import { useDateRangePicker } from '../hooks/useDateRangePicker';
import { modalDatePickerClasses } from '../ModalDatePicker';
import { useDefaultDates, useNow, useUtils } from '../hooks';
import { unstable_createUseMediaQuery as createUseMediaQuery } from '@mui/system/useMediaQuery';
import { CalendarWeekDay } from '../DayCalendar';
import { DateField } from '../DateField';
import { PickerDateType } from '../models/pickers';
import { Box } from '../../Box';
import { IconButton } from '../../IconButton';
import { CalendarIcon } from '../icons';

const useMediaQuery = createUseMediaQuery();

const useUtilityClasses = (ownerState) => {
  const slots = {
    root: ['root'],
    dialog: ['dialog'],
    content: ['content'],
  };

  return composeClasses(slots, getModalDateRangePickerUtilityClass, {});
};

const ModalDateRangePickerRoot = styled('div', {
  name: 'NovaModalDateRangePicker',
  slot: 'Root',
})({
  display: 'inline-flex',
  flexDirection: 'column',
});

/**
 * Modal version of DateRangePicker component
 */
export const ModalDateRangePicker = React.forwardRef<HTMLDivElement, ModalDateRangePickerProps>((props, ref) => {
  const defaultDate = useDefaultDates();
  const {
    className,
    value,
    defaultValue,
    onChange,
    disabled = false,
    readOnly = false,
    onOpen,
    onClose,
    open: openProp,
    endDecorator,
    helperText,
    format = 'MM/DD/YYYY',
    label,
    startLabel,
    endLabel,
    separator,
    calendars,
    minDate = defaultDate.minDate,
    maxDate = defaultDate.maxDate,
    disableFuture,
    disablePast,
    shouldDisableDate,
    closeOnSelect = false,
    clearText = 'Clear',
    todayText = 'Today',
    okText = 'OK',
    cancelText = 'Cancel',
    views = ['day', 'month', 'year'],
    autoFocus = false,
    ...other
  } = props;

  const [isEditing, setIsEditing] = useState(false);
  const isSmall = useMediaQuery('(max-width:768px)', { noSsr: true });
  const utils = useUtils();
  const now = useNow();
  // Use the date range picker hook
  const {
    selectedRange,
    updateRange,
    rangePosition,
    setRangePosition,
    open,
    handleOpen,
    anchorEl,
    currentView,
    setCurrentView,
    availableViews,
    handleAccept,
    handleCancel,
    handleClear,
    validateDate,
  } = useDateRangePicker({
    value,
    defaultValue,
    onChange,
    open: openProp,
    onOpen,
    onClose,
    disabled,
    readOnly,
    minDate,
    maxDate,
    disableFuture,
    disablePast,
    shouldDisableDate,
    format,
    closeOnSelect,
    autoFocus,
    views,
  });

  // Get utility classes
  const ownerState = { ...props };
  const classes = useUtilityClasses(ownerState);

  const openPickerButton = (
    <IconButton
      className="DateField-calendarIcon"
      variant="standard"
      onClick={handleOpen}
      disabled={disabled || readOnly}
    >
      {endDecorator || <CalendarIcon />}
    </IconButton>
  );

  // Memoize the field component
  const Field = DateRangeField;
  const baseFieldProps = {
    format,
    disabled,
    readOnly,
    helperText,
    disableFuture,
    disablePast,
    maxDate,
    minDate,
    error: false,
  };
  const fieldProps = {
    ...baseFieldProps,
    value: selectedRange,
    onChange: updateRange,
    label,
    startLabel,
    endLabel,
    separator,
    onClick: handleOpen,
    endDecorator: openPickerButton,
  };

  // Memoize the calendar component
  const RangeCalendar = DateRangeCalendar;
  const calendarProps = {
    value: selectedRange,
    onChange: updateRange,
    rangePosition,
    onRangePositionChange: setRangePosition,
    view: currentView,
    calendars,
    availableViews,
    disabled,
    readOnly,
    disableFuture,
    disablePast,
    minDate,
    maxDate,
  };

  // Memoize handlers to prevent unnecessary re-renders
  const handleViewChange = useCallback(
    (view: DatePickerView) => {
      setCurrentView(view);
    },
    [setCurrentView],
  );

  const handleSwitchDateField = () => {
    setIsEditing((editing) => !editing);
  };

  const handleChangeStartDate = useCallback(
    (newValue: PickerDateType) => {
      updateRange([newValue, value[1]]);
    },
    [updateRange, value],
  );

  const handleChangeEndDate = useCallback(
    (newValue: PickerDateType) => {
      updateRange([value[0], newValue]);
    },
    [updateRange, value],
  );

  return (
    <ModalDateRangePickerRoot ref={ref} className={clsx(classes.root, className)} {...other}>
      <Field style={{ minWidth: '260px' }} {...fieldProps} />
      <Dialog.Root
        open={open}
        onClose={handleCancel}
        fullScreen={isSmall && !isEditing}
        aria-labelledby="date-picker-dialog-title"
      >
        <PickerViewHeader
          days={selectedRange}
          className={modalDatePickerClasses.calendarHeader}
          label={label || isEditing ? 'Select dates' : 'Depart - return dates'}
          view={currentView}
          views={availableViews}
          onViewChange={handleViewChange}
          disabled={disabled}
          readOnly={readOnly}
          isEditing={isEditing}
          onSwitchDateField={handleSwitchDateField}
        />
        {!isEditing && <CalendarWeekDay utils={utils} now={now} />}
        <Divider />
        {!isEditing && <RangeCalendar {...calendarProps} variant="modal" />}
        {isEditing && (
          <Box sx={{ display: 'flex', gap: '8px', paddingInline: '24px', paddingBlock: '16px' }}>
            <DateField
              value={value[0]}
              onChange={handleChangeStartDate}
              label="Start"
              sx={{ width: '136px' }}
              {...baseFieldProps}
            />
            <DateField
              value={value[1]}
              onChange={handleChangeEndDate}
              label="End"
              sx={{ width: '136px' }}
              {...baseFieldProps}
            />
          </Box>
        )}
        {currentView === 'year' && <Divider />}
        {(currentView === 'day' || currentView === 'year') && (
          <PickerViewFooter
            onAccept={handleAccept}
            onCancel={handleCancel}
            disabled={disabled || readOnly}
            clearText={clearText}
            cancelText={cancelText}
            fullScreen={isSmall && !isEditing}
            okText={okText}
          />
        )}
      </Dialog.Root>
    </ModalDateRangePickerRoot>
  );
});
