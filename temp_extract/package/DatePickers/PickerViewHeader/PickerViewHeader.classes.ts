import {
  unstable_generateUtilityClasses as generateUtilityClasses,
  unstable_generateUtilityClass as generateUtilityClass,
} from '@mui/utils';

export interface PickerViewHeaderClasses {
  /** Styles applied to the root element. */
  root: string;
  /** Styles applied to the component when disabled. */
  disabled: string;
}

export type PickerViewHeaderClassKey = keyof PickerViewHeaderClasses;

export function getPickerViewHeaderUtilityClass(slot: string): string {
  return generateUtilityClass('NovaPickerViewHeader', slot);
}

const pickerViewHeaderClasses: PickerViewHeaderClasses = generateUtilityClasses('NovaPickerViewHeader', [
  'root',
  'disabled',
]);

export default pickerViewHeaderClasses;
