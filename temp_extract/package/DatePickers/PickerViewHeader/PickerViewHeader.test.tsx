import * as React from 'react';
import '@testing-library/jest-dom/vitest';
import { render, screen, cleanup, fireEvent } from '@testing-library/react';
import { describe, expect, it, afterEach, vi } from 'vitest';
import { PickerViewHeader } from './PickerViewHeader';
import { DatePickerView } from './PickerViewHeader.types';
import dayjs from 'dayjs/esm';
import { PickerProvider } from '../PickerContext';

afterEach(() => {
  cleanup();
  vi.clearAllMocks();
});

describe('<PickerViewHeader />', () => {
  const defaultProps = {
    view: 'day' as DatePickerView,
    views: ['day', 'month', 'year'] as DatePickerView[],
    onViewChange: vi.fn(),
    onSwitchDateField: vi.fn(),
    date: dayjs('2023-05-15'),
  };

  describe('renders correctly', () => {
    it('should render with default values', () => {
      const { container } = render(
        <PickerProvider>
          <PickerViewHeader
            view={defaultProps.view}
            views={defaultProps.views}
            onViewChange={defaultProps.onViewChange}
            onSwitchDateField={defaultProps.onSwitchDateField}
            days={[defaultProps.date]}
          />
        </PickerProvider>,
      );
      expect(container.firstChild).toBeInTheDocument();

      // Check for label and date
      expect(screen.getByText('Select date')).toBeInTheDocument();
      expect(screen.getByText('Mon, May 15')).toBeInTheDocument();

      // Check for edit button
      expect(screen.getByLabelText('Edit date')).toBeInTheDocument();
    });

    it('should render with custom label', () => {
      const { container } = render(
        <PickerProvider>
          <PickerViewHeader
            view={defaultProps.view}
            views={defaultProps.views}
            onViewChange={defaultProps.onViewChange}
            onSwitchDateField={defaultProps.onSwitchDateField}
            days={[defaultProps.date]}
            label="Choose a date"
          />
        </PickerProvider>,
      );
      expect(screen.getByText('Choose a date')).toBeInTheDocument();
    });

    it('should render empty date message when date is not provided', () => {
      const { container } = render(
        <PickerProvider>
          <PickerViewHeader
            view={defaultProps.view}
            views={defaultProps.views}
            onViewChange={defaultProps.onViewChange}
            onSwitchDateField={defaultProps.onSwitchDateField}
            days={undefined}
          />
        </PickerProvider>,
      );

      // The component renders an empty string for the date when no days are provided
      const dateElement = screen.getByRole('heading', { level: 6 });
      expect(dateElement).toBeInTheDocument();
      expect(dateElement).toHaveTextContent('');
    });
  });

  describe('callbacks', () => {
    it('should call onSwitchDateField when edit button is clicked', () => {
      render(
        <PickerProvider>
          <PickerViewHeader
            view={defaultProps.view}
            views={defaultProps.views}
            onViewChange={defaultProps.onViewChange}
            onSwitchDateField={defaultProps.onSwitchDateField}
            days={[defaultProps.date]}
          />
        </PickerProvider>,
      );
      const editButton = screen.getByLabelText('Edit date');
      fireEvent.click(editButton);
      expect(defaultProps.onSwitchDateField).toHaveBeenCalledTimes(1);
    });

    it('should use onSwitchDateField when edit button is clicked', () => {
      const mockSwitchDateField = vi.fn();
      render(
        <PickerProvider>
          <PickerViewHeader
            view={defaultProps.view}
            views={defaultProps.views}
            onViewChange={defaultProps.onViewChange}
            onSwitchDateField={mockSwitchDateField}
            days={[defaultProps.date]}
          />
        </PickerProvider>,
      );
      const editButton = screen.getByLabelText('Edit date');
      fireEvent.click(editButton);
      expect(mockSwitchDateField).toHaveBeenCalledTimes(1);
    });
  });

  describe('disabled state', () => {
    it('should disable the edit button when disabled', () => {
      render(
        <PickerProvider>
          <PickerViewHeader
            view={defaultProps.view}
            views={defaultProps.views}
            onViewChange={defaultProps.onViewChange}
            onSwitchDateField={defaultProps.onSwitchDateField}
            days={[defaultProps.date]}
            disabled
          />
        </PickerProvider>,
      );
      const editButton = screen.getByLabelText('Edit date');
      expect(editButton).toBeDisabled();
    });

    it('should disable the edit button when readOnly', () => {
      render(
        <PickerProvider>
          <PickerViewHeader
            view={defaultProps.view}
            views={defaultProps.views}
            onViewChange={defaultProps.onViewChange}
            onSwitchDateField={defaultProps.onSwitchDateField}
            days={[defaultProps.date]}
            readOnly
          />
        </PickerProvider>,
      );
      const editButton = screen.getByLabelText('Edit date');
      expect(editButton).toBeDisabled();
    });
  });

  describe('classes and styling', () => {
    it('should apply the root class', () => {
      const { container } = render(
        <PickerProvider>
          <PickerViewHeader
            view={defaultProps.view}
            views={defaultProps.views}
            onViewChange={defaultProps.onViewChange}
            onSwitchDateField={defaultProps.onSwitchDateField}
            days={[defaultProps.date]}
          />
        </PickerProvider>,
      );

      expect(container.firstChild).toHaveClass('NovaPickerViewHeader-root');
    });

    it('should accept custom className prop', () => {
      const { container } = render(
        <PickerProvider>
          <PickerViewHeader
            view={defaultProps.view}
            views={defaultProps.views}
            onViewChange={defaultProps.onViewChange}
            onSwitchDateField={defaultProps.onSwitchDateField}
            days={[defaultProps.date]}
            className="custom-class"
          />
        </PickerProvider>,
      );

      expect(container.firstChild).toHaveClass('custom-class');
    });
  });
});
