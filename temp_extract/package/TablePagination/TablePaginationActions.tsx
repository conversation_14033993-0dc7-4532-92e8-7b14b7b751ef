'use client';
import * as React from 'react';
import LastPageIconDefault from '../internal/svg-icons/LastPage';
import FirstPageIconDefault from '../internal/svg-icons/FirstPage';
import { IconButton } from '../IconButton/IconButton';
import KeyboardArrowRight from '../internal/svg-icons/KeyboardArrowRight';
import KeyboardArrowLeft from '../internal/svg-icons/KeyboardArrowLeft';
import { PageInfoProps, TablePaginationActionsProps } from './TablePaginationActions.types';
import { styled } from '@pigment-css/react';

const Root = styled('div')({
  display: 'flex',
  alignItems: 'center',
  flexShrink: 0,
});

const PageInfo = styled('span', {
  name: 'NovaTablePaginationActions',
  slot: 'PageInfo',
})<PageInfoProps>(({ theme }) => ({
  display: 'inline-flex',
  alignItems: 'center',
  margin: '0 8px',
}));

const TableIconButton = styled(IconButton, {
  name: 'NovaTablePaginationActions',
  slot: 'Button',
})<TablePaginationActionsProps>(({ theme }) => ({
  width: 25,
  height: 40,
  variants: [
    {
      props: { size: 'small' },
      style: {
        height: 28,
      },
    },
    {
      props: { size: 'large' },
      style: {
        height: 52,
      },
    },
  ],
  borderRadius: 8,
  margin: '0 1px',
  backgroundColor: 'transparent',
  '& svg': {
    fill: theme.vars.palette.onSurfaceVariant,
    fontSize: 20,
    variants: [
      {
        props: { size: 'small' },
        style: {
          fontSize: 18,
        },
      },
      {
        props: { size: 'large' },
        style: {
          fontSize: 24,
        },
      },
    ],
  },
  '&:hover': {
    backgroundColor: `color-mix(in srgb, ${theme.vars.palette.backgroundStates}, ${theme.vars.palette.onSurfaceVariant} ${theme.vars.palette.stateLayers.hoverOnSurface})`,
  },
  '&:focus': {
    outlineColor: 'transparent',
    '& svg': {
      fill: theme.vars.palette.onSecondaryContainer,
    },
    backgroundColor: `color-mix(in srgb, ${theme.vars.palette.backgroundStates}, ${theme.vars.palette.onSurfaceVariant} ${theme.vars.palette.stateLayers.focusOnSurface})`,
  },
  '&:active': {
    '& svg': {
      fill: theme.vars.palette.onSecondaryContainer,
    },
    backgroundColor: `color-mix(in srgb, ${theme.vars.palette.backgroundStates}, ${theme.vars.palette.onSurfaceVariant} ${theme.vars.palette.stateLayers.pressOnSurface})`,
  },
  '&.Mui-disabled': {
    '& svg': {
      fill: theme.vars.palette.onBackgroundDisabled,
    },
  },
}));

// eslint-disable-next-line react/display-name
export const TablePaginationActions = React.forwardRef<HTMLDivElement, TablePaginationActionsProps>(
  function TablePaginationActions(props, ref) {
    const {
      count,
      disabled = false,
      getItemAriaLabel,
      onPageChange,
      page,
      rowsPerPage,
      showFirstButton,
      showLastButton,
      slots = {},
      slotProps = {},
      size = 'medium',
      renderPageInfo,
      ...other
    } = props;

    const handleFirstPageButtonClick = (event: React.MouseEvent<HTMLButtonElement>) => {
      onPageChange(event, 0);
    };

    const handleBackButtonClick = (event: React.MouseEvent<HTMLButtonElement>) => {
      onPageChange(event, page - 1);
    };

    const handleNextButtonClick = (event: React.MouseEvent<HTMLButtonElement>) => {
      onPageChange(event, page + 1);
    };

    const handleLastPageButtonClick = (event: React.MouseEvent<HTMLButtonElement>) => {
      onPageChange(event, Math.max(0, Math.ceil(count / rowsPerPage) - 1));
    };

    const totalPages = Math.ceil(count / rowsPerPage);
    const isFirstPageDisabled = disabled || page === 0;
    const isLastPageDisabled = disabled || page >= Math.ceil(count / rowsPerPage) - 1;

    const pageInfoContent = renderPageInfo(page, totalPages);

    return (
      <Root ref={ref} {...other}>
        {showFirstButton && (
          <TableIconButton
            onClick={handleFirstPageButtonClick}
            disabled={isFirstPageDisabled}
            aria-label={getItemAriaLabel('first')}
            title={getItemAriaLabel('first')}
            size={size}
            {...slotProps.firstButton}
          >
            <FirstPageIconDefault {...slotProps.firstButtonIcon} />
          </TableIconButton>
        )}

        <TableIconButton
          onClick={handleBackButtonClick}
          disabled={isFirstPageDisabled}
          aria-label={getItemAriaLabel('previous')}
          title={getItemAriaLabel('previous')}
          size={size}
          {...slotProps.previousButton}
        >
          <KeyboardArrowLeft {...slotProps.previousButtonIcon} />
        </TableIconButton>

        <PageInfo size={size}>{pageInfoContent}</PageInfo>

        <TableIconButton
          onClick={handleNextButtonClick}
          disabled={isLastPageDisabled}
          aria-label={getItemAriaLabel('next')}
          title={getItemAriaLabel('next')}
          size={size}
          {...slotProps.nextButton}
        >
          <KeyboardArrowRight {...slotProps.nextButtonIcon} />
        </TableIconButton>

        {showLastButton && (
          <TableIconButton
            onClick={handleLastPageButtonClick}
            disabled={isLastPageDisabled}
            aria-label={getItemAriaLabel('last')}
            title={getItemAriaLabel('last')}
            size={size}
            {...slotProps.lastButton}
          >
            <LastPageIconDefault {...slotProps.lastButtonIcon} />
          </TableIconButton>
        )}
      </Root>
    );
  },
);
