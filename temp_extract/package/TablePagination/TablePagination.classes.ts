import generateUtilityClasses from '@mui/utils/generateUtilityClasses';
import generateUtilityClass from '@mui/utils/generateUtilityClass';

export interface TablePaginationClasses {
  /** Styles applied to the root element. */
  root: string;
  /** Styles applied to the dropdown label component. */
  dropdownLabel: string;
  /** Styles applied to the dropdown element. */
  dropdown: string;
  /** Styles applied to the dropdown option element. */
  dropdownOption: string;
  /** Styles applied to the action element. */
  actions: string;
  /** Styles applied to the root element if `size="small"`. */
  sizeSmall: string;
  /** Styles applied to the root element if `size="medium"`. */
  sizeMedium: string;
  /** Styles applied to the root element if `size="large"`. */
  sizeLarge: string;
  /** Styles applied to the root element if `disabled={true}`. */
  disabled: string;
}

export type TablePaginationClassKey = keyof TablePaginationClasses;

export function getTablePaginationUtilityClass(slot: string): string {
  return generateUtilityClass('NovaTablePagination', slot);
}

const tablePaginationClasses: TablePaginationClasses = generateUtilityClasses('NovaTablePagination', [
  'root',
  'dropdownLabel',
  'dropdown',
  'dropdownOption',
  'actions',
  'sizeSmall',
  'sizeMedium',
  'sizeLarge',
  'disabled',
]);

export default tablePaginationClasses;
