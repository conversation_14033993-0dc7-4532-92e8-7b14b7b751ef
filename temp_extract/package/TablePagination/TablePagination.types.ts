import * as React from 'react';
import { TablePaginationActionsProps, TablePaginationActionsSlots } from './TablePaginationActions.types';
import { OverrideProps } from '@mui/types';
import { CreateSlotsAndSlotProps, SlotProps } from '../types/slot';
import { OptionProps } from '../Option';
import { DropdownProps } from '../Dropdown';

export interface LabelDisplayedRowsArgs {
  from: number;
  to: number;
  count: number;
  page: number;
}

export interface TablePaginationLocale {
  /**
   * Accepts a function which returns a string value that provides a user-friendly name for the current page.
   * This is important for screen reader users.
   *
   * @param {string} type The link or button type to format ('first' | 'last' | 'next' | 'previous').
   * @returns {string}
   * @default function defaultGetAriaLabel(type) {
   *   return `Go to ${type} page`;
   * }
   */
  getItemAriaLabel?: (type: 'first' | 'last' | 'next' | 'previous') => string;
  /**
   * Customize the rows per page label.
   * @default 'Items per page:'
   */
  labelRowsPerPage?: React.ReactNode;
  /**
   * Custom render function for page info content
   * @param {number} page Current page number (0-based)
   * @param {number} totalPages Total number of pages
   * @returns {React.ReactNode}
   * @default function renderPageInfo(page, totalPages) {
   *   return `Page ${page + 1} of ${totalPages}`;
   * }
   */
  renderPageInfo?: (page: number, totalPages: number) => React.ReactNode;
}

export interface TablePaginationSlots {
  /**
   * The component that renders the root slot.
   * @default 'div'
   */
  root?: React.ElementType;
  /**
   * The tag that renders the dropdownLabel slot.
   * @default 'p'
   */
  dropdownLabel?: React.ElementType;
  /**
   * The component that renders the dropdown slot.
   * @default Dropdown
   */
  dropdown?: React.ElementType;
  /**
   * The component that renders the dropdown option slot.
   * @default Option
   */
  dropdownOption?: React.ElementType;
  /**
   * The slots for actions component.
   */
  actions?: TablePaginationActionsSlots;
}

export type TablePaginationSlotsAndSlotProps = CreateSlotsAndSlotProps<
  TablePaginationSlots,
  {
    /**
     * Props forwarded to the root slot.
     */
    root: SlotProps<'div', object, TablePaginationOwnerState>;
    /**
     * Props forwarded to the dropdownLabel slot.
     * By default, the available props are based on the paragraph element.
     */
    dropdownLabel: SlotProps<'p', object, TablePaginationOwnerState>;
    /**
     * Props forwarded to the dropdown slot.
     */
    dropdown: DropdownProps<number, false>;
    /**
     * Props forwarded to the menuItem slot.
     */
    dropdownOption: OptionProps;
    /**
     * Props forwarded to the actions slot.
     */
    actions: TablePaginationActionsProps['slotProps'];
  }
>;

/**
 * This type is kept for compatibility. Use `TablePaginationOwnProps` instead.
 */
export interface TablePaginationBaseProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'classes' | 'component' | 'children'> {
  component?: React.ElementType;
  'data-testid'?: string;
}

export interface TablePaginationOwnProps extends TablePaginationBaseProps {
  /**
   * The component used for displaying the actions.
   * Either a string to use a HTML element or a component.
   * @default TablePaginationActions
   */
  ActionsComponent?: React.ElementType<TablePaginationActionsProps>;
  /**
   * The total number of rows.
   *
   * To enable server side pagination for an unknown number of items, provide -1.
   */
  count: number;
  /**
   * If `true`, the component is disabled.
   * @default false
   */
  disabled?: boolean;
  /**
   * Callback fired when the page is changed.
   *
   * @param {React.MouseEvent<HTMLButtonElement> | null} event The event source of the callback.
   * @param {number} page The dropdown page.
   */
  onPageChange: (event: React.MouseEvent<HTMLButtonElement> | null, page: number) => void;
  /**
   * Callback fired when the number of rows per page is changed.
   *
   * @param {React.SyntheticEvent | null} event The event source of the callback.
   */
  onRowsPerPageChange?: (event: React.SyntheticEvent | null, page: number) => void;
  /**
   * The zero-based index of the current page.
   */
  page: number;
  /**
   * The number of rows per page.
   *
   * Set -1 to display all the rows.
   */
  rowsPerPage: number;
  /**
   * Customizes the options of the rows per page dropdown field. If less than two options are
   * available, no dropdown field will be displayed.
   * Use -1 for the value with a custom label to show all the rows.
   * @default [10, 25, 50, 100]
   */
  rowsPerPageOptions?: ReadonlyArray<number | { value: number; label: string }>;
  /**
   * If `true`, show the first-page button.
   * @default false
   */
  showFirstButton?: boolean;
  /**
   * If `true`, show the last-page button.
   * @default false
   */
  showLastButton?: boolean;
  /**
   * The size of the component.
   * @default 'medium'
   */
  size?: 'small' | 'medium' | 'large';
}

export interface TablePaginationOwnerState extends TablePaginationOwnProps {}

export interface TablePaginationTypeMap<AdditionalProps = object, RootComponent extends React.ElementType = 'div'> {
  props: AdditionalProps &
    TablePaginationOwnProps &
    TablePaginationSlotsAndSlotProps &
    TablePaginationLocale &
    Omit<React.HTMLAttributes<HTMLDivElement>, 'children'>;
  defaultComponent: RootComponent;
}

export type TablePaginationProps<
  RootComponent extends React.ElementType = 'div',
  AdditionalProps = object,
> = OverrideProps<TablePaginationTypeMap<AdditionalProps, RootComponent>, RootComponent>;
