import * as React from 'react';
import { SvgIconProps } from '../SvgIcon';
import { IconButtonProps } from '../IconButton/IconButton.types';

export interface PageInfoProps {
  size?: 'small' | 'medium' | 'large';
}

export interface TablePaginationActionsProps extends React.HTMLAttributes<HTMLDivElement> {
  /**
   * Override or extend the styles applied to the component.
   */
  classes?: object;
  /**
   * The total number of items.
   */
  count: number;
  /**
   * If `true`, the component is disabled.
   * @default false
   */
  disabled?: boolean;
  /**
   * Accepts a function which returns a string value that provides a user-friendly name for the current page.
   * @param {string} type The link or button type to format ('first' | 'last' | 'next' | 'previous').
   * @returns {string}
   */
  getItemAriaLabel: (type: 'first' | 'last' | 'next' | 'previous') => string;
  /**
   * Callback fired when the page is changed.
   * @param {React.MouseEvent<HTMLButtonElement> | null} event The event source of the callback.
   * @param {number} page The page selected.
   */
  onPageChange: (event: React.MouseEvent<HTMLButtonElement> | null, page: number) => void;
  /**
   * The current page.
   */
  page: number;
  /**
   * The number of rows per page.
   */
  rowsPerPage: number;
  /**
   * If `true`, the first button is shown.
   */
  showFirstButton: boolean;
  /**
   * If `true`, the last button is shown.
   */
  showLastButton: boolean;
  /**
   * The props used for each slot.
   */
  slotProps?: {
    firstButton?: Partial<IconButtonProps>;
    lastButton?: Partial<IconButtonProps>;
    nextButton?: Partial<IconButtonProps>;
    previousButton?: Partial<IconButtonProps>;
    firstButtonIcon?: Partial<SvgIconProps>;
    lastButtonIcon?: Partial<SvgIconProps>;
    nextButtonIcon?: Partial<SvgIconProps>;
    previousButtonIcon?: Partial<SvgIconProps>;
  };
  /**
   * The components used for each slot.
   */
  slots?: TablePaginationActionsSlots;
  /**
   * The size of the component.
   * @default 'medium'
   */
  size?: 'small' | 'medium' | 'large';
  /**
   * Custom render function for page info content
   * @param {number} page Current page number (0-based)
   * @param {number} totalPages Total number of pages
   * @returns {React.ReactNode}
   */
  renderPageInfo: (page: number, totalPages: number) => React.ReactNode;
}

export interface TablePaginationActionsSlots {
  /**
   * The component that renders the first button.
   * @default IconButton
   */
  firstButton?: React.ElementType;
  /**
   * The component that renders the last button.
   * @default IconButton
   */
  lastButton?: React.ElementType;
  /**
   * The component that renders the next button.
   * @default IconButton
   */
  nextButton?: React.ElementType;
  /**
   * The component that renders the previous button.
   * @default IconButton
   */
  previousButton?: React.ElementType;
  /**
   * The component that renders the first button icon.
   * @default FirstPageIcon
   */
  firstButtonIcon?: React.ElementType;
  /**
   * The component that renders the last button icon.
   * @default LastPageIcon
   */
  lastButtonIcon?: React.ElementType;
  /**
   * The component that renders the next button icon.
   * @default KeyboardArrowRight
   */
  nextButtonIcon?: React.ElementType;
  /**
   * The component that renders the previous button icon.
   * @default KeyboardArrowLeft
   */
  previousButtonIcon?: React.ElementType;
}
