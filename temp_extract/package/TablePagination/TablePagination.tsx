'use client';
import * as React from 'react';
import composeClasses from '@mui/utils/composeClasses';
import { styled } from '@pigment-css/react';
import { TablePaginationActions } from './TablePaginationActions';
import capitalize from '@mui/utils/capitalize';
import useId from '@mui/utils/useId';
import { getTablePaginationUtilityClass } from './TablePagination.classes';
import { TablePaginationOwnerState, TablePaginationProps } from './TablePagination.types';
import useSlotProps from '@mui/utils/useSlotProps';
import { Option } from '../Option';
import { Dropdown } from '../Dropdown';
import { useLocale } from '../Locale';
import enUS from '../Locale/en_US';

const useUtilityClasses = (ownerState: TablePaginationOwnerState) => {
  const { size, disabled } = ownerState;
  const slots = {
    root: ['root', size && `size${capitalize(size)}`, disabled && 'disabled'],
    dropdownLabel: ['dropdownLabel'],
    dropdown: ['dropdown'],
    dropdownOption: ['dropdownOption'],
    actions: ['actions'],
  };

  return composeClasses(slots, getTablePaginationUtilityClass, {});
};

const TablePaginationRoot = styled('div', {
  name: 'NovaTablePagination',
  slot: 'Root',
  overridesResolver: (props, styles) => styles.root,
})<TablePaginationOwnerState>(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  color: theme.vars.palette.onSurfaceVariant,
  variants: [
    {
      props: { size: 'small' },
      style: {
        height: '32px',
        fontSize: theme.vars.sys.size.typescale.labelSmall.size.small,
      },
    },
    {
      props: { size: 'medium' },
      style: {
        height: '40px',
        fontSize: theme.vars.sys.size.typescale.labelSmall.size.medium,
      },
    },
    {
      props: { size: 'large' },
      style: {
        height: '52px',
        fontSize: theme.vars.sys.size.typescale.labelSmall.size.large,
      },
    },
    {
      props: { disabled: true },
      style: {
        color: theme.vars.palette.onBackgroundDisabled,
      },
    },
  ],
}));

const TablePaginationDropdownGroup = styled('div')({
  display: 'flex',
  alignItems: 'center',
  marginRight: 'auto',
});

const TablePaginationDropdownLabel = styled('p', {
  name: 'NovaTablePagination',
  slot: 'DropdownLabel',
  overridesResolver: (props, styles) => styles.dropdownLabel,
})(({ theme }) => ({
  margin: 0,
  marginRight: 4,
  display: 'flex',
  alignItems: 'center',
}));

const TablePaginationDropdown = styled(Dropdown, {
  name: 'NovaTablePagination',
  slot: 'Dropdown',
  overridesResolver: (props, styles) => styles.dropdown,
})(({ theme }) => ({
  backgroundColor: 'inherit',
  border: 'none',
}));

// eslint-disable-next-line react/display-name
export const TablePagination = React.forwardRef<HTMLDivElement, TablePaginationProps>(
  function TablePagination(props, ref) {
    const tablePaginationLocale = useLocale('TablePagination');
    const {
      ActionsComponent = TablePaginationActions,
      component,
      count,
      disabled = false,
      getItemAriaLabel = enUS.TablePagination.getItemAriaLabel,
      labelRowsPerPage = enUS.TablePagination.labelRowsPerPage,
      renderPageInfo = enUS.TablePagination.renderPageInfo,
      onPageChange,
      onRowsPerPageChange,
      page,
      rowsPerPage = 10,
      rowsPerPageOptions = [10, 25, 50, 100],
      showFirstButton = false,
      showLastButton = false,
      slotProps = {},
      slots = {},
      size = 'medium',
      ...other
    } = { ...tablePaginationLocale, ...props };

    const ownerState = {
      ...props,
      rowsPerPage,
      rowsPerPageOptions,
      size,
      disabled,
      showFirstButton,
      showLastButton,
    };
    const classes = useUtilityClasses(ownerState);
    const dropdownId = useId(slotProps?.dropdown?.id);

    const externalForwardedProps = { slots, slotProps, ...other };

    const RootSlot = slots.root ?? TablePaginationRoot;
    const rootSlotProps = useSlotProps({
      elementType: TablePaginationRoot,
      externalSlotProps: slotProps.root,
      externalForwardedProps,
      additionalProps: {
        ref,
        as: component,
      },
      ownerState,
      className: classes.root,
    });

    const DropdownLabelSlot = slots.dropdownLabel ?? TablePaginationDropdownLabel;
    const dropdownLabelSlotProps = useSlotProps({
      elementType: DropdownLabelSlot,
      externalSlotProps: slotProps.dropdownLabel,
      ownerState,
      className: classes.dropdownLabel,
    });

    const DropdownSlot = slots.dropdown ?? TablePaginationDropdown;
    const dropdownSlotProps = useSlotProps({
      elementType: DropdownSlot,
      externalSlotProps: slotProps.dropdown,
      ownerState,
      additionalProps: {
        id: dropdownId,
      },
      className: classes.dropdown,
    });

    const DropdownOptionSlot = slots.dropdownOption ?? Option;
    const dropdownOptionSlotProps = useSlotProps({
      elementType: DropdownOptionSlot,
      externalSlotProps: slotProps.dropdownOption,
      ownerState,
      className: classes.dropdownOption,
    });

    return (
      <RootSlot {...rootSlotProps}>
        {rowsPerPageOptions.length > 1 && (
          <TablePaginationDropdownGroup>
            <DropdownLabelSlot {...dropdownLabelSlotProps}>{labelRowsPerPage}</DropdownLabelSlot>

            <DropdownSlot
              value={rowsPerPage}
              onChange={onRowsPerPageChange}
              disabled={disabled}
              size={size}
              {...dropdownSlotProps}
            >
              {rowsPerPageOptions.map((rowsPerPageOption) => {
                const value = typeof rowsPerPageOption === 'number' ? rowsPerPageOption : rowsPerPageOption.value;
                const label = typeof rowsPerPageOption === 'number' ? rowsPerPageOption : rowsPerPageOption.label;
                return (
                  <DropdownOptionSlot {...dropdownOptionSlotProps} key={value} value={value}>
                    {label}
                  </DropdownOptionSlot>
                );
              })}
            </DropdownSlot>
          </TablePaginationDropdownGroup>
        )}
        <ActionsComponent
          className={classes.actions}
          count={count}
          onPageChange={onPageChange}
          page={page}
          rowsPerPage={rowsPerPage}
          showFirstButton={showFirstButton}
          showLastButton={showLastButton}
          slotProps={slotProps.actions}
          slots={slots.actions}
          getItemAriaLabel={getItemAriaLabel}
          disabled={disabled}
          size={size}
          renderPageInfo={renderPageInfo}
        />
      </RootSlot>
    );
  },
);
