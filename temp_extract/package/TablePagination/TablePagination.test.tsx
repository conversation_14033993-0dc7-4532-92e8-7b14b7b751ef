/// <reference types="@testing-library/jest-dom" />
import React from 'react';
import { expect, test, describe, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { TablePagination } from './TablePagination';

describe('TablePagination Component', () => {
  const defaultProps = {
    count: 100,
    page: 0,
    rowsPerPage: 10,
    onPageChange: vi.fn(),
  };

  test('renders basic table pagination correctly', () => {
    render(<TablePagination {...defaultProps} />);
    expect(screen.getByText('Items per page:')).toBeDefined();
    expect(screen.getByRole('combobox')).toBeDefined();
    expect(screen.getByText(/Page \d+ of \d+/)).toBeDefined();
  });

  test('can pass data-testid', () => {
    render(<TablePagination {...defaultProps} data-testid="tablePagination" />);
    const root = screen.getByTestId('tablePagination');
    expect(root).toHaveClass('NovaTablePagination-root');
  });

  test('handles custom className', () => {
    const customClass = 'custom-table-pagination';
    render(<TablePagination {...defaultProps} className={customClass} data-testid="tablePagination" />);
    const root = screen.getByTestId('tablePagination');
    expect(root).toHaveClass('NovaTablePagination-root');
    expect(root).toHaveClass(customClass);
  });

  test('displays correct rows per page options', () => {
    const rowsPerPageOptions = [5, 10, 25];
    render(<TablePagination {...defaultProps} rowsPerPageOptions={rowsPerPageOptions} />);

    fireEvent.click(screen.getByRole('combobox'));
    expect(screen.getByText('25')).toBeInTheDocument();
  });

  test('handles rows per page change', () => {
    const handleRowsPerPageChange = vi.fn();
    render(<TablePagination {...defaultProps} onRowsPerPageChange={handleRowsPerPageChange} />);

    fireEvent.click(screen.getByRole('combobox'));
    fireEvent.click(screen.getByText('100'));

    expect(handleRowsPerPageChange).toHaveBeenCalled();
  });

  test('can be disabled', () => {
    render(<TablePagination {...defaultProps} disabled />);
    const buttons = screen.getAllByRole('button');
    buttons.forEach((button) => {
      expect(button).toHaveProperty('disabled', true);
    });

    const select = screen.getByRole('combobox');
    expect(select).toHaveProperty('disabled', true);
  });

  test('shows navigation buttons correctly', () => {
    render(<TablePagination {...defaultProps} showFirstButton showLastButton />);

    expect(screen.getByRole('button', { name: /first/i })).toBeDefined();
    expect(screen.getByRole('button', { name: /previous/i })).toBeDefined();
    expect(screen.getByRole('button', { name: /next/i })).toBeDefined();
    expect(screen.getByRole('button', { name: /last/i })).toBeDefined();
  });

  test('displays correct page information', () => {
    render(<TablePagination {...defaultProps} page={1} rowsPerPage={10} count={100} />);
    expect(screen.getByText(/page 2 of 10/i)).toBeDefined();
  });

  test('handles page navigation', () => {
    const handlePageChange = vi.fn();
    render(<TablePagination {...defaultProps} onPageChange={handlePageChange} showFirstButton showLastButton />);

    fireEvent.click(screen.getByRole('button', { name: /next/i }));
    expect(handlePageChange).toHaveBeenCalledWith(expect.anything(), 1);

    fireEvent.click(screen.getByRole('button', { name: /last/i }));
    expect(handlePageChange).toHaveBeenCalledWith(expect.anything(), 9); // For 100 items with 10 per page
  });

  test('customizes rows per page label', () => {
    const customLabel = 'Custom rows per page:';
    render(<TablePagination {...defaultProps} labelRowsPerPage={customLabel} />);
    expect(screen.getByText(customLabel)).toBeDefined();
  });
});
