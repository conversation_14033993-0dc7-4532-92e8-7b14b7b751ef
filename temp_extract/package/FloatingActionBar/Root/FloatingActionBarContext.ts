import * as React from 'react';
import type { FloatingActionBarRootProps } from './FloatingActionBar.types';

interface FloatingActionBarContextValue {
  size?: FloatingActionBarRootProps['size'];
  orientation?: FloatingActionBarRootProps['orientation'];
}

const FloatingActionBarContext = React.createContext<FloatingActionBarContextValue>({});

if (process.env.NODE_ENV !== 'production') {
  FloatingActionBarContext.displayName = 'FloatingActionBarContext';
}

export default FloatingActionBarContext;
