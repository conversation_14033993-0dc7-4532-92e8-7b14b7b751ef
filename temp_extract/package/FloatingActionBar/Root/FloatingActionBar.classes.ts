import {
  unstable_generateUtilityClasses as generateUtilityClasses,
  unstable_generateUtilityClass as generateUtilityClass,
} from '@mui/utils';

export interface FloatingActionBarClasses {
  /** Styles applied to the root element. */
  root: string;

  /** Styles applied to the root element if `orientation="horizontal"`. */
  horizontal: string;
  /** Styles applied to the root element if `orientation="vertical"`. */
  vertical: string;

  /** Styles name applied to the root element if `size="small"`. */
  sizeSmall: string;
  /** Styles name applied to the root element if `size="medium"`. */
  sizeMedium: string;
  /** Styles name applied to the root element if `size="large"`. */
  sizeLarge: string;
}

export type FloatingActionBarClassKey = keyof FloatingActionBarClasses;

export function getFloatingActionBarUtilityClass(slot: string): string {
  return generateUtilityClass('NovaFloatingActionBar', slot);
}

const floatingActionBarRootClasses: FloatingActionBarClasses = generateUtilityClasses('NovaFloatingActionBar', [
  'root',
  'horizontal',
  'vertical',
  'sizeSmall',
  'sizeMedium',
  'sizeLarge',
]);

export default floatingActionBarRootClasses;
