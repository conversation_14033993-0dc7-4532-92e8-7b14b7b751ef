import { FloatingActionBarRoot } from './Root';
import { FloatingAction } from './Item';

export { FloatingActionBarRoot, floatingActionBarRootClasses } from './Root';
export { FloatingAction, floatingActionClasses } from './Item';

export type { FloatingActionBarRootProps } from './Root';
export type { FloatingActionProps } from './Item';

export const FloatingActionBar = {
  Root: FloatingActionBarRoot,
  Item: FloatingAction,
};
