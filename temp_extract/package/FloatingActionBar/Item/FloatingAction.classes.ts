import {
  unstable_generateUtilityClasses as generateUtilityClasses,
  unstable_generateUtilityClass as generateUtilityClass,
} from '@mui/utils';

export interface FloatingActionClasses {
  /** Styles applied to the root element. */
  root: string;
  /** Styles applied to the dropdown element. */
  dropdown: string;
}

export type FloatingActionClassKey = keyof FloatingActionClasses;

export function getFloatingActionUtilityClass(slot: string): string {
  return generateUtilityClass('NovaFloatingAction', slot);
}

export const FloatingActionClasses: FloatingActionClasses = generateUtilityClasses('NovaFloatingAction', [
  'root',
  'dropdown',
]);

export default FloatingActionClasses;
