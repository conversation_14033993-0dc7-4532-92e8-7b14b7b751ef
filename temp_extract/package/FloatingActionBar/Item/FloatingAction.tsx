'use client';

import * as React from 'react';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import useSlotProps from '@mui/utils/useSlotProps';
import { styled } from '@pigment-css/react';
import { getFloatingActionUtilityClass } from './FloatingAction.classes';
import { FloatingActionOwnerState, FloatingActionProps } from './FloatingAction.types';
import { ToggleIconButton } from '../../ToggleIconButton';
import { Divider } from '../../Divider';
import FloatingActionBarContext from '../../FloatingActionBar/Root/FloatingActionBarContext';

const useUtilityClasses = () => {
  const slots = {
    root: ['root'],
    dropdown: ['dropdown'],
  };

  return composeClasses(slots, getFloatingActionUtilityClass, {});
};

const FloatingActionRoot = styled('div')<FloatingActionOwnerState>(({ theme }) => ({
  display: 'flex',
  flexDirection: 'row',
  alignItems: 'center',
  variants: [
    {
      props: { orientation: 'vertical' },
      style: {
        flexDirection: 'column',
      },
    },
  ],
}));

const FloatingActionButton = styled(ToggleIconButton)<FloatingActionOwnerState>(({ theme }) => ({
  position: 'relative',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  border: 'none',
  borderRadius: '8px',
  color: theme.vars.palette.onSurfaceVariant,

  '&:focus-visible': {
    outline: `2px solid ${theme.vars.palette.outline}`,
    outlineOffset: 1,
    backgroundColor: `color-mix(in srgb, ${theme.vars.palette.backgroundStates}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.focusOnSurface})`,
  },
  variants: [
    {
      props: { disabled: true },
      style: {
        color: theme.vars.palette.onBackgroundDisabled,
      },
    },
    {
      props: { selected: true },
      style: {
        color: theme.vars.palette.onSecondaryContainer,
        backgroundColor: theme.vars.palette.secondaryContainer,
        '&:hover': {
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.secondaryContainer}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.hoverOnSurface})`,
        },
        '&:active': {
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.backgroundStates}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.pressOnSurface})`,
        },
      },
    },
    {
      props: { size: 'small' },
      style: {
        width: '40px',
      },
    },
    {
      props: { showDropdownIcon: true },
      style: {
        borderBottomRightRadius: 0,
      },
    },
  ],
}));

const FloatingActionDropdownIcon = styled('div')<FloatingActionOwnerState>(({ theme }) => ({
  position: 'absolute',
  boxSizing: 'border-box',
  right: '3px',
  bottom: '3px',
  width: '8px',
  height: '8px',
  border: '2px solid',
  borderTop: 'none',
  borderLeft: 'none',
  borderColor: 'inherit',
}));

const FloatingActionDivider = styled(Divider)<FloatingActionOwnerState>(({ theme }) => ({
  variants: [
    {
      props: { dividerOrientation: 'horizontal' },
      style: {
        marginTop: '4px',
      },
    },
    {
      props: { dividerOrientation: 'vertical' },
      style: { marginLeft: '4px' },
    },
  ],
}));

export const FloatingAction = React.forwardRef(function FloatingAction(
  props: FloatingActionProps,
  ref: React.Ref<HTMLDivElement>,
) {
  const {
    className,
    children,
    size: sizeProp,
    selected = false,
    showDivider = false,
    showDropdownIcon = false,
    ...other
  } = props;

  const contextValue = React.useContext(FloatingActionBarContext);
  const size = sizeProp ?? contextValue?.size;
  const orientation = contextValue?.orientation;

  const dividerOrientation = orientation === 'horizontal' ? 'vertical' : 'horizontal';
  const ownerState = { ...props, size, showDropdownIcon, orientation, dividerOrientation };

  const classes = useUtilityClasses();

  const rootProps = useSlotProps({
    className: classes.root,
    elementType: FloatingActionRoot,
    externalForwardedProps: {},
    externalSlotProps: {},
    ownerState: ownerState,
  });

  const buttonProps = useSlotProps({
    elementType: FloatingActionButton,
    className: className,
    externalSlotProps: {},
    externalForwardedProps: other,
    additionalProps: {
      ref,
      size,
    },
    ownerState: ownerState,
  });

  const dropdownProps = useSlotProps({
    elementType: FloatingActionDropdownIcon,
    externalSlotProps: {},
    className: classes.dropdown,
    additionalProps: {
      size,
      'data-testid': 'NovaFloatingAction-dropdown',
    },
    ownerState: ownerState,
  });

  const dividerProps = useSlotProps({
    elementType: FloatingActionDivider,
    externalSlotProps: {},
    externalForwardedProps: {},
    additionalProps: {
      orientation: dividerOrientation as 'horizontal' | 'vertical',
    },
    ownerState: ownerState,
  });

  return (
    <FloatingActionRoot {...rootProps}>
      <FloatingActionButton variant="neutral" {...buttonProps}>
        {children}
        {showDropdownIcon && <FloatingActionDropdownIcon {...dropdownProps} />}
      </FloatingActionButton>
      {showDivider && <FloatingActionDivider {...dividerProps} />}
    </FloatingActionRoot>
  );
});
