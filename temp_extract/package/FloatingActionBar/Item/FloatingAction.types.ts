import { ToggleIconButtonProps } from '../../ToggleIconButton';

export interface FloatingActionProps
  extends Omit<ToggleIconButtonProps, 'variant' | 'color' | 'startIcon' | 'endIcon' | 'fullWidth'> {
  /**
   * Display a divider after the component.
   * - When FloatingActionBar is horizontal, the divider will be vertical (on the right)
   * - When FloatingActionBar is vertical, the divider will be horizontal (on the bottom)
   * @default false
   */
  showDivider?: boolean;
  /**
   * Whether a dropdown icon should appear on the corner.
   * @default false
   */
  showDropdownIcon?: boolean;
  /**
   * Whether the component is selected.
   * @default false
   */
  selected?: boolean;
  /**
   * How large the FloatingAction should be.
   * @default 'medium'
   */
  size?: 'small' | 'medium' | 'large';
}

export interface FloatingActionOwnerState extends FloatingActionProps {
  showDropdownIcon?: boolean;
  orientation?: 'horizontal' | 'vertical';
  dividerOrientation?: 'horizontal' | 'vertical';
}
