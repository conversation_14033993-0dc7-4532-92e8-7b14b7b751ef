import * as React from 'react';
import { render, screen, cleanup, fireEvent } from '@testing-library/react';
import { describe, expect, it, afterEach, vi } from 'vitest';
import { FloatingAction } from './FloatingAction';

afterEach(() => {
  cleanup();
});

describe('<FloatingAction />', () => {
  it('should render with default slot classes', () => {
    render(<FloatingAction>Click Me</FloatingAction>);
    const button = screen.getByRole('button');
    expect(button).toHaveClass('NovaToggleIconButton-root');
    expect(button).toHaveClass('NovaToggleIconButton-neutral');
  });
  it('should render with disabled slot class', () => {
    render(
      <FloatingAction data-testid="btn" disabled>
        Click Me
      </FloatingAction>,
    );
    const button = screen.getByTestId('btn');
    expect(button).toHaveClass('Mui-disabled');
  });
  it('should render with small size slot class', () => {
    render(<FloatingAction size="small">Click Me</FloatingAction>);
    const button = screen.getByRole('button');
    expect(button).toHaveClass('NovaButtonBase-sizeSmall');
  });
  it('should render with large size slot class', () => {
    render(<FloatingAction size="large">Click Me</FloatingAction>);
    const button = screen.getByRole('button');
    expect(button).toHaveClass('NovaButtonBase-sizeLarge');
  });
  it('should render with custom slot class', () => {
    render(
      <FloatingAction data-testid="btn" className="custom-class">
        Click Me
      </FloatingAction>,
    );
    const button = screen.getByTestId('btn');
    expect(button).toHaveClass('custom-class');
  });
  it('should pass `slotProps` down to slots', () => {
    render(<FloatingAction slotProps={{ root: { 'data-testid': 'root' } }}>Click Me</FloatingAction>);
    const button = screen.getByTestId('root');
    expect(button).toBeInTheDocument();
  });

  it('handle click event', () => {
    const onClick = vi.fn();
    render(<FloatingAction onClick={onClick}>Click Me</FloatingAction>);
    const button = screen.getByRole('button');
    fireEvent.click(button);
    expect(onClick).toHaveBeenCalled();
  });
});
