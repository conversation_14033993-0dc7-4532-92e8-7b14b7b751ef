import { expect, test, describe } from 'vitest';
import { render, screen } from '@testing-library/react';
import React from 'react';
import { Slider } from './Slider';

describe('Slider', () => {
  test('renders basic slider component correctly', () => {
    const view = render(<Slider defaultValue={30} />);
    expect(view).toBeTruthy();
  });

  test('renders a slider with the data-testid property', () => {
    render(<Slider defaultValue={30} data-testid="NovaSlider-root" />);
    expect(screen.getByTestId('NovaSlider-root')).toBeDefined();
  });

  test('should render the rail as the first child of the Slider', () => {
    const { container } = render(<Slider />);

    const sliderComponent = container.firstChild!;

    expect(sliderComponent.childNodes[0]).to.have.property('tagName', 'SPAN');
    expect(sliderComponent.childNodes[0]).toHaveClass('NovaSlider-rail');
  });

  test('should show formatted label', () => {
    const { getByText } = render(
      <Slider value={10} valueLabelDisplay="on" valueLabelFormat={(value) => `${value}px`} />,
    );

    expect(getByText('10px')).toBeVisible();
  });
});
