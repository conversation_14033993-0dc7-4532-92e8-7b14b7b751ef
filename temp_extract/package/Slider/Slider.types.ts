import * as React from 'react';
import { OverrideProps } from '@mui/types';
import { SxProps } from '../types/theme';
import { CreateSlotsAndSlotProps, SlotProps } from '../types/slot';
import { UseSliderParameters } from '../internal/hooks/useSlider';
import { BaseSliderClasses } from './Slider.classes';

export type SliderSlot = 'root' | 'mark' | 'markLabel' | 'rail' | 'track' | 'thumb' | 'valueLabel' | 'input';

export interface SliderSlots {
  /**
   * The component that renders the root.
   * @default 'span'
   */
  root?: React.ElementType;
  /**
   * The component that renders the track.
   * @default 'span'
   */
  track?: React.ElementType;
  /**
   * The component that renders the rail.
   * @default 'span'
   */
  rail?: React.ElementType;
  /**
   * The component that renders the thumb.
   * @default 'span'
   */
  thumb?: React.ElementType;
  /**
   * The component that renders the mark.
   * @default 'span'
   */
  mark?: React.ElementType;
  /**
   * The component that renders the mark label.
   * @default 'span'
   */
  markLabel?: React.ElementType;
  /**
   * The component that renders the value label.
   * @default 'span'
   */
  valueLabel?: React.ElementType;
  /**
   * The component that renders the input.
   * @default 'input'
   */
  input?: React.ElementType;
}

export type SliderSlotsAndSlotProps = CreateSlotsAndSlotProps<
  SliderSlots,
  {
    root: SlotProps<'span', Record<string, never>, SliderOwnerState>;
    track: SlotProps<'span', Record<string, never>, SliderOwnerState>;
    rail: SlotProps<'span', Record<string, never>, SliderOwnerState>;
    thumb: SlotProps<'span', Record<string, never>, SliderOwnerState>;
    mark: SlotProps<'span', Record<string, never>, SliderOwnerState & { percent?: number }>;
    markLabel: SlotProps<'span', Record<string, never>, SliderOwnerState>;
    valueLabel: SlotProps<'span', Record<string, never>, SliderOwnerState>;
    input: SlotProps<'input', Record<string, never>, SliderOwnerState>;
  }
>;

export type SliderTypeMap<D extends React.ElementType = 'span', P = Record<string, never>> = {
  props: P &
    Omit<UseSliderParameters, 'ref'> & {
      /**
       * The label of the slider.
       */
      'aria-label'?: string;
      /**
       * A string value that provides a user-friendly name for the current value of the slider.
       */
      'aria-valuetext'?: string;
      /**
       * Accepts a function which returns a string value that provides a user-friendly name for the thumb labels of the slider.
       * This is important for screen reader users.
       * @param {number} index The thumb label's index to format.
       * @returns {string}
       */
      getAriaLabel?: (index: number) => string;
      /**
       * Accepts a function which returns a string value that provides a user-friendly name for the current value of the slider.
       * This is important for screen reader users.
       * @param {number} value The thumb label's value to format.
       * @param {number} index The thumb label's index to format.
       * @returns {string}
       */
      getAriaValueText?: (value: number, index: number) => string;
      /**
       * The track presentation:
       *
       * - `normal` the track will render a bar representing the slider value.
       * - `inverted` the track will render a bar representing the remaining slider value.
       * - `false` the track will render without a bar.
       * @default 'normal'
       */
      track?: 'normal' | false | 'inverted';
      /**
       * The format function the value label's value.
       *
       * When a function is provided, it should have the following signature:
       *
       * - {number} value The value label's value to format
       * - {number} index The value label's index to format
       * @param {any} x
       * @returns {any}
       * @default function Identity(x) {
       *   return x;
       * }
       */
      valueLabelFormat?: string | ((value: number, index: number) => React.ReactNode);
      /**
       * Override or extend the styles applied to the component.
       */
      classes?: Partial<BaseSliderClasses>;
      /**
       * The system prop that allows defining system overrides as well as additional CSS styles.
       */
      sx?: SxProps;
      /**
       * Controls when the value label is displayed:
       *
       * - `auto` the value label will display when the thumb is hovered or focused.
       * - `on` will display persistently.
       * - `off` will never display.
       * @default 'off'
       */
      valueLabelDisplay?: 'on' | 'auto' | 'off';
      /**
       * The component used for the Root slot.
       * Either a string to use a HTML element or a component.
       */
      component?: React.ElementType;
    } & SliderSlotsAndSlotProps;
  defaultComponent: D;
};

export type SliderProps<
  D extends React.ElementType = SliderTypeMap['defaultComponent'],
  P = { component?: React.ElementType },
> = OverrideProps<SliderTypeMap<D, P>, D>;

export interface SliderOwnerState extends SliderProps, Record<string, unknown> {
  /**
   * If `true`, the thumb is in dragging state.
   */
  dragging: boolean;
  /**
   * If `true`, some of the marks has `label` property.
   */
  marked: boolean;
}
