'use client';
import * as React from 'react';
import clsx from 'clsx';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import { styled } from '@pigment-css/react';
import useSlotProps from '@mui/utils/useSlotProps';
import sliderClasses, { getSliderUtilityClass } from './Slider.classes';
import { SliderProps, SliderOwnerState } from './Slider.types';
import { useSlider } from '../internal/hooks/useSlider';
import { isHostComponent } from '../utils';
import { valueToPercent } from '../internal/hooks/useSlider/useSlider';

const useUtilityClasses = (ownerState: SliderOwnerState) => {
  const { disabled, dragging, marked, orientation, track } = ownerState;

  const slots = {
    root: [
      'root',
      disabled && 'disabled',
      dragging && 'dragging',
      marked && 'marked',
      orientation === 'vertical' && 'vertical',
      track === 'inverted' && 'trackInverted',
      track === false && 'trackFalse',
    ],
    rail: ['rail'],
    track: ['track'],
    thumb: ['thumb', disabled && 'disabled'],
    input: ['input'],
    mark: ['mark'],
    markActive: ['markActive'],
    markLabel: ['markLabel'],
    markLabelActive: ['markLabelActive'],
    valueLabel: ['valueLabel'],
    valueLabelOpen: ['valueLabelOpen'],
    active: ['active'],
    focusVisible: ['focusVisible'],
  };

  return composeClasses(slots, getSliderUtilityClass, {});
};

const SliderRoot = styled('span')<SliderOwnerState>(({ theme }) => ({
  '--nova-slider-size': 'max(42px, max(var(--nova-slider-thumbSize), var(--nova-slider-trackSize)))',
  '--nova-slider-trackRadius': 'var(--nova-slider-size)',
  '--nova-slider-markBackground': theme.vars.palette.primary,
  '--nova-slider-markSize': '6px',
  '--nova-slider-trackSize': '16px',
  '--nova-slider-thumbSize': '20px',
  '--nova-slider-thumbRadius': 'calc(var(--nova-slider-thumbSize) / 2)',
  '--nova-slider-thumbWidth': 'var(--nova-slider-thumbSize)',
  '--nova-slider-trackColor': theme.vars.palette.primary,
  '--nova-slider-thumbBackground': theme.vars.palette.primary,
  '--nova-slider-thumbColor': theme.vars.palette.onPrimary,
  '--nova-slider-trackBackground': theme.vars.palette.primary,
  '--nova-slider-trackBorderColor': theme.vars.palette.primary,
  '--nova-slider-railBackground': theme.vars.palette.secondaryContainer,
  [`& .${sliderClasses.markActive}`]: {
    '--nova-slider-markBackground': theme.vars.palette.onPrimary,
  },
  variants: [
    {
      props: { orientation: 'horizontal' },
      style: {
        padding: 'calc(var(--nova-slider-size) / 2) 0',
        width: '100%',
      },
    },
    {
      props: { orientation: 'vertical' },
      style: {
        padding: '0 calc(var(--nova-slider-size) / 2)',
        height: '100%',
      },
    },
  ],
  '&:hover': {
    '@media (hover: hover)': {
      '--nova-slider-trackColor': theme.vars.palette.primary,
      '--nova-slider-thumbBackground': theme.vars.palette.primary,
      '--nova-slider-thumbColor': theme.vars.palette.onPrimary,
      '--nova-slider-trackBackground': theme.vars.palette.primary,
      '--nova-slider-trackBorderColor': theme.vars.palette.primary,
      '--nova-slider-railBackground': theme.vars.palette.secondaryContainer,
    },
  },
  '&:active': {
    '--nova-slider-trackColor': theme.vars.palette.primary,
    '--nova-slider-thumbBackground': theme.vars.palette.primary,
    '--nova-slider-thumbColor': theme.vars.palette.onPrimary,
    '--nova-slider-trackBackground': theme.vars.palette.primary,
    '--nova-slider-trackBorderColor': theme.vars.palette.primary,
    '--nova-slider-railBackground': theme.vars.palette.secondaryContainer,
  },
  [`&.${sliderClasses.disabled}`]: {
    pointerEvents: 'none',
    color: theme.vars.palette.backgroundDisabled,
    '--nova-slider-trackColor': theme.vars.palette.onBackgroundDisabled,
    '--nova-slider-thumbBackground': theme.vars.palette.onBackgroundDisabled,
    '--nova-slider-thumbColor': theme.vars.palette.onPrimary,
    '--nova-slider-trackBackground': theme.vars.palette.onBackgroundDisabled,
    '--nova-slider-trackBorderColor': theme.vars.palette.onBackgroundDisabled,
    '--nova-slider-railBackground': `color-mix(in srgb, ${theme.vars.palette.backgroundDisabled}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.disabledOnSurface})`,
    '--nova-slider-markBackground': theme.vars.palette.onBackgroundDisabled,
  },
  boxSizing: 'border-box',
  display: 'inline-block',
  position: 'relative',
  cursor: 'pointer',
  touchAction: 'none',
  WebkitTapHighlightColor: 'transparent',
}));

const SliderRail = styled('span')<SliderOwnerState>(({ theme }) => ({
  display: 'block',
  position: 'absolute',
  backgroundColor: 'var(--nova-slider-railBackground)',
  border: 'initial',
  variants: [
    {
      props: { orientation: 'horizontal' },
      style: {
        height: 'var(--nova-slider-trackSize)',
        top: '50%',
        left: 0,
        right: 0,
        transform: 'translateY(-50%)',
      },
    },
    {
      props: { orientation: 'vertical' },
      style: {
        width: 'var(--nova-slider-trackSize)',
        top: 0,
        bottom: 0,
        left: '50%',
        transform: 'translateX(-50%)',
      },
    },
    {
      props: { track: 'inverted' },
      style: {
        backgroundColor: 'var(--nova-slider-trackBackground)',
        border: 'var(--variant-borderWidth, 0px) solid var(--nova-slider-trackBorderColor)',
        opacity: 1,
      },
    },
  ],
  borderRadius: 'var(--nova-slider-trackRadius)',
}));

const SliderTrack = styled('span')<SliderOwnerState>(({ theme }) => ({
  display: 'block',
  position: 'absolute',
  color: 'var(--nova-slider-trackColor)',
  border: 'var(--variant-borderWidth, 0px) solid var(--nova-slider-trackBorderColor)',
  backgroundColor: 'var(--nova-slider-trackBackground)',
  variants: [
    {
      props: { orientation: 'horizontal' },
      style: {
        height: 'var(--nova-slider-trackSize)',
        top: '50%',
        transform: 'translateY(-50%)',
        borderRadius: 'var(--nova-slider-trackRadius)',
        left: 0,
      },
    },
    {
      props: { orientation: 'vertical' },
      style: {
        width: 'var(--nova-slider-trackSize)',
        left: '50%',
        transform: 'translateX(-50%)',
        borderRadius: 'var(--nova-slider-trackRadius)',
        bottom: 0,
      },
    },
    {
      props: { track: false },
      style: {
        display: 'none',
      },
    },
    {
      props: { track: 'inverted' },
      style: {
        backgroundColor: 'var(--nova-slider-railBackground)',
        border: 'initial',
      },
    },
  ],
}));

const SliderThumb = styled('span')<SliderOwnerState>(({ theme }) => ({
  position: 'absolute',
  boxSizing: 'border-box',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  width: '4px',
  height: '44px',
  borderRadius: '2px',
  boxShadow: 'var(--nova-slider-thumbShadow)',
  backgroundColor: 'var(--nova-slider-thumbBackground)',
  color: 'var(--nova-slider-thumbColor)',
  variants: [
    {
      props: { orientation: 'horizontal' },
      style: {
        top: '50%',
        transform: 'translate(-50%, -50%)',
      },
    },
    {
      props: { orientation: 'vertical' },
      style: {
        left: '50%',
        width: '44px',
        height: '4px',
        transform: 'translate(-50%, 50%)',
      },
    },
  ],
  '&::before': {
    boxSizing: 'border-box',
    content: '""',
    display: 'block',
    position: 'absolute',
    background: 'transparent',
    top: -4,
    left: -4,
    right: -4,
    bottom: -4,
    border: '4px solid',
    borderColor: 'var(--nova-slider-thumbColor)',
    borderRadius: '4px',
  },
}));

const SliderMark = styled('span')<SliderOwnerState & { percent?: number }>(({ theme }) => ({
  position: 'absolute',
  width: 'var(--nova-slider-markSize)',
  height: 'var(--nova-slider-markSize)',
  borderRadius: 'var(--nova-slider-markSize)',
  backgroundColor: 'var(--nova-slider-markBackground)',
  variants: [
    {
      props: { orientation: 'horizontal' },
      style: {
        top: '50%',
        transform: `translate(calc(var(--nova-slider-markSize) / -2), -50%)`,
      },
    },
    {
      props: { orientation: 'vertical' },
      style: {
        left: '50%',
        transform: 'translate(-50%, calc(var(--nova-slider-markSize) / 2))',
      },
    },
    {
      props: { orientation: 'horizontal', percent: 0 },
      style: {
        transform: `translate(0, -50%)`,
      },
    },
    {
      props: { orientation: 'horizontal', percent: 100 },
      style: {
        transform: `translate(-100%, -50%)`,
      },
    },
    {
      props: { orientation: 'vertical', percent: 0 },
      style: {
        transform: `translate(-50%, 0)`,
      },
    },
    {
      props: { orientation: 'vertical', percent: 100 },
      style: {
        transform: `translate(-50%, -100%)`,
      },
    },
    {
      props: { disabled: true },
      style: {
        backgroundColor: theme.vars.palette.onBackgroundDisabled,
      },
    },
    {
      props: { disabled: true, marks: true },
      match: (props: any) => props?.step > 0,
      style: {
        [`&.${sliderClasses.markActive}`]: {
          backgroundColor: theme.vars.palette.onPrimary,
        },
      },
    },
  ],
}));

const SliderValueLabel = styled('span')<SliderOwnerState>(({ theme }) => ({
  ...theme.typography.labelSmall,
  paddingInline: theme.sys.viewport.spacing.padding.leftRight.md,
  paddingBlock: theme.sys.viewport.spacing.padding.leftRight.xs,
  minWidth: '24px',
  zIndex: 1,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  whiteSpace: 'nowrap',
  fontFamily: theme.typography.fontFamily,
  fontWeight: 400,
  position: 'absolute',
  backgroundColor: theme.vars.palette.inverseSurface,
  borderRadius: 16,
  color: theme.vars.palette.onPrimary,
  top: -4,
  left: '50%',
  transform: 'translate(-50%, -100%) scale(0)',
  transformOrigin: 'bottom center',
  transition: 'transform 0.2s ease-in-out',
  [`&.${sliderClasses.valueLabelOpen}`]: {
    transform: 'translate(-50%, -100%) scale(1)',
  },
}));

const SliderMarkLabel = styled('span')<SliderOwnerState>(({ theme }) => ({
  ...theme.typography.bodyMedium,
  color: theme.vars.palette.onSurfaceVariant,
  position: 'absolute',
  whiteSpace: 'nowrap',
  variants: [
    {
      props: { orientation: 'horizontal' },
      style: {
        top: 'calc(50% + 4px + (var(--nova-slider-size) / 2))',
        transform: 'translateX(-50%)',
      },
    },
    {
      props: { orientation: 'vertical' },
      style: {
        left: 'calc(50% + 8px + (var(--nova-slider-size) / 2))',
        transform: 'translateY(50%)',
      },
    },
  ],
}));

const SliderInput = styled('input')<SliderOwnerState>({});

// eslint-disable-next-line react/display-name
export const Slider = React.forwardRef((props: SliderProps, ref: React.ForwardedRef<Element>) => {
  const {
    'aria-label': ariaLabel,
    'aria-valuetext': ariaValuetext,
    className,
    classes: classesProp,
    disableSwap = false,
    disabled = false,
    defaultValue,
    getAriaLabel,
    getAriaValueText,
    max = 100,
    min = 0,
    marks: marksProp = false,
    name,
    onChange,
    onChangeCommitted,
    onMouseDown,
    orientation = 'horizontal',
    shiftStep = 10,
    scale = (x) => x,
    step = 1,
    tabIndex,
    track = 'normal',
    value: valueProp,
    valueLabelDisplay = 'off',
    valueLabelFormat = (x) => x,
    isRtl = false,
    component,
    slots = {},
    slotProps = {},
    ...other
  } = props;

  const ownerState = {
    ...props,
    marks: marksProp,
    classes: classesProp,
    disabled,
    defaultValue,
    disableSwap,
    isRtl,
    max,
    min,
    orientation,
    shiftStep,
    scale,
    step,
    track,
    valueLabelDisplay,
    valueLabelFormat,
  } as SliderOwnerState;

  const {
    axisProps,
    getRootProps,
    getHiddenInputProps,
    getThumbProps,
    open,
    active,
    axis,
    focusedThumbIndex,
    range,
    dragging,
    marks,
    values,
    trackOffset,
    trackLeap,
    getThumbStyle,
  } = useSlider({ ...ownerState, rootRef: ref });

  ownerState.marked = marks.length > 0 && marks.some((mark) => mark.label);
  ownerState.dragging = dragging;

  const trackStyle = {
    ...axisProps[axis].offset(trackOffset),
    ...axisProps[axis].leap(trackLeap),
  };

  const classes = useUtilityClasses(ownerState);
  const externalForwardedProps = { ...other, component, slots, slotProps };

  const SlotRoot = slots.root ?? SliderRoot;
  const rootProps = useSlotProps({
    elementType: SliderRoot,
    externalSlotProps: slotProps.root,
    externalForwardedProps,
    additionalProps: {
      ref,
      as: component,
    },
    ownerState,
    getSlotProps: getRootProps,
    className: clsx(classes.root, className),
  });

  const SlotRail = slots.rail ?? SliderRail;
  const railProps = useSlotProps({
    elementType: SliderRail,
    externalSlotProps: slotProps.rail,
    ownerState,
    className: classes.rail,
  });

  const SlotTrack = slots.track ?? SliderTrack;
  const trackProps = useSlotProps({
    elementType: SliderTrack,
    externalSlotProps: slotProps.track,
    additionalProps: {
      style: {
        ...trackStyle,
        ...(orientation === 'horizontal'
          ? {
              width: `${trackLeap}%`,
              left: `${trackOffset}%`,
            }
          : {
              height: `${trackLeap}%`,
              bottom: `${trackOffset}%`,
            }),
      },
    },
    ownerState,
    className: classes.track,
  });

  const SlotMark = slots.mark ?? SliderMark;
  const markProps = useSlotProps({
    elementType: SliderMark,
    externalSlotProps: slotProps.mark,
    ownerState,
    className: classes.mark,
  });

  const SlotMarkLabel = slots.markLabel ?? SliderMarkLabel;
  const markLabelProps = useSlotProps({
    elementType: SliderMarkLabel,
    externalSlotProps: slotProps.markLabel,
    ownerState,
    additionalProps: {
      'aria-hidden': true,
    },
    className: classes.markLabel,
  });

  const SlotThumb = slots.thumb ?? SliderThumb;
  const thumbProps = useSlotProps({
    elementType: SliderThumb,
    externalSlotProps: slotProps.thumb,
    ownerState,
    getSlotProps: getThumbProps,
    className: classes.thumb,
  });

  const SlotInput = slots.input ?? SliderInput;
  const inputProps = useSlotProps({
    elementType: SliderInput,
    externalSlotProps: slotProps.input,
    ownerState,
    getSlotProps: getHiddenInputProps,
    className: classes.input,
  });

  const SlotValueLabel = slots.valueLabel ?? SliderValueLabel;
  const valueLabelProps = useSlotProps({
    elementType: SliderValueLabel,
    externalSlotProps: slotProps.valueLabel,
    ownerState,
    className: classes.valueLabel,
  });

  return (
    <SlotRoot {...rootProps}>
      <SlotRail {...railProps} />
      <SlotTrack {...trackProps} />
      {marks
        .filter((mark) => mark.value >= min && mark.value <= max)
        .map((mark, index) => {
          const percent = valueToPercent(mark.value, min, max);
          let style = axisProps[axis].offset(percent);

          if (orientation === 'horizontal') {
            if (mark.value === min) {
              style = { ...style, left: '4px' };
            } else if (mark.value === max) {
              style = { ...style, left: 'calc(100% - 4px)' };
            }
          } else if (orientation === 'vertical') {
            if (mark.value === min) {
              style = { ...style, bottom: '16px' };
            } else if (mark.value === max) {
              style = { ...style, bottom: 'calc(100% - 16px)' };
            }
          }

          let markActive;
          if (track === false) {
            markActive = values.includes(mark.value);
          } else {
            markActive =
              (track === 'normal' &&
                (range
                  ? mark.value >= values[0] && mark.value <= values[values.length - 1]
                  : mark.value <= values[0])) ||
              (track === 'inverted' &&
                (range ? mark.value <= values[0] || mark.value >= values[values.length - 1] : mark.value >= values[0]));
          }

          return (
            <React.Fragment key={mark.value}>
              <SlotMark
                data-index={index}
                {...markProps}
                {...(!isHostComponent(SlotMark) && {
                  ownerState: { ...markProps.ownerState, percent },
                })}
                style={{ ...style, ...markProps.style }}
                className={clsx(markProps.className, {
                  [classes.markActive]: markActive,
                })}
              />
              {mark.label != null ? (
                <SlotMarkLabel
                  data-index={index}
                  {...markLabelProps}
                  style={{ ...style, ...markLabelProps.style }}
                  className={clsx(classes.markLabel, markLabelProps.className, {
                    [classes.markLabelActive]: markActive,
                  })}
                >
                  {mark.label}
                </SlotMarkLabel>
              ) : null}
            </React.Fragment>
          );
        })}
      {values.map((value, index) => {
        const percent = valueToPercent(value, min, max);
        let style = axisProps[axis].offset(percent);

        // Special positioning for min and max values to align with marks
        if (orientation === 'horizontal') {
          if (value === min) {
            style = { ...style, left: '4px' };
          } else if (value === max) {
            style = { ...style, left: 'calc(100% - 4px)' };
          }
        } else if (orientation === 'vertical') {
          if (value === min) {
            style = { ...style, bottom: '16px' };
          } else if (value === max) {
            style = { ...style, bottom: 'calc(100% - 4px)' };
          }
        }

        return (
          <SlotThumb
            key={index}
            data-index={index}
            {...thumbProps}
            className={clsx(thumbProps.className, {
              [classes.active]: active === index,
              [classes.focusVisible]: focusedThumbIndex === index,
            })}
            style={{
              ...style,
              ...getThumbStyle(index),
              ...thumbProps.style,
            }}
          >
            <SlotInput
              data-index={index}
              aria-label={getAriaLabel ? getAriaLabel(index) : ariaLabel}
              aria-valuenow={scale(value)}
              aria-valuetext={getAriaValueText ? getAriaValueText(scale(value), index) : ariaValuetext}
              value={values[index]}
              {...inputProps}
            />
            {valueLabelDisplay !== 'off' ? (
              <SlotValueLabel
                {...valueLabelProps}
                className={clsx(valueLabelProps.className, {
                  [classes.valueLabelOpen]: open === index || active === index || valueLabelDisplay === 'on',
                })}
              >
                {typeof valueLabelFormat === 'function' ? valueLabelFormat(scale(value), index) : valueLabelFormat}
              </SlotValueLabel>
            ) : null}
          </SlotThumb>
        );
      })}
    </SlotRoot>
  );
});
