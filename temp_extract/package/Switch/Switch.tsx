'use client';

import * as React from 'react';
import { styled } from '@pigment-css/react';
import {
  EventHandlers,
  unstable_capitalize as capitalize,
  unstable_composeClasses as composeClasses,
} from '@mui/utils';
import useSlotProps from '@mui/utils/useSlotProps';
import { SwitchProps, SwitchOwnerState } from './Switch.types';
import switchClasses, { getSwitchUtilityClass } from './Switch.classes';
import { useSwitch } from '../internal/hooks/useSwitch';
import FormControlContext from '../FormControl/FormControlContext';

const useUtilityClasses = (ownerState: SwitchOwnerState) => {
  const { checked, disabled, focusVisible, size, onIcon, offIcon } = ownerState;

  const slots = {
    root: [
      'root',
      checked && 'checked',
      disabled && 'disabled',
      focusVisible && 'focusVisible',
      size && `size${capitalize(size)}`,
    ],
    thumb: [
      'thumb',
      checked && 'checked',
      disabled && 'disabled',
      focusVisible && 'focusVisible',
      Boolean(onIcon || offIcon) && 'withIcon',
    ],
    track: ['track', checked && 'checked'],
    action: ['action', focusVisible && 'focusVisible'],
    input: ['input'],
    startDecorator: ['startDecorator'],
    endDecorator: ['endDecorator'],
  };

  return composeClasses(slots, getSwitchUtilityClass, {});
};

const switchVariables = (theme: any) => ({
  '--nova-switch-thumbSize-medium-checked': '24px',
  '--nova-switch-thumbSize-small-checked': '16px',
  '--nova-switch-thumbSize-large-checked': '28px',
  '--nova-switch-thumbSize-medium-unchecked': '16px',
  '--nova-switch-thumbSize-small-unchecked': '12px',
  '--nova-switch-thumbSize-large-unchecked': '24px',
  '--nova-switch-trackWidth-medium': '52px',
  '--nova-switch-trackWidth-small': '44px',
  '--nova-switch-trackWidth-large': '56px',
  '--nova-switch-trackHeight-medium': '32px',
  '--nova-switch-trackHeight-small': '24px',
  '--nova-switch-trackHeight-large': '36px',
});

const SwitchRoot = styled('div')<SwitchProps>(({ theme }) => ({
  ...switchVariables(theme),
  display: 'inline-flex',
  alignItems: 'center',
  position: 'relative',
  borderRadius: '16px',
  cursor: 'pointer',
  flexShrink: 0,
  WebkitTapHighlightColor: 'transparent',
  userSelect: 'none',
  margin: '8px',
  minWidth: 'fit-content',

  [`&.${switchClasses.disabled}`]: {
    opacity: 0.4,
    cursor: 'default',
  },

  variants: [
    {
      props: { size: 'medium', startDecorator: undefined, endDecorator: undefined },
      style: {
        width: 'var(--nova-switch-trackWidth-medium)',
        height: 'var(--nova-switch-trackHeight-medium)',
      },
    },
    {
      props: { size: 'small', startDecorator: undefined, endDecorator: undefined },
      style: {
        width: 'var(--nova-switch-trackWidth-small)',
        height: 'var(--nova-switch-trackHeight-small)',
      },
    },
    {
      props: { size: 'large', startDecorator: undefined, endDecorator: undefined },
      style: {
        width: 'var(--nova-switch-trackWidth-large)',
        height: 'var(--nova-switch-trackHeight-large)',
      },
    },
    {
      props: { size: 'medium' },
      style: {
        minWidth: 'var(--nova-switch-trackWidth-medium)',
        height: 'var(--nova-switch-trackHeight-medium)',
      },
    },
    {
      props: { size: 'small' },
      style: {
        minWidth: 'var(--nova-switch-trackWidth-small)',
        height: 'var(--nova-switch-trackHeight-small)',
      },
    },
    {
      props: { size: 'large' },
      style: {
        minWidth: 'var(--nova-switch-trackWidth-large)',
        height: 'var(--nova-switch-trackHeight-large)',
      },
    },
  ],
}));

const SwitchTrack = styled('span')<SwitchProps>(({ theme }) => ({
  boxSizing: 'border-box',
  outline: `2px solid ${theme.vars.palette.onSurfaceVariant}`,
  outlineOffset: '-2px',
  backgroundColor: theme.vars.palette.surfaceContainerHighest,
  borderRadius: '100px',
  position: 'relative',
  transition: 'all 150ms cubic-bezier(0.4, 0, 0.2, 1)',
  borderShadow: '0px 2px 4px rgba(0, 0, 0, 0.1)',
  flexShrink: 0,
  display: 'inline-flex',
  alignItems: 'center',

  [`&.${switchClasses.checked}`]: {
    outline: 'none',
  },
  [`${SwitchRoot}.${switchClasses.disabled} &`]: {
    outlineColor: theme.vars.palette.outlineDisabled,
    outline: `2px solid ${theme.vars.palette.outlineDisabled}`,
  },

  variants: [
    {
      props: { disabled: true, checked: true },
      style: {
        backgroundColor: theme.vars.palette.surfaceContainerHighest,
        outline: `2px solid ${theme.vars.palette.outlineDisabled}`,
      },
    },
    {
      props: { disabled: true, checked: false },
      style: {
        backgroundColor: 'transparent',
        outline: `2px solid ${theme.vars.palette.outlineDisabled}`,
      },
    },
    {
      props: { disabled: false, checked: true },
      style: {
        backgroundColor: theme.vars.palette.primary,
      },
    },
    {
      props: { size: 'medium' },
      style: {
        width: 'var(--nova-switch-trackWidth-medium)',
        height: 'var(--nova-switch-trackHeight-medium)',
      },
    },
    {
      props: { size: 'small' },
      style: {
        width: 'var(--nova-switch-trackWidth-small)',
        height: 'var(--nova-switch-trackHeight-small)',
      },
    },
    {
      props: { size: 'large' },
      style: {
        width: 'var(--nova-switch-trackWidth-large)',
        height: 'var(--nova-switch-trackHeight-large)',
      },
    },
  ],
}));

const SwitchThumb = styled('span')<SwitchProps>(({ theme }) => ({
  boxSizing: 'border-box',
  backgroundColor: theme.vars.palette.onSurfaceVariant,
  borderRadius: '50%',
  position: 'absolute',
  left: '4px',
  transition: 'transform 150ms cubic-bezier(0.4, 0, 0.2, 1), background-color 150ms cubic-bezier(0.4, 0, 0.2, 1)',
  display: 'inline-flex',
  alignItems: 'center',
  justifyContent: 'center',
  color: theme.vars.palette.inverseOnSurface,
  cursor: 'pointer',
  zIndex: 1,
  pointerEvents: 'none',

  '& .MuiSvgIcon-root': {
    transition: 'all 0.2s ease-in-out',
    pointerEvents: 'none',
  },

  variants: [
    {
      props: { disabled: true },
      style: {
        cursor: 'default',
      },
    },
    {
      props: { disabled: false, checked: true },
      style: {
        backgroundColor: theme.vars.palette.surfaceContainer,
        color: theme.vars.palette.primary,
        transform: 'translateX(20px)',
        [`${SwitchRoot}:hover &`]: {
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.surfaceContainer}, ${theme.vars.palette.primary} ${theme.vars.palette.stateLayers.hoverPrimary})`,
        },
        [`${SwitchRoot}:active &`]: {
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.surfaceContainer}, ${theme.vars.palette.primary} ${theme.vars.palette.stateLayers.pressPrimary})`,
        },
        [`&.${switchClasses.focusVisible}`]: {
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.surfaceContainer}, ${theme.vars.palette.primary} ${theme.vars.palette.stateLayers.focusPrimary})`,
          '& .MuiSvgIcon-root': {
            fill: theme.vars.palette.addOn.primaryFixedDim,
            transform: 'scale(1.1)',
          },
        },
      },
    },
    {
      props: { disabled: true, checked: true },
      style: {
        transform: 'translateX(20px)',
        backgroundColor: theme.vars.palette.onPrimary,
        color: theme.vars.palette.onBackgroundDisabled,
      },
    },
    {
      props: { size: 'medium', checked: false },
      style: {
        left: '6px',
        width: 'var(--nova-switch-thumbSize-medium-unchecked)',
        height: 'var(--nova-switch-thumbSize-medium-unchecked)',
        [`&.${switchClasses.withIcon}`]: {
          '& > *': {
            width: 'var(--nova-switch-thumbSize-medium-unchecked)',
            height: 'var(--nova-switch-thumbSize-medium-unchecked)',
          },
        },
      },
    },
    {
      props: { size: 'small', checked: false },
      style: {
        left: '6px',
        width: 'var(--nova-switch-thumbSize-small-unchecked)',
        height: 'var(--nova-switch-thumbSize-small-unchecked)',
        [`&.${switchClasses.withIcon}`]: {
          '& > *': {
            width: 'var(--nova-switch-thumbSize-small-unchecked)',
            height: 'var(--nova-switch-thumbSize-small-unchecked)',
          },
        },
      },
    },
    {
      props: { size: 'large', checked: false },
      style: {
        left: '6px',
        width: 'var(--nova-switch-thumbSize-large-unchecked)',
        height: 'var(--nova-switch-thumbSize-large-unchecked)',
        [`&.${switchClasses.withIcon}`]: {
          '& > *': {
            width: 'calc(var(--nova-switch-thumbSize-large-unchecked) - 1px)',
            height: 'calc(var(--nova-switch-thumbSize-large-unchecked) - 1px)',
          },
        },
      },
    },
    {
      props: { size: 'medium', checked: true },
      style: {
        width: 'var(--nova-switch-thumbSize-medium-checked)',
        height: 'var(--nova-switch-thumbSize-medium-checked)',
        [`&.${switchClasses.withIcon}`]: {
          '& > *': {
            width: 'var(--nova-switch-thumbSize-medium-checked)',
            height: 'var(--nova-switch-thumbSize-medium-checked)',
          },
        },
      },
    },
    {
      props: { size: 'small', checked: true },
      style: {
        width: 'var(--nova-switch-thumbSize-small-checked)',
        height: 'var(--nova-switch-thumbSize-small-checked)',
        [`&.${switchClasses.withIcon}`]: {
          '& > *': {
            width: 'var(--nova-switch-thumbSize-small-checked)',
            height: 'var(--nova-switch-thumbSize-small-checked)',
          },
        },
      },
    },
    {
      props: { size: 'large', checked: true },
      style: {
        width: 'var(--nova-switch-thumbSize-large-checked)',
        height: 'var(--nova-switch-thumbSize-large-checked)',
        [`&.${switchClasses.withIcon}`]: {
          '& > *': {
            width: 'var(--nova-switch-thumbSize-large-checked)',
            height: 'var(--nova-switch-thumbSize-large-checked)',
          },
        },
      },
    },
  ],
}));

const SwitchAction = styled('div')<SwitchProps>(({ theme }) => ({
  position: 'absolute',
  top: 0,
  left: 0,
  bottom: 0,
  right: 0,
  zIndex: 1,
  cursor: 'inherit',
  borderRadius: '100px',
  [`&.${switchClasses.focusVisible}`]: {
    outline: `2px solid ${theme.vars.palette.secondary}`,
    outlineOffset: '1px',
  },

  variants: [
    {
      props: { size: 'medium' },
      style: {
        width: 'var(--nova-switch-trackWidth-medium)',
        height: 'var(--nova-switch-trackHeight-medium)',
      },
    },
    {
      props: { size: 'small' },
      style: {
        width: 'var(--nova-switch-trackWidth-small)',
        height: 'var(--nova-switch-trackHeight-small)',
      },
    },
    {
      props: { size: 'large' },
      style: {
        width: 'var(--nova-switch-trackWidth-large)',
        height: 'var(--nova-switch-trackHeight-large)',
      },
    },
  ],
}));

const SwitchInput = styled('input')<SwitchProps>(() => ({
  cursor: 'inherit',
  position: 'absolute',
  width: '100%',
  height: '100%',
  top: 0,
  left: 0,
  margin: 0,
  opacity: 0,
  padding: 0,
  zIndex: 3,

  '&:disabled': {
    cursor: 'default',
  },
}));

const SwitchStartDecorator = styled('span')<SwitchProps>(({ theme }) => ({
  display: 'inline-flex',
  alignItems: 'center',
  marginRight: '8px',
  color: theme.vars.palette.onSurface,
  flexShrink: 0,
  cursor: 'pointer',
  ...theme.typography.bodyMedium,
  variants: [
    {
      props: { disabled: true },
      style: {
        color: theme.vars.palette.onBackgroundDisabled,
        cursor: 'default',
      },
    },
    {
      props: { size: 'small' },
      style: {
        fontSize: '12px',
      },
    },
    {
      props: { size: 'medium' },
      style: {
        fontSize: '14px',
      },
    },
    {
      props: { size: 'large' },
      style: {
        fontSize: '16px',
      },
    },
  ],
}));

const SwitchEndDecorator = styled('span')<SwitchProps>(({ theme }) => ({
  display: 'inline-flex',
  alignItems: 'center',
  marginLeft: '8px',
  color: theme.vars.palette.onSurface,
  flexShrink: 0,
  cursor: 'pointer',
  ...theme.typography.bodyMedium,
  variants: [
    {
      props: { disabled: true },
      style: {
        color: theme.vars.palette.onBackgroundDisabled,
        cursor: 'default',
      },
    },
    {
      props: { size: 'small' },
      style: {
        fontSize: '12px',
      },
    },
    {
      props: { size: 'medium' },
      style: {
        fontSize: '14px',
      },
    },
    {
      props: { size: 'large' },
      style: {
        fontSize: '16px',
      },
    },
  ],
}));

// eslint-disable-next-line react/display-name
export const Switch = React.forwardRef((props: SwitchProps, ref: React.ForwardedRef<Element>) => {
  const {
    id: idOverride,
    name,
    value,
    className,
    component,
    onIcon,
    offIcon,
    checked: checkedProp,
    defaultChecked,
    readOnly,
    required: requiredProp = false,
    disabled: disabledProp = false,
    size: sizeProp = 'medium',
    startDecorator,
    endDecorator,
    slots = {},
    slotProps = {},
    onBlur,
    onChange,
    onFocus,
    onFocusVisible,
    ...other
  } = props;

  const formControl = React.useContext(FormControlContext);

  const id = idOverride || formControl?.htmlFor;

  const inDisabled = props.disabled ?? formControl?.disabled ?? disabledProp;
  const inRequired = props.required ?? formControl?.required ?? requiredProp;
  const size = props.size ?? formControl?.size ?? sizeProp;

  const useSwitchProps = {
    checked: checkedProp,
    defaultChecked,
    disabled: inDisabled,
    readOnly,
    required: inRequired,
    onBlur: formControl?.onBlur,
    onChange: formControl?.onChange,
    onFocus: formControl?.onFocus,
    onFocusVisible,
  };
  const { getInputProps, checked, disabled, focusVisible } = useSwitch(useSwitchProps);

  const ownerState = {
    ...props,
    checked,
    disabled,
    focusVisible,
    size,
    onIcon,
    offIcon,
    startDecorator,
    endDecorator,
  };

  const classes = useUtilityClasses(ownerState);

  const handleDecoratorClick = React.useCallback(
    (event: React.MouseEvent) => {
      if (!disabled) {
        event.preventDefault();
        const input = event.currentTarget.parentElement?.querySelector('input');
        if (input) {
          input.click();
        }
      }
    },
    [disabled],
  );

  const SlotRoot = slots.root ?? SwitchRoot;
  const rootProps = useSlotProps({
    elementType: SwitchRoot,
    externalSlotProps: slotProps.root,
    externalForwardedProps: other,
    additionalProps: {
      ref,
      as: component,
    },
    ownerState,
    className: [classes.root, className],
  });

  const SlotStartDecorator = slots.startDecorator ?? SwitchStartDecorator;
  const startDecoratorProps = useSlotProps({
    elementType: SwitchStartDecorator,
    externalSlotProps: slotProps.startDecorator,
    additionalProps: {
      'aria-hidden': true,
      onClick: handleDecoratorClick,
    },
    ownerState,
    className: classes.startDecorator,
  });

  const SlotEndDecorator = slots.endDecorator ?? SwitchEndDecorator;
  const endDecoratorProps = useSlotProps({
    elementType: SwitchEndDecorator,
    externalSlotProps: slotProps.endDecorator,
    additionalProps: {
      'aria-hidden': true,
      onClick: handleDecoratorClick,
    },
    ownerState,
    className: classes.endDecorator,
  });

  const SlotTrack = slots.track ?? SwitchTrack;
  const trackProps = useSlotProps({
    elementType: SwitchTrack,
    externalSlotProps: slotProps.track,
    ownerState,
    className: classes.track,
  });

  const SlotThumb = slots.thumb ?? SwitchThumb;
  const thumbProps = useSlotProps({
    elementType: SwitchThumb,
    externalSlotProps: slotProps.thumb,
    ownerState,
    className: classes.thumb,
  });

  const SlotAction = slots.action ?? SwitchAction;
  const actionProps = useSlotProps({
    elementType: SwitchAction,
    externalSlotProps: slotProps.action,
    ownerState,
    className: classes.action,
  });

  const SlotInput = slots.input ?? SwitchInput;
  const inputProps = useSlotProps({
    elementType: SwitchInput,
    externalSlotProps: slotProps.input,
    additionalProps: {
      id,
      name,
      value,
    },
    getSlotProps: (eventHandlers: EventHandlers) => {
      return getInputProps({
        ...eventHandlers,
        onChange,
        onFocus,
        onBlur,
      });
    },
    ownerState,
    className: classes.input,
  });

  const checkedIcon = React.isValidElement(onIcon)
    ? React.cloneElement(onIcon as React.ReactElement, { fontSize: onIcon.props.fontSize ?? 'inherit' })
    : onIcon;

  const uncheckedIcon = React.isValidElement(offIcon)
    ? React.cloneElement(offIcon as React.ReactElement, { fontSize: offIcon.props.fontSize ?? 'inherit' })
    : offIcon;

  return (
    <SlotRoot {...rootProps}>
      {startDecorator && (
        <SlotStartDecorator {...startDecoratorProps}>
          {typeof startDecorator === 'function' ? startDecorator(ownerState) : startDecorator}
        </SlotStartDecorator>
      )}
      <SlotTrack {...trackProps}>
        <SlotThumb {...thumbProps}>{checked ? checkedIcon : uncheckedIcon}</SlotThumb>
        <SlotAction {...actionProps}>
          <SlotInput {...inputProps} />
        </SlotAction>
      </SlotTrack>
      {endDecorator && (
        <SlotEndDecorator {...endDecoratorProps}>
          {typeof endDecorator === 'function' ? endDecorator(ownerState) : endDecorator}
        </SlotEndDecorator>
      )}
    </SlotRoot>
  );
});
