import {
  unstable_generateUtilityClasses as generateUtilityClasses,
  unstable_generateUtilityClass as generateUtilityClass,
} from '@mui/utils';

export interface SwitchClasses {
  /** Class name applied to the root element. */
  root: string;
  /** State class applied to the root `checked` class. */
  checked: string;
  /** State class applied to the root disabled class. */
  disabled: string;
  /** Class name applied to the action element. */
  action: string;
  /** Class name applied to the input element. */
  input: string;
  /** Class name applied to the input element. */
  thumb: string;
  /** Class name applied to the track element. */
  track: string;
  /** State class applied to the root element if the switch has visible focus */
  focusVisible: string;
  /** Styles applied to the root element if `size="small"`. */
  sizeSmall: string;
  /** Styles applied to the root element if `size="medium"`. */
  sizeMedium: string;
  /** Styles applied to the root element if `size="large"`. */
  sizeLarge: string;
  /** Class name applied to the startDecorator element. */
  startDecorator: string;
  /** Class name applied to the endDecorator element. */
  endDecorator: string;
  /** Class name applied to the thumb `withIcon` class. */
  withIcon: string;
}

export type SwitchClassKey = keyof SwitchClasses;

export function getSwitchUtilityClass(slot: string): string {
  return generateUtilityClass('NovaSwitch', slot);
}

const switchClasses: SwitchClasses = generateUtilityClasses('NovaSwitch', [
  'root',
  'checked',
  'disabled',
  'action',
  'input',
  'thumb',
  'track',
  'focusVisible',
  'sizeSmall',
  'sizeMedium',
  'sizeLarge',
  'startDecorator',
  'endDecorator',
  'withIcon',
]);

export default switchClasses;
