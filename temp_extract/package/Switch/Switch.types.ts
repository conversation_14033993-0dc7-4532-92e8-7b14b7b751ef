import { UseSwitchParameters } from '../internal/hooks/useSwitch';
import { OverridableStringUnion } from '@mui/types';
import { SxProps } from '../types/theme';
import { CreateSlotsAndSlotProps, SlotProps } from '../types/slot';

export type SwitchSlot = 'root' | 'action' | 'input' | 'track' | 'thumb' | 'startDecorator' | 'endDecorator';

export interface SwitchSlots {
  /**
   * The component that renders the root.
   * @default 'div'
   */
  root?: React.ElementType;
  /**
   * The component that renders the thumb.
   * @default 'span'
   */
  thumb?: React.ElementType;
  /**
   * The component that renders the action.
   * @default 'div'
   */
  action?: React.ElementType;
  /**
   * The component that renders the input.
   * @default 'input'
   */
  input?: React.ElementType;
  /**
   * The component that renders the track.
   * @default 'span'
   */
  track?: React.ElementType;
  /**
   * The component that renders the start decorator.
   * @default 'span'
   */
  startDecorator?: React.ElementType;
  /**
   * The component that renders the end decorator.
   * @default 'span'
   */
  endDecorator?: React.ElementType;
}

export interface SwitchPropsSizeOverrides {}

export type SwitchSlotsAndSlotProps = CreateSlotsAndSlotProps<
  SwitchSlots,
  {
    root: SlotProps<'div', object, SwitchOwnerState>;
    thumb: SlotProps<'span', object, SwitchOwnerState>;
    action: SlotProps<'div', object, SwitchOwnerState>;
    input: SlotProps<'input', object, SwitchOwnerState>;
    track: SlotProps<'span', object, SwitchOwnerState>;
    startDecorator: SlotProps<'span', object, SwitchOwnerState>;
    endDecorator: SlotProps<'span', object, SwitchOwnerState>;
  }
>;

export interface SwitchProps extends UseSwitchParameters, SwitchSlotsAndSlotProps {
  /**
   * The id of the `input` element.
   */
  id?: string;
  /**
   * Name attribute of the `input` element.
   */
  name?: string;
  /**
   * The value of the component. The DOM API casts this to a string.
   */
  value?: unknown;
  /**
   * The icon to display when the component is on.
   */
  onIcon?: React.ReactNode;
  /**
   * The icon to display when the component is off.
   */
  offIcon?: React.ReactNode;
  /**
   * Class name applied to the root element.
   */
  className?: string;

  /**
   * The component used for the Root slot.
   * Either a string to use a HTML element or a component.
   */
  component?: React.ElementType;

  /**
   * The size of the switch.
   * @default 'medium'
   */
  size?: OverridableStringUnion<'small' | 'medium' | 'large', SwitchPropsSizeOverrides>;

  /**
   * The system prop that allows defining system overrides as well as additional CSS styles.
   */
  sx?: SxProps;

  /**
   * The element that appears at the start of the switch.
   */
  startDecorator?: React.ReactNode | ((ownerState: SwitchOwnerState) => React.ReactNode);

  /**
   * The element that appears at the end of the switch.
   */
  endDecorator?: React.ReactNode | ((ownerState: SwitchOwnerState) => React.ReactNode);
}

export interface SwitchOwnerState extends SwitchProps {
  /**
   * If `true`, the switch's focus is visible.
   */
  focusVisible?: boolean;
}
