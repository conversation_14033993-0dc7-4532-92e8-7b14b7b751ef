import { expect, test, describe, afterEach, vi } from 'vitest';
import { render, screen, fireEvent, cleanup, act } from '@testing-library/react';
import React from 'react';
import { Switch } from './Switch';

afterEach(() => {
  cleanup();
});

describe('Switch Component', () => {
  test('renders basic switch correctly', () => {
    render(<Switch />);
    const switchElement = screen.getByRole('switch');
    expect(switchElement).toBeDefined();
    expect((switchElement as HTMLInputElement).checked).toBe(false);
  });

  test('can pass data-testid', () => {
    render(<Switch data-testid="NovaSwitch-root" />);
    expect(screen.getByTestId('NovaSwitch-root')).toBeDefined();
  });

  test('renders a switch with the Checked state when On', () => {
    render(<Switch defaultChecked />);
    expect(screen.getByRole('switch')).to.have.property('checked', true);
  });

  test('the switch can be disabled', () => {
    render(<Switch disabled />);
    expect(screen.getByRole('switch')).to.have.property('disabled', true);
  });

  test('renders with decorators', () => {
    render(<Switch startDecorator="Start" endDecorator="End" />);
    expect(screen.getByText('Start')).toBeDefined();
    expect(screen.getByText('End')).toBeDefined();
  });

  test('handles click events', () => {
    const handleChange = vi.fn();
    render(<Switch onChange={handleChange} />);
    const switchElement = screen.getByRole('switch');
    fireEvent.click(switchElement);
    expect(handleChange).toHaveBeenCalled();
    expect(switchElement as HTMLInputElement).to.have.property('checked', true);
  });

  test('renders with different densities', () => {
    const { rerender } = render(<Switch size="small" />);
    let switchRoot = screen.getByRole('switch').closest('span');
    expect(switchRoot).toHaveStyle({ height: 'var(--nova-switch-trackHeight-small)' });
    rerender(<Switch size="large" />);
    switchRoot = screen.getByRole('switch').closest('span');
    expect(switchRoot).toHaveStyle({ height: 'var(--nova-switch-trackHeight-large)' });
  });

  test('handles custom className', () => {
    const customClass = 'custom-switch';
    render(<Switch data-testid="NovaSwitch-root" className={customClass} />);
    const switchRoot = screen.getByTestId('NovaSwitch-root');
    expect(switchRoot).toHaveClass(customClass);
  });

  test('should pass `slotProps` down to slots', () => {
    const { container } = render(
      <Switch
        data-testid="root-switch"
        slotProps={{
          thumb: { className: 'custom-thumb' },
          track: { className: 'custom-track' },
          action: { className: 'custom-action' },
          input: { className: 'custom-input' },
        }}
      />,
    );

    expect(screen.getByTestId('root-switch')).toBeVisible();
    expect(container.querySelector('.custom-thumb')).toHaveClass('NovaSwitch-thumb');
    expect(container.querySelector('.custom-track')).toHaveClass('NovaSwitch-track');
    expect(container.querySelector('.custom-action')).toHaveClass('NovaSwitch-action');
    expect(container.querySelector('.custom-input')).toHaveClass('NovaSwitch-input');
  });

  test('can receive startDecorator as string', () => {
    render(<Switch startDecorator="foo" />);
    expect(screen.getByText('foo')).toBeVisible();
  });

  test('can receive endDecorator as string', () => {
    render(<Switch endDecorator="bar" />);
    expect(screen.getByText('bar')).toBeVisible();
  });

  test('can receive startDecorator as function', () => {
    render(<Switch startDecorator={({ checked }) => (checked ? 'On' : 'Off')} />);
    expect(screen.getByText('Off')).toBeVisible();

    // how a user would trigger it
    act(() => {
      screen.getByRole('switch').click();
      fireEvent.change(screen.getByRole('switch'), { target: { checked: '' } });
    });

    expect(screen.getByText('On')).toBeVisible();
  });

  test('can receive endDecorator as function', () => {
    render(<Switch endDecorator={({ checked }) => (checked ? 'On' : 'Off')} />);
    expect(screen.getByText('Off')).toBeVisible();

    // how a user would trigger it
    act(() => {
      screen.getByRole('switch').click();
      fireEvent.change(screen.getByRole('switch'), { target: { checked: '' } });
    });

    expect(screen.getByText('On')).toBeVisible();
  });
});
