import * as React from 'react';
import { CardRootProps } from '../Root/CardRoot.types';

/**
 * @internal
 */
export type CardContextValue = Pick<CardRootProps, 'orientation' | 'disabled'>;

const CardContext = React.createContext<CardContextValue>({ orientation: 'vertical', disabled: false });

if (process.env.NODE_ENV !== 'production') {
  CardContext.displayName = 'CardContext';
}

export function useCardContext(): CardContextValue | undefined {
  return React.useContext(CardContext);
}

export default CardContext;
