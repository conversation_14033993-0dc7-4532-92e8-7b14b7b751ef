'use client';
import * as React from 'react';
import { OverridableComponent } from '@mui/types';
import { unstable_capitalize as capitalize, unstable_composeClasses as composeClasses } from '@mui/utils';
import useSlotProps from '@mui/utils/useSlotProps';
import { styled } from '@pigment-css/react';
import { CardRootOwnerState, CardRootProps, CardRootTypeMap } from './CardRoot.types';
import { getCardRootUtilityClass } from './CardRoot.classes';
import CardContext from '../Context/CardContext';

const useUtilityClasses = (ownerState: CardRootOwnerState) => {
  const { orientation, disabled } = ownerState;
  const slots = {
    root: ['root', orientation && `orientation${capitalize(orientation)}`, disabled && `disabled`],
  };

  return composeClasses(slots, getCardRootUtilityClass, {});
};

const CardRootSlot = styled('div')<CardRootOwnerState>(({ theme }) => ({
  display: 'flex',
  border: '1px solid',
  borderColor: theme.vars.palette.outlineVariant,
  borderRadius: '16px',
  backgroundColor: theme.vars.palette.surfaceContainer,
  overflow: 'hidden',
  variants: [
    {
      props: { orientation: 'horizontal' },
      style: {
        flexDirection: 'row',
        alignItems: 'center',
      },
    },
    {
      props: { orientation: 'vertical' },
      style: {
        flexDirection: 'column',
      },
    },
    {
      props: { clickable: true, disabled: false },
      style: {
        cursor: 'pointer',
        transition: 'backgroundColor .2s',
        '&:focus-visible': {
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.surfaceContainer}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.focusOnSurface})`,
          outline: `2px solid ${theme.vars.palette.primary}`,
        },
        '&:hover': {
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.surfaceContainer}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.hoverOnSurface})`,
        },
        '&:active': {
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.surfaceContainer}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.pressOnSurface})`,
        },
      },
    },
    {
      props: { disabled: true },
      style: {
        backgroundColor: `color-mix(in srgb, ${theme.vars.palette.surfaceContainer}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.disabledOnSurface})`,
      },
    },
  ],
}));

// eslint-disable-next-line react/display-name
export const CardRoot = React.forwardRef((props: CardRootProps, ref: React.ForwardedRef<Element>) => {
  const {
    orientation = 'vertical',
    disabled = false,
    children,
    component,
    slots = {},
    slotProps = {},
    onClick,
    ...rest
  } = props;

  const clickable = Boolean(onClick);
  const ownerState = { ...props, orientation, clickable, disabled };
  const classes = useUtilityClasses(ownerState);

  const SlotRoot = slots.root ?? CardRootSlot;

  const slotRootProps = useSlotProps({
    elementType: SlotRoot,
    externalSlotProps: slotProps.root,
    externalForwardedProps: rest,
    additionalProps: {
      ref: ref,
      as: component,
      tabIndex: disabled || !clickable ? -1 : 0,
      onClick: !disabled ? onClick : undefined,
    },
    ownerState,
    className: classes.root,
  });

  return (
    <CardContext.Provider value={{ orientation, disabled }}>
      <SlotRoot {...slotRootProps}>{children}</SlotRoot>
    </CardContext.Provider>
  );
}) as OverridableComponent<CardRootTypeMap>;
