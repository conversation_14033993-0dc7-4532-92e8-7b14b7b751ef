import '@testing-library/jest-dom/vitest';
import { render, screen, cleanup } from '@testing-library/react';
import React from 'react';
import { describe, expect, it, afterEach } from 'vitest';
import { CardRoot } from './CardRoot';
import { CardHeader } from '../Header';
import { CardContent } from '../Content';

afterEach(() => {
  cleanup();
});

describe('CardRoot', () => {
  it('should render normal', () => {
    render(<CardRoot data-testid="NovaCardRoot-root" />);
    expect(screen.getByTestId('NovaCardRoot-root')).toHaveClass('NovaCardRoot-root');
  });

  it('should render card content', () => {
    render(<CardRoot data-testid="NovaCardRoot-root">Hello world</CardRoot>);
    expect(screen.getByTestId('NovaCardRoot-root')).toHaveTextContent('Hello world');
  });

  it('should render vertical card by default', () => {
    render(
      <CardRoot data-testid="NovaCardRoot-root">
        <div>1</div>
        <div>2</div>
      </CardRoot>,
    );
    expect(screen.getByTestId('NovaCardRoot-root')).toHaveClass('NovaCardRoot-orientationVertical');
  });

  it('should render horizontal card', () => {
    render(
      <CardRoot data-testid="NovaCardRoot-root" orientation="horizontal">
        <div>1</div>
        <div>2</div>
      </CardRoot>,
    );
    expect(screen.getByTestId('NovaCardRoot-root')).toHaveClass('NovaCardRoot-orientationHorizontal');
  });

  it('should render disabled card', () => {
    render(
      <CardRoot data-testid="NovaCardRoot-root" orientation="horizontal" disabled>
        <div>1</div>
      </CardRoot>,
    );
    expect(screen.getByTestId('NovaCardRoot-root')).toHaveClass('Mui-disabled');
  });

  it('should forward orientation & disabled to children elements', () => {
    render(
      <CardRoot data-testid="NovaCardRoot-root" orientation="horizontal" disabled>
        <CardHeader
          data-testid="NovaCardHeader-header"
          heading="Heading"
          subheading="Subheading"
          slotProps={{
            heading: { 'data-testid': 'NovaCardHeader-heading' },
            subheading: { 'data-testid': 'NovaCardHeader-subheading' },
          }}
        />
        <CardContent
          data-testid="NovaCardRoot-content"
          title="Title"
          subtitle="Subtitle"
          supportingText="Supporting text"
          slotProps={{
            title: { 'data-testid': 'NovaCardContent-title' },
            subtitle: { 'data-testid': 'NovaCardContent-subtitle' },
            supportingText: { 'data-testid': 'NovaCardContent-supportingText' },
          }}
        />
      </CardRoot>,
    );
    expect(screen.getByTestId('NovaCardHeader-heading')).toHaveClass('Mui-disabled');
    expect(screen.getByTestId('NovaCardHeader-subheading')).toHaveClass('Mui-disabled');
    expect(screen.getByTestId('NovaCardRoot-content')).toHaveClass('NovaCardContent-orientationHorizontal');
    expect(screen.getByTestId('NovaCardContent-title')).toHaveClass('Mui-disabled');
    expect(screen.getByTestId('NovaCardContent-subtitle')).toHaveClass('Mui-disabled');
    expect(screen.getByTestId('NovaCardContent-supportingText')).toHaveClass('Mui-disabled');
  });
});
