import generateUtilityClasses from '@mui/utils/generateUtilityClasses';
import generateUtilityClass from '@mui/utils/generateUtilityClass';

export interface CardRootClasses {
  /** Class name applied to the root element. */
  root: string;
  /** Class name applied to the root element if `orientation="vertical"`. */
  orientationVertical: string;
  /** Class name applied to the root element if `orientation="horizontal"`. */
  orientationHorizontal: string;
  /** Class name applied to the root element if `disabled={true}`. */
  disabled: string;
}

export type CardRootClassKey = keyof CardRootClasses;

export function getCardRootUtilityClass(slot: string): string {
  return generateUtilityClass('NovaCardRoot', slot);
}

const cardRootClasses: CardRootClasses = generateUtilityClasses('NovaCardRoot', [
  'root',
  'orientationVertical',
  'orientationHorizontal',
  'disabled',
]);

export default cardRootClasses;
