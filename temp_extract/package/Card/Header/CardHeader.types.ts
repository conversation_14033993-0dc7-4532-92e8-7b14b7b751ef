import * as React from 'react';
import { OverrideProps } from '@mui/types';
import { CreateSlotsAndSlotProps, SlotProps } from '../../types/slot';
import { TypographyProps } from '../../Typography/Typography.types';

export interface CardHeaderSlots {
  /**
   * The component that renders the root slot.
   * @default 'div'
   */
  root?: React.ElementType;
  /**
   * The component that renders the avatar slot.
   * @default 'div'
   */
  avatar?: React.ElementType;
  /**
   * The component that renders the action slot.
   * @default 'div'
   */
  action?: React.ElementType;
  /**
   * The component that renders the content slot.
   * @default 'div'
   */
  content?: React.ElementType;
  /**
   * The component that renders the heading slot.
   * @default Typography
   */
  heading?: React.ElementType;
  /**
   * The component that renders the subheading slot.
   * @default Typography
   */
  subheading?: React.ElementType;
}

export type CardHeaderSlotsAndSlotProps = CreateSlotsAndSlotProps<
  CardHeaderSlots,
  {
    /**
     * Props forwarded to the root slot.
     * By default, the available props are based on the div element.
     */
    root: SlotProps<'div', object, CardHeaderOwnerState>;
    /**
     * Props forwarded to the avatar slot.
     * By default, the available props are based on the div element.
     */
    avatar: SlotProps<'div', object, CardHeaderOwnerState>;
    /**
     * Props forwarded to the action slot.
     * By default, the available props are based on the div element.
     */
    action: SlotProps<'div', object, CardHeaderOwnerState>;
    /**
     * Props forwarded to the content slot.
     * By default, the available props are based on the div element.
     */
    content: SlotProps<'div', object, CardHeaderOwnerState>;
    /**
     * Props forwarded to the heading slot.
     * By default, the available props are based on the Typography component.
     */
    heading: SlotProps<React.ElementType<TypographyProps>, object, CardHeaderOwnerState>;
    /**
     * Props forwarded to the subheading slot.
     * By default, the available props are based on the Typography component.
     */
    subheading: SlotProps<React.ElementType<TypographyProps>, object, CardHeaderOwnerState>;
  }
>;

export interface CardHeaderTypeMap<P = object, D extends React.ElementType = 'div'> {
  props: P & {
    /**
     * The action to display in the card header.
     */
    action?: React.ReactNode;
    /**
     * The Avatar element to display.
     */
    avatar?: React.ReactNode;
    /**
     * The heading of the component.
     */
    heading?: React.ReactNode;
    /**
     * The subheading of the component.
     */
    subheading?: React.ReactNode;
  } & CardHeaderSlotsAndSlotProps;
  defaultComponent: D;
}

export type CardHeaderProps<
  D extends React.ElementType = CardHeaderTypeMap['defaultComponent'],
  P = { component?: React.ElementType },
> = OverrideProps<CardHeaderTypeMap<P, D>, D>;

export interface CardHeaderOwnerState extends CardHeaderProps {
  disabled: boolean;
}
