'use client';
import * as React from 'react';
import { OverridableComponent } from '@mui/types';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import useSlotProps from '@mui/utils/useSlotProps';
import { styled } from '@pigment-css/react';
import { CardHeaderOwnerState, CardHeaderProps, CardHeaderTypeMap } from './CardHeader.types';
import { getCardHeaderUtilityClass } from './CardHeader.classes';
import { Typography } from '../../Typography';
import CardContext from '../Context/CardContext';

const useUtilityClasses = (ownerState: CardHeaderOwnerState) => {
  const { disabled } = ownerState;
  const slots = {
    root: ['root'],
    avatar: ['avatar'],
    action: ['action'],
    content: ['content'],
    heading: ['heading', disabled && 'disabled'],
    subheading: ['subheading', disabled && 'disabled'],
  };

  return composeClasses(slots, getCardHeaderUtilityClass, {});
};

const CardHeaderRoot = styled('div')<CardHeaderOwnerState>(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  padding: '24px 16px 16px 24px',
  variants: [],
}));

const CardHeaderAvatar = styled('div')<CardHeaderOwnerState>(({ theme }) => ({
  display: 'flex',
  flex: '0 0 auto',
  marginRight: 16,
}));

const CardHeaderContent = styled('div')<CardHeaderOwnerState>(({ theme }) => ({
  flex: '1 1 auto',
}));

const CardHeaderHeading = styled(Typography)<CardHeaderOwnerState>(({ theme }) => ({
  display: 'block',
  color: theme.vars.palette.onSurface,
  fontWeight: 400,
  variants: [
    {
      props: { disabled: true },
      style: {
        color: theme.vars.palette.onBackgroundDisabled,
      },
    },
  ],
}));

const CardHeaderSubheading = styled(Typography)<CardHeaderOwnerState>(({ theme }) => ({
  display: 'block',
  color: theme.vars.palette.onSurfaceVariant,
  fontWeight: 400,
  variants: [
    {
      props: { disabled: true },
      style: {
        color: theme.vars.palette.onBackgroundDisabled,
      },
    },
  ],
}));

const CardHeaderAction = styled('div')<CardHeaderOwnerState>(({ theme }) => ({}));

// eslint-disable-next-line react/display-name
export const CardHeader = React.forwardRef((props: CardHeaderProps, ref: React.ForwardedRef<Element>) => {
  const { avatar, action, heading, subheading, children, component, slots = {}, slotProps = {}, ...rest } = props;

  const cardContext = React.useContext(CardContext);
  const disabled = cardContext?.disabled ?? false;
  const hasPropsContent = Boolean(heading) || Boolean(subheading);
  const ownerState = { ...props, disabled };
  const classes = useUtilityClasses(ownerState);

  const SlotRoot = slots.root ?? CardHeaderRoot;
  const SlotAvatar = slots.avatar ?? CardHeaderAvatar;
  const SlotHeading = slots.heading ?? CardHeaderHeading;
  const SlotSubheading = slots.subheading ?? CardHeaderSubheading;
  const SlotContent = slots.content ?? CardHeaderContent;
  const SlotAction = slots.action ?? CardHeaderAction;

  const slotRootProps = useSlotProps({
    elementType: SlotRoot,
    externalSlotProps: slotProps.root,
    externalForwardedProps: rest,
    additionalProps: {
      ref: ref,
      as: component,
    },
    ownerState,
    className: classes.root,
  });

  const slotAvatarProps = useSlotProps({
    elementType: SlotAvatar,
    externalSlotProps: slotProps.avatar,
    ownerState,
    className: classes.avatar,
  });

  const slotHeadingProps = useSlotProps({
    elementType: SlotHeading,
    externalSlotProps: slotProps.heading,
    additionalProps: {
      variant: 'titleSmall',
    },
    ownerState,
    className: classes.heading,
  });

  const slotSubheadingProps = useSlotProps({
    elementType: SlotSubheading,
    externalSlotProps: slotProps.subheading,
    additionalProps: {
      variant: 'bodyMedium',
    },
    ownerState,
    className: classes.subheading,
  });

  const slotContentProps = useSlotProps({
    elementType: SlotContent,
    externalSlotProps: slotProps.content,
    ownerState,
    className: classes.content,
  });

  const slotActionProps = useSlotProps({
    elementType: SlotAction,
    externalSlotProps: slotProps.action,
    ownerState,
    className: classes.action,
  });

  return (
    <SlotRoot {...slotRootProps}>
      {avatar && <SlotAvatar {...slotAvatarProps}>{avatar}</SlotAvatar>}
      {hasPropsContent && (
        <SlotContent {...slotContentProps}>
          {heading && <SlotHeading {...slotHeadingProps}>{heading}</SlotHeading>}
          {subheading && <SlotSubheading {...slotSubheadingProps}>{subheading}</SlotSubheading>}
        </SlotContent>
      )}
      {children}
      {action && <SlotAction {...slotActionProps}>{action}</SlotAction>}
    </SlotRoot>
  );
}) as OverridableComponent<CardHeaderTypeMap>;
