import '@testing-library/jest-dom/vitest';
import { render, screen, cleanup } from '@testing-library/react';
import React from 'react';
import { describe, expect, it, afterEach } from 'vitest';
import { CardHeader } from './CardHeader';
import { Avatar } from '../../Avatar';
import { Checkbox } from '../../Checkbox';

afterEach(() => {
  cleanup();
});

describe('CardHeader', () => {
  it('should render normal', () => {
    render(<CardHeader data-testid="NovaCardHeader-root" />);
    expect(screen.getByTestId('NovaCardHeader-root')).toHaveClass('NovaCardHeader-root');
  });

  it('should render avatar', () => {
    render(<CardHeader data-testid="NovaCardHeader-root" avatar={<Avatar>AA</Avatar>} />);
    expect(screen.getByTestId('NovaCardHeader-root')).toHaveTextContent('AA');
  });

  it('should render heading', () => {
    render(<CardHeader data-testid="NovaCardHeader-root" avatar={<Avatar>AA</Avatar>} heading={'Heading'} />);
    expect(screen.getByTestId('NovaCardHeader-root')).toHaveTextContent('Heading');
  });

  it('should render subheading', () => {
    render(
      <CardHeader
        data-testid="NovaCardHeader-root"
        avatar={<Avatar>AA</Avatar>}
        heading={'Heading'}
        subheading={'Subheading'}
      />,
    );
    expect(screen.getByTestId('NovaCardHeader-root')).toHaveTextContent('Subheading');
  });

  it('should render action', () => {
    render(
      <CardHeader
        data-testid="NovaCardHeader-root"
        avatar={<Avatar>AA</Avatar>}
        heading={'Heading'}
        subheading={'Subheading'}
        action={<Checkbox data-testid="NovaCardHeader-checkbox" />}
      />,
    );
    expect(screen.getByTestId('NovaCardHeader-checkbox')).toBeInTheDocument();
  });

  it('should slotProps work', () => {
    render(
      <CardHeader
        data-testid="NovaCardHeader-root"
        avatar={<Avatar>AA</Avatar>}
        heading={'Heading'}
        subheading={'Subheading'}
        action={<Checkbox data-testid="NovaCardHeader-checkbox" />}
        slotProps={{
          avatar: { 'data-testid': 'NovaCardHeader-avatar' },
          content: { 'data-testid': 'NovaCardHeader-content' },
          heading: { 'data-testid': 'NovaCardHeader-heading' },
          subheading: { 'data-testid': 'NovaCardHeader-subheading' },
          action: { 'data-testid': 'NovaCardHeader-action' },
        }}
      />,
    );
    expect(screen.getByTestId('NovaCardHeader-avatar')).toHaveClass('NovaCardHeader-avatar');
    expect(screen.getByTestId('NovaCardHeader-content')).toHaveClass('NovaCardHeader-content');
    expect(screen.getByTestId('NovaCardHeader-heading')).toHaveClass('NovaCardHeader-heading');
    expect(screen.getByTestId('NovaCardHeader-subheading')).toHaveClass('NovaCardHeader-subheading');
    expect(screen.getByTestId('NovaCardHeader-action')).toHaveClass('NovaCardHeader-action');
  });
});
