import * as React from 'react';
import { OverrideProps } from '@mui/types';
import { CreateSlotsAndSlotProps, SlotProps } from '../../types/slot';
import { TypographyProps } from '../../Typography/Typography.types';

export interface CardContentSlots {
  /**
   * The component that renders the root slot.
   * @default 'div'
   */
  root?: React.ElementType;
  /**
   * The component that renders the body slot.
   * @default 'div'
   */
  body?: React.ElementType;
  /**
   * The component that renders the title slot.
   * @default Typography
   */
  title?: React.ElementType;
  /**
   * The component that renders the subtitle slot.
   * @default Typography
   */
  subtitle?: React.ElementType;
  /**
   * The component that renders the supportingText slot.
   * @default Typography
   */
  supportingText?: React.ElementType;
  /**
   * The component that renders the action slot.
   * @default 'div'
   */
  action?: React.ElementType;
}

export type CardContentSlotsAndSlotProps = CreateSlotsAndSlotProps<
  CardContentSlots,
  {
    /**
     * Props forwarded to the root slot.
     * By default, the available props are based on the div element.
     */
    root: SlotProps<'div', object, CardContentOwnerState>;
    /**
     * Props forwarded to the body slot.
     * By default, the available props are based on the div element.
     */
    body: SlotProps<'div', object, CardContentOwnerState>;
    /**
     * Props forwarded to the title slot.
     * By default, the available props are based on the Typography component.
     */
    title: SlotProps<React.ElementType<TypographyProps>, object, CardContentOwnerState>;
    /**
     * Props forwarded to the subtitle slot.
     * By default, the available props are based on the Typography component.
     */
    subtitle: SlotProps<React.ElementType<TypographyProps>, object, CardContentOwnerState>;
    /**
     * Props forwarded to the supportingText slot.
     * By default, the available props are based on the Typography component.
     */
    supportingText: SlotProps<React.ElementType<TypographyProps>, object, CardContentOwnerState>;
    /**
     * Props forwarded to the action slot.
     * By default, the available props are based on the div element.
     */
    action: SlotProps<'div', object, CardContentOwnerState>;
  }
>;

export interface CardContentTypeMap<P = object, D extends React.ElementType = 'div'> {
  props: P & {
    /**
     * The orientation of the card content.
     * @default 'vertical'
     */
    orientation?: 'vertical' | 'horizontal';
    /**
     * The action to display in the card header.
     */
    action?: React.ReactNode;
    /**
     * The Avatar element to display.
     */
    title?: React.ReactNode;
    /**
     * The header of the component.
     */
    subtitle?: React.ReactNode;
    /**
     * The subheader of the component.
     */
    supportingText?: React.ReactNode;
    /**
     * The start decorator of the component.
     */
    startDecorator?: React.ReactNode;
    /**
     * The end decorator of the component.
     */
    endDecorator?: React.ReactNode;
  } & CardContentSlotsAndSlotProps;
  defaultComponent: D;
}

export type CardContentProps<
  D extends React.ElementType = CardContentTypeMap['defaultComponent'],
  P = { component?: React.ElementType },
> = OverrideProps<CardContentTypeMap<P, D>, D>;

export interface CardContentOwnerState extends CardContentProps {
  orientation: 'vertical' | 'horizontal';
  disabled: boolean;
  lineLimited: 'one' | 'two';
}
