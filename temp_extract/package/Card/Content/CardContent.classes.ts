import generateUtilityClasses from '@mui/utils/generateUtilityClasses';
import generateUtilityClass from '@mui/utils/generateUtilityClass';

export interface CardContentClasses {
  /** Class name applied to the root element. */
  root: string;
  /** Class name applied to the body element. */
  body: string;
  /** Class name applied to the bodyMain element. */
  bodyMain: string;
  /** Class name applied to the title element. */
  title: string;
  /** Class name applied to the subtitle element. */
  subtitle: string;
  /** Class name applied to the supportingText element. */
  supportingText: string;
  /** Class name applied to the action element. */
  action: string;
  /** Class name applied to the root element if `orientation="vertical"`. */
  orientationVertical: string;
  /** Class name applied to the root element if `orientation="horizontal"`. */
  orientationHorizontal: string;
  /** Class name applied to the title, subtitle and supportingText elements if `disabled={true}`. */
  disabled: string;
}

export type CardContentClassKey = keyof CardContentClasses;

export function getCardContentUtilityClass(slot: string): string {
  return generateUtilityClass('NovaCardContent', slot);
}

const cardContentClasses: CardContentClasses = generateUtilityClasses('NovaCardContent', [
  'root',
  'body',
  'bodyMain',
  'title',
  'subtitle',
  'supportingText',
  'action',
  'orientationVertical',
  'orientationHorizontal',
  'disabled',
]);

export default cardContentClasses;
