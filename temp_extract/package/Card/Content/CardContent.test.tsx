import '@testing-library/jest-dom/vitest';
import { render, screen, cleanup } from '@testing-library/react';
import React from 'react';
import { describe, expect, it, afterEach } from 'vitest';
import { CardContent } from './CardContent';
import { Button } from '../../Button';

afterEach(() => {
  cleanup();
});

describe('CardContent', () => {
  it('should render normal', () => {
    render(<CardContent data-testid="NovaCardContent-root" />);
    expect(screen.getByTestId('NovaCardContent-root')).toHaveClass('NovaCardContent-root');
  });

  it('should render title', () => {
    render(<CardContent data-testid="NovaCardContent-root" title={'Title'} />);
    expect(screen.getByTestId('NovaCardContent-root')).toHaveTextContent('Title');
  });

  it('should render subtitle', () => {
    render(<CardContent data-testid="NovaCardContent-root" title={'Title'} subtitle={'Subtitle'} />);
    expect(screen.getByTestId('NovaCardContent-root')).toHaveTextContent('Subtitle');
  });

  it('should render supporting text', () => {
    render(
      <CardContent
        data-testid="NovaCardContent-root"
        title={'Title'}
        subtitle={'Subtitle'}
        supportingText={'Supporting text'}
      />,
    );
    expect(screen.getByTestId('NovaCardContent-root')).toHaveTextContent('Supporting text');
  });

  it('should render start decorator', () => {
    render(
      <CardContent
        data-testid="NovaCardContent-root"
        title={'Title'}
        subtitle={'Subtitle'}
        supportingText={'Supporting text'}
        startDecorator={<div>Start Decorator</div>}
      />,
    );
    expect(screen.getByTestId('NovaCardContent-root')).toHaveTextContent('Start Decorator');
  });

  it('should render end decorator', () => {
    render(
      <CardContent
        data-testid="NovaCardContent-root"
        title={'Title'}
        subtitle={'Subtitle'}
        supportingText={'Supporting text'}
        endDecorator={<div>End Decorator</div>}
      />,
    );
    expect(screen.getByTestId('NovaCardContent-root')).toHaveTextContent('End Decorator');
  });

  it('should render action', () => {
    render(
      <CardContent
        data-testid="NovaCardContent-root"
        title={'Title'}
        subtitle={'Subtitle'}
        supportingText={'Supporting text'}
        endDecorator={<div>End Decorator</div>}
        action={<Button>Button</Button>}
      />,
    );
    expect(screen.getByTestId('NovaCardContent-root')).toHaveTextContent('Button');
  });

  it('should render horizontal card content', () => {
    render(
      <CardContent
        data-testid="NovaCardContent-root"
        orientation={'horizontal'}
        title={'Title'}
        subtitle={'Subtitle'}
        supportingText={'Supporting text'}
        endDecorator={<div>End Decorator</div>}
        action={<Button>Button</Button>}
      />,
    );
    expect(screen.getByTestId('NovaCardContent-root')).toHaveClass('NovaCardContent-orientationHorizontal');
  });

  it('should slotProps work', () => {
    render(
      <CardContent
        data-testid="NovaCardContent-root"
        orientation={'horizontal'}
        title={'Title'}
        subtitle={'Subtitle'}
        supportingText={'Supporting text'}
        endDecorator={<div>End Decorator</div>}
        action={<Button>Button</Button>}
        slotProps={{
          body: { 'data-testid': 'NovaCardContent-body' },
          title: { 'data-testid': 'NovaCardContent-title' },
          subtitle: { 'data-testid': 'NovaCardContent-subtitle' },
          supportingText: { 'data-testid': 'NovaCardContent-supportingText' },
          action: { 'data-testid': 'NovaCardContent-action' },
        }}
      />,
    );
    expect(screen.getByTestId('NovaCardContent-body')).toHaveClass('NovaCardContent-body');
    expect(screen.getByTestId('NovaCardContent-title')).toHaveClass('NovaCardContent-title');
    expect(screen.getByTestId('NovaCardContent-subtitle')).toHaveClass('NovaCardContent-subtitle');
    expect(screen.getByTestId('NovaCardContent-supportingText')).toHaveClass('NovaCardContent-supportingText');
    expect(screen.getByTestId('NovaCardContent-action')).toHaveClass('NovaCardContent-action');
  });
});
