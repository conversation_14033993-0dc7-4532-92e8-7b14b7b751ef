import generateUtilityClasses from '@mui/utils/generateUtilityClasses';
import generateUtilityClass from '@mui/utils/generateUtilityClass';

export interface CardMediaClasses {
  /** Class name applied to the root element. */
  root: string;
  /** Class name applied to the root element if `component="video, audio, picture, iframe, or img"`. */
  media: string;
  /** Class name applied to the root element if `component="picture or img"`. */
  img: string;
}

export type CardMediaClassKey = keyof CardMediaClasses;

export function getCardMediaUtilityClass(slot: string): string {
  return generateUtilityClass('NovaCardMedia', slot);
}

const cardMediaClasses: CardMediaClasses = generateUtilityClasses('NovaCardMedia', ['root', 'media', 'img']);

export default cardMediaClasses;
