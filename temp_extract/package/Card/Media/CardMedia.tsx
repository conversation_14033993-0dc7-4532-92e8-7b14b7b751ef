'use client';
import * as React from 'react';
import { OverridableComponent } from '@mui/types';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import useSlotProps from '@mui/utils/useSlotProps';
import { styled } from '@pigment-css/react';
import { CardMediaOwnerState, CardMediaProps, CardMediaTypeMap } from './CardMedia.types';
import { getCardMediaUtilityClass } from './CardMedia.classes';

const useUtilityClasses = (ownerState: CardMediaOwnerState) => {
  const { isMediaComponent, isImageComponent } = ownerState;
  const slots = {
    root: ['root', isMediaComponent && 'media', isImageComponent && 'img'],
  };

  return composeClasses(slots, getCardMediaUtilityClass, {});
};

const CardMediaRoot = styled('div')<CardMediaOwnerState>(({ theme }) => ({
  display: 'block',
  backgroundSize: 'cover',
  backgroundRepeat: 'no-repeat',
  backgroundPosition: 'center',
  variants: [
    {
      props: { isMediaComponent: true },
      style: {
        width: '100%',
      },
    },
    {
      props: { isImageComponent: true },
      style: {
        objectFit: 'cover',
      },
    },
  ],
}));

// eslint-disable-next-line react/display-name
export const CardMedia = React.forwardRef((props: CardMediaProps, ref: React.ForwardedRef<Element>) => {
  const { children, component = 'div', image, src, slots = {}, slotProps = {}, style, ...rest } = props;

  const MEDIA_COMPONENTS = ['video', 'audio', 'picture', 'iframe', 'img'] as React.ElementType[];
  const IMAGE_COMPONENTS = ['picture', 'img'] as React.ElementType[];

  const isMediaComponent = MEDIA_COMPONENTS.includes(component);
  const isImageComponent = IMAGE_COMPONENTS.includes(component);

  const composedStyle = !isMediaComponent && image ? { backgroundImage: `url("${image}")`, ...style } : style;

  const ownerState = {
    ...props,
    component,
    isMediaComponent,
    isImageComponent,
  };

  const classes = useUtilityClasses(ownerState);

  const SlotRoot = slots.root ?? CardMediaRoot;

  const slotRootProps = useSlotProps({
    elementType: SlotRoot,
    externalSlotProps: slotProps.root,
    externalForwardedProps: { style: composedStyle, ...rest },
    additionalProps: {
      ref: ref,
      as: component,
      role: !isMediaComponent && image ? 'img' : undefined,
      src: isMediaComponent ? image || src : undefined,
    },
    ownerState: ownerState,
    className: classes.root,
  });

  return <SlotRoot {...slotRootProps}>{children}</SlotRoot>;
}) as OverridableComponent<CardMediaTypeMap>;
