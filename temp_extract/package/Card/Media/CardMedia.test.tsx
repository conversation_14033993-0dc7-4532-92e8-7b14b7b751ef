import '@testing-library/jest-dom/vitest';
import { render, screen, cleanup } from '@testing-library/react';
import React from 'react';
import { describe, expect, it, afterEach } from 'vitest';
import { CardMedia } from './CardMedia';

afterEach(() => {
  cleanup();
});

describe('CardMedia', () => {
  it('should render normal', () => {
    render(<CardMedia data-testid="NovaCardMedia-root" />);
    expect(screen.getByTestId('NovaCardMedia-root')).toHaveClass('NovaCardMedia-root');
  });

  it('should render image', () => {
    render(<CardMedia component="img" data-testid="NovaCardMedia-root" image="https://picsum.photos/100/100" />);
    expect(screen.getByRole('img')).toBeInTheDocument();
    expect(screen.getByTestId('NovaCardMedia-root')).toHaveClass('NovaCardMedia-img');
  });

  it('should render video', () => {
    render(<CardMedia component="video" data-testid="NovaCardMedia-root" src="https://test.mp4" />);
    expect(screen.getByTestId('NovaCardMedia-root')).toHaveClass('NovaCardMedia-media');
  });
});
