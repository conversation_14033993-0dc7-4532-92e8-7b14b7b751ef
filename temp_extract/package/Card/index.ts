import { CardRoot } from './Root';
import { CardActions } from './Actions';
import { CardContent } from './Content';
import { CardHeader } from './Header';
import { CardMedia } from './Media';

export { CardRoot, cardRootClasses } from './Root';
export { CardActions, cardActionsClasses } from './Actions';
export { CardContent, cardContentClasses } from './Content';
export { CardHeader, cardHeaderClasses } from './Header';
export { CardMedia, cardMediaClasses } from './Media';

export type { CardRootProps } from './Root';
export type { CardActionsProps } from './Actions';
export type { CardContentProps } from './Content';
export type { CardHeaderProps } from './Header';
export type { CardMediaProps } from './Media';

export const Card = {
  Root: CardRoot,
  Actions: CardActions,
  Content: CardContent,
  Header: CardHeader,
  Media: CardMedia,
};
