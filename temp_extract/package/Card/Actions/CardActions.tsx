'use client';
import * as React from 'react';
import { OverridableComponent } from '@mui/types';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import useSlotProps from '@mui/utils/useSlotProps';
import { styled } from '@pigment-css/react';
import { CardActionsOwnerState, CardActionsProps, CardActionsTypeMap } from './CardActions.types';
import { getCardActionsUtilityClass } from './CardActions.classes';
import CardContext from '../Context/CardContext';

const useUtilityClasses = (ownerState: CardActionsOwnerState) => {
  const slots = {
    root: ['root'],
  };

  return composeClasses(slots, getCardActionsUtilityClass, {});
};

const CardActionsRoot = styled('div')<CardActionsOwnerState>(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'flex-end',
  alignSelf: 'stretch',
  gap: '8px',
  variants: [
    {
      props: { orientation: 'horizontal' },
      style: {
        padding: '0px 16px 0px 8px',
      },
    },
    {
      props: { orientation: 'vertical' },
      style: {
        padding: '16px 24px 24px 24px',
      },
    },
  ],
}));

// eslint-disable-next-line react/display-name
export const CardActions = React.forwardRef((props: CardActionsProps, ref: React.ForwardedRef<Element>) => {
  const { children, component, slots = {}, slotProps = {}, ...rest } = props;

  const cardContext = React.useContext(CardContext);
  const orientation = cardContext?.orientation ?? 'vertical';
  const ownerState = { ...props, orientation };
  const classes = useUtilityClasses(ownerState);

  const SlotRoot = slots.root ?? CardActionsRoot;

  const slotRootProps = useSlotProps({
    elementType: SlotRoot,
    externalSlotProps: slotProps.root,
    externalForwardedProps: rest,
    additionalProps: {
      ref: ref,
      as: component,
    },
    ownerState,
    className: classes.root,
  });

  return <SlotRoot {...slotRootProps}>{children}</SlotRoot>;
}) as OverridableComponent<CardActionsTypeMap>;
