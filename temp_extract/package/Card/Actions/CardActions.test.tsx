import '@testing-library/jest-dom/vitest';
import { render, screen, cleanup } from '@testing-library/react';
import { Button } from '../../Button';
import React from 'react';
import { describe, expect, it, afterEach } from 'vitest';
import { CardActions } from './CardActions';

afterEach(() => {
  cleanup();
});

describe('CardActions', () => {
  it('should render normal', () => {
    render(<CardActions data-testid="NovaCardActions-root" />);
    expect(screen.getByTestId('NovaCardActions-root')).toHaveClass('NovaCardActions-root');
  });

  it('should render actions content', () => {
    render(
      <CardActions data-testid="NovaCardActions-root">
        <Button>Button</Button>
      </CardActions>,
    );
    expect(screen.getByTestId('NovaCardActions-root')).toHaveTextContent('Button');
  });
});
