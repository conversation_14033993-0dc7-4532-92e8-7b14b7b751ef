import * as React from 'react';
import { OverrideProps } from '@mui/types';
import { CreateSlotsAndSlotProps, SlotProps } from '../../types/slot';

export interface CardActionsSlots {
  /**
   * The component that renders the root slot.
   * @default 'div'
   */
  root?: React.ElementType;
}

export type CardActionsSlotsAndSlotProps = CreateSlotsAndSlotProps<
  CardActionsSlots,
  {
    root: SlotProps<'div', object, CardActionsOwnerState>;
  }
>;

export interface CardActionsTypeMap<P = object, D extends React.ElementType = 'div'> {
  props: P & {
    id?: string;
  } & CardActionsSlotsAndSlotProps;
  defaultComponent: D;
}

export type CardActionsProps<
  D extends React.ElementType = CardActionsTypeMap['defaultComponent'],
  P = { component?: React.ElementType },
> = OverrideProps<CardActionsTypeMap<P, D>, D>;

export interface CardActionsOwnerState extends CardActionsProps {
  orientation: 'vertical' | 'horizontal';
}
