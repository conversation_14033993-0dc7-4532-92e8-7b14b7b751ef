import * as React from 'react';
import { OverrideProps, Simplify } from '@mui/types';
import { SlotComponentProps } from '@mui/utils';
import { UseButtonParameters, UseButtonRootSlotProps } from '../../hooks/useButton';

export interface ButtonActions {
  focusVisible(): void;
}

export interface ButtonRootSlotPropsOverrides {}

export interface ButtonOwnProps extends Omit<UseButtonParameters, 'rootRef'> {
  /**
   * A ref for imperative actions. It currently only supports `focusVisible()` action.
   */
  action?: React.Ref<ButtonActions>;
  children?: React.ReactNode;
  className?: string;
  /**
   * The props used for each slot inside the Button.
   * @default {}
   */
  slotProps?: {
    root?: SlotComponentProps<'button', ButtonRootSlotPropsOverrides, ButtonOwnerState>;
  };
  /**
   * The components used for each slot inside the Button.
   * Either a string to use a HTML element or a component.
   * @default {}
   */
  slots?: ButtonSlots;
  /**
   * The HTML element that is ultimately rendered, for example 'button' or 'a'
   * @default 'button'
   */
  rootElementName?: keyof HTMLElementTagNameMap;
}

export interface ButtonSlots {
  /**
   * The component that renders the root.
   * @default props.href || props.to ? 'a' : 'button'
   */
  root?: React.ElementType;
}

export type ButtonProps<
  D extends React.ElementType = ButtonTypeMap['defaultComponent'],
  P = {
    component?: React.ElementType;
  },
> = OverrideProps<ButtonTypeMap<P, D>, D>;

export interface ButtonTypeMap<AdditionalProps = object, RootComponentType extends React.ElementType = 'button'> {
  props: ButtonOwnProps & AdditionalProps;
  defaultComponent: RootComponentType;
}

export type ButtonOwnerState = Simplify<
  ButtonOwnProps & {
    active: boolean;
    focusVisible: boolean;
  }
>;

export type ButtonRootSlotProps = Simplify<
  UseButtonRootSlotProps & {
    ownerState: ButtonOwnerState;
    className?: string;
    children?: React.ReactNode;
  }
>;
