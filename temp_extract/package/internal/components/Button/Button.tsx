'use client';
import * as React from 'react';
import { unstable_composeClasses as composeClasses, WithOptionalOwnerState } from '@mui/utils';

import { getButtonUtilityClass } from './buttonClasses';
import { ButtonProps, ButtonTypeMap, ButtonRootSlotProps, ButtonOwnerState } from './Button.types';
import { OverridableComponent } from '@mui/types';
import useSlotProps from '@mui/utils/useSlotProps';
import { useButton } from '../../hooks/useButton';

const useUtilityClasses = (ownerState: ButtonOwnerState) => {
  const { active, disabled, focusVisible } = ownerState;

  const slots = {
    root: ['root', disabled && 'disabled', focusVisible && 'focusVisible', active && 'active'],
  };

  return composeClasses(slots, getButtonUtilityClass, {});
};
/**
 * The foundation for building custom-styled buttons.
 *
 * Demos:
 *
 * - [But<PERSON>](https://mui.com/base-ui/react-button/)
 *
 * API:
 *
 * - [Button API](https://mui.com/base-ui/react-button/components-api/#button)
 */
const Button = React.forwardRef(function Button<RootComponentType extends React.ElementType>(
  props: ButtonProps<RootComponentType>,
  forwardedRef: React.ForwardedRef<Element>,
) {
  const {
    action,
    children,
    disabled,
    focusableWhenDisabled = false,
    onFocusVisible,
    slotProps = {},
    slots = {},
    rootElementName: rootElementNameProp = 'button',
    ...other
  } = props;

  const buttonRef = React.useRef<HTMLButtonElement | HTMLAnchorElement | HTMLElement>(null);

  let rootElementName = rootElementNameProp;

  if (typeof slots.root === 'string') {
    rootElementName = slots.root as keyof HTMLElementTagNameMap;
  } else if (other.href || other.to) {
    rootElementName = 'a';
  }

  const { active, focusVisible, setFocusVisible, getRootProps } = useButton({
    ...props,
    focusableWhenDisabled,
    rootElementName,
  });

  React.useImperativeHandle(
    action,
    () => ({
      focusVisible: () => {
        setFocusVisible(true);
        buttonRef.current!.focus();
      },
    }),
    [setFocusVisible],
  );

  const ownerState: ButtonOwnerState = {
    ...props,
    active,
    focusableWhenDisabled,
    focusVisible,
  };

  const classes = useUtilityClasses(ownerState);

  const defaultElement = other.href || other.to ? 'a' : 'button';
  const Root: React.ElementType = slots.root ?? defaultElement;
  const rootProps: WithOptionalOwnerState<ButtonRootSlotProps> = useSlotProps({
    elementType: Root,
    getSlotProps: getRootProps,
    externalForwardedProps: other,
    externalSlotProps: slotProps.root,
    additionalProps: {
      ref: forwardedRef,
    },
    ownerState,
    className: classes.root,
  });

  return <Root {...rootProps}>{children}</Root>;
}) as OverridableComponent<ButtonTypeMap>;

export { Button };
