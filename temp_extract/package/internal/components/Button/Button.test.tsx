import React from 'react';
import { Button } from './Button';
import { render, fireEvent } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';

describe('Button', () => {
  describe('role attribute', () => {
    it('is set when the root component is an HTML element other than a button', () => {
      const { getByRole } = render(<Button slots={{ root: 'span' }} />);
      expect(getByRole('button')).not.to.equal(null);
    });

    it('is set when the root component is a component that renders an HTML component other than a button', () => {
      const WrappedSpan = React.forwardRef(
        (props: React.HTMLAttributes<HTMLSpanElement>, ref: React.ForwardedRef<HTMLSpanElement>) => (
          <span role={props.role} ref={ref} />
        ),
      );
      WrappedSpan.displayName = 'WrappedSpan';

      const { getByRole } = render(<Button slots={{ root: WrappedSpan }} rootElementName="span" />);
      expect(getByRole('button')).not.to.equal(null);
    });

    it('is not set when the root component is a component that renders an HTML button component', () => {
      const WrappedButton = React.forwardRef(
        (props: React.HTMLAttributes<HTMLButtonElement>, ref: React.ForwardedRef<HTMLButtonElement>) => (
          <button role={props.role} ref={ref} />
        ),
      );
      WrappedButton.displayName = 'WrappedButton';

      const { getByRole } = render(<Button slots={{ root: WrappedButton }} />);
      expect(getByRole('button')).not.haveOwnProperty('role');
    });
  });

  describe('prop: focusableWhenDisabled', () => {
    describe('as native button', () => {
      it('has the aria-disabled instead of disabled attribute when disabled', () => {
        const { getByRole } = render(<Button focusableWhenDisabled disabled />);

        const button = getByRole('button');
        expect(button).toHaveAttribute('aria-disabled');
        expect(button).not.toHaveAttribute('disabled');
      });

      it('can receive focus when focusableWhenDisabled is set', () => {
        const { getByRole } = render(<Button focusableWhenDisabled disabled />);

        const button = getByRole('button');
        button.focus();

        expect(document.activeElement).toBe(button);
      });
    });

    describe('as non-button element', () => {
      it('can receive focus when focusableWhenDisabled is set', () => {
        const { getByRole } = render(<Button slots={{ root: 'span' }} focusableWhenDisabled disabled />);

        const button = getByRole('button');
        button.focus();

        expect(document.activeElement).toBe(button);
      });

      it('has aria-disabled and tabIndex attributes set', () => {
        const { getByRole } = render(<Button slots={{ root: 'span' }} focusableWhenDisabled disabled />);

        const button = getByRole('button');

        expect(button).toHaveAttribute('aria-disabled', 'true');
        expect(button).toHaveAttribute('tabindex', '0');
      });

      it('does not respond to user actions when disabled and focused', () => {
        const handleClick = vi.fn();
        const { getByRole } = render(
          <Button slots={{ root: 'span' }} focusableWhenDisabled disabled onClick={handleClick} />,
        );

        const button = getByRole('button');
        button.focus();

        button.click();
        fireEvent.keyDown(button, { key: 'Enter' });
        fireEvent.keyUp(button, { key: ' ' });

        expect(handleClick).not.toHaveBeenCalled();
      });
    });
  });

  describe('as non-button element', () => {
    it('can receive focus when focusableWhenDisabled is set', () => {
      const { getByRole } = render(<Button slots={{ root: 'span' }} focusableWhenDisabled disabled />);

      const button = getByRole('button');
      button.focus();

      expect(document.activeElement).toBe(button);
    });

    it('has aria-disabled and tabIndex attributes set', () => {
      const { getByRole } = render(<Button slots={{ root: 'span' }} focusableWhenDisabled disabled />);

      const button = getByRole('button');

      expect(button).toHaveAttribute('aria-disabled', 'true');
      expect(button).toHaveAttribute('tabindex', '0');
    });

    it('does not respond to user actions when disabled and focused', () => {
      const handleClick = vi.fn();
      const { getByRole } = render(
        <Button slots={{ root: 'span' }} focusableWhenDisabled disabled onClick={handleClick} />,
      );

      const button = getByRole('button');
      button.focus();

      button.click();
      fireEvent.keyDown(button, { key: 'Enter' });
      fireEvent.keyUp(button, { key: ' ' });

      expect(handleClick).not.toHaveBeenCalled();
    });
  });

  describe('prop: href', () => {
    it('renders as a link when the "href" prop is provided', () => {
      const { getByRole } = render(<Button href="#" />);
      expect(getByRole('link')).not.to.equal(null);
    });

    it('renders as the element provided in the "component" prop, even with a "href" prop', () => {
      const { getByRole } = render(<Button slots={{ root: 'h1' }} href="#" />);
      expect(getByRole('heading')).not.to.equal(null);
    });
  });

  describe('prop: to', () => {
    it('renders as a link when the "to" prop is provided', () => {
      const { container } = render(<Button to="#" />);
      expect(container.querySelector('a')).not.to.equal(null);
    });

    it('renders as the element provided in the "component" prop, even with a "to" prop', () => {
      const { getByRole } = render(<Button slots={{ root: 'h1' }} to="#" />);
      expect(getByRole('heading')).not.to.equal(null);
    });
  });
});
