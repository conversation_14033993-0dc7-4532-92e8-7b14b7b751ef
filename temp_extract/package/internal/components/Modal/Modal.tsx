'use client';
import * as React from 'react';

import { ModalOwnerState, ModalProps, ModalTypeMap } from './Modal.types';
import { unstable_composeClasses as composeClasses, EventHandlers } from '@mui/utils';
import { Portal } from '../Portal';

import { FocusTrap } from '../FocusTrap';
import { getModalUtilityClass } from './modalClasses';
import useSlotProps from '@mui/utils/useSlotProps';
import { OverridableComponent } from '@mui/types';
import { useModal } from '../../hooks/useModal';

const useUtilityClasses = (ownerState: ModalOwnerState) => {
  const { open, exited } = ownerState;

  const slots = {
    root: ['root', !open && exited && 'hidden'],
    backdrop: ['backdrop'],
  };

  return composeClasses(slots, getModalUtilityClass, {});
};

/**
 * Modal is a lower-level construct that is leveraged by the following components:
 *
 * *   [Dialog](https://mui.com/material-ui/api/dialog/)
 * *   [Drawer](https://mui.com/material-ui/api/drawer/)
 * *   [Menu](https://mui.com/material-ui/api/menu/)
 * *   [Popover](https://mui.com/material-ui/api/popover/)
 *
 * If you are creating a modal dialog, you probably want to use the [Dialog](https://mui.com/material-ui/api/dialog/) component
 * rather than directly using Modal.
 *
 * This component shares many concepts with [react-overlays](https://react-bootstrap.github.io/react-overlays/#modals).
 *
 * Demos:
 *
 * - [Modal](https://mui.com/base-ui/react-modal/)
 *
 * API:
 *
 * - [Modal API](https://mui.com/base-ui/react-modal/components-api/#modal)
 */
const Modal = React.forwardRef<HTMLElement, ModalProps>(function Modal<RootComponentType extends React.ElementType>(
  props: ModalProps<RootComponentType>,
  forwardedRef: React.ForwardedRef<HTMLElement>,
) {
  const {
    children,
    closeAfterTransition = false,
    container,
    disableAutoFocus = false,
    disableEnforceFocus = false,
    disableEscapeKeyDown = false,
    disablePortal = false,
    disableRestoreFocus = false,
    disableScrollLock = false,
    hideBackdrop = false,
    keepMounted = false,
    onBackdropClick,
    onClose,
    onKeyDown,
    open,
    onTransitionEnter,
    onTransitionExited,
    slotProps = {},
    slots = {},
    ...other
  } = props;

  const propsWithDefaults: Omit<ModalOwnerState, 'exited' | 'hasTransition'> = {
    ...props,
    closeAfterTransition,
    disableAutoFocus,
    disableEnforceFocus,
    disableEscapeKeyDown,
    disablePortal,
    disableRestoreFocus,
    disableScrollLock,
    hideBackdrop,
    keepMounted,
  };

  const { getRootProps, getBackdropProps, getTransitionProps, portalRef, isTopModal, exited, hasTransition } = useModal(
    {
      ...propsWithDefaults,
      rootRef: forwardedRef,
    },
  );

  const ownerState = {
    ...propsWithDefaults,
    exited,
    hasTransition,
  };

  const classes = useUtilityClasses(ownerState);

  const childProps: {
    onEnter?: () => void;
    onExited?: () => void;
    tabIndex?: string;
  } = {};
  if (children.props.tabIndex === undefined) {
    childProps.tabIndex = '-1';
  }

  // It's a Transition like component
  if (hasTransition) {
    const { onEnter, onExited } = getTransitionProps();
    childProps.onEnter = onEnter;
    childProps.onExited = onExited;
  }

  const Root = slots.root ?? 'div';
  const rootProps = useSlotProps({
    elementType: Root,
    externalSlotProps: slotProps.root,
    externalForwardedProps: other,
    getSlotProps: getRootProps,
    className: classes.root,
    ownerState,
  });

  const BackdropComponent = slots.backdrop;
  const backdropProps = useSlotProps({
    elementType: BackdropComponent,
    externalSlotProps: slotProps.backdrop,
    getSlotProps: (otherHandlers: EventHandlers) => {
      return getBackdropProps({
        ...otherHandlers,
        onClick: (event: React.MouseEvent) => {
          if (onBackdropClick) {
            onBackdropClick(event);
          }
          if (otherHandlers?.onClick) {
            otherHandlers.onClick(event);
          }
        },
      });
    },
    className: classes.backdrop,
    ownerState,
  });

  if (!keepMounted && !open && (!hasTransition || exited)) {
    return null;
  }

  return (
    <Portal ref={portalRef} container={container} disablePortal={disablePortal}>
      <Root {...rootProps}>
        {!hideBackdrop && BackdropComponent ? <BackdropComponent {...backdropProps} /> : null}
        <FocusTrap
          disableEnforceFocus={disableEnforceFocus}
          disableAutoFocus={disableAutoFocus}
          disableRestoreFocus={disableRestoreFocus}
          isEnabled={isTopModal}
          open={open}
        >
          {React.cloneElement(children, childProps)}
        </FocusTrap>
      </Root>
    </Portal>
  );
}) as OverridableComponent<ModalTypeMap>;

export { Modal };
