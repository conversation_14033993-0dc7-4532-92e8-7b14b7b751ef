import * as React from 'react';
import { useSnackbar } from './useSnackbar';
import { render, screen, fireEvent } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';

describe('useSnackbar', () => {
  describe('getRootProps', () => {
    it('returns props for the root slot', () => {
      function Snackbar() {
        const { getRootProps } = useSnackbar();

        return <div {...getRootProps()} />;
      }

      render(<Snackbar />);

      const snackbar = screen.getByRole('presentation');
      expect(snackbar).toBeInTheDocument();
    });

    it('forwards external props including event handlers', () => {
      const fn = vi.fn();
      function Snackbar() {
        const { getRootProps } = useSnackbar();

        return <div {...getRootProps({ onClick: fn, random: 'arbitraryValue' })} />;
      }

      const { getByRole } = render(<Snackbar />);

      const snackbar = getByRole('presentation');
      expect(snackbar).toHaveAttribute('random', 'arbitraryValue');

      fireEvent.click(snackbar);
      expect(fn).toHaveBeenCalled();
    });
  });
});
