'use client';
import * as React from 'react';
import { unstable_useForkRef as useForkRef } from '@mui/utils';
import extractEventHandlers from '@mui/utils/extractEventHandlers';
import { FormControlContextValue, useFormControlContext } from '../../../FormControl/FormControlContext';
import {
  UseInputInputSlotProps,
  UseInputParameters,
  UseInputRootSlotProps,
  UseInputReturnValue,
} from './useInput.types';

export function useInput(parameters: UseInputParameters = {}): UseInputReturnValue {
  const {
    defaultValue: defaultValueProp,
    disabled: disabledProp = false,
    error: errorProp = false,
    onBlur,
    onChange,
    onFocus,
    required: requiredProp = false,
    value: valueProp,
    inputRef: inputRefProp,
  } = parameters;

  const formControlContext: FormControlContextValue | undefined = useFormControlContext();
  const [focused, setFocused] = React.useState(false);
  const inputRef = React.useRef<HTMLInputElement>(null);

  const defaultValue = parameters.defaultValue ?? defaultValueProp;
  const disabled: boolean = parameters.disabled ?? formControlContext?.disabled ?? disabledProp;
  const error: boolean = parameters.error ?? formControlContext?.error ?? errorProp;
  const required: boolean = parameters.required ?? formControlContext?.required ?? requiredProp;
  const value = parameters.value ?? formControlContext?.value ?? valueProp;

  const { current: isControlled } = React.useRef(value != null);

  const handleInputRefWarning = React.useCallback((instance: HTMLElement) => {
    if (process.env.NODE_ENV !== 'production') {
      if (instance && instance.nodeName !== 'INPUT' && !instance.focus) {
        console.error(
          [
            'MUI: You have provided a `slots.input` to the input component',
            'that does not correctly handle the `ref` prop.',
            'Make sure the `ref` prop is called with a HTMLInputElement.',
          ].join('\n'),
        );
      }
    }
  }, []);

  const handleInputRef = useForkRef(inputRef, inputRefProp, handleInputRefWarning);

  // The blur won't fire when the disabled state is set on a focused input.
  // We need to book keep the focused state manually.
  React.useEffect(() => {
    if (!formControlContext && disabled && focused) {
      setFocused(false);

      // @ts-expect-error no event
      onBlur?.();
    }
  }, [formControlContext, disabled, focused, onBlur]);

  const handleFocus =
    (otherHandlers: Record<string, React.EventHandler<any> | undefined>) =>
    (event: React.FocusEvent<HTMLInputElement>) => {
      // Fix a bug with IE11 where the focus/blur events are triggered
      // while the component is disabled.
      if (formControlContext?.disabled) {
        event.stopPropagation();
        return;
      }

      otherHandlers.onFocus?.(event);

      if (formControlContext && formControlContext.onFocus) {
        formControlContext?.onFocus?.();
      } else {
        setFocused(true);
      }
    };

  const handleBlur =
    (otherHandlers: Record<string, React.EventHandler<any> | undefined>) =>
    (event: React.FocusEvent<HTMLInputElement>) => {
      otherHandlers.onBlur?.(event);

      if (formControlContext && formControlContext.onBlur) {
        formControlContext.onBlur();
      } else {
        setFocused(false);
      }
    };

  const handleChange =
    (otherHandlers: Record<string, React.EventHandler<any> | undefined>) =>
    (event: React.ChangeEvent<HTMLInputElement>, ...args: unknown[]) => {
      if (!isControlled) {
        const element = event.target || inputRef.current;
        if (element == null) {
          throw /* minify-error */ new Error(
            'MUI: Expected valid input target. ' +
              'Did you use a custom `slots.input` and forget to forward refs? ' +
              'See https://mui.com/r/input-component-ref-interface for more info.',
          );
        }
      }

      formControlContext?.onChange?.(event);

      // @ts-expect-error no args
      otherHandlers.onChange?.(event, ...args);
    };

  const handleClick =
    (otherHandlers: Record<string, React.EventHandler<any>>) => (event: React.MouseEvent<HTMLInputElement>) => {
      if (inputRef.current && event.currentTarget === event.target) {
        inputRef.current.focus();
      }

      otherHandlers.onClick?.(event);
    };

  const getRootProps = <ExternalProps extends Record<string, any> = object>(
    externalProps: ExternalProps = {} as ExternalProps,
  ): UseInputRootSlotProps<ExternalProps> => {
    // onBlur, onChange and onFocus are forwarded to the input slot.
    const propsEventHandlers = extractEventHandlers(parameters, ['onBlur', 'onChange', 'onFocus']);
    const externalEventHandlers = { ...propsEventHandlers, ...extractEventHandlers(externalProps) };

    return {
      ...externalProps,
      ...externalEventHandlers,
      onClick: handleClick(externalEventHandlers),
    };
  };

  const getInputProps = <ExternalProps extends Record<string, any> = object>(
    externalProps: ExternalProps = {} as ExternalProps,
  ): UseInputInputSlotProps<ExternalProps> => {
    const propsEventHandlers: Record<string, React.EventHandler<any> | undefined> = {
      onBlur,
      onChange,
      onFocus,
    };

    const externalEventHandlers = { ...propsEventHandlers, ...extractEventHandlers(externalProps) };

    const mergedEventHandlers = {
      ...externalEventHandlers,
      onBlur: handleBlur(externalEventHandlers),
      onChange: handleChange(externalEventHandlers),
      onFocus: handleFocus(externalEventHandlers),
    };

    return {
      ...mergedEventHandlers,
      'aria-invalid': error || undefined,
      defaultValue: defaultValue as string | number | readonly string[] | undefined,
      value: value as string | number | readonly string[] | undefined,
      required,
      disabled,
      ...externalProps,
      ref: handleInputRef,
      ...mergedEventHandlers,
    };
  };

  return {
    disabled,
    error,
    focused: formControlContext?.focused || focused,
    required,
    value,
    getInputProps,
    getRootProps,
    inputRef: handleInputRef,
  };
}
