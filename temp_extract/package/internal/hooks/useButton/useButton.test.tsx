import React from 'react';
import { render, fireEvent } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';
import { useButton } from './useButton';

describe('useButton', () => {
  describe('state: active', () => {
    describe('when using a button element', () => {
      it('is set when triggered by mouse', () => {
        function TestComponent() {
          const buttonRef = React.useRef(null);
          const { active, getRootProps } = useButton({ rootRef: buttonRef });

          return <button {...getRootProps()} className={active ? 'active' : ''} />;
        }

        const { getByRole } = render(<TestComponent />);
        const button = getByRole('button');
        fireEvent.mouseDown(button);
        expect(button).toHaveClass('active');
        fireEvent.mouseUp(button);
        expect(button).not.toHaveClass('active');
      });

      it('is set when triggered by keyboard', () => {
        function TestComponent() {
          const buttonRef = React.useRef(null);
          const { active, getRootProps } = useButton({ rootRef: buttonRef });

          return <button {...getRootProps()} className={active ? 'active' : ''} />;
        }

        const { getByRole } = render(<TestComponent />);
        const button = getByRole('button');
        button.focus();
        fireEvent.keyDown(button, { key: ' ' });
        expect(button).toHaveClass('active');
        fireEvent.keyUp(button, { key: ' ' });
        expect(button).not.toHaveClass('active');
      });

      it('is set when clicked on an element inside the button', () => {
        function TestComponent() {
          const buttonRef = React.useRef(null);
          const { active, getRootProps } = useButton({ rootRef: buttonRef });

          return (
            <button {...getRootProps()} className={active ? 'active' : ''}>
              <span>Click here</span>
            </button>
          );
        }

        const { getByText, getByRole } = render(<TestComponent />);
        const span = getByText('Click here');
        const button = getByRole('button');
        fireEvent.mouseDown(span);
        expect(button).toHaveClass('active');
      });

      it('is unset when mouse button is released above another element', () => {
        function TestComponent() {
          const buttonRef = React.useRef(null);
          const { active, getRootProps } = useButton({ rootRef: buttonRef });

          return (
            <div data-testid="parent">
              <button {...getRootProps()} className={active ? 'active' : ''} />
            </div>
          );
        }

        const { getByRole, getByTestId } = render(<TestComponent />);
        const button = getByRole('button');
        const background = getByTestId('parent');
        fireEvent.mouseDown(button);
        expect(button).toHaveClass('active');
        fireEvent.mouseUp(background);
        expect(button).not.toHaveClass('active');
      });
    });

    describe('when using a span element', () => {
      it('is set when triggered by mouse', () => {
        function TestComponent() {
          const buttonRef = React.useRef(null);
          const { active, getRootProps } = useButton({ rootRef: buttonRef });

          return <span {...getRootProps()} className={active ? 'active' : ''} />;
        }

        const { getByRole } = render(<TestComponent />);
        const button = getByRole('button');
        fireEvent.mouseDown(button);
        expect(button).toHaveClass('active');
        fireEvent.mouseUp(button);
        expect(button).not.toHaveClass('active');
      });

      it('is set when triggered by keyboard', () => {
        function TestComponent() {
          const buttonRef = React.useRef(null);
          const { active, getRootProps } = useButton({ rootRef: buttonRef });

          return <span {...getRootProps()} className={active ? 'active' : ''} />;
        }

        const { getByRole } = render(<TestComponent />);
        const button = getByRole('button');
        button.focus();
        fireEvent.keyDown(button, { key: ' ' });
        expect(button).toHaveClass('active');
        fireEvent.keyUp(button, { key: ' ' });
        expect(button).not.toHaveClass('active');
      });
    });

    describe('event handlers', () => {
      interface WithClickHandler {
        onClick: React.MouseEventHandler;
      }

      it('calls them when provided in props', () => {
        function TestComponent(props: WithClickHandler) {
          const ref = React.useRef(null);
          const { getRootProps } = useButton({ ...props, rootRef: ref });
          return <button {...getRootProps()} />;
        }

        const handleClick = vi.fn();

        const { getByRole } = render(<TestComponent onClick={handleClick} />);
        fireEvent.click(getByRole('button'));

        expect(handleClick).toHaveBeenCalled();
      });

      it('calls them when provided in getRootProps()', () => {
        const handleClick = vi.fn();

        function TestComponent() {
          const ref = React.useRef(null);
          const { getRootProps } = useButton({ rootRef: ref });
          return <button {...getRootProps({ onClick: handleClick })} />;
        }

        const { getByRole } = render(<TestComponent />);
        fireEvent.click(getByRole('button'));

        expect(handleClick).toHaveBeenCalled();
      });

      it('calls the one provided in getRootProps() when both props and getRootProps have ones', () => {
        const handleClickExternal = vi.fn();
        const handleClickInternal = vi.fn();

        function TestComponent(props: WithClickHandler) {
          const ref = React.useRef(null);
          const { getRootProps } = useButton({ ...props, rootRef: ref });
          return <button {...getRootProps({ onClick: handleClickInternal })} />;
        }

        const { getByRole } = render(<TestComponent onClick={handleClickExternal} />);
        fireEvent.click(getByRole('button'));

        expect(handleClickInternal).toHaveBeenCalled();
        expect(handleClickExternal).not.toHaveBeenCalled();
      });
    });
  });

  describe('tabIndex', () => {
    it('returns tabIndex in getRootProps when host component is not BUTTON', () => {
      function TestComponent() {
        const ref = React.useRef(null);
        const { getRootProps } = useButton({ rootRef: ref });

        expect(getRootProps().tabIndex).toBe(ref.current ? 0 : undefined);

        return <span {...getRootProps()} />;
      }

      const { getByRole } = render(<TestComponent />);
      expect(getByRole('button')).toHaveAttribute('tabIndex', '0');
    });

    it('returns tabIndex in getRootProps if it is explicitly provided', () => {
      const customTabIndex = 3;
      function TestComponent() {
        const ref = React.useRef(null);
        const { getRootProps } = useButton({ rootRef: ref, tabIndex: customTabIndex });
        return <button {...getRootProps()} />;
      }

      const { getByRole } = render(<TestComponent />);
      expect(getByRole('button')).toHaveAttribute('tabIndex', customTabIndex.toString());
    });
  });

  describe('arbitrary props', () => {
    it('are passed to the host component', () => {
      const buttonTestId = 'button-test-id';
      function TestComponent() {
        const { getRootProps } = useButton({});
        return <button {...getRootProps({ 'data-testid': buttonTestId })} />;
      }

      const { getByRole } = render(<TestComponent />);
      expect(getByRole('button')).toHaveAttribute('data-testid', buttonTestId);
    });
  });
});
