'use client';
import * as React from 'react';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import useForkRef from '@mui/utils/useForkRef';
import useSlotProps from '@mui/utils/useSlotProps';
import capitalize from '@mui/utils/capitalize';
import { styled } from '@pigment-css/react';
import { DataGridOwnerState, DataGridProps } from './DataGrid.types';
import { getDataGridUtilityClass } from './DataGrid.classes';
import { DataGridProvider } from './context/DataGridProvider';
import { DataGridPagination } from './components/DataGridPagination';
import { DataGridContainer } from './components/DataGridContainer';
import { useElementSize } from './hooks/useElementSize';
import { useDataGridProps } from './hooks/useDataGridProps';

const useUtilityClasses = ({ density }: { density: DataGridProps['density'] }) => {
  const slots = {
    root: ['root', density && `density${capitalize(density)}`],
  };

  return composeClasses(slots, getDataGridUtilityClass, {});
};

const DataGridRoot = styled('div')<DataGridOwnerState>(() => ({
  display: 'flex',
  flexDirection: 'column',
  overflow: 'hidden',
  minHeight: 0,
  height: '100%',
}));

const DataGridInner = <T,>(props: DataGridProps<T>, ref: React.ForwardedRef<HTMLDivElement>) => {
  const { passProps, slots, slotProps, component, rest } = useDataGridProps<T>(props);
  const classes = useUtilityClasses({ density: passProps.density });
  const { width = 0, height = 0, ref: elRef } = useElementSize<HTMLDivElement>();
  const forkRef = useForkRef(elRef, ref);
  const SlotRoot = slots?.root ?? DataGridRoot;
  const slotRootProps = useSlotProps({
    elementType: SlotRoot,
    externalSlotProps: slotProps?.root,
    externalForwardedProps: rest,
    additionalProps: {
      ref: forkRef,
      as: component,
    },
    ownerState: passProps,
    className: classes.root,
  });
  return (
    <DataGridProvider<T>
      props={{
        ...passProps,
        gridHeight: height,
        gridWidth: width,
        gridRef: elRef,
      }}
    >
      <SlotRoot {...slotRootProps}>
        <DataGridContainer />
        {passProps.pagination && <DataGridPagination />}
      </SlotRoot>
    </DataGridProvider>
  );
};

export const DataGrid = React.forwardRef(DataGridInner) as <T>(
  props: DataGridProps<T> & React.RefAttributes<HTMLDivElement>,
) => React.ReactElement;
