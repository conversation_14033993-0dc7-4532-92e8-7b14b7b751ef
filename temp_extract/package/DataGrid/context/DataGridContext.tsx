import * as React from 'react';
import { DataGridProps } from '../DataGrid.types';
import { DATA_GRID_PROPS_DEFAULT_VALUES } from '../constants/dataGridDefaultProps';
import { ColumnType } from '../types/column.type';
import { RowId } from '../types/row.type';
import { CellType } from '../types/cell.type';
import { EditFinishParas } from '../types/edit.type';

type DataGridNonNullableProps<T> = Required<
  Pick<
    DataGridProps<T>,
    | 'data'
    | 'columns'
    | 'initialState'
    | 'density'
    | 'uniqueField'
    | 'sortMode'
    | 'pagination'
    | 'paginationMode'
    | 'scrollbarSize'
    | 'slots'
    | 'slotProps'
  >
>;
export type DataGridPropsWithDefaultValue<T> = Omit<DataGridProps<T>, keyof DataGridNonNullableProps<T>> &
  DataGridNonNullableProps<T>;

export type DataGridContextValue<T> = DataGridPropsWithDefaultValue<T> & {
  paginationHeight: number;
  computedColumnWidths: number[];
  allRowIds: Array<RowId>;
  gridHeight: number;
  gridWidth: number;
  gridScrollHeight: number;
  gridScrollWidth: number;
  isScrollX: boolean;
  isScrollY: boolean;
  onColumnResize: (column: ColumnType, columnWidth: number, newWidth: number, stopped?: boolean) => void;
  editingCell: CellType;
  editingValueCache: Record<string, any>;
  updateEditingValue: (rowId: RowId, field: string, value: any) => void;
  onFinishEditing: (paras: EditFinishParas<T>) => Promise<void>;
};

export const defaultContextValue = {
  ...DATA_GRID_PROPS_DEFAULT_VALUES,
  paginationHeight: 48,
  computedColumnWidths: [],
  allRowIds: [],
  gridHeight: 0,
  gridWidth: 0,
  gridScrollHeight: 0,
  gridScrollWidth: 0,
  isScrollX: false,
  isScrollY: false,
  onColumnResize: () => {},
  editingCell: { rowId: '', columnField: '' },
  editingValueCache: {},
  updateEditingValue: () => {},
  onFinishEditing: async () => {},
} as DataGridContextValue<unknown>;

export const DataGridContext = React.createContext<DataGridContextValue<unknown>>(defaultContextValue);

if (process.env.NODE_ENV !== 'production') {
  DataGridContext.displayName = 'DataGridContext';
}

export function useDataGridContext<T>() {
  const context = React.useContext(DataGridContext);
  if (!context) {
    throw new Error('useDataGridContext must be used within a DataGrid');
  }
  return context as DataGridContextValue<T>;
}
