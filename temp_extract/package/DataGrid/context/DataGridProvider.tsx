import { DataControllerProps, useDataController } from '../hooks/useDataController';
import { useDataGridRefs } from '../hooks/useDataGridRefs';
import { DataGridContext, DataGridContextValue } from './DataGridContext';
import { DataGridRefsContext } from './DataGridRefsContext';

type DataGridProviderProps<T> = {
  children: React.ReactNode;
  props: DataControllerProps<T>;
};

export function DataGridProvider<T>({ children, props }: DataGridProviderProps<T>) {
  const data = useDataController<T>(props);
  const refs = useDataGridRefs();
  return (
    <DataGridContext.Provider value={data as DataGridContextValue<unknown>}>
      <DataGridRefsContext.Provider value={refs}>{children}</DataGridRefsContext.Provider>
    </DataGridContext.Provider>
  );
}
