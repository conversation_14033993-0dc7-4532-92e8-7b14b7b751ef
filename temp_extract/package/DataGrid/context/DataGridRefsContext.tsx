import React, { createContext } from 'react';
type DataGridRefsContextValue = {
  rootRef: React.RefObject<HTMLDivElement>;
  headersRef: React.RefObject<HTMLDivElement>;
  bodyRef: React.RefObject<HTMLDivElement>;
  containerRef: React.RefObject<HTMLDivElement>;
  scrollbarVerticalRef: React.RefObject<HTMLDivElement>;
  scrollbarHorizontalRef: React.RefObject<HTMLDivElement>;
} | null;

export const DataGridRefsContext = createContext<DataGridRefsContextValue | null>(null);

if (process.env.NODE_ENV !== 'production') {
  DataGridRefsContext.displayName = 'DataGridRefsContext';
}

export function useDataGridRefsContext(): DataGridRefsContextValue {
  return React.useContext(DataGridRefsContext);
}
