import React, { useCallback, useMemo } from 'react';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import { styled } from '@pigment-css/react';
import useSlotProps from '@mui/utils/useSlotProps';
import dataGridClasses, { getDataGridUtilityClass } from '../DataGrid.classes';
import { useDataGridContext } from '../context/DataGridContext';
import { RowCellItem } from './cells/RowCellItem';
import { CheckboxCellItem } from './cells/CheckboxCellItem';
import { RadioCellItem } from './cells/RadioCellItem';
import { EmptyCellItem } from './cells/EmptyCellItem';
import { ExpandCellItem } from './cells/ExpandCellItem';
import { ColumnType } from '../types/column.type';
import { RowId } from '../types/row.type';
import { ClickAwayListener } from '../../ClickAwayListener';
export interface DataGridRowProps<T> extends React.HTMLAttributes<HTMLDivElement> {
  row: T;
  rowIndex: number;
}

const useUtilityClasses = (ownerState: OwnerState) => {
  const { selected, isEditing } = ownerState;
  const slots = {
    root: ['row', selected && 'rowSelected', isEditing && 'rowEditing'],
  };

  return composeClasses(slots, getDataGridUtilityClass, {});
};

type OwnerState = {
  selected: boolean;
  isEditing: boolean;
};

const DataGridRowRoot = styled('div')<OwnerState>(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  borderBottom: '1px solid',
  borderBottomColor: theme.vars.palette.outlineVariant,
  boxSizing: 'border-box',
  '&:hover': {
    backgroundColor: `color-mix(in srgb, ${theme.vars.palette.surfaceContainer}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.hoverOnSurface})`,
    [`& .${dataGridClasses.cellSelected}`]: {
      backgroundColor: `color-mix(in srgb, ${theme.vars.palette.secondaryContainer}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.hoverOnSurface})`,
    },
    [`& .${dataGridClasses.cellFixed}`]: {
      backgroundColor: `color-mix(in srgb, ${theme.vars.palette.surfaceContainer}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.hoverOnSurface})`,
    },
  },
  '&:active': {
    backgroundColor: `color-mix(in srgb, ${theme.vars.palette.surfaceContainer}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.pressOnSurface})`,
    [`& .${dataGridClasses.cellSelected}`]: {
      backgroundColor: `color-mix(in srgb, ${theme.vars.palette.secondaryContainer}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.pressOnSurface})`,
    },
    [`& .${dataGridClasses.cellFixed}`]: {
      backgroundColor: `color-mix(in srgb, ${theme.vars.palette.surfaceContainer}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.pressOnSurface})`,
    },
  },
  height: 'var(--nova-datagrid-row-height)',
  variants: [
    {
      props: { selected: true },
      style: {
        background: theme.vars.palette.secondaryContainer,
        borderBottomColor: theme.vars.palette.outline,
        '&:hover': {
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.secondaryContainer}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.hoverOnSurface})`,
        },
      },
    },
  ],
}));

const DataGridRowInner = <T,>(props: DataGridRowProps<T>, ref: React.ForwardedRef<HTMLDivElement>) => {
  const contextProps = useDataGridContext<T>();
  const {
    rowHeight,
    density,
    columns,
    computedColumnWidths,
    slots,
    slotProps,
    rowSelectionMode,
    selectedRows = [],
    uniqueField,
    onSelectedRowsChange,
    isRowSelectable,
    expandedRows = [],
    expandedRowPanelRender,
    onExpandedRowsChange,
    isRowExpandable,
    editMode,
    editingRows = [],
    onFinishEditing,
    rowEvents,
  } = contextProps;

  const { rowIndex, row, ...rest } = props;
  const SlotRoot = slots.row ?? DataGridRowRoot;
  const rowId = row[uniqueField as keyof T] as RowId;
  const isEditing = editingRows.includes(rowId);
  const handlers = useMemo(() => {
    const handlers: Record<string, React.EventHandler<any>> = {};
    if (rowEvents) {
      Object.entries(rowEvents).forEach(([eventName, handler]) => {
        handlers[eventName] = (e: any) => {
          handler?.({ rowId, rowData: row }, e);
        };
      });
    }
    return handlers;
  }, [rowEvents, rowId, row]);

  const ownerState = { selected: Boolean(rowSelectionMode) && selectedRows.includes(rowId), isEditing };
  const classes = useUtilityClasses(ownerState);
  const slotRootProps = useSlotProps({
    elementType: SlotRoot,
    ownerState,
    className: classes.root,
    externalSlotProps: slotProps.row,
    additionalProps: {
      tabIndex: -1,
      'data-id': rowId,
      role: 'row',
      'aria-rowindex': rowIndex + 1,
      ref,
      style: {
        '--nova-datagrid-row-height': `${rowHeight}px`,
      },
      ...handlers,
      ...rest,
    },
  });

  const onCheckChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const checked = e.target.checked;
      if (rowSelectionMode === 'checkboxSelection') {
        let cloneRowSelection = selectedRows.slice();
        if (checked && !cloneRowSelection.includes(rowId)) {
          cloneRowSelection.push(rowId);
        } else if (!checked) {
          cloneRowSelection = cloneRowSelection.filter((i) => i !== rowId);
        }
        onSelectedRowsChange?.(cloneRowSelection);
      } else if (rowSelectionMode === 'radioSelection') {
        if (checked) {
          onSelectedRowsChange?.([rowId]);
        } else {
          onSelectedRowsChange?.([]);
        }
      }
    },
    [onSelectedRowsChange, rowId, selectedRows, rowSelectionMode],
  );

  const rowCells = useMemo(() => {
    let colIndex = 0;
    const displayCells = [];
    const leftFixedColumns = columns.filter((c) => c.fixed === 'left');
    const rightFixedColumns = [...columns].reverse().filter((c) => c.fixed === 'right');
    if (expandedRowPanelRender) {
      colIndex = colIndex + 1;
      displayCells.push(
        <ExpandCellItem
          key={'expand'}
          showExpandIcon={isRowExpandable ? isRowExpandable(row) : true}
          expanded={expandedRows.includes(rowId)}
          onExpandClick={(expanded: boolean, e: React.MouseEvent<HTMLButtonElement>) => {
            let newExpandedRows = [...expandedRows];
            if (expanded && !expandedRows.includes(rowId)) {
              newExpandedRows.push(rowId);
            } else if (!expanded) {
              newExpandedRows = newExpandedRows.filter((i) => i !== rowId);
            }
            onExpandedRowsChange?.(newExpandedRows);
          }}
          size={density === 'compact' ? 'small' : 'medium'}
          colIndex={colIndex}
        />,
      );
    }

    if (rowSelectionMode) {
      colIndex = colIndex + 1;
      if (rowSelectionMode === 'checkboxSelection') {
        const isSelectable = isRowSelectable ? isRowSelectable(row) : true;
        displayCells.push(
          <CheckboxCellItem
            key={'checkbox'}
            checked={selectedRows.includes(rowId)}
            onChange={onCheckChange}
            size={density === 'compact' ? 'small' : 'medium'}
            disabled={!isSelectable}
            colIndex={colIndex}
          />,
        );
      } else if (rowSelectionMode === 'radioSelection') {
        const isSelectable = isRowSelectable ? isRowSelectable(row) : true;
        displayCells.push(
          <RadioCellItem
            key={'radio'}
            checked={selectedRows.includes(rowId)}
            onChange={onCheckChange}
            size={density === 'compact' ? 'small' : 'medium'}
            disabled={!isSelectable}
            colIndex={colIndex}
          />,
        );
      }
    }
    columns.forEach((column, index) => {
      let leftOffsetProp: number | undefined;
      let rightOffsetProp: number | undefined;
      let showPinBorder = false;

      if (column.fixed === 'left') {
        const colIndex = leftFixedColumns.indexOf(column);
        leftOffsetProp = leftFixedColumns
          .slice(0, colIndex)
          .reduce((sum, c) => sum + (computedColumnWidths[columns.indexOf(c)] || 0), 0);
        showPinBorder = column.field === leftFixedColumns[leftFixedColumns.length - 1]?.field;
      }

      if (column.fixed === 'right') {
        const reversedIndex = rightFixedColumns.indexOf(column);
        rightOffsetProp = rightFixedColumns
          .slice(0, reversedIndex)
          .reduce((sum, c) => sum + (computedColumnWidths[columns.indexOf(c)] || 0), 0);
        showPinBorder = column.field === rightFixedColumns[rightFixedColumns.length - 1]?.field;
      }
      displayCells.push(
        <RowCellItem
          key={`${rowId}-${column.field}`}
          rowId={rowId}
          column={column as ColumnType<T>}
          row={row}
          columnWidth={computedColumnWidths[index] || 100}
          colIndex={colIndex + index + 1}
          leftOffset={leftOffsetProp}
          rightOffset={rightOffsetProp}
          showPinBorder={showPinBorder}
        />,
      );
    });

    displayCells.push(<EmptyCellItem key={'empty'} />);
    return displayCells;
  }, [
    columns,
    computedColumnWidths,
    density,
    expandedRowPanelRender,
    expandedRows,
    isRowExpandable,
    isRowSelectable,
    onCheckChange,
    onExpandedRowsChange,
    row,
    rowId,
    selectedRows,
    rowSelectionMode,
  ]);

  const handleFinishEditing = useCallback(() => {
    onFinishEditing({ rowId, originalRow: row, saveChanges: false, trigger: 'ClickAway' });
  }, [onFinishEditing, row, rowId]);

  return editMode === 'row' && isEditing ? (
    <ClickAwayListener onClickAway={handleFinishEditing}>
      <SlotRoot {...slotRootProps}>{rowCells}</SlotRoot>
    </ClickAwayListener>
  ) : (
    <SlotRoot {...slotRootProps}>{rowCells}</SlotRoot>
  );
};

export const DataGridRow = React.forwardRef(DataGridRowInner) as <T>(
  props: DataGridRowProps<T> & React.RefAttributes<HTMLDivElement>,
) => React.ReactElement;
