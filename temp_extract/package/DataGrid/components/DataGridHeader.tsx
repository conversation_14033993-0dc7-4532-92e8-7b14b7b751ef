import React, { useCallback, useMemo } from 'react';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import { styled } from '@pigment-css/react';
import useSlotProps from '@mui/utils/useSlotProps';
import { getDataGridUtilityClass } from '../DataGrid.classes';
import { useDataGridContext } from '../context/DataGridContext';
import { HeaderCellItem } from './cells/HeaderCellItem';
import { CheckboxCellItem } from './cells/CheckboxCellItem';
import { RadioCellItem } from './cells/RadioCellItem';
import { EmptyCellItem } from './cells/EmptyCellItem';
import { DataGridOwnerState } from '../DataGrid.types';
import { ExpandCellItem } from './cells/ExpandCellItem';

const useUtilityClasses = () => {
  const slots = {
    header: ['header'],
  };

  return composeClasses(slots, getDataGridUtilityClass, {});
};

const ColumnHeaderRoot = styled('div')<DataGridOwnerState>(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  background: theme.vars.palette.secondaryContainer,
  borderBottom: '1px solid',
  borderBottomColor: theme.vars.palette.outline,
  boxSizing: 'border-box',
  height: 'var(--nova-datagrid-header-height)',
  minHeight: 'var(--nova-datagrid-header-height)',
  position: 'sticky',
  top: 0,
  zIndex: 2,
}));

// eslint-disable-next-line react/display-name
export const DataGridHeader = React.forwardRef((props: object, ref: React.ForwardedRef<Element>) => {
  const contextProps = useDataGridContext();
  const {
    headerHeight,
    density,
    columns,
    computedColumnWidths,
    rowSelectionMode,
    total,
    selectedRows = [],
    onSelectedRowsChange,
    expandedRowPanelRender,
    allRowIds,
    gridScrollWidth,
    gridWidth,
    slots,
    slotProps,
  } = contextProps;

  const classes = useUtilityClasses();
  const headerWidth = Math.max(gridScrollWidth, gridWidth);
  const SlotRoot = slots.header ?? ColumnHeaderRoot;
  const slotRootProps = useSlotProps({
    elementType: SlotRoot,
    externalSlotProps: slotProps.header,
    externalForwardedProps: props,
    ownerState: contextProps,
    className: classes.header,
    additionalProps: {
      role: 'row',
      'aria-rowindex': 1,
      'data-grid': 'header',
      ref,
      style: {
        width: `${headerWidth}px`,
        '--nova-datagrid-header-height': `${headerHeight}px`,
      },
    },
  });

  const onCheckChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      if (selectedRows.length) {
        onSelectedRowsChange?.([]);
      } else {
        onSelectedRowsChange?.(allRowIds);
      }
    },
    [allRowIds, onSelectedRowsChange, selectedRows.length],
  );

  const headerCells = useMemo(() => {
    let colIndex = 0;
    const displayCells = [];
    const leftFixedColumns = columns.filter((c) => c.fixed === 'left');
    const rightFixedColumns = [...columns].reverse().filter((c) => c.fixed === 'right');
    const firstRightFixedColumnIndex = columns.findIndex((x) => x.fixed === 'right');
    if (expandedRowPanelRender) {
      colIndex = colIndex + 1;
      displayCells.push(
        <ExpandCellItem
          type="header"
          key={'expand'}
          showExpandIcon={false}
          size={density === 'compact' ? 'small' : 'medium'}
          colIndex={colIndex}
        />,
      );
    }
    if (rowSelectionMode) {
      colIndex = colIndex + 1;
      if (rowSelectionMode === 'checkboxSelection') {
        displayCells.unshift(
          <CheckboxCellItem
            type="header"
            key={'checkbox'}
            checked={selectedRows.length === total}
            indeterminate={selectedRows.length > 0 && selectedRows.length !== total}
            onChange={onCheckChange}
            size={density === 'compact' ? 'small' : 'medium'}
            colIndex={colIndex}
          />,
        );
      } else if (rowSelectionMode === 'radioSelection') {
        displayCells.unshift(
          <RadioCellItem
            type="header"
            key={'radio'}
            invisible
            size={density === 'compact' ? 'small' : 'medium'}
            colIndex={colIndex}
          />,
        );
      }
    }

    columns.forEach((column, index) => {
      let leftOffsetProp: number | undefined;
      let rightOffsetProp: number | undefined;

      if (column.fixed === 'left') {
        const colIndex = leftFixedColumns.indexOf(column);
        leftOffsetProp = leftFixedColumns
          .slice(0, colIndex)
          .reduce((sum, c) => sum + (computedColumnWidths[columns.indexOf(c)] || 0), 0);
      }

      if (column.fixed === 'right') {
        const reversedIndex = rightFixedColumns.indexOf(column);
        rightOffsetProp = rightFixedColumns
          .slice(0, reversedIndex)
          .reduce((sum, c) => sum + (computedColumnWidths[columns.indexOf(c)] || 0), 0);
      }
      displayCells.push(
        <HeaderCellItem
          key={index}
          column={column}
          columnWidth={computedColumnWidths[index] || 100}
          colIndex={colIndex + index + 1}
          leftOffset={leftOffsetProp}
          rightOffset={rightOffsetProp}
          resizable={!(firstRightFixedColumnIndex > -1 && firstRightFixedColumnIndex - 1 === index)}
        />,
      );
    });

    displayCells.push(<EmptyCellItem key={'empty'} />);
    return displayCells;
  }, [
    columns,
    computedColumnWidths,
    density,
    expandedRowPanelRender,
    onCheckChange,
    selectedRows.length,
    rowSelectionMode,
    total,
  ]);

  return <SlotRoot {...slotRootProps}>{headerCells}</SlotRoot>;
});
