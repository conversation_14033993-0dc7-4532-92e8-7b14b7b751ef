import React from 'react';
import { styled } from '@pigment-css/react';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import { getDataGridUtilityClass } from '../DataGrid.classes';
import { TablePagination } from '../../TablePagination';
import { useDataGridContext } from '../context/DataGridContext';
import { DataGridProps } from '../DataGrid.types';

type OwnerState = {
  hasLeftAction?: boolean;
  density?: DataGridProps['density'];
};

const useUtilityClasses = () => {
  const slots = {
    pagination: ['pagination'],
  };

  return composeClasses(slots, getDataGridUtilityClass, {});
};

const DataGridPaginationContainer = styled('div')<OwnerState>(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  minHeight: 'var(--nova-datagrid-pagination-height)',
  variants: [
    {
      props: { hasLeftAction: false, density: 'compact' },
      style: {
        padding: '0px 16px',
      },
    },
    {
      props: { hasLeftAction: false, density: 'standard' },
      style: {
        padding: '0px 24px',
      },
    },
    {
      props: { hasLeftAction: false, density: 'comfortable' },
      style: {
        padding: '0px 24px',
      },
    },
    {
      props: { hasLeftAction: true, density: 'compact' },
      style: {
        padding: '0px 8px',
      },
    },
    {
      props: { hasLeftAction: true, density: 'standard' },
      style: {
        padding: '0px 12px',
      },
    },
    {
      props: { hasLeftAction: true, density: 'comfortable' },
      style: {
        padding: '0px 12px',
      },
    },
  ],
}));

const DataGridBottomPagination = styled(TablePagination)<OwnerState>(() => ({
  flex: 1,
}));

export const DataGridPagination = () => {
  const {
    paginationHeight,
    total = 0,
    page = 0,
    onPageChange,
    rowsPerPage = 10,
    onRowsPerPageChange,
    rowSelectionMode,
    expandedRowPanelRender,
    paginationConfig,
    density,
  } = useDataGridContext();
  const classes = useUtilityClasses();
  const hasLeftAction = Boolean(rowSelectionMode) || Boolean(expandedRowPanelRender);
  return (
    <DataGridPaginationContainer
      ownerState={{ hasLeftAction, density }}
      className={classes.pagination}
      style={{
        '--nova-datagrid-pagination-height': `${paginationHeight}px`,
      }}
    >
      <DataGridBottomPagination
        page={page}
        count={total}
        size={'small'}
        onPageChange={(event: React.MouseEvent<HTMLButtonElement, MouseEvent> | null, page: number) => {
          onPageChange?.(page);
        }}
        rowsPerPage={rowsPerPage}
        onRowsPerPageChange={(e, rowsPerPage: number) => {
          onRowsPerPageChange?.(rowsPerPage);
          onPageChange?.(0);
        }}
        {...paginationConfig}
      />
    </DataGridPaginationContainer>
  );
};
