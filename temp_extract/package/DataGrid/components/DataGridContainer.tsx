import React, { useCallback } from 'react';
import { styled } from '@pigment-css/react';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import { getDataGridUtilityClass } from '../DataGrid.classes';
import { DataGridHeader } from './DataGridHeader';
import { DataGridBody } from './DataGridBody';
import { useDataGridContext } from '../context/DataGridContext';
import Scrollbar from './scrollbar/Scrollbar';
import { useDataGridRefs } from '../hooks/useDataGridRefs';

const useUtilityClasses = () => {
  const slots = {
    container: ['container'],
    scrollArea: ['scrollArea'],
  };

  return composeClasses(slots, getDataGridUtilityClass, {});
};

const DataGridContainerSlot = styled('div')(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  position: 'relative',
  height: '100%',
  overflow: 'hidden',
  ...theme.typography.bodySmall,
}));

const ScrollArea = styled('div')(() => ({
  position: 'relative',
  height: '100%',
  flexGrow: 1,
  overflow: 'scroll',
  scrollbarWidth: 'none',
  display: 'flex',
  flexDirection: 'column',
  '&::-webkit-scrollbar': {
    display: 'none',
  },

  '@media print': {
    overflow: 'hidden',
  },
  zIndex: 0,
}));

export const DataGridContainer = () => {
  const { headersRef, bodyRef, containerRef, scrollbarHorizontalRef, scrollbarVerticalRef } = useDataGridRefs();
  const contextProps = useDataGridContext();
  const {
    gridHeight,
    gridWidth,
    gridScrollWidth,
    gridScrollHeight,
    isScrollX,
    isScrollY,
    scrollbarSize,
    headerHeight,
    paginationHeight,
  } = contextProps;
  const classes = useUtilityClasses();
  const handleScrollY = useCallback(
    (offset: number) => {
      if (containerRef.current) {
        containerRef.current.scrollTo({ top: offset });
      }
    },
    [containerRef],
  );
  const handleScrollX = useCallback(
    (offset: number) => {
      if (containerRef.current) {
        containerRef.current.scrollTo({ left: offset });
      }
    },
    [containerRef],
  );
  const handleContainerScroll = useCallback(
    (e: React.SyntheticEvent) => {
      const target = e.target as HTMLElement;

      // Handle vertical scroll
      if (isScrollY) {
        const scrollY = target.scrollTop;
        if (scrollbarVerticalRef.current) {
          scrollbarVerticalRef.current.scrollTo({ top: scrollY });
        }
      }

      // Handle horizontal scroll
      if (isScrollX) {
        const scrollX = target.scrollLeft;
        if (scrollbarHorizontalRef.current) {
          scrollbarHorizontalRef.current.scrollTo({ left: scrollX });
        }
      }
    },
    [isScrollY, isScrollX, scrollbarVerticalRef, scrollbarHorizontalRef],
  );

  return (
    <DataGridContainerSlot className={classes.container} role="grid">
      <ScrollArea onScroll={handleContainerScroll} ref={containerRef} className={classes.scrollArea}>
        <DataGridHeader ref={headersRef} />
        <DataGridBody ref={bodyRef} style={{ paddingBottom: isScrollX ? scrollbarSize : 0 }} />
      </ScrollArea>
      {isScrollX && (
        <Scrollbar
          ref={scrollbarHorizontalRef}
          direction="horizontal"
          gridWidth={gridWidth}
          gridScrollWidth={gridScrollWidth}
          onScrollTo={handleScrollX}
          isScrollY={isScrollY}
          headerHeight={headerHeight}
          paginationHeight={paginationHeight}
        />
      )}
      {isScrollY && (
        <Scrollbar
          ref={scrollbarVerticalRef}
          direction="vertical"
          gridHeight={gridHeight}
          gridScrollHeight={gridScrollHeight}
          onScrollTo={handleScrollY}
          isScrollX={isScrollX}
          headerHeight={headerHeight}
          paginationHeight={paginationHeight}
        />
      )}
    </DataGridContainerSlot>
  );
};
