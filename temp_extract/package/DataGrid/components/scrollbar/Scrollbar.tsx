import React, { useCallback, useEffect } from 'react';
import { unstable_composeClasses as composeClasses, unstable_useForkRef as useForkRef } from '@mui/utils';
import { getDataGridUtilityClass } from '../../DataGrid.classes';
import { styled } from '@pigment-css/react';
import capitalize from '@mui/utils/capitalize';

type ScrollbarDirection = 'vertical' | 'horizontal';

type OwnerState = {
  direction: ScrollbarDirection;
};

interface ScrollbarProps extends React.HTMLAttributes<HTMLDivElement> {
  direction: ScrollbarDirection;
  headerHeight?: number;
  paginationHeight?: number;
  gridHeight?: number;
  gridWidth?: number;
  gridScrollHeight?: number;
  gridScrollWidth?: number;
  scrollbarSize?: number;
  isScrollX?: boolean;
  isScrollY?: boolean;
  onScrollTo?: (value: number) => void;
}

const useUtilityClasses = (ownerState: OwnerState) => {
  const { direction } = ownerState;

  const slots = {
    root: ['scrollbar', direction && `scroll${capitalize(direction)}`],
    content: ['scrollbarContent'],
  };

  return composeClasses(slots, getDataGridUtilityClass, {});
};

const ScrollbarRoot = styled('div')(() => ({
  position: 'absolute',
  display: 'inline-block',
  zIndex: 60,
  '&:hover': {
    zIndex: 70,
  },
}));

const ScrollbarInner = styled('div')(() => ({}));

const ScrollbarVertical = styled(ScrollbarRoot)(({ theme }) => ({
  width: 'var(--nova-datagrid-scrollbar-size)',
  overflowY: 'auto',
  overflowX: 'hidden',
  // Disable focus-visible style, it's a scrollbar.
  outline: 0,
  '& > div': {
    width: 'var(--nova-datagrid-scrollbar-size)',
  },
  top: 'var(--nova-datagrid-header-height)',
  right: '0px',
}));

const ScrollbarHorizontal = styled(ScrollbarRoot)(({ theme }) => ({
  width: '100%',
  height: 'var(--nova-datagrid-scrollbar-size)',
  overflowY: 'hidden',
  overflowX: 'auto',
  // Disable focus-visible style, it's a scrollbar.
  outline: 0,
  '& > div': {
    height: 'var(--nova-datagrid-scrollbar-size)',
  },
  bottom: '0px',
}));

// eslint-disable-next-line react/display-name
const Scrollbar = React.forwardRef((props: ScrollbarProps, ref: React.ForwardedRef<Element>) => {
  const {
    onScrollTo,
    direction,
    headerHeight = 48,
    paginationHeight = 48,
    gridHeight = 0,
    gridWidth = 0,
    gridScrollHeight = 0,
    gridScrollWidth = 0,
    scrollbarSize = 15,
    isScrollX,
  } = props;
  const scrollbarRef = React.useRef<HTMLDivElement>(null);
  const innerScrollRef = React.useRef<HTMLDivElement>(null);
  const ScrollbarRootSlot = direction === 'vertical' ? ScrollbarVertical : ScrollbarHorizontal;
  const ScrollbarInnerSlot = ScrollbarInner;
  const scrollbarLengthProperty = direction === 'vertical' ? 'height' : 'width';
  const scrollbarScrollProperty = direction === 'vertical' ? 'scrollTop' : 'scrollLeft';
  const scrollbarLength =
    direction === 'vertical'
      ? gridHeight - headerHeight - paginationHeight - (isScrollX ? scrollbarSize : 0)
      : gridWidth;
  const scrollbarInnerSize = direction === 'vertical' ? gridScrollHeight : gridScrollWidth;
  const classes = useUtilityClasses({ direction });
  const onScrollbarScroll = useCallback(
    (e: React.UIEvent<HTMLDivElement>) => {
      const translateValue = e.currentTarget[scrollbarScrollProperty];
      onScrollTo?.(translateValue);
    },
    [onScrollTo, scrollbarScrollProperty],
  );

  useEffect(() => {
    const scrollbar = scrollbarRef.current!;
    const scrollbarInner = innerScrollRef.current!;
    scrollbar.style.setProperty(scrollbarLengthProperty, `${scrollbarLength}px`);
    scrollbarInner.style.setProperty(scrollbarLengthProperty, `${scrollbarInnerSize}px`);
  }, [scrollbarInnerSize, scrollbarLengthProperty, scrollbarLength]);

  return (
    <ScrollbarRootSlot
      ref={useForkRef(ref, scrollbarRef)}
      tabIndex={-1}
      aria-hidden="true"
      onFocus={(event) => {
        event.target.blur();
      }}
      onScroll={onScrollbarScroll}
      className={classes.root}
      style={{
        '--nova-datagrid-scrollbar-size': `${scrollbarSize}px`,
        '--nova-datagrid-header-height': `${headerHeight}px`,
      }}
    >
      <ScrollbarInnerSlot ref={innerScrollRef} className={classes.content} />
    </ScrollbarRootSlot>
  );
});

export default Scrollbar;
