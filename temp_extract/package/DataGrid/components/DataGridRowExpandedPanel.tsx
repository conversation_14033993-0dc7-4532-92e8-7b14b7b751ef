import React from 'react';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import { styled } from '@pigment-css/react';
import { getDataGridUtilityClass } from '../DataGrid.classes';
import { DATA_GRID_DEFAULT_EXPAND_ROW_HEIGHT } from '../constants/dataGridDefaultProps';
import { useDataGridContext } from '../context/DataGridContext';

export type DataGridRowExpandedPanelProps<T> = React.HTMLAttributes<HTMLDivElement> & {
  row: T;
};

const useUtilityClasses = () => {
  const slots = {
    rowExpandedPanel: ['rowExpandedPanel'],
  };

  return composeClasses(slots, getDataGridUtilityClass, {});
};

const DataGridRowExpandedRoot = styled('div')(() => ({
  position: 'relative',
  overflowY: 'auto',
}));

export const DataGridRowExpandedInner = <T,>(
  props: DataGridRowExpandedPanelProps<T>,
  ref: React.ForwardedRef<HTMLDivElement>,
) => {
  const contextProps = useDataGridContext<T>();
  const { expandedRowPanelRender, getExpandedRowHeight } = contextProps;
  const { row } = props;
  const expandPanelHeight = getExpandedRowHeight?.(row) || DATA_GRID_DEFAULT_EXPAND_ROW_HEIGHT;
  const classes = useUtilityClasses();
  return (
    <DataGridRowExpandedRoot
      className={classes.rowExpandedPanel}
      style={{ height: `${expandPanelHeight}px` }}
      ref={ref}
      role={'presentation'}
    >
      {expandedRowPanelRender?.(row)}
    </DataGridRowExpandedRoot>
  );
};
export const DataGridRowExpandedPanel = React.forwardRef(DataGridRowExpandedInner) as <T>(
  props: DataGridRowExpandedPanelProps<T> & React.RefAttributes<HTMLDivElement>,
) => React.ReactElement;
