import React from 'react';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import { styled } from '@pigment-css/react';
import { getDataGridUtilityClass } from '../../DataGrid.classes';
import { Radio } from '../../../Radio';

export interface RadioCellItemProps {
  type?: 'header' | 'row';
  invisible?: boolean;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  checked?: boolean;
  disabled?: boolean;
  size?: 'small' | 'medium' | 'large';
  colIndex: number;
}

type OwnerState = {
  invisible?: boolean;
  size?: 'small' | 'medium' | 'large';
};

const useUtilityClasses = () => {
  const slots = {
    radio: ['cell', 'radioCell'],
  };

  return composeClasses(slots, getDataGridUtilityClass, {});
};

const RadioCellItemRoot = styled('div')<OwnerState>(() => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  flex: '0 0 auto',
  height: 'var(--nova-datagrid-row-height)',
  variants: [
    {
      props: { invisible: true },
      style: {
        visibility: 'hidden',
      },
    },
    {
      props: { size: 'small' },
      style: {
        minWidth: '32px',
      },
    },
    {
      props: { size: 'medium' },
      style: {
        minWidth: '40px',
      },
    },
    {
      props: { size: 'large' },
      style: {
        minWidth: '40px',
      },
    },
  ],
}));

export const RadioCellItem = (props: RadioCellItemProps) => {
  const { type = 'row', invisible, onChange, checked, size = 'medium', colIndex, disabled = false } = props;
  const classes = useUtilityClasses();
  return (
    <RadioCellItemRoot
      ownerState={{ invisible, size }}
      className={classes.radio}
      tabIndex={0}
      data-field={'radio-cell'}
      aria-colindex={colIndex}
      role={type === 'header' ? 'columnheader' : 'gridcell'}
    >
      <Radio onChange={onChange} checked={checked} size={size} disabled={disabled} />
    </RadioCellItemRoot>
  );
};
