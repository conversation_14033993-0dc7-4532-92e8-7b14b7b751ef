import React from 'react';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import { getDataGridUtilityClass } from '../../DataGrid.classes';
import { styled } from '@pigment-css/react';

const useUtilityClasses = () => {
  const slots = {
    cell: ['cell', 'emptyCell'],
  };

  return composeClasses(slots, getDataGridUtilityClass, {});
};
const EmptyCellItemRoot = styled('div')(() => ({
  flex: 1,
  padding: 0,
  display: 'flex',
  border: 0,
}));
export const EmptyCellItem = () => {
  const classes = useUtilityClasses();
  return <EmptyCellItemRoot className={classes.cell} />;
};
