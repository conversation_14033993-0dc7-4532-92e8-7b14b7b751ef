import React from 'react';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import { styled } from '@pigment-css/react';
import { getDataGridUtilityClass } from '../../DataGrid.classes';
import { Checkbox } from '../../../Checkbox';

export interface CheckboxCellItemProps {
  type?: 'header' | 'row';
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  checked?: boolean;
  indeterminate?: boolean;
  disabled?: boolean;
  size?: 'small' | 'medium' | 'large';
  colIndex: number;
}

type OwnerState = {
  size?: 'small' | 'medium' | 'large';
};

const useUtilityClasses = () => {
  const slots = {
    checkbox: ['cell', 'checkboxCell'],
  };

  return composeClasses(slots, getDataGridUtilityClass, {});
};

const CheckboxCellItemRoot = styled('div')<OwnerState>(() => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  flex: '0 0 auto',
  height: 'var(--nova-datagrid-row-height)',
  variants: [
    {
      props: { size: 'small' },
      style: {
        minWidth: '32px',
      },
    },
    {
      props: { size: 'medium' },
      style: {
        minWidth: '40px',
      },
    },
    {
      props: { size: 'large' },
      style: {
        minWidth: '40px',
      },
    },
  ],
}));

export const CheckboxCellItem = (props: CheckboxCellItemProps) => {
  const { type = 'row', onChange, checked, indeterminate, size = 'medium', disabled = false, colIndex } = props;
  const classes = useUtilityClasses();
  return (
    <CheckboxCellItemRoot
      className={classes.checkbox}
      ownerState={{ size }}
      tabIndex={0}
      data-field={'checkbox-cell'}
      aria-colindex={colIndex}
      role={type === 'header' ? 'columnheader' : 'gridcell'}
    >
      <Checkbox onChange={onChange} checked={checked} indeterminate={indeterminate} size={size} disabled={disabled} />
    </CheckboxCellItemRoot>
  );
};
