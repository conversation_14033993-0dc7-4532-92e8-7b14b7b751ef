import React, { useCallback, useMemo, useRef } from 'react';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import capitalize from '@mui/utils/capitalize';
import { styled } from '@pigment-css/react';
import useSlotProps from '@mui/utils/useSlotProps';
import { getDataGridUtilityClass } from '../../DataGrid.classes';
import { ColumnType } from '../../types/column.type';
import { useDataGridContext } from '../../context/DataGridContext';
import { DataGridProps } from '../../DataGrid.types';
import { RowId } from '../../types/row.type';
import { EditCellInput } from './EditCellInput';
import { ClickAwayListener } from '../../../ClickAwayListener';

export interface RowCellItemProps<T> extends React.HTMLAttributes<HTMLDivElement> {
  rowId: RowId;
  column: ColumnType<T>;
  row: T;
  columnWidth: number;
  colIndex: number;
  leftOffset?: number;
  rightOffset?: number;
  showPinBorder?: boolean;
}

type OwnerState = {
  density: DataGridProps['density'];
  selected: boolean;
  cellSelection?: boolean;
  isEditing: boolean;
  fixed?: 'left' | 'right';
  showPinBorder?: boolean;
};

const useUtilityClasses = (ownerState: OwnerState) => {
  const { selected, isEditing, fixed } = ownerState;
  const slots = {
    cell: [
      'cell',
      'rowCell',
      selected && 'cellSelected',
      isEditing && 'cellEditing',
      fixed && 'cellFixed',
      fixed && `cellFixed${capitalize(fixed)}`,
    ],
  };

  return composeClasses(slots, getDataGridUtilityClass, {});
};

const RowCellItemRoot = styled('div')<OwnerState>(({ theme }) => ({
  flex: '0 0 auto',
  padding: '0px 24px',
  boxSizing: 'border-box',
  overflow: 'hidden',
  whiteSpace: 'nowrap',
  textOverflow: 'ellipsis',
  color: theme.vars.palette.onSurface,
  minWidth: '50px',
  height: 'var(--nova-datagrid-row-height)',
  lineHeight: 'var(--nova-datagrid-row-height)',
  '&:focus-visible': {
    outline: `2px solid ${theme.vars.palette.secondary}`,
    outlineOffset: -2,
  },
  variants: [
    {
      props: { density: 'compact' },
      style: {
        padding: '0px 16px',
      },
    },
    {
      props: { cellSelection: true, selected: false },
      style: {
        '&:focus': {
          outline: `2px solid ${theme.vars.palette.secondary}`,
          outlineOffset: -2,
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.surfaceContainer}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.focusOnSurface})`,
        },
        '&:active': {
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.surfaceContainer}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.pressOnSurface})`,
        },
      },
    },
    {
      props: { cellSelection: true, selected: true },
      style: {
        background: theme.vars.palette.secondaryContainer,
        '&:focus': {
          outline: `2px solid ${theme.vars.palette.secondary}`,
          outlineOffset: -2,
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.secondaryContainer}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.focusOnSurface})`,
        },
        '&:active': {
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.secondaryContainer}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.pressOnSurface})`,
        },
      },
    },
    {
      props: { fixed: 'left' },
      style: {
        position: 'sticky',
        zIndex: 3,
        left: 'var(--nova-datagrid-fixed-leftOffset)',
        boxSizing: 'border-box',
        backgroundColor: theme.vars.palette.surfaceContainer,
        height: 'calc(var(--nova-datagrid-row-height) - 2px)',
      },
    },
    {
      props: { fixed: 'left', showPinBorder: true },
      style: {
        borderRight: `1px solid ${theme.vars.palette.outlineVariant}`,
      },
    },
    {
      props: { fixed: 'right' },
      style: {
        position: 'sticky',
        zIndex: 3,
        right: 'var(--nova-datagrid-fixed-rightOffset)',
        boxSizing: 'border-box',
        backgroundColor: theme.vars.palette.surfaceContainer,
        height: 'calc(var(--nova-datagrid-row-height) - 2px)',
      },
    },
    {
      props: { fixed: 'right', showPinBorder: true },
      style: {
        borderLeft: `1px solid ${theme.vars.palette.outlineVariant}`,
      },
    },
  ],
}));

export const RowCellItem = <T,>(props: RowCellItemProps<T>) => {
  const {
    cellSelection = false,
    selectedCells = [],
    density,
    cellEvents,
    editMode,
    editingCells = [],
    editingRows = [],
    isCellEditable,
    editingCell,
    editingValueCache,
    updateEditingValue,
    onFinishEditing,
    slots,
    slotProps,
  } = useDataGridContext<T>();

  const { rowId, column, row, columnWidth, colIndex, leftOffset, rightOffset, showPinBorder, ...rest } = props;
  const cellRef = useRef<HTMLElement>();
  const SlotRoot = slots.rowCell ?? RowCellItemRoot;
  const selected =
    cellSelection && selectedCells.findIndex((cell) => cell.rowId === rowId && cell.columnField === column.field) > -1;
  const cellClassName = typeof column.className === 'function' ? column.className(row) : (column.className ?? '');
  const cellStyle = typeof column.style === 'function' ? column.style(row) : (column.style ?? {});
  const fixed = column.fixed;
  const handlers = useMemo(() => {
    const handlers: Record<string, React.EventHandler<any>> = {};
    if (cellEvents) {
      Object.entries(cellEvents).forEach(([eventName, handler]) => {
        handlers[eventName] = (e: any) => {
          handler?.({ rowId, columnField: column.field, rowData: row, column }, e);
        };
      });
    }
    return handlers;
  }, [cellEvents, column, row, rowId]);

  const handleFinishEditing = useCallback(() => {
    onFinishEditing({ rowId, columnField: column.field, originalRow: row, saveChanges: false, trigger: 'ClickAway' });
  }, [column, onFinishEditing, row, rowId]);

  const isEditing = useMemo(() => {
    if (editMode === 'row') {
      const editing = editingRows.includes(rowId) && (column.editable ?? false);
      if (isCellEditable) {
        return editing && isCellEditable({ rowId, columnField: column.field });
      }
      return editing;
    }
    return editingCells.some((c) => c.rowId === rowId && c.columnField === column.field);
  }, [editMode, editingCells, editingRows, rowId, column.editable, column.field, isCellEditable]);

  const renderEditComponent = useCallback(() => {
    const currentValue = editingValueCache[rowId]?.[column.field] ?? row[column.field as keyof T];

    if (column.renderEditCell) {
      return column.renderEditCell({
        value: currentValue,
        onValueChange: (value) => {
          updateEditingValue(rowId, column.field, value);
          if (cellRef.current) {
            cellRef.current.focus();
          }
        },
        originalRow: row,
        autoFocus: editingCell.rowId === rowId && editingCell.columnField === column.field,
        cellRef,
        columnWidth,
        onFinishEditing,
      });
    }

    return (
      <EditCellInput
        value={currentValue}
        onValueChange={async (e, value) => {
          updateEditingValue(rowId, column.field, value);
        }}
        autoFocus={editingCell.rowId === rowId && editingCell.columnField === column.field}
        type={typeof currentValue === 'number' ? 'number' : 'string'}
        size={density === 'compact' ? 'small' : 'medium'}
        style={{ width: '100%' }}
      />
    );
  }, [
    editingValueCache,
    rowId,
    column,
    row,
    onFinishEditing,
    editingCell.rowId,
    editingCell.columnField,
    density,
    columnWidth,
    updateEditingValue,
  ]);

  const ownerState = { density, cellSelection, selected, isEditing, fixed, showPinBorder };
  const classes = useUtilityClasses(ownerState);
  const slotRootProps = useSlotProps({
    elementType: SlotRoot,
    externalSlotProps: slotProps.rowCell,
    ownerState,
    className: [classes.cell, cellClassName],
    additionalProps: {
      tabIndex: -1,
      'data-field': column.field || undefined,
      'aria-colindex': colIndex,
      role: 'gridcell',
      ref: cellRef,
      style: {
        width: `${columnWidth}px`,
        minWidth: column.minWidth ? `${column.minWidth}px` : undefined,
        maxWidth: column.maxWidth ? `${column.maxWidth}px` : undefined,
        textAlign: column.align || undefined,
        ...(fixed && { '--nova-datagrid-fixed-leftOffset': `${leftOffset || 0}px` }),
        ...(fixed && { '--nova-datagrid-fixed-rightOffset': `${rightOffset || 0}px` }),
        ...cellStyle,
      },
      ...handlers,
      ...rest,
    },
  });
  let cell;
  const cachedCellValue = editingValueCache[rowId]?.[column.field];
  if (column.cell) {
    cell = column.cell(cachedCellValue === undefined ? row : { ...row, [column.field]: cachedCellValue });
  } else {
    cell = cachedCellValue ?? row[column.field as keyof T];
  }
  return editMode === 'cell' && isEditing ? (
    <ClickAwayListener onClickAway={handleFinishEditing}>
      <SlotRoot {...slotRootProps}>{renderEditComponent()}</SlotRoot>
    </ClickAwayListener>
  ) : (
    <SlotRoot {...slotRootProps}>{isEditing ? renderEditComponent() : cell}</SlotRoot>
  );
};
