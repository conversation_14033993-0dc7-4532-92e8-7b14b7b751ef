import composeClasses from '@mui/utils/composeClasses';
import capitalize from '@mui/utils/capitalize';
import { styled } from '@pigment-css/react';
import { getDataGridUtilityClass } from '../../DataGrid.classes';
import { SortDirection, SortItem } from '../../types/sort.type';
import ArrowDownIcon from '../../icons/ArrowDown';
import ArrowUpIcon from '../../icons/ArrowUp';
import { IconButton } from '../../../IconButton';

export interface HeaderCellSortIconProps {
  field: string;
  sort: SortDirection;
  onSortChange?: (sort: SortItem[]) => void;
}

type OwnerState = {
  hasSort: boolean;
};

const useUtilityClasses = ({ sort }: { sort: SortDirection }) => {
  const slots = {
    sortIcon: ['sortIcon', sort && `sort${capitalize(sort)}`],
  };

  return composeClasses(slots, getDataGridUtilityClass, {});
};

const IconButtonRoot = styled(IconButton)<OwnerState>(({ theme }) => ({
  marginLeft: '2px',
  visibility: 'visible',
  overflow: 'visible',
  variants: [
    {
      props: { hasSort: false },
      style: {
        visibility: 'var(--nova-datagrid-unSortCell-visibility)' as 'visible' | 'hidden',
        '& svg': {
          color: theme.vars.palette.onBackgroundDisabled,
        },
      },
    },
  ],
}));

export const HeaderCellSortIcon = (props: HeaderCellSortIconProps) => {
  const { sort, field, onSortChange } = props;
  const classes = useUtilityClasses({ sort });
  let hasSort = true;
  let element;
  if (sort === 'asc') {
    element = <ArrowUpIcon />;
  } else if (sort === 'desc') {
    element = <ArrowDownIcon />;
  } else {
    element = <ArrowUpIcon />;
    hasSort = false;
  }
  const nextSortDirection = !hasSort ? 'asc' : sort === 'asc' ? 'desc' : null;
  return (
    <IconButtonRoot
      size={'small'}
      variant={'neutral'}
      ownerState={{ hasSort }}
      className={classes.sortIcon}
      onClick={() => {
        onSortChange?.(nextSortDirection ? [{ sort: nextSortDirection, field }] : []);
      }}
    >
      {element}
    </IconButtonRoot>
  );
};
