import React, { useState, useCallback, useRef } from 'react';
import { Input, InputProps } from '../../../Input';
import { styled } from '@pigment-css/react';

export interface EditCellInputProps extends Omit<InputProps, 'value' | 'onChange'> {
  value: string | number;
  onValueChange: (event: React.ChangeEvent<HTMLInputElement>, value: string | number) => Promise<void>;
}

const InputRoot = styled('div')(() => ({
  display: 'flex',
  alignItems: 'center',
  height: '100%',
}));

export const EditCellInput = (props: EditCellInputProps) => {
  const { value, onValueChange, ...others } = props;
  const [valueState, setValueState] = useState(value);
  const handleChange = useCallback(
    async (event: React.ChangeEvent<HTMLInputElement>) => {
      const newValue = event.target.value;

      if (onValueChange) {
        await onValueChange(event, newValue);
      }

      setValueState(newValue);
    },
    [onValueChange],
  );

  return (
    <InputRoot>
      <Input {...others} value={valueState ?? ''} onChange={handleChange} />
    </InputRoot>
  );
};
