import React, { useState } from 'react';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import { styled } from '@pigment-css/react';
import useSlotProps from '@mui/utils/useSlotProps';
import capitalize from '@mui/utils/capitalize';
import { getDataGridUtilityClass } from '../../DataGrid.classes';
import { ColumnType } from '../../types/column.type';
import { HeaderCellSortIcon } from './HeaderCellSortIcon';
import { useDataGridContext } from '../../context/DataGridContext';
import { DataGridProps } from '../../DataGrid.types';
import { HeaderCellResizeHandle } from './HeaderCellResizeHandle';

export interface HeaderCellItemProps {
  column: ColumnType;
  columnWidth: number;
  colIndex: number;
  leftOffset?: number;
  rightOffset?: number;
  resizable?: boolean;
}

type OwnerState = {
  density: DataGridProps['density'];
  fixed?: 'left' | 'right';
};

const useUtilityClasses = (ownerState: OwnerState) => {
  const { fixed } = ownerState;
  const slots = {
    cell: ['cell', 'headerCell', fixed && 'cellFixed', fixed && `cellFixed${capitalize(fixed)}`],
    cellText: ['headerCellText'],
  };

  return composeClasses(slots, getDataGridUtilityClass, {});
};

const HeaderCellRoot = styled('div')<OwnerState>(({ theme }) => ({
  position: 'relative',
  display: 'flex',
  alignItems: 'center',
  flex: '0 0 auto',
  padding: '0px 24px',
  color: theme.vars.palette.onSurface,
  fontWeight: 700,
  boxSizing: 'border-box',
  overflow: 'hidden',
  minWidth: '50px',
  height: 'var(--nova-datagrid-header-height)',
  lineHeight: 'var(--nova-datagrid-header-height)',
  maxHeight: 'max-content',
  '--nova-datagrid-unSortCell-visibility': 'hidden',
  '&:hover': {
    '--nova-datagrid-unSortCell-visibility': 'visible',
  },
  variants: [
    {
      props: { density: 'compact' },
      style: {
        padding: '0px 16px',
      },
    },
    {
      props: { fixed: 'left' },
      style: {
        position: 'sticky',
        left: 'var(--nova-datagrid-fixed-leftOffset)',
        background: theme.vars.palette.secondaryContainer,
        zIndex: 3,
        height: 'calc(var(--nova-datagrid-header-height) - 2px)',
        minHeight: 'calc(var(--nova-datagrid-header-height) - 2px)',
      },
    },
    {
      props: { fixed: 'right' },
      style: {
        position: 'sticky',
        right: 'var(--nova-datagrid-fixed-rightOffset)',
        background: theme.vars.palette.secondaryContainer,
        zIndex: 3,
        height: 'calc(var(--nova-datagrid-header-height) - 2px)',
        minHeight: 'calc(var(--nova-datagrid-header-height) - 2px)',
      },
    },
  ],
}));

const HeaderCellText = styled('div')(() => ({
  overflow: 'hidden',
  whiteSpace: 'nowrap',
  textOverflow: 'ellipsis',
}));

export const HeaderCellItem = (props: HeaderCellItemProps) => {
  const contextProps = useDataGridContext();
  const { density, slots, slotProps, sortedColumns = [], onSortedColumnsChange, onColumnResize } = contextProps;
  const { column, columnWidth, colIndex, leftOffset, rightOffset, resizable: resizableProp = true } = props;
  const [hovering, setHovering] = useState(false);
  const sortable = column.sortable ?? true;
  const resizable = resizableProp && (column.resizable ?? true);
  const fixed = column.fixed;
  const ownerState = { density, fixed };
  const classes = useUtilityClasses(ownerState);
  const SlotRoot = slots.headerCell ?? HeaderCellRoot;
  const slotRootProps = useSlotProps({
    elementType: SlotRoot,
    externalSlotProps: slotProps.headerCell,
    ownerState,
    className: [classes.cell, column.headerClassName],
    additionalProps: {
      tabIndex: -1,
      'data-field': column.field || undefined,
      'aria-colindex': colIndex,
      role: 'columnheader',
      onMouseEnter: () => setHovering(true),
      onMouseLeave: () => setHovering(false),
      style: {
        width: `${columnWidth}px`,
        minWidth: column.minWidth ? `${column.minWidth}px` : undefined,
        maxWidth: column.maxWidth ? `${column.maxWidth}px` : undefined,
        textAlign: column.headerAlign || undefined,
        ...(fixed && { '--nova-datagrid-fixed-leftOffset': `${leftOffset || 0}px` }),
        ...(fixed && { '--nova-datagrid-fixed-rightOffset': `${rightOffset || 0}px` }),
        ...column.headerStyle,
      },
    },
  });
  let element: React.ReactNode;
  if (column.header && typeof column.header === 'function') {
    element = column.header(column);
  } else {
    element = (column.header || '') as React.ReactNode;
  }
  const columnSorted = sortedColumns.find((i) => i.field === column.field)?.sort;
  const rightFixed = column.fixed === 'right';
  return (
    <SlotRoot {...slotRootProps}>
      {rightFixed && resizable && (
        <HeaderCellResizeHandle
          column={column}
          onColumnResize={onColumnResize}
          columnWidth={columnWidth}
          fixed="left"
        />
      )}
      <HeaderCellText className={classes.cellText}>{element}</HeaderCellText>
      {sortable && (columnSorted || hovering) && (
        <HeaderCellSortIcon field={column.field} sort={columnSorted} onSortChange={onSortedColumnsChange} />
      )}
      {!rightFixed && resizable && (
        <HeaderCellResizeHandle
          column={column}
          onColumnResize={onColumnResize}
          columnWidth={columnWidth}
          fixed="right"
        />
      )}
    </SlotRoot>
  );
};
