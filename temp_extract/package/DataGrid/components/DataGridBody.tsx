import React, { ReactNode, useMemo } from 'react';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import { styled } from '@pigment-css/react';
import useSlotProps from '@mui/utils/useSlotProps';
import { getDataGridUtilityClass } from '../DataGrid.classes';
import { useDataGridContext } from '../context/DataGridContext';
import { DataGridOwnerState } from '../DataGrid.types';
import { DataGridRow } from './DataGridRow';
import { DataGridRowExpandedPanel } from './DataGridRowExpandedPanel';
import { RowId } from '../types/row.type';

export type DataGridBodyProps = React.HTMLAttributes<HTMLDivElement>;

const useUtilityClasses = () => {
  const slots = {
    body: ['body'],
  };

  return composeClasses(slots, getDataGridUtilityClass, {});
};

const DataGridBodyRoot = styled('div')<DataGridOwnerState>(() => ({
  display: 'flex',
  flexDirection: 'column',
}));

// eslint-disable-next-line react/display-name
export const DataGridBody = React.forwardRef<HTMLDivElement, DataGridBodyProps>((props, ref) => {
  const contextProps = useDataGridContext();
  const { data, slots, slotProps, gridScrollWidth, gridWidth, uniqueField, expandedRows, expandedRowPanelRender } =
    contextProps;
  const classes = useUtilityClasses();
  const SlotDataGridRow = slots.body ?? DataGridBodyRoot;
  const rowWidth = Math.max(gridScrollWidth, gridWidth);
  const slotColumnHeaderProps = useSlotProps({
    elementType: SlotDataGridRow,
    externalSlotProps: slotProps.body,
    ownerState: contextProps,
    className: classes.body,
    externalForwardedProps: props,
    additionalProps: {
      ref,
      role: 'presentation',
      'data-grid': 'body',
      style: {
        width: `${rowWidth}px`,
      },
    },
  });

  const rows = useMemo(() => {
    const gridRows: ReactNode[] = [];
    data.forEach((row: any, index: number) => {
      const rowId = row[uniqueField] as RowId;
      gridRows.push(<DataGridRow key={rowId} row={row} rowIndex={index + 1} />);
      if (expandedRows?.length && expandedRowPanelRender && expandedRows.includes(rowId)) {
        gridRows.push(<DataGridRowExpandedPanel key={`${rowId}-expandRow`} row={row} />);
      }
    });
    return gridRows;
  }, [data, expandedRowPanelRender, expandedRows, uniqueField]);
  return <SlotDataGridRow {...slotColumnHeaderProps}>{rows}</SlotDataGridRow>;
});
