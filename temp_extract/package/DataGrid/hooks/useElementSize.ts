import { useRef, useLayoutEffect, useState, RefObject, useMemo, useCallback } from 'react';

type ResizeCallback = (entry: ResizeObserverEntry[]) => void;

interface SizeChangeSubscribe {
  (el: HTMLElement, resizeCallback: ResizeCallback): () => void;
  observerSingleton?: ResizeObserver;
  callbacks: Map<HTMLElement, ResizeCallback>;
}

const subscribe: SizeChangeSubscribe = Object.assign(
  (el: HTMLElement, resizeCallback: ResizeCallback) => {
    subscribe.callbacks.set(el, resizeCallback);
    if (!subscribe.observerSingleton) {
      subscribe.observerSingleton = new ResizeObserver((entries) => {
        subscribe.callbacks.forEach((cb, targetEl) => {
          if (entries.some(({ target }) => target === targetEl)) {
            cb(entries.filter(({ target }) => target === targetEl));
          }
        });
      });
    }
    subscribe.observerSingleton.observe(el);
    return () => {
      subscribe.observerSingleton?.unobserve(el);
      subscribe.callbacks.delete(el);
    };
  },
  {
    callbacks: new Map(),
    observerSingleton: undefined,
  },
);

export const useElementSize = <T extends HTMLElement>(ref?: RefObject<T>) => {
  const elRef = useRef<T>(null);
  const [{ height, width }, setSize] = useState<{
    height?: number;
    width?: number;
  }>(() => ({
    width: undefined,
    height: undefined,
  }));

  const resizeCallback = useCallback((entries: any) => {
    entries.forEach(({ contentRect: { height, width } }: ResizeObserverEntry) => {
      setSize({ height, width });
    });
  }, []);

  useLayoutEffect(() => {
    const element = elRef.current || ref?.current;
    if (element) {
      const unsubscribe = subscribe(element, resizeCallback);
      return () => unsubscribe();
    }
  }, [ref, resizeCallback]);

  return useMemo(() => ({ height, width, ref: elRef }), [height, width]);
};
