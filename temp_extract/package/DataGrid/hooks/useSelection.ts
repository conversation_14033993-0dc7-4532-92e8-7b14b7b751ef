import { useState, useCallback, useEffect } from 'react';
import { RowId } from '../types/row.type';
import { CellType } from '../types/cell.type';
import { DataGridProps } from '../DataGrid.types';

const EmptyArray: any[] = [];
export const useSelection = <T>(
  props: Pick<
    DataGridProps<T>,
    | 'initialState'
    | 'rowSelectionMode'
    | 'selectedRows'
    | 'onSelectedRowsChange'
    | 'selectedCells'
    | 'onSelectedCellsChange'
  >,
) => {
  const {
    initialState,
    rowSelectionMode,
    selectedRows: selectedRowsProp,
    onSelectedRowsChange: onSelectedRowsChangeProp,
    selectedCells: selectedCellsProp,
    onSelectedCellsChange: onSelectedCellsChangeProp,
  } = props;
  const [selectedRows, setSelectedRows] = useState(selectedRowsProp ?? initialState?.selectedRows ?? EmptyArray);
  const [selectedCells, setSelectedCells] = useState(selectedCellsProp ?? initialState?.selectedCells ?? EmptyArray);
  useEffect(() => {
    if (rowSelectionMode === 'radioSelection' && selectedRows.length > 1) {
      setSelectedRows([]);
    }
  }, [rowSelectionMode, selectedRows]);

  const onSelectedRowsChange = useCallback(
    (selectedRows: Array<RowId>) => {
      setSelectedRows(selectedRows);
      onSelectedRowsChangeProp?.(selectedRows);
    },
    [onSelectedRowsChangeProp],
  );

  const onSelectedCellsChange = useCallback(
    (selectedCells: Array<CellType>) => {
      setSelectedCells(selectedCells);
      onSelectedCellsChangeProp?.(selectedCells);
    },
    [onSelectedCellsChangeProp],
  );

  return {
    selectedRows: selectedRowsProp ?? selectedRows,
    selectedCells: selectedCellsProp ?? selectedCells,
    onSelectedRowsChange,
    onSelectedCellsChange,
  };
};
