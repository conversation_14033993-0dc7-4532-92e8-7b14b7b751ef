import React, { useState, useMemo, useRef, useCallback } from 'react';
import {
  calculateWidths,
  getGridRowsHeight,
  getGridHeaderHeight,
  getGridHeight,
  getGridPaginationHeight,
  getGridColumnsWidth,
} from '../utils';
import { ColumnType } from '../types/column.type';
import { DATA_GRID_DEFAULT_EXPAND_ROW_HEIGHT } from '../constants/dataGridDefaultProps';
import { DataGridProps } from '../DataGrid.types';

export const useColumnWidthCalculator = <T>(
  props: Pick<
    DataGridProps<T>,
    'density' | 'expandedRows' | 'uniqueField' | 'getExpandedRowHeight' | 'rowSelectionMode'
  > & {
    gridWidth: number;
    gridHeight: number;
    scrollbarSize: number;
    headerHeight: number;
    rowHeight: number;
    displayData: readonly T[];
    visibleColumns: ColumnType<T>[];
    isEnabledRowExpansion: boolean;
    gridRef: React.RefObject<HTMLDivElement>;
  },
) => {
  const {
    density = 'standard',
    expandedRows = [],
    uniqueField = 'id',
    displayData,
    visibleColumns,
    gridWidth,
    gridHeight,
    scrollbarSize,
    headerHeight: headerHeightProp,
    rowHeight: rowHeightProp,
    getExpandedRowHeight,
    rowSelectionMode,
    isEnabledRowExpansion,
    gridRef,
  } = props;
  const [resizedColumnWidth, setResizedColumnWidth] = useState<Record<string, number>>();
  /** Ref */
  const gridCache = useRef<{
    lastGridWidth: number;
    lastScrollingWidth: number;
    lastColumnsHash: string;
    cachedWidths: number[];
  }>({ lastGridWidth: 0, lastScrollingWidth: 0, lastColumnsHash: '', cachedWidths: [] });

  const { headerHeight, rowHeight, paginationHeight } = useMemo(() => {
    const headerHeight = getGridHeaderHeight(headerHeightProp, density);
    const rowHeight = getGridHeaderHeight(rowHeightProp, density);
    const paginationHeight = getGridPaginationHeight(undefined, density);
    return {
      headerHeight,
      rowHeight,
      paginationHeight,
    };
  }, [density, headerHeightProp, rowHeightProp]);

  const expandedPanelHeight = useMemo(() => {
    let finalHeight = 0;
    const expandedData = expandedRows
      .map((rowId) => displayData.find((i: any) => i[uniqueField] === rowId))
      .filter((i) => Boolean(i));
    if (getExpandedRowHeight) {
      finalHeight = expandedData.reduce((acc, item) => acc + getExpandedRowHeight(item as T), 0);
    } else {
      return expandedData.length * DATA_GRID_DEFAULT_EXPAND_ROW_HEIGHT;
    }
    return finalHeight;
  }, [displayData, expandedRows, getExpandedRowHeight, uniqueField]);

  const { computedColumnWidths, gridScrollHeight, gridScrollWidth, isScrollX, isScrollY } = useMemo(() => {
    if (gridWidth > 0) {
      const resizedWidth = resizedColumnWidth || {};
      const columnsHash = JSON.stringify(
        visibleColumns.map((c) => [c.width, c.flex, c.minWidth, c.maxWidth, resizedWidth[c.field]]),
      );
      let computedColumnWidths = [];
      let gridScrollWidth = 0;
      if (gridCache.current.lastScrollingWidth === gridWidth && gridCache.current.lastColumnsHash === columnsHash) {
        computedColumnWidths = gridCache.current.cachedWidths;
        gridScrollWidth = gridCache.current.lastScrollingWidth;
      } else {
        computedColumnWidths = calculateWidths(
          visibleColumns,
          gridWidth,
          rowSelectionMode,
          isEnabledRowExpansion,
          density,
          gridCache.current?.cachedWidths,
          resizedColumnWidth,
        );
        gridScrollWidth = getGridColumnsWidth(computedColumnWidths, rowSelectionMode, isEnabledRowExpansion, density);
        gridCache.current = {
          lastGridWidth: gridWidth,
          lastScrollingWidth: gridScrollWidth,
          lastColumnsHash: columnsHash,
          cachedWidths: computedColumnWidths,
        };
      }
      const gridFullHeight = getGridHeight(
        displayData.length,
        headerHeight,
        rowHeight,
        paginationHeight,
        expandedPanelHeight,
        density,
      );
      const gridScrollHeight = getGridRowsHeight(displayData.length, rowHeight, expandedPanelHeight, density);
      const isScrollX = gridScrollWidth - gridWidth > scrollbarSize;
      const isScrollY = gridFullHeight - gridHeight > scrollbarSize;
      return {
        computedColumnWidths,
        gridScrollHeight,
        gridScrollWidth,
        isScrollX,
        isScrollY,
      };
    } else {
      return {
        computedColumnWidths: [],
        gridScrollHeight: 0,
        gridScrollWidth: 0,
        isScrollX: false,
        isScrollY: false,
      };
    }
  }, [
    gridWidth,
    resizedColumnWidth,
    visibleColumns,
    displayData.length,
    headerHeight,
    rowHeight,
    paginationHeight,
    density,
    expandedPanelHeight,
    scrollbarSize,
    gridHeight,
    rowSelectionMode,
    isEnabledRowExpansion,
  ]);

  const onColumnResize = useCallback(
    (column: ColumnType, columnWidth: number, newWidth: number, stopped?: boolean) => {
      if (gridRef.current && column.field) {
        const allCells = gridRef.current.querySelectorAll<HTMLElement>(`[data-field=${column.field}]`);
        const header = gridRef.current.querySelector<HTMLElement>(`[data-grid=header]`);
        const body = gridRef.current.querySelector<HTMLElement>(`[data-grid=body]`);
        allCells?.forEach((element) => {
          element.style.width = `${newWidth}px`;
        });
        const newScrollWidth = Math.max(gridScrollWidth + (newWidth - columnWidth), gridScrollWidth, gridWidth);
        if (header) {
          header.style.width = `${newScrollWidth}px`;
        }
        if (body) {
          body.style.width = `${newScrollWidth}px`;
        }
        if (stopped) {
          const newResizedColumnWidth = { ...resizedColumnWidth };
          newResizedColumnWidth[column.field] = newWidth;
          setResizedColumnWidth(newResizedColumnWidth);
        }
      }
    },
    [gridRef, gridScrollWidth, gridWidth, resizedColumnWidth],
  );

  return {
    computedColumnWidths,
    gridScrollHeight,
    gridScrollWidth,
    isScrollX,
    isScrollY,
    onColumnResize,
    headerHeight,
    rowHeight,
    paginationHeight,
  };
};
