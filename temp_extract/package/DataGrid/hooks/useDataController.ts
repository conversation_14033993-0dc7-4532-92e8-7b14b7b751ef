import React, { useMemo } from 'react';
import { DataGridContextValue, DataGridPropsWithDefaultValue } from '../context/DataGridContext';
import { RowId } from '../types/row.type';
import { useSelection } from './useSelection';
import { useSorting } from './useSorting';
import { useGridEvents } from './useGridEvents';
import { usePagination } from './usePagination';
import { useExpandedRows } from './useExpandedRows';
import { useColumnWidthCalculator } from './useColumnWidthCalculator';
import { useEditState } from './useEditState';

export type DataControllerProps<T> = DataGridPropsWithDefaultValue<T> & {
  gridHeight: number;
  gridWidth: number;
  gridRef: React.RefObject<HTMLDivElement>;
};

export const useDataController = <T>(props: DataControllerProps<T>): DataGridContextValue<T> => {
  const {
    columns: columnsProp,
    data: dataProp,
    uniqueField,
    density,
    initialState: initialStateProp,

    /**pagination */
    total: totalProp,
    pagination,
    paginationMode: paginationModeProp,
    page: pageProp,
    rowsPerPage: rowsPerPageProp,
    onPageChange: onPageChangeProp,
    onRowsPerPageChange: onRowsPerPageChangeProp,

    /** Sort */
    sortMode: sortModeProp,
    sortedColumns: sortedColumnsProp,
    onSortedColumnsChange: onSortedColumnsChangeProp,

    /** Selection */
    rowSelectionMode,
    disableRowSelectionOnClick,
    isRowSelectable,
    selectedRows: selectedRowsProp,
    onSelectedRowsChange: onSelectedRowsChangeProp,
    cellSelection,
    selectedCells: selectedCellsProp,
    onSelectedCellsChange: onSelectedCellsChangeProp,

    /** Expand */
    expandedRows: expandedRowsProp,
    onExpandedRowsChange: onExpandedRowsChangeProp,
    getExpandedRowHeight,
    expandedRowPanelRender,

    /** Edit */
    editMode,
    editingRows: editingRowsProp,
    isRowEditable,
    onRowEditStart,
    onRowEditStop,
    onEditingRowsChange: onEditingRowsChangeProp,
    editingCells: editingCellsProp,
    onEditingCellsChange: onEditingCellsChangeProp,
    isCellEditable,
    onCellEditStart,
    onCellEditStop,
    processRowUpdate: processRowUpdateProp,
    onProcessRowUpdateError: onProcessRowUpdateErrorProp,

    /** Events */
    cellEvents: cellEventsProp,
    rowEvents: rowEventsProp,

    /** Others */
    gridWidth,
    gridHeight,
    scrollbarSize,
    headerHeight: headerHeightProp,
    rowHeight: rowHeightProp,
    gridRef,
    ...rest
  } = props;

  /** Memo state */
  const allRowIds = useMemo(() => dataProp.map((i: any) => i[uniqueField] as RowId), [dataProp, uniqueField]);
  const visibleColumns = useMemo(() => columnsProp.filter((i) => i.visible ?? true), [columnsProp]);
  const isEnabledRowExpansion = Boolean(expandedRowPanelRender);

  /**
   * Row selection state
   */
  const { selectedRows, selectedCells, onSelectedRowsChange, onSelectedCellsChange } = useSelection<T>({
    initialState: initialStateProp,
    rowSelectionMode,
    selectedRows: selectedRowsProp,
    onSelectedRowsChange: onSelectedRowsChangeProp,
    selectedCells: selectedCellsProp,
    onSelectedCellsChange: onSelectedCellsChangeProp,
  });

  /**
   * Column sorting state
   */
  const { sortedColumns, onSortedColumnsChange, sortedData } = useSorting<T>({
    initialState: initialStateProp,
    sortedColumns: sortedColumnsProp,
    onSortedColumnsChange: onSortedColumnsChangeProp,
    sortMode: sortModeProp,
    visibleColumns,
    data: dataProp,
  });

  /**
   * Pagination state
   */
  const { page, rowsPerPage, onPageChange, onRowsPerPageChange, displayData, total } = usePagination<T>({
    initialState: initialStateProp,
    page: pageProp,
    rowsPerPage: rowsPerPageProp,
    onPageChange: onPageChangeProp,
    onRowsPerPageChange: onRowsPerPageChangeProp,
    paginationMode: paginationModeProp,
    total: totalProp,
    sortedData,
    data: dataProp,
  });

  /**
   * Expanded rows state
   */
  const { expandedRows, onExpandedRowsChange } = useExpandedRows<T>({
    initialState: initialStateProp,
    expandedRows: expandedRowsProp,
    onExpandedRowsChange: onExpandedRowsChangeProp,
  });

  /**
   * Cell/Row edit state
   */
  const {
    editingRows,
    editingCells,
    onEditingCellsChange,
    onEditingRowsChange,
    editingCell,
    editingValueCache,
    startEditing,
    stopEditing,
    updateEditingValue,
  } = useEditState({
    editMode,
    editingCells: editingCellsProp,
    onEditingCellsChange: onEditingCellsChangeProp,
    editingRows: editingRowsProp,
    onEditingRowsChange: onEditingRowsChangeProp,
    processRowUpdate: processRowUpdateProp,
    onProcessRowUpdateError: onProcessRowUpdateErrorProp,
  });

  /**
   * Grid row/cell events
   */
  const { rowEvents, cellEvents, onFinishEditing } = useGridEvents<T>({
    rowSelectionMode,
    disableRowSelectionOnClick,
    isRowSelectable,
    selectedRows,
    onSelectedRowsChange,
    cellSelection,
    onSelectedCellsChange,
    editMode,
    editingRows,
    isRowEditable,
    onRowEditStart,
    onRowEditStop,
    editingCells,
    isCellEditable,
    onCellEditStart,
    onCellEditStop,
    startEditing,
    stopEditing,
    cellEvents: cellEventsProp,
    rowEvents: rowEventsProp,
  });

  const {
    computedColumnWidths,
    gridScrollHeight,
    gridScrollWidth,
    isScrollX,
    isScrollY,
    onColumnResize,
    headerHeight,
    rowHeight,
    paginationHeight,
  } = useColumnWidthCalculator<T>({
    density,
    expandedRows,
    uniqueField,
    displayData,
    visibleColumns,
    gridWidth,
    gridHeight,
    scrollbarSize,
    headerHeight: headerHeightProp as number,
    rowHeight: rowHeightProp as number,
    getExpandedRowHeight,
    rowSelectionMode,
    isEnabledRowExpansion,
    gridRef,
  });

  return {
    columns: visibleColumns,
    computedColumnWidths,
    data: displayData,
    density,
    total,
    initialState: initialStateProp,
    uniqueField,

    /**pagination */
    pagination,
    paginationMode: paginationModeProp,
    page,
    rowsPerPage,
    onPageChange,
    onRowsPerPageChange,

    /** Sort */
    sortMode: sortModeProp,
    sortedColumns,
    onSortedColumnsChange,

    /** Selection */
    rowSelectionMode,
    disableRowSelectionOnClick,
    isRowSelectable,
    selectedRows,
    onSelectedRowsChange,
    cellSelection,
    selectedCells,
    onSelectedCellsChange,
    allRowIds,

    /** Expand */
    expandedRows,
    onExpandedRowsChange,
    getExpandedRowHeight,
    expandedRowPanelRender,

    /** Edit */
    editMode,
    editingRows,
    onEditingRowsChange,
    isRowEditable,
    onRowEditStart,
    onRowEditStop,
    editingCells,
    onEditingCellsChange,
    isCellEditable,
    onCellEditStart,
    onCellEditStop,
    processRowUpdate: processRowUpdateProp,
    onProcessRowUpdateError: onProcessRowUpdateErrorProp,
    editingCell,
    editingValueCache,
    updateEditingValue,

    /** Events */
    cellEvents,
    rowEvents,
    onFinishEditing,

    /** Others */
    scrollbarSize,
    headerHeight,
    rowHeight,
    paginationHeight,
    gridWidth,
    gridHeight,
    gridScrollHeight,
    gridScrollWidth,
    isScrollX,
    isScrollY,
    onColumnResize,
    ...rest,
  };
};
