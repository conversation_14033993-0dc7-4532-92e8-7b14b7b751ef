import { useState, useCallback, useMemo } from 'react';
import { DataGridProps } from '../DataGrid.types';

export const usePagination = <T>(
  props: Pick<
    DataGridProps<T>,
    | 'initialState'
    | 'page'
    | 'rowsPerPage'
    | 'onPageChange'
    | 'onRowsPerPageChange'
    | 'paginationMode'
    | 'total'
    | 'data'
  > & {
    sortedData: readonly T[];
  },
) => {
  const {
    initialState: initialStateProp,
    page: pageProp,
    rowsPerPage: rowsPerPageProp,
    onPageChange: onPageChangeProp,
    onRowsPerPageChange: onRowsPerPageChangeProp,
    paginationMode: paginationModeProp,
    total: totalProp,
    sortedData,
    data: dataProp,
  } = props;

  const [page, setPage] = useState(pageProp ?? initialStateProp?.pagination?.page ?? 0);
  const [rowsPerPage, setRowsPerPage] = useState(rowsPerPageProp ?? initialStateProp?.pagination?.rowsPerPage ?? 10);

  const syncedPage = pageProp ?? page;
  const syncedRowsPerPage = rowsPerPageProp ?? rowsPerPage;

  const onPageChange = useCallback(
    (newPage: number) => {
      setPage(newPage);
      onPageChangeProp?.(newPage);
    },
    [onPageChangeProp],
  );

  const onRowsPerPageChange = useCallback(
    (newRowsPerPage: number) => {
      setRowsPerPage(newRowsPerPage);
      onRowsPerPageChangeProp?.(newRowsPerPage);
    },
    [onRowsPerPageChangeProp],
  );

  const displayData = useMemo(() => {
    if (paginationModeProp === 'client') {
      const start = syncedPage * syncedRowsPerPage;
      return sortedData.slice(start, start + syncedRowsPerPage);
    }
    return sortedData;
  }, [paginationModeProp, sortedData, syncedPage, syncedRowsPerPage]);

  const total = useMemo(() => {
    if (paginationModeProp === 'server' && totalProp && totalProp > 0) {
      return totalProp;
    } else {
      return dataProp.length;
    }
  }, [dataProp.length, paginationModeProp, totalProp]);

  return {
    page: syncedPage,
    rowsPerPage: syncedRowsPerPage,
    onPageChange,
    onRowsPerPageChange,
    displayData,
    total,
  };
};
