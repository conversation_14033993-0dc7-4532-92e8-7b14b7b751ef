import { DataGridProps } from '../DataGrid.types';
import { DATA_GRID_PROPS_DEFAULT_VALUES } from '../constants/dataGridDefaultProps';
import { DataGridPropsWithDefaultValue } from '../context/DataGridContext';
import { deepMerge, validProps } from '../utils';

export const useDataGridProps = <T>(props: DataGridProps<T>) => {
  const {
    data,
    total,
    columns,
    uniqueField,
    initialState,
    density,
    pagination,
    paginationConfig,
    paginationMode,
    page,
    rowsPerPage,
    onPageChange,
    onRowsPerPageChange,
    rowSelectionMode,
    disableRowSelectionOnClick,
    selectedRows,
    onSelectedRowsChange,
    isRowSelectable,
    cellSelection,
    selectedCells,
    onSelectedCellsChange,
    sortMode,
    sortedColumns,
    onSortedColumnsChange,
    expandedRows,
    onExpandedRowsChange,
    expandedRowPanelRender,
    isRowExpandable,
    getExpandedRowHeight,
    editMode,
    editingRows,
    onEditingRowsChange,
    onRowEditStart,
    onRowEditStop,
    isRowEditable,
    editingCells,
    onEditingCellsChange,
    onCellEditStart,
    onCellEditStop,
    isCellEditable,
    processRowUpdate,
    onProcessRowUpdateError,
    rowEvents,
    cellEvents,
    scrollbarSize,
    headerHeight,
    rowHeight,
    component,
    slots,
    slotProps,
    ...rest
  } = { ...DATA_GRID_PROPS_DEFAULT_VALUES, ...props } as DataGridPropsWithDefaultValue<T>;
  const initialStateProp = deepMerge(props.initialState, DATA_GRID_PROPS_DEFAULT_VALUES.initialState);
  const passProps = {
    data,
    total,
    columns,
    uniqueField,
    initialState: initialStateProp,
    density,
    pagination,
    paginationConfig,
    paginationMode,
    page,
    rowsPerPage,
    onPageChange,
    onRowsPerPageChange,
    rowSelectionMode,
    disableRowSelectionOnClick,
    selectedRows,
    onSelectedRowsChange,
    isRowSelectable,
    cellSelection,
    selectedCells,
    onSelectedCellsChange,
    sortMode,
    sortedColumns,
    onSortedColumnsChange,
    expandedRows,
    onExpandedRowsChange,
    expandedRowPanelRender,
    isRowExpandable,
    getExpandedRowHeight,
    editMode,
    editingRows,
    onEditingRowsChange,
    onRowEditStart,
    onRowEditStop,
    isRowEditable,
    editingCells,
    onEditingCellsChange,
    onCellEditStart,
    onCellEditStop,
    isCellEditable,
    processRowUpdate,
    onProcessRowUpdateError,
    rowEvents,
    cellEvents,
    scrollbarSize,
    headerHeight,
    rowHeight,
    slots,
    slotProps,
  };
  validProps(passProps);
  return {
    passProps,
    slots,
    slotProps,
    component,
    rest,
  };
};
