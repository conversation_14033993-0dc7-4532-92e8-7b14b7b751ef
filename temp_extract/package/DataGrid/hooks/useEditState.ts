import { useState, useCallback } from 'react';
import { RowId } from '../types/row.type';
import { CellType } from '../types/cell.type';
import { DataGridProps } from '../DataGrid.types';
import { EditingTriggerAction } from '../types/edit.type';

const EmptyArray: any[] = [];
export const useEditState = <T>(
  props: Pick<
    DataGridProps<T>,
    | 'editMode'
    | 'editingCells'
    | 'onEditingCellsChange'
    | 'editingRows'
    | 'onEditingRowsChange'
    | 'processRowUpdate'
    | 'onProcessRowUpdateError'
  >,
) => {
  const {
    editMode,
    editingCells: editingCellsProp,
    onEditingCellsChange: onEditingCellsChangeProp,
    editingRows: editingRowsProp,
    onEditingRowsChange: onEditingRowsChangeProp,
    processRowUpdate: processRowUpdateProp,
    onProcessRowUpdateError: onProcessRowUpdateErrorProp,
  } = props;

  const [editingCells, setEditCells] = useState(editingCellsProp ?? EmptyArray);
  const [editingRows, setEditRows] = useState(editingRowsProp ?? EmptyArray);
  const [editingValueCache, setEditingValueCache] = useState<Record<string, any>>({});
  const [editingCell, setEditingCell] = useState<CellType>({ rowId: '', columnField: '' });
  const syncedEditCells = editingCellsProp ?? editingCells;
  const syncedEditRows = editingRowsProp ?? editingRows;

  const onEditingCellsChange = useCallback(
    (cells: CellType[]) => {
      setEditCells(cells);
      onEditingCellsChangeProp?.(cells);
    },
    [onEditingCellsChangeProp],
  );

  const onEditingRowsChange = useCallback(
    (rowIds: RowId[]) => {
      setEditRows(rowIds);
      onEditingRowsChangeProp?.(rowIds);
    },
    [onEditingRowsChangeProp],
  );

  const startEditing = useCallback(
    (params: { rowId: RowId; columnField?: string }) => {
      const { rowId, columnField } = params;
      if (editMode === 'row') {
        onEditingRowsChange([rowId]);
      } else if (editMode === 'cell' && columnField) {
        onEditingCellsChange([{ rowId, columnField }]);
      }
      if (rowId && columnField) {
        setEditingCell({ rowId, columnField });
      }
    },
    [editMode, onEditingRowsChange, onEditingCellsChange],
  );

  const stopEditing = useCallback(
    async (params: {
      rowId: RowId;
      columnField?: string;
      saveChanges?: boolean;
      originalRow: T;
      trigger: EditingTriggerAction;
      onCellEditStop?: DataGridProps<T>['onCellEditStop'];
      onRowEditStop?: DataGridProps<T>['onRowEditStop'];
    }) => {
      const { rowId, columnField, saveChanges, originalRow, trigger, onCellEditStop, onRowEditStop } = params;
      let newRow = originalRow;
      try {
        if (saveChanges && editingValueCache[rowId]) {
          newRow = { ...originalRow, ...editingValueCache[rowId] };
          if (processRowUpdateProp) {
            newRow = await processRowUpdateProp(newRow, originalRow);
            setEditingValueCache((prev) => {
              const newCache = { ...prev };
              delete newCache[rowId];
              return newCache;
            });
          }
        } else {
          if (editMode === 'cell') {
            if (columnField) {
              setEditingValueCache((prev) => ({
                ...prev,
                [rowId]: {
                  ...prev[rowId],
                  [columnField]: originalRow[columnField as keyof T],
                },
              }));
            }
          } else if (editMode === 'row') {
            setEditingValueCache((prev) => {
              const newCache = { ...prev };
              delete newCache[rowId];
              return newCache;
            });
          }
        }

        if (editMode === 'cell' && columnField) {
          onCellEditStop?.({ rowId, columnField, newRow, originalRow, trigger });
          onEditingCellsChange([]);
        } else if (editMode === 'row') {
          onRowEditStop?.({ rowId, newRow, originalRow, trigger });
          onEditingRowsChange([]);
        }

        return newRow;
      } catch (error) {
        onProcessRowUpdateErrorProp?.(error, { newRow, originalRow });
        return originalRow;
      }
    },
    [
      editMode,
      editingValueCache,
      onEditingCellsChange,
      onEditingRowsChange,
      onProcessRowUpdateErrorProp,
      processRowUpdateProp,
    ],
  );

  const updateEditingValue = useCallback((rowId: RowId, field: string, value: any) => {
    setEditingValueCache((prev) => ({
      ...prev,
      [rowId]: {
        ...prev[rowId],
        [field]: value,
      },
    }));
  }, []);

  return {
    editingRows: syncedEditRows,
    editingCells: syncedEditCells,
    onEditingCellsChange,
    onEditingRowsChange,
    editingCell,
    editingValueCache,
    startEditing,
    stopEditing,
    updateEditingValue,
  };
};
