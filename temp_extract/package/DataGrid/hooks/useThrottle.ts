import { useRef, useEffect, useCallback } from 'react';

export const useThrottle = <T extends (...args: any[]) => void>(callback: T, delay: number) => {
  const lastExecutedRef = useRef<number>(Date.now());
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastArgsRef = useRef<Parameters<T> | null>(null);

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return useCallback(
    (...args: Parameters<T>) => {
      const now = Date.now();
      lastArgsRef.current = args;

      if (now - lastExecutedRef.current >= delay) {
        callback(...args);
        lastExecutedRef.current = now;
      }

      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      timeoutRef.current = setTimeout(() => {
        if (lastArgsRef.current) {
          callback(...lastArgsRef.current);
          lastArgsRef.current = null;
        }
      }, delay);
    },
    [callback, delay],
  );
};
