import { useState, useCallback, useMemo } from 'react';
import { SortItem } from '../types/sort.type';
import { DataGridProps } from '../DataGrid.types';
import { ColumnType } from '../types/column.type';
import { compareValues } from '../utils';

const EmptyArray: any[] = [];
export const useSorting = <T>(
  props: Pick<DataGridProps<T>, 'initialState' | 'sortedColumns' | 'onSortedColumnsChange' | 'sortMode' | 'data'> & {
    visibleColumns: ColumnType<T>[];
  },
) => {
  const {
    initialState: initialStateProp,
    sortedColumns: sortedColumnsProp,
    onSortedColumnsChange: onSortedColumnsChangeProp,
    sortMode: sortModeProp,
    visibleColumns,
    data: dataProp,
  } = props;

  const [sortedColumns, setSortedColumns] = useState(
    sortedColumnsProp ?? initialStateProp?.sortedColumns ?? EmptyArray,
  );

  const onSortedColumnsChange = useCallback(
    (newSort: SortItem[]) => {
      setSortedColumns(newSort);
      onSortedColumnsChangeProp?.(newSort);
    },
    [onSortedColumnsChangeProp],
  );

  const sortedData = useMemo(() => {
    if (sortModeProp === 'client') {
      return [...dataProp].sort((a: any, b: any) => {
        let comparison = 0;
        for (const { field, sort } of sortedColumns) {
          const column = visibleColumns.find((col) => col.field === field);
          const valueA = a[field];
          const valueB = b[field];
          comparison = column?.sortFn?.(a, b, sort) ?? compareValues(valueA, valueB, sort);
          if (comparison !== 0) break;
        }
        return comparison;
      });
    }
    return dataProp;
  }, [dataProp, sortModeProp, sortedColumns, visibleColumns]);

  return {
    sortedColumns: sortedColumnsProp ?? sortedColumns,
    onSortedColumnsChange,
    sortedData,
  };
};
