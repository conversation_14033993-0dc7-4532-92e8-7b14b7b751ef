import '@testing-library/jest-dom/vitest';
import { render, screen, cleanup, fireEvent } from '@testing-library/react';
import React from 'react';
import { vi, describe, expect, it, afterEach } from 'vitest';
import { DataGrid, ColumnType } from './index';

afterEach(() => {
  cleanup();
});

type DataType = {
  id: number;
  firstName: string;
  lastName: string;
};

vi.mock('./hooks/useElementSize', () => ({
  useElementSize: vi.fn(() => ({
    height: 600,
    width: 1200,
    ref: React.createRef(),
  })),
}));

const columns: ColumnType<DataType>[] = [
  { field: 'id', header: 'ID', width: 150 },
  { field: 'firstName', header: 'First Name', width: 200 },
  { field: 'lastName', header: 'Last Name', width: 200 },
];

const fixedColumns: ColumnType<DataType>[] = [
  { field: 'id', header: 'ID', width: 150, fixed: 'left' },
  { field: 'firstName', header: 'First Name', width: 200 },
  { field: 'lastName', header: 'Last Name', width: 200, fixed: 'right' },
];

const editingColumns: ColumnType<DataType>[] = [
  { field: 'id', header: 'ID', width: 150 },
  { field: 'firstName', header: 'First Name', width: 200, editable: true },
  { field: 'lastName', header: 'Last Name', width: 200, editable: true },
];

const extendedColumns = [
  {
    field: 'fullName',
    header: 'Full Name',
    width: 300,
    cell: (row) => `${row.firstName} ${row.lastName}`,
    sortable: false,
  },
];

const data: DataType[] = [
  { id: 1, firstName: 'Tony', lastName: 'Smith' },
  { id: 2, firstName: 'Isla', lastName: 'Fletcher' },
  { id: 3, firstName: 'Evie', lastName: 'Easton' },
  { id: 4, firstName: 'Liam', lastName: 'Johnson' },
  { id: 5, firstName: 'Ava', lastName: 'Brown' },
  { id: 6, firstName: 'Noah', lastName: 'Williams' },
];

const generateRandomData = (count: number) => {
  const firstNames = ['Oliver', 'Jack', 'Emily', 'Sophia', 'James', 'Charlotte'];
  const lastNames = ['Taylor', 'Anderson', 'Thomas', 'Jackson', 'White', 'Harris'];
  const finalData: DataType[] = [];
  for (let i = 0; i < count; i++) {
    const id = i + 1;
    const firstName = firstNames[Math.floor(Math.random() * firstNames.length)];
    const lastName = lastNames[Math.floor(Math.random() * lastNames.length)];
    finalData.push({ id, firstName, lastName });
  }
  return finalData;
};

export default function DataGridDemo() {
  return <DataGrid columns={columns} data={data} />;
}

describe('DataGrid', () => {
  it('should render normal', () => {
    render(<DataGrid data-testid="NovaDataGrid-root" columns={columns} data={data} />);
    expect(screen.getByTestId('NovaDataGrid-root')).toHaveClass('NovaDataGrid-root');
    expect(screen.getByTestId('NovaDataGrid-root')).toHaveClass('NovaDataGrid-densityStandard');
    expect(screen.getAllByRole('columnheader')).toHaveLength(3); // 3 header cells
    expect(screen.getAllByRole('row')).toHaveLength(7); // 1 header + 6 rows
    expect(screen.getAllByRole('gridcell')).toHaveLength(18); // 18 row cells
    expect(screen.getByText('Page 1 of 1')).toBeInTheDocument(); // pagination
  });

  it('should compact density', () => {
    render(<DataGrid data-testid="NovaDataGrid-root" columns={columns} data={data} density="compact" />);
    expect(screen.getByTestId('NovaDataGrid-root')).toHaveClass('NovaDataGrid-densityCompact');
  });

  it('should comfortable density', () => {
    render(<DataGrid data-testid="NovaDataGrid-root" columns={columns} data={data} density="comfortable" />);
    expect(screen.getByTestId('NovaDataGrid-root')).toHaveClass('NovaDataGrid-densityComfortable');
  });

  it('should render checkbox', () => {
    render(
      <DataGrid data-testid="NovaDataGrid-root" columns={columns} data={data} rowSelectionMode="checkboxSelection" />,
    );
    expect(screen.getByTestId('NovaDataGrid-root')).toHaveClass('NovaDataGrid-root');
    expect(screen.getAllByRole('columnheader')).toHaveLength(4); // 4 header cells
    expect(screen.getAllByRole('row')).toHaveLength(7); // 1 header + 6 rows
    expect(screen.getAllByRole('gridcell')).toHaveLength(24); // 24 row cells
  });

  it('should render initial checked checkbox', () => {
    const fn = vi.fn();
    render(
      <DataGrid
        data-testid="NovaDataGrid-root"
        columns={columns}
        data={data}
        rowSelectionMode="checkboxSelection"
        initialState={{ selectedRows: [1, 3] }}
        onSelectedRowsChange={fn}
      />,
    );
    expect(screen.getAllByTestId('NovaIcon-checkboxIndeterminate')).toHaveLength(1);
    expect(screen.getAllByTestId('NovaIcon-checkboxChecked')).toHaveLength(2);
    // Uncheck the 1th row
    fireEvent.click(screen.getAllByRole('checkbox')[1]);
    expect(screen.getAllByTestId('NovaIcon-checkboxChecked')).toHaveLength(1);
    expect(fn).toHaveBeenCalledWith([3]);
    // Uncheck the 3th row
    fireEvent.click(screen.getAllByRole('checkbox')[3]);
    expect(screen.queryAllByTestId('NovaIcon-checkboxIndeterminate')).toHaveLength(0);
    expect(fn).toHaveBeenCalledWith([]);
    // Check all
    fireEvent.click(screen.getAllByRole('checkbox')[0]);
    expect(screen.getAllByTestId('NovaIcon-checkboxChecked')).toHaveLength(7);
    expect(fn).toHaveBeenCalledWith([1, 2, 3, 4, 5, 6]);
  });

  it('should render radio', () => {
    render(
      <DataGrid data-testid="NovaDataGrid-root" columns={columns} data={data} rowSelectionMode="radioSelection" />,
    );
    expect(screen.getByTestId('NovaDataGrid-root')).toHaveClass('NovaDataGrid-root');
    expect(screen.getAllByRole('columnheader')).toHaveLength(4); // 4 header cells
    expect(screen.getAllByRole('row')).toHaveLength(7); // 1 header + 6 rows
    expect(screen.getAllByRole('gridcell')).toHaveLength(24); // 24 row cells
  });

  it('should render initial checked radio', () => {
    const fn = vi.fn();
    render(
      <DataGrid
        data-testid="NovaDataGrid-root"
        columns={columns}
        data={data}
        rowSelectionMode="radioSelection"
        initialState={{ selectedRows: [1] }}
        onSelectedRowsChange={fn}
      />,
    );
    expect(screen.getAllByRole('radio')[1]).toBeChecked();
    // Check the 2th row
    fireEvent.click(screen.getAllByRole('radio')[2]);
    expect(screen.getAllByRole('radio')[2]).toBeChecked();
    expect(screen.getAllByRole('radio')[1]).not.toBeChecked();
    expect(fn).toHaveBeenCalledWith([2]);
  });

  it('should render initial selected cell', () => {
    render(
      <DataGrid
        data-testid="NovaDataGrid-root"
        columns={columns}
        data={data}
        cellSelection
        initialState={{ selectedCells: [{ columnField: 'firstName', rowId: 2 }] }}
      />,
    );
    const secondRow = screen.getAllByRole('row')[2];
    expect(secondRow.getElementsByClassName('NovaDataGrid-cellSelected')).toHaveLength(1);
  });

  it('should change selected cell', () => {
    const fn = vi.fn();
    render(
      <DataGrid
        data-testid="NovaDataGrid-root"
        columns={columns}
        data={data}
        cellSelection
        initialState={{ selectedCells: [{ columnField: 'firstName', rowId: 2 }] }}
        onSelectedCellsChange={fn}
      />,
    );
    fireEvent.click(screen.getAllByRole('gridcell')[0]);
    expect(screen.getAllByRole('gridcell')[0]).toHaveClass('NovaDataGrid-cellSelected');
    expect(fn).toHaveBeenCalledWith([{ columnField: 'id', rowId: 1 }]);
  });

  it('should sort function work', () => {
    const fn = vi.fn();
    render(
      <DataGrid
        data-testid="NovaDataGrid-root"
        onSortedColumnsChange={fn}
        columns={[...columns, ...extendedColumns]}
        data={data}
      />,
    );
    fireEvent.mouseEnter(screen.getAllByRole('columnheader')[0]);
    fireEvent.click(screen.getAllByRole('button')[0]);
    expect(fn).toHaveBeenCalledWith([{ field: 'id', sort: 'asc' }]);
    expect(screen.getAllByRole('gridcell')[0]).toHaveTextContent('1');
    fireEvent.click(screen.getAllByRole('button')[0]);
    expect(fn).toHaveBeenCalledWith([{ field: 'id', sort: 'desc' }]);
    expect(screen.getAllByRole('gridcell')[0]).toHaveTextContent('6');
    fireEvent.click(screen.getAllByRole('button')[0]);
    expect(fn).toHaveBeenCalledWith([]);
    expect(screen.getAllByRole('gridcell')[0]).toHaveTextContent('1');

    fireEvent.mouseEnter(screen.getAllByRole('columnheader')[1]);
    fireEvent.click(screen.getAllByRole('button')[1]);
    expect(screen.getAllByRole('gridcell')[1]).toHaveTextContent('Ava');
    fireEvent.click(screen.getAllByRole('button')[1]);
    expect(screen.getAllByRole('gridcell')[1]).toHaveTextContent('Tony');
    fireEvent.click(screen.getAllByRole('button')[1]);
    expect(screen.getAllByRole('gridcell')[1]).toHaveTextContent('Tony');
  });

  it('should render custom cells', () => {
    render(
      <DataGrid
        data-testid="NovaDataGrid-root"
        columns={[...columns, ...extendedColumns]}
        data={data}
        rowSelectionMode="radioSelection"
      />,
    );
    expect(screen.getByTestId('NovaDataGrid-root')).toHaveClass('NovaDataGrid-root');
    expect(screen.getAllByRole('columnheader')).toHaveLength(5); // 5 header cells
    expect(screen.getAllByRole('row')).toHaveLength(7); // 1 header + 6 rows
    expect(screen.getAllByRole('gridcell')).toHaveLength(30); // 30 row cells
    expect(screen.getByText('Tony Smith')).toBeInTheDocument();
  });

  it('should render pagination with multiple page correctly', () => {
    render(
      <DataGrid
        data-testid="NovaDataGrid-root"
        columns={[...columns]}
        data={generateRandomData(100)}
        rowSelectionMode="radioSelection"
      />,
    );
    expect(screen.getByText('Page 1 of 10')).toBeInTheDocument();
    fireEvent.click(screen.getByLabelText('Go to next page'));
    expect(screen.getByText('Page 2 of 10')).toBeInTheDocument();
  });

  it('should expand row', () => {
    const isRowExpandable = (row) => row.id !== 2;
    const expandedRowPanelRender = (row) => <div>Row expanded</div>;
    const getExpandedRowHeight = (row) => 400;
    render(
      <DataGrid
        pagination={false}
        data-testid="NovaDataGrid-root"
        columns={columns}
        data={data}
        isRowExpandable={isRowExpandable}
        expandedRowPanelRender={expandedRowPanelRender}
        getExpandedRowHeight={getExpandedRowHeight}
      />,
    );
    expect(screen.getAllByTestId('KeyboardArrowRightIcon')).toHaveLength(5);
    expect(screen.queryByText('Row expanded')).not.toBeInTheDocument();
    fireEvent.click(screen.getAllByTestId('KeyboardArrowRightIcon')[0]);
    expect(screen.queryByText('Row expanded')).toBeInTheDocument();
    expect(screen.getAllByTestId('KeyboardArrowRightIcon')).toHaveLength(4);
    expect(screen.getAllByTestId('KeyboardArrowDownIcon')).toHaveLength(1);
  });

  it('should render initial expanded row', () => {
    const isRowExpandable = (row) => row.id !== 2;
    const expandedRowPanelRender = (row) => <div>Row expanded</div>;
    const getExpandedRowHeight = (row) => 400;
    render(
      <DataGrid
        pagination={false}
        data-testid="NovaDataGrid-root"
        columns={columns}
        data={data}
        initialState={{ expandedRows: [3] }}
        isRowExpandable={isRowExpandable}
        expandedRowPanelRender={expandedRowPanelRender}
        getExpandedRowHeight={getExpandedRowHeight}
      />,
    );
    expect(screen.getAllByTestId('KeyboardArrowRightIcon')).toHaveLength(4);
    expect(screen.queryByText('Row expanded')).toBeInTheDocument();
    expect(screen.getAllByTestId('KeyboardArrowDownIcon')).toHaveLength(1);
  });

  it('should cell/row events triggered', () => {
    const cellClickFn = vi.fn();
    const rowClickFn = vi.fn();
    render(
      <DataGrid
        pagination={false}
        data-testid="NovaDataGrid-root"
        columns={columns}
        data={data}
        cellEvents={{ onClick: cellClickFn }}
        rowEvents={{ onClick: rowClickFn }}
      />,
    );
    fireEvent.click(screen.getAllByRole('gridcell')[0]);
    expect(cellClickFn).toBeCalledWith(
      expect.objectContaining({
        rowId: 1,
        columnField: 'id',
      }),
      expect.anything(),
    );
    expect(rowClickFn).toBeCalledWith(
      expect.objectContaining({
        rowId: 1,
      }),
      expect.anything(),
    );
  });

  it('should render fixed/pinned column', () => {
    render(<DataGrid data-testid="NovaDataGrid-root" columns={fixedColumns} data={data} />);
    const header = screen.getAllByRole('row')[0];
    expect(header.getElementsByClassName('NovaDataGrid-cellFixed')).toHaveLength(2);
    expect(header.getElementsByClassName('NovaDataGrid-cellFixedLeft')).toHaveLength(1);
    expect(header.getElementsByClassName('NovaDataGrid-cellFixedRight')).toHaveLength(1);
    const row = screen.getAllByRole('row')[1];
    expect(row.getElementsByClassName('NovaDataGrid-cellFixed')).toHaveLength(2);
    expect(row.getElementsByClassName('NovaDataGrid-cellFixedLeft')).toHaveLength(1);
    expect(row.getElementsByClassName('NovaDataGrid-cellFixedRight')).toHaveLength(1);
  });

  it('should render editing cell', () => {
    const onCellEditStart = vi.fn();
    const onCellEditStop = vi.fn();
    const onEditingCellsChange = vi.fn();
    render(
      <DataGrid
        data-testid="NovaDataGrid-root"
        columns={editingColumns}
        data={data}
        onCellEditStart={onCellEditStart}
        onCellEditStop={onCellEditStop}
        onEditingCellsChange={onEditingCellsChange}
      />,
    );
    const firstName = screen.getAllByRole('gridcell')[1];
    expect(firstName).toHaveTextContent('Tony');
    fireEvent.doubleClick(firstName);
    expect(onCellEditStart).toHaveBeenCalled();
    expect(onEditingCellsChange).toHaveBeenCalledWith([{ rowId: 1, columnField: 'firstName' }]);
    expect(screen.getAllByRole('gridcell')[1]).toHaveClass('NovaDataGrid-cellEditing');
    fireEvent.change(screen.getAllByRole('gridcell')[1].getElementsByTagName('input')[0], { target: { value: 'AAA' } });
    fireEvent.keyDown(screen.getAllByRole('gridcell')[1], { key: 'Enter', code: 'Enter', charCode: 13 });
    expect(onEditingCellsChange).toHaveBeenCalledWith([]);
    expect(onCellEditStop).toHaveBeenCalled();
    expect(screen.getAllByRole('gridcell')[1]).not.toHaveClass('NovaDataGrid-cellEditing');
    expect(screen.getAllByRole('gridcell')[1]).toHaveTextContent('AAA');
  });

  it('should render editing row', () => {
    const onRowEditStart = vi.fn();
    const onRowEditStop = vi.fn();
    const onEditingRowsChange = vi.fn();
    render(
      <DataGrid
        data-testid="NovaDataGrid-root"
        columns={editingColumns}
        data={data}
        onRowEditStart={onRowEditStart}
        onRowEditStop={onRowEditStop}
        onEditingRowsChange={onEditingRowsChange}
        editMode="row"
      />,
    );
    const firstRow = screen.getAllByRole('row')[1];
    fireEvent.doubleClick(firstRow);
    expect(onRowEditStart).toHaveBeenCalled();
    expect(onEditingRowsChange).toHaveBeenCalledWith([1]);
    expect(screen.getAllByRole('row')[1]).toHaveClass('NovaDataGrid-rowEditing');
    expect(screen.getAllByRole('row')[1].getElementsByClassName('NovaDataGrid-cellEditing')).toHaveLength(2);
    fireEvent.change(screen.getAllByRole('row')[1].getElementsByTagName('input')[0], { target: { value: 'AAA' } });
    fireEvent.change(screen.getAllByRole('row')[1].getElementsByTagName('input')[1], { target: { value: 'BBB' } });
    fireEvent.keyDown(screen.getAllByRole('row')[1].getElementsByTagName('input')[1], {
      key: 'Enter',
      code: 'Enter',
      charCode: 13,
    });
    expect(onEditingRowsChange).toHaveBeenCalledWith([]);
    expect(onRowEditStop).toHaveBeenCalled();
    expect(screen.getAllByRole('gridcell')[1]).toHaveTextContent('AAA');
    expect(screen.getAllByRole('gridcell')[2]).toHaveTextContent('BBB');
  });
});
