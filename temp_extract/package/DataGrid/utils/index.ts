import { DataGridProps } from '../DataGrid.types';
import { ColumnType } from '../types/column.type';
import { SortDirection } from '../types/sort.type';

/**
 * Error thrown when a non-unique value is found for a specified field.
 */
export class NonUniqueFieldError extends Error {
  /**
   * @param {string} field - The field that caused the error.
   * @param {string} duplicateValue - The non-unique value found.
   */
  constructor(name: string, field: string, duplicateValue: string) {
    super(`Nova DataGrid: The ${name} -> ${field} contains non-unique value: ${duplicateValue}.`);
  }
}

/**
 * Error thrown when an undefined, null or empty value is found for a specified field.
 */
export class EmptyFieldError extends Error {
  /**
   * @param {string} field - The field that caused the error.
   */
  constructor(name: string, field: string) {
    super(`Nova DataGrid: The ${name} -> ${field} contains undefined, null or empty value`);
  }
}

/**
 * Validates that all values for a specified field in the data array are unique.
 *
 * @param {ReadonlyArray<Record<string, any>>} data - The array of data records to check.
 * @param {string} [field='id'] - The field property to validate for uniqueness.
 * @returns {boolean} - Returns true if all values are unique.
 * @throws {EmptyFieldError} - If any value for the field is undefined or null.
 * @throws {NonUniqueFieldError} - If any value for the field is not unique.
 *
 * @example
 * // Example usage
 * const data = [
 *   { id: '1' },
 *   { id: '2' },
 *   { id: '1' } // This will throw a NonUniqueFieldError
 * ];
 * validUniqueData(data);
 */
export const validUniqueData = (data: ReadonlyArray<any>, field = 'id') => {
  const seenValues = new Set();

  for (const item of data) {
    const value = item[field];

    if (value == null || value === '') {
      if (process.env.NODE_ENV !== 'production') {
        throw new EmptyFieldError('data', field);
      }
    }

    // Check if the value has already been seen
    if (seenValues.has(value)) {
      if (process.env.NODE_ENV !== 'production') {
        throw new NonUniqueFieldError('data', field, value); // Include the duplicate value
      }
    }

    // Add the value to the set
    seenValues.add(value);
  }

  return true; // All values are unique
};

/**
 * Validates that each column in the provided array has a unique field value.
 * Throws errors if any field is null, undefined, or an empty string,
 * or if there are duplicate field values.
 *
 * @param {ColumnType[]} columns - An array of columns to validate.
 * @throws {EmptyFieldError} If any column's field is null, undefined, or an empty string.
 * @throws {NonUniqueFieldError} If any field value is duplicated among the columns.
 *
 * @example
 * // Example usage
 * const columns = [
 *   { field: 'name' },
 *   { field: 'age' },
 *   { field: 'name' } // This will throw a NonUniqueFieldError
 * ];
 * validUniqueColumn(columns);
 */
export const validUniqueColumn = (columns: ReadonlyArray<ColumnType>) => {
  const seenValues = new Set();
  for (const item of columns) {
    if (item.field == null || item.field === '') {
      if (process.env.NODE_ENV !== 'production') {
        throw new EmptyFieldError('columns', 'field');
      }
    }
    if (seenValues.has(item.field)) {
      if (process.env.NODE_ENV !== 'production') {
        throw new NonUniqueFieldError('columns', 'field', item.field); // Include the duplicate value
      }
    }
    seenValues.add(item.field);
  }
  return true; // All values are unique
};

/**
 * Validates that all values for a specified field in the data array are unique.
 *
 * @param {ReadonlyArray<Record<string, any>>} data - The array of data records to check.
 * @param {string} [field='id'] - The field property to validate for uniqueness.
 * @returns {boolean} - Returns true if all values are unique.
 * @throws {EmptyFieldError} - If any value for the field is undefined or null.
 * @throws {NonUniqueFieldError} - If any value for the field is not unique.
 */
export const validProps = (props: DataGridProps<any>) => {
  const { data, columns, uniqueField } = props;
  if (data.length) {
    // Check unique data id valid
    validUniqueData(data, uniqueField);
  }
  if (columns.length) {
    // Check unique column field valid
    validUniqueColumn(columns);
  }
};

/**
 * Compares two values based on the specified sort direction.
 *
 * @param {any} value1 - The first value to compare.
 * @param {any} value2 - The second value to compare.
 * @param {SortDirection} sortDirection - The direction to sort ('asc' or 'desc').
 * @returns {number} - A negative number if value1 < value2, a positive number if value1 > value2, and 0 if they are equal.
 */
export const compareValues = (value1: any, value2: any, sortDirection: SortDirection) => {
  if (typeof value1 === 'string' && typeof value2 === 'string') {
    return sortDirection === 'asc' ? value1.localeCompare(value2) : value2.localeCompare(value1);
  } else if (typeof value1 === 'number' && typeof value2 === 'number') {
    return sortDirection === 'asc' ? value1 - value2 : value2 - value1;
  } else if (value1 instanceof Date && value2 instanceof Date) {
    return sortDirection === 'asc' ? value1.getTime() - value2.getTime() : value2.getTime() - value1.getTime();
  } else {
    return 0;
  }
};

/**
 * Deep merges two objects, giving priority to properties in the first object.
 *
 * @param {T} passedProps - The properties to merge.
 * @param {T} defaultProps - The default properties to merge into.
 * @returns {T} - The merged properties.
 */
export const deepMerge = <T extends object>(passedProps: T | undefined, defaultProps: T | undefined): T => {
  if (!passedProps) return { ...defaultProps } as T;
  if (!defaultProps) return { ...passedProps };

  const mergedProps: T = { ...defaultProps };

  for (const key in passedProps) {
    if (Object.prototype.hasOwnProperty.call(passedProps, key)) {
      const passedValue = passedProps[key];
      const defaultValue = (defaultProps as any)[key];

      if (
        typeof passedValue === 'object' &&
        passedValue !== null &&
        typeof defaultValue === 'object' &&
        defaultValue !== null &&
        !Array.isArray(passedValue) && // Exclude arrays
        !Array.isArray(defaultValue)
      ) {
        mergedProps[key] = deepMerge(passedValue, defaultValue);
      } else {
        mergedProps[key] = passedValue;
      }
    }
  }

  return mergedProps;
};

/**
 * Clamps a given width between a specified minimum and maximum width.
 *
 * If the maximum width is less than the minimum width, the maximum width
 * is ignored, and the result is clamped only by the minimum width.
 *
 * @param {number} width - The initial column width to be clamped.
 * @param {number} minWidth - The minimum allowable width.
 * @param {number} maxWidth - The maximum allowable width.
 * @returns {number} The clamped column width.
 */
export const clampColumnWidth = (width: number, minWidth: number, maxWidth: number): number => {
  const newWidth = Math.max(width, minWidth);

  // ignore maxWidth if it less than minWidth
  if (maxWidth >= minWidth) {
    return Math.min(newWidth, maxWidth);
  }

  return newWidth;
};

/**
 * Calculates the height of the grid header based on the specified density.
 *
 * @param {number} rowHeight - The height of the rows.
 * @param {'compact' | 'standard' | 'comfortable'} [density='standard'] - The density setting for the grid.
 * @returns {number} - The calculated header height.
 */
export const getGridHeaderHeight = (
  rowHeight: number | undefined = 0,
  density: 'compact' | 'standard' | 'comfortable' = 'standard',
) => {
  switch (density) {
    case 'compact':
      return rowHeight || 40;
    case 'standard':
      return rowHeight || 48;
    case 'comfortable':
      return rowHeight || 56;
    default:
      return rowHeight || 48;
  }
};

/**
 * Calculates the total height of the grid rows.
 *
 * @param {number} [rowCount=1] - The number of rows.
 * @param {number} rowHeight - The height of each row.
 * @param {number} expandedPanelHeight - The height of expanded row panel.
 * @param {'compact' | 'standard' | 'comfortable'} [density='standard'] - The density setting for the grid.
 * @returns {number} - The total height of the rows.
 */
export const getGridRowsHeight = (
  rowCount: number | undefined = 0,
  rowHeight: number | undefined = 0,
  expandedPanelHeight: number | undefined = 0,
  density: 'compact' | 'standard' | 'comfortable' = 'standard',
) => {
  return rowCount * getGridHeaderHeight(rowHeight, density) + expandedPanelHeight;
};

/**
 * Calculates the height of the grid pagination based on the specified density.
 *
 * @param {number} paginationHeight - The height of the pagination.
 * @param {'compact' | 'standard' | 'comfortable'} density - The density setting for the grid.
 * @returns {number} - The calculated pagination height.
 */
export const getGridPaginationHeight = (
  paginationHeight: number | undefined = 0,
  density: 'compact' | 'standard' | 'comfortable',
) => {
  switch (density) {
    case 'compact':
      return paginationHeight || 40;
    case 'standard':
      return paginationHeight || 48;
    case 'comfortable':
      return paginationHeight || 56;
    default:
      return paginationHeight || 48;
  }
};

/**
 * Calculates the total height of the grid including header, rows, and pagination.
 *
 * @param {number} rowCount - The number of rows.
 * @param {number} headerHeight - The height of the header.
 * @param {number} rowHeight - The height of each row.
 * @param {number} paginationHeight - The height of the pagination.
 * @param {number} expandedPanelHeight - The height of expanded row panel.
 * @param {'compact' | 'standard' | 'comfortable'} density - The density setting for the grid.
 * @returns {number} - The total height of the grid.
 */
export const getGridHeight = (
  rowCount: number | undefined = 0,
  headerHeight: number | undefined = 0,
  rowHeight: number | undefined = 0,
  paginationHeight: number | undefined = 0,
  expandedPanelHeight: number | undefined = 0,
  density: 'compact' | 'standard' | 'comfortable',
) => {
  return (
    getGridHeaderHeight(headerHeight, density) +
    getGridRowsHeight(rowCount, rowHeight, expandedPanelHeight, density) +
    getGridPaginationHeight(paginationHeight, density)
  );
};

/**
 * Calculates the total width of the grid columns based on their widths and selection mode.
 *
 * @param {number[]} [columnWidths=[]] - The widths of the columns.
 * @param {'checkboxSelection' | 'radioSelection' | undefined} rowSelectionMode - The selection mode for the grid.
 * @param {boolean} isEnabledRowExpansion - Indicate if the row expansion feature enabled.
 * @param {'compact' | 'standard' | 'comfortable'} density - The density setting for the grid.
 * @returns {number} - The total width of the grid columns.
 */
export const getGridColumnsWidth = (
  columnWidths: number[] = [],
  rowSelectionMode: 'checkboxSelection' | 'radioSelection' | undefined,
  isEnabledRowExpansion: boolean,
  density: 'compact' | 'standard' | 'comfortable',
) => {
  const initialWidth = 0;
  let totalWidth = columnWidths.reduce((accumulator, width) => accumulator + (width || 100), initialWidth);
  let operationCellCount = 0;
  if (rowSelectionMode) {
    operationCellCount = operationCellCount + 1;
  }
  if (isEnabledRowExpansion) {
    operationCellCount = operationCellCount + 1;
  }
  switch (density) {
    case 'compact':
      totalWidth = totalWidth + 32 * operationCellCount;
      break;
    case 'standard':
    case 'comfortable':
      totalWidth = totalWidth + 40 * operationCellCount;
      break;
    default:
      totalWidth = totalWidth + 40 * operationCellCount;
  }
  return totalWidth;
};

/**
 * Calculates the widths of the columns based on their fixed and flexible settings.
 *
 * @param {ColumnType[]} columns - The columns to calculate widths for.
 * @param {number} gridWidth - The total width of the grid.
 * @param {'checkboxSelection' | 'radioSelection' | undefined} rowSelectionMode - The selection mode for the grid.
 * @param {'compact' | 'standard' | 'comfortable'} density - The density setting for the grid.
 * @param {number[]} cachedWidth - Cached widths for the columns to optimize recalculation.
 * @returns {number[]} - The calculated widths for each column.
 */
export const calculateWidths = (
  columns: ColumnType[],
  gridWidth: number,
  rowSelectionMode: 'checkboxSelection' | 'radioSelection' | undefined,
  isEnabledRowExpansion: boolean,
  density: 'compact' | 'standard' | 'comfortable',
  cachedWidth: number[],
  resizedColumnWidth: Record<string, number> = {},
) => {
  const { fixedWidthColumns, flexColumns } = columns.reduce(
    (acc, col) => {
      if (col.flex && col.flex > 0 && !resizedColumnWidth[col.field]) {
        acc.flexColumns.push(col);
      } else {
        acc.fixedWidthColumns.push(col);
      }
      return acc;
    },
    { fixedWidthColumns: [], flexColumns: [] } as {
      fixedWidthColumns: ColumnType[];
      flexColumns: ColumnType[];
    },
  );

  const totalFixedWidth = fixedWidthColumns.reduce(
    (sum, col) => {
      return sum + (resizedColumnWidth[col.field] || col.width || 100);
    },
    getGridColumnsWidth([], rowSelectionMode, isEnabledRowExpansion, density),
  );

  const remainingWidth = gridWidth - totalFixedWidth;
  const totalFlex = flexColumns.reduce((sum, col) => sum + (col.flex || 0), 0);

  const newWidths = columns.map((col, index) => {
    if (col.flex && col.flex > 0 && !resizedColumnWidth[col.field]) {
      const flexWidth = (remainingWidth / totalFlex) * col.flex;
      const calcWidth = Math.max(col.minWidth || 0, Math.min(col.maxWidth || Infinity, flexWidth));
      const previousCachedWidth = cachedWidth[index] || 0;
      if (Math.abs(calcWidth - previousCachedWidth) < 1) {
        return previousCachedWidth;
      }
      return calcWidth;
    } else {
      return resizedColumnWidth[col.field] || col.width || 100;
    }
  });

  const totalWidth = newWidths.reduce((sum, w) => sum + w, 0);
  if (Math.abs(totalWidth - gridWidth) < 2) {
    return newWidths.map((w) => Math.floor(w));
  }

  return newWidths;
};
