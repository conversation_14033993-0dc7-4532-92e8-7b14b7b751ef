import { describe, expect, it } from 'vitest';
import {
  validUniqueData,
  validUniqueColumn,
  validProps,
  compareValues,
  deepMerge,
  getGridHeaderHeight,
  getGridRowsHeight,
  getGridPaginationHeight,
  getGridHeight,
  getGridColumnsWidth,
  calculateWidths,
  NonUniqueFieldError,
  EmptyFieldError,
} from './index';

describe('DataGrid Utility Functions', () => {
  describe('validUniqueData', () => {
    it('should return true for unique values', () => {
      const data = [{ id: 1 }, { id: 2 }, { id: 3 }];
      expect(validUniqueData(data)).toBe(true);
    });

    it('should throw EmptyFieldError for undefined values', () => {
      const data = [{ id: 1 }, { id: null }, { id: 3 }];
      expect(() => validUniqueData(data)).toThrow(EmptyFieldError);
    });

    it('should throw NonUniqueFieldError for non-unique values', () => {
      const data = [{ id: 1 }, { id: 2 }, { id: 1 }];
      expect(() => validUniqueData(data)).toThrow(NonUniqueFieldError);
    });
  });

  describe('validUniqueColumn', () => {
    it('should return true for unique columns', () => {
      const columns = [{ field: 'name' }, { field: 'age' }, { field: 'action' }];
      expect(validUniqueColumn(columns)).toBe(true);
    });

    it('should throw EmptyFieldError for undefined values', () => {
      const columns = [{ field: 'name' }, { field: 'age' }, { field: '' }];
      expect(() => validUniqueColumn(columns)).toThrow(EmptyFieldError);
    });

    it('should throw NonUniqueFieldError for non-unique values', () => {
      const columns = [{ field: 'name' }, { field: 'age' }, { field: 'name' }];
      expect(() => validUniqueColumn(columns)).toThrow(NonUniqueFieldError);
    });
  });

  describe('validProps', () => {
    it('should validate props correctly', () => {
      const props: any = { data: [{ id: 1 }, { id: 2 }], uniqueField: 'id', columns: [] };
      expect(validProps(props)).toBeUndefined(); // No error thrown
    });

    it('should throw error if data contains non-unique values', () => {
      const props: any = { data: [{ id: 1 }, { id: 2 }, { id: 1 }], uniqueField: 'id', columns: [] };
      expect(() => validProps(props)).toThrow(NonUniqueFieldError);
    });
    it('should throw error if columns contains non-unique field', () => {
      const props: any = {
        data: [{ id: 1 }, { id: 2 }, { id: 3 }],
        uniqueField: 'id',
        columns: [{ field: 'name' }, { field: 'age' }, { field: 'name' }],
      };
      expect(() => validProps(props)).toThrow(NonUniqueFieldError);
    });
  });

  describe('compareValues', () => {
    it('should compare strings correctly', () => {
      expect(compareValues('a', 'b', 'asc')).toBeLessThan(0);
      expect(compareValues('b', 'a', 'asc')).toBeGreaterThan(0);
      expect(compareValues('a', 'b', 'desc')).toBeGreaterThan(0);
      expect(compareValues('b', 'a', 'desc')).toBeLessThan(0);
    });

    it('should compare numbers correctly', () => {
      expect(compareValues(1, 2, 'asc')).toBeLessThan(0);
      expect(compareValues(2, 1, 'asc')).toBeGreaterThan(0);
      expect(compareValues(1, 2, 'desc')).toBeGreaterThan(0);
      expect(compareValues(2, 1, 'desc')).toBeLessThan(0);
    });

    it('should compare dates correctly', () => {
      const date1 = new Date('2021-01-01');
      const date2 = new Date('2022-01-01');
      expect(compareValues(date1, date2, 'asc')).toBeLessThan(0);
      expect(compareValues(date2, date1, 'asc')).toBeGreaterThan(0);
      expect(compareValues(date1, date2, 'desc')).toBeGreaterThan(0);
      expect(compareValues(date2, date1, 'desc')).toBeLessThan(0);
    });
  });

  describe('deepMerge', () => {
    it('should merge objects correctly', () => {
      const obj1: any = { a: 1, b: { c: 2 } };
      const obj2: any = { b: { d: 3 }, e: 4 };
      const result = deepMerge(obj1, obj2);
      expect(result).toEqual({ a: 1, b: { c: 2, d: 3 }, e: 4 });
    });
    it('should handle null and undefined', () => {
      expect(deepMerge(null, { a: 1 })).toEqual({ a: 1 });
      expect(deepMerge({ a: 1 }, null)).toEqual({ a: 1 });
    });
    it('should deepMerge work', () => {
      const inProps: any = { sortedColumns: [{ field: 'status', sort: 'asc' as const }], selectedRows: [1, 2, 4] };
      expect(
        deepMerge(inProps, {
          pagination: { page: 0, rowsPerPage: 10 },
          sortedColumns: [],
          selectedRows: [],
        }),
      ).toEqual({
        pagination: { page: 0, rowsPerPage: 10 },
        sortedColumns: [{ field: 'status', sort: 'asc' as const }],
        selectedRows: [1, 2, 4],
      });
    });
  });

  describe('getGridHeaderHeight', () => {
    it('should return correct header height based on density', () => {
      expect(getGridHeaderHeight(50, 'compact')).toBe(50);
      expect(getGridHeaderHeight(50, 'standard')).toBe(50);
      expect(getGridHeaderHeight(50, 'comfortable')).toBe(50);
    });

    it('should return default heights when rowHeight is not specified', () => {
      expect(getGridHeaderHeight(undefined, 'compact')).toBe(40);
      expect(getGridHeaderHeight(undefined, 'standard')).toBe(48);
      expect(getGridHeaderHeight(undefined, 'comfortable')).toBe(56);
    });
  });

  describe('getGridRowsHeight', () => {
    it('should calculate total rows height with providing rowHeight', () => {
      expect(getGridRowsHeight(5, 30, 0, 'standard')).toBe(150); // 5 rows * 30px
    });
    it('should calculate total rows height with no rowHeight in standard mode', () => {
      expect(getGridRowsHeight(5, 0, 0, 'standard')).toBe(240); // 5 rows * 48px
    });
    it('should calculate total rows height no rowHeight in comfortable mode', () => {
      expect(getGridRowsHeight(5, 0, 0, 'comfortable')).toBe(280); // 5 rows * 56px
    });
  });

  describe('getGridPaginationHeight', () => {
    it('should return correct pagination height based on density', () => {
      expect(getGridPaginationHeight(0, 'compact')).toBe(40);
      expect(getGridPaginationHeight(0, 'standard')).toBe(48);
      expect(getGridPaginationHeight(0, 'comfortable')).toBe(56);
      expect(getGridPaginationHeight(50, 'compact')).toBe(50);
      expect(getGridPaginationHeight(50, 'standard')).toBe(50);
      expect(getGridPaginationHeight(50, 'comfortable')).toBe(50);
    });
  });

  describe('getGridHeight', () => {
    it('should calculate total table height with default value', () => {
      expect(getGridHeight(5, 0, 0, 0, 0, 'standard')).toBe(336); // 48 + (5 * 48) + 48
    });
    it('should calculate total table height', () => {
      expect(getGridHeight(5, 48, 30, 40, 0, 'standard')).toBe(238); // 48 + (5 * 30) + 40
    });
  });

  describe('getGridColumnsWidth', () => {
    it('should calculate total columns width with undefine selection column', () => {
      expect(getGridColumnsWidth([100, 150], undefined, false, 'standard')).toBe(250);
    });
    it('should calculate total columns width with checkboxSelection', () => {
      expect(getGridColumnsWidth([100, 150], 'checkboxSelection', false, 'compact')).toBe(282);
    });
    it('should calculate total columns width with checkboxSelection', () => {
      expect(getGridColumnsWidth([100, 150], 'checkboxSelection', false, 'standard')).toBe(290);
    });
    it('should calculate total columns width with checkboxSelection', () => {
      expect(getGridColumnsWidth([100, 150], 'checkboxSelection', false, 'comfortable')).toBe(290);
    });
  });

  describe('calculateWidths', () => {
    it('should calculate widths of columns correctly with no selection cell', () => {
      const columns = [
        { field: 'id', width: 100 },
        { field: 'name', flex: 1 },
        { field: 'age', flex: 2, minWidth: 50 },
      ];
      const gridWidth = 400;
      const widths = calculateWidths(columns, gridWidth, undefined, false, 'standard', []);
      expect(widths).toEqual([100, 100, 200]);
    });

    it('should calculate widths of columns correctly with selection cell in standard mode', () => {
      const columns = [
        { field: 'id', width: 100 },
        { field: 'name', flex: 1 },
        { field: 'age', flex: 2, minWidth: 50 },
      ];
      const gridWidth = 400;
      const widths = calculateWidths(columns, gridWidth, 'checkboxSelection', false, 'standard', []);
      // Remaining Flex Width: 400 - 40 - 100 = 260;
      // 1 flex width: 252 / 3 = 86.66666666666667
      expect(widths).toEqual([100, 86.66666666666667, 173.33333333333334]);
    });

    it('should calculate widths of columns correctly with selection cell in compact mode', () => {
      const columns = [
        { field: 'id', width: 100 },
        { field: 'name', flex: 1 },
        { field: 'age', flex: 2, minWidth: 50 },
      ];
      const gridWidth = 400;
      const widths = calculateWidths(columns, gridWidth, 'checkboxSelection', false, 'compact', []);
      // Remaining Flex Width: 400 - 32 - 100 = 268;
      // 1 flex width: 252 / 3 = 89.33333333333333
      expect(widths).toEqual([100, 89.33333333333333, 178.66666666666666]);
    });
  });
});
