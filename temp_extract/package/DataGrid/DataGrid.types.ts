import * as React from 'react';
import { OverrideProps } from '@mui/types';
import { CreateSlotsAndSlotProps, SlotProps } from '../types/slot';
import { InitialState } from './types/state.type';
import { ColumnType } from './types/column.type';
import { SortItem } from './types/sort.type';
import { CellEventHandlers, RowEventHandlers } from './types/event.type';
import { CellType } from './types/cell.type';
import { TablePaginationProps } from '../TablePagination';
import { RowId } from './types/row.type';
import { CellStopEditParas, RowStopEditParas } from './types/edit.type';

export interface DataGridSlots {
  /**
   * The component that renders the root of datagrid.
   * @default 'div'
   */
  root?: React.ElementType;
  /**
   * The component that renders the header of datagrid.
   * @default 'div'
   */
  header?: React.ElementType;
  /**
   * The component that renders the header cell of datagrid .
   * @default 'div'
   */
  headerCell?: React.ElementType;
  /**
   * The component that renders the body of datagrid.
   * @default 'div'
   */
  body?: React.ElementType;
  /**
   * The component that renders the body row of datagrid.
   * @default 'div'
   */
  row?: React.ElementType;
  /**
   * The component that renders the body row cell of datagrid.
   * @default 'div'
   */
  rowCell?: React.ElementType;
}

export type DataGridSlotsAndSlotProps = CreateSlotsAndSlotProps<
  DataGridSlots,
  {
    root: SlotProps<'div', object, DataGridOwnerState>;
    header: SlotProps<'div', object, DataGridOwnerState>;
    headerCell: SlotProps<'div', object, object>;
    body: SlotProps<'div', object, DataGridOwnerState>;
    row: SlotProps<'div', object, object>;
    rowCell: SlotProps<'div', object, object>;
  }
>;

export interface DataGridTypeMap<T = Record<string, any>, D extends React.ElementType = 'div', P = object> {
  props: P & {
    /**
     * The data to be displayed in the DataGrid.
     */
    data: ReadonlyArray<T>;
    /**
     * The total data of DataGrid. Default is length of data. You need pass it once you are using server side pagination.
     */
    total?: number;
    /**
     * Configuration for the columns of the DataGrid.
     */
    columns: ReadonlyArray<ColumnType<T>>;
    /**
     * The unique field for the DataGrid. Default is 'id'.
     * @default 'id'
     */
    uniqueField?: string;
    /**
     * Initial state configuration for the DataGrid.
     */
    initialState?: InitialState;
    /**
     * The visual density of the DataGrid. Options are 'compact', 'standard', or 'comfortable'. Default is 'standard'.
     * @default 'standard'
     */
    density?: 'compact' | 'standard' | 'comfortable';
    /**
     * Flag to display pagination in the DataGrid, it's enabled by default. You can hide it by setting "pagination = false".
     * @default true
     */
    pagination?: boolean;
    /**
     * Additional pagination config
     */
    paginationConfig?: Omit<
      TablePaginationProps,
      'page' | 'count' | 'rowsPerPage' | 'onPageChange' | 'onRowsPerPageChange'
    >;
    /**
     * Determines pagination mode: 'client' for client-side or 'server' for server-side. Default is 'client'.
     * @default 'client'
     */
    paginationMode?: 'client' | 'server';
    /**
     * The zero-based index of the current page.
     */
    page?: number;
    /**
     * Rows number of data items per page.
     */
    rowsPerPage?: number;
    /**
     * Callback function triggered when page changes.
     */
    onPageChange?: (page: number) => void;
    /**
     * Callback function triggered when page size changes.
     */
    onRowsPerPageChange?: (rowsPerPage: number) => void;
    /**
     * Selection mode for rows. Options are 'checkboxSelection' or 'radioSelection'.
     */
    rowSelectionMode?: 'checkboxSelection' | 'radioSelection';
    /**
     * If `true`, the selection on click on a row or cell is disabled.
     * @default false
     */
    disableRowSelectionOnClick?: boolean;
    /**
     * Array of unique identifiers for selected rows.
     */
    selectedRows?: ReadonlyArray<RowId>;
    /**
     * Callback function triggered when row selection changes.
     */
    onSelectedRowsChange?: (rowIds: Array<RowId>) => void;
    /**
     * indicate if row is selectable
     */
    isRowSelectable?: (row: T) => boolean;
    /**
     * Indicate if the cell selection is enabled.
     * @default false
     */
    cellSelection?: boolean;
    /**
     * Array of selected cells.
     */
    selectedCells?: ReadonlyArray<CellType>;
    /**
     * Callback function triggered when cell selection changes.
     */
    onSelectedCellsChange?: (cells: Array<CellType>) => void;
    /**
     * Sort mode for the DataGrid: 'client' for client-side sorting or 'server' for server-side. Default is 'client'.
     * @default 'client'
     */
    sortMode?: 'client' | 'server';
    /**
     * Array of sorted columns.
     */
    sortedColumns?: ReadonlyArray<SortItem>;
    /**
     * Callback function triggered when the sort order changes.
     */
    onSortedColumnsChange?: (sorts: SortItem[]) => void;
    /**
     * The size of the data grid scroll bar in pixels.
     * @default 15
     */
    scrollbarSize?: number;
    /**
     * The header height of DataGrid in pixels, default is based on `density` prop.
     * compact: 40px
     * standard: 48px (default)
     * comfortable: 56px
     */
    headerHeight?: number;
    /**
     * The row height of DataGrid in pixels, default is based on `density` prop.
     * compact: 40px
     * standard: 48px (default)
     * comfortable: 56px
     */
    rowHeight?: number;
    /**
     * Array of unique identifiers for selected rows.
     */
    expandedRows?: ReadonlyArray<RowId>;
    /**
     * Callback once expand rows change
     */
    onExpandedRowsChange?: (rowIds: Array<RowId>) => void;
    /**
     * Render expand row panel
     */
    expandedRowPanelRender?: (row: T) => React.ReactNode;
    /**
     * Get the expanded row height. By default, it will be 200px for each row.
     */
    getExpandedRowHeight?: (row: T) => number;
    /**
     * indicate if row is expandable
     */
    isRowExpandable?: (row: T) => boolean;
    /**
     * indicate the edit mode.
     * @default 'cell'
     */
    editMode?: 'row' | 'cell';
    /**
     * Array of unique identifiers for rows under editing state .
     */
    editingRows?: ReadonlyArray<RowId>;
    /**
     * Callback once edit rows change
     */
    onEditingRowsChange?: (rowIds: Array<RowId>) => void;
    /**
     * Callback fired when the row turns to edit mode.
     */
    onRowEditStart?: (row: T) => void;
    /**
     * Callback fired when the row turns to view mode.
     */
    onRowEditStop?: (paras: RowStopEditParas<T>) => void;
    /**
     * indicate if row is editable
     */
    isRowEditable?: (row: T) => boolean;
    /**
     * Array of unique identifiers for cells under editing state .
     */
    editingCells?: ReadonlyArray<CellType>;
    /**
     * Callback once edit cells change
     */
    onEditingCellsChange?: (cells: Array<CellType>) => void;
    /**
     * Callback fired when the cell turns to edit mode.
     */
    onCellEditStart?: (cell: CellType) => void;
    /**
     * Callback fired when the cell turns to view mode.
     */
    onCellEditStop?: (paras: CellStopEditParas<T>) => void;
    /**
     * indicate if cell is editable
     */
    isCellEditable?: (cell: CellType) => boolean;
    /**
     * Callback called before updating a row with new values in the row and cell editing.
     */
    processRowUpdate?: (newRow: T, originalRow: T) => Promise<T> | T;
    /**
     * Callback called when `processRowUpdate` throws an error or rejects.
     */
    onProcessRowUpdateError?: (error: any, paras: { newRow: T; originalRow: T }) => void;
    /**
     * Row-level event handlers (such as onClick, onMouseLeave)
     */
    rowEvents?: RowEventHandlers<T>;
    /**
     * Cell-level event handlers (such as onClick, onKeyDown)
     */
    cellEvents?: CellEventHandlers<T>;
  } & DataGridSlotsAndSlotProps;
  defaultComponent: D;
}

export type DataGridProps<
  T = Record<string, any>,
  D extends React.ElementType = DataGridTypeMap['defaultComponent'],
  P = { component?: React.ElementType },
> = OverrideProps<DataGridTypeMap<T, D, P>, D>;

export interface DataGridOwnerState<T extends object = any> extends DataGridProps<T> {}
