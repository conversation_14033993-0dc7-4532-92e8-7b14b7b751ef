import { ColumnType } from './column.type';
import { RowId } from './row.type';

export type RowEventParas<T = any> = {
  rowId: RowId;
  rowData: T;
};

export type CellEventParas<T = any> = {
  rowId: RowId;
  columnField: string;
  rowData: T;
  column: ColumnType<T>;
};

type EventTypeMap = {
  onKeyDown: React.KeyboardEvent<HTMLDivElement>;
  onKeyUp: React.KeyboardEvent<HTMLDivElement>;
  onKeyPress: React.KeyboardEvent<HTMLDivElement>;

  onClick: React.MouseEvent<HTMLDivElement>;
  onDoubleClick: React.MouseEvent<HTMLDivElement>;
  onMouseDown: React.MouseEvent<HTMLDivElement>;
  onMouseUp: React.MouseEvent<HTMLDivElement>;
  onMouseEnter: React.MouseEvent<HTMLDivElement>;
  onMouseLeave: React.MouseEvent<HTMLDivElement>;
  onContextMenu: React.MouseEvent<HTMLDivElement>;

  onCopy: React.ClipboardEvent<HTMLDivElement>;
  onCut: React.ClipboardEvent<HTMLDivElement>;
  onPaste: React.ClipboardEvent<HTMLDivElement>;
};

type DefaultEventType = React.SyntheticEvent<HTMLDivElement>;

type EventHandlerWithType<T, K extends keyof React.HTMLAttributes<HTMLDivElement>> = (
  paras: T,
  event: K extends keyof EventTypeMap ? EventTypeMap[K] : DefaultEventType,
) => void;

// Row event type: contains all possible DOM events, parameters include rowId, rowData and event object
export type RowEventHandlers<T = any> = {
  [K in keyof React.HTMLAttributes<HTMLDivElement>]?: EventHandlerWithType<RowEventParas<T>, K>;
};

//Cell event type: parameters include rowId, columnField, rowData and event object
export type CellEventHandlers<T = any> = {
  [K in keyof React.HTMLAttributes<HTMLDivElement>]?: EventHandlerWithType<CellEventParas<T>, K>;
};
