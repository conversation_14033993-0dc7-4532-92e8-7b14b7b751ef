import { CellType } from './cell.type';
import { PaginationState } from './pagination.type';
import { RowId } from './row.type';
import { SortItem } from './sort.type';

export interface InitialState {
  /**
   * Initial pagination state
   */
  pagination?: PaginationState;
  /**
   * Initial sorted columns state
   */
  sortedColumns?: SortItem[];
  /**
   * Initial selected rows state
   */
  selectedRows?: ReadonlyArray<RowId>;
  /**
   * Initial selected rows state
   */
  selectedCells?: ReadonlyArray<CellType>;
  /**
   * Initial expanded rows state
   */
  expandedRows?: ReadonlyArray<RowId>;
}
