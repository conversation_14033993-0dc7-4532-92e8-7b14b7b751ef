import { CSSProperties } from 'react';
import { SortDirection } from './sort.type';
import { CellEditParas } from './edit.type';

export type GridAlignment = 'left' | 'right' | 'center';

export interface ColumnType<T = any> {
  /**
   * The key that corresponds to the value in the data object.
   */
  field: string;
  /**
   * The header title for the column, which can be a function.
   */
  header?: React.ReactNode | ((args: T) => React.ReactNode);
  /**
   * A function for rendering the cell content.
   */
  cell?: (args: T) => React.ReactNode;
  /**
   * A function for rendering the editing cell.
   */
  renderEditCell?: (args: CellEditParas<T>) => React.ReactNode;
  /**
   * The width of the column in pixels.
   * @default 100
   */
  width?: number;
  /**
   * The minimum width of the column in pixels.
   * @default 50
   */
  minWidth?: number;
  /**
   * The maximum width of the column in pixels.
   * @default Infinity
   */
  maxWidth?: number;
  /**
   * The flex grow factor for the column, determining how much the column can grow relative to others. Must be a positive number, otherwise it will be regard as undefined.
   */
  flex?: number;
  /**
   * Align cell content
   */
  align?: GridAlignment;
  /**
   * Align column header content.
   */
  headerAlign?: GridAlignment;
  /**
   * Indicate if current column is visible.
   * @default true
   */
  visible?: boolean;
  /**
   * Indicates whether the column is sortable.
   * @default true
   */
  sortable?: boolean;
  /**
   * Indicates whether the column is resizable.
   * @default true
   */
  resizable?: boolean;
  /**
   * Indicates whether the column is editable.
   *  @default false
   */
  editable?: boolean;
  /**
   * Indicate if the column should fixed at `left` or `right`.
   */
  fixed?: 'left' | 'right';
  /**
   * A custom sorting function for sorting the column data.
   */
  sortFn?: (d1: T, d2: T, sort: SortDirection) => number;
  /**
   * Class name for the cell.
   */
  className?: string | ((args: T) => string);
  /**
   * Class name for the header cell.
   */
  headerClassName?: string;
  /**
   * Style for the column;
   */
  style?: CSSProperties | ((args: T) => CSSProperties);
  /**
   * Style for the header cell.
   */
  headerStyle?: CSSProperties;
}
