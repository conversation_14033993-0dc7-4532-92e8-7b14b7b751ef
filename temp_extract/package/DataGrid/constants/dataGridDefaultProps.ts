import { DataGridProps } from '../DataGrid.types';

export const DATA_GRID_DEFAULT_SCROLLBAR_SIZE = 15;
export const DATA_GRID_DEFAULT_EXPAND_ROW_HEIGHT = 200;
export const DATA_GRID_PROPS_DEFAULT_VALUES: DataGridProps<unknown> = {
  data: [],
  columns: [],
  initialState: {
    pagination: { page: 0, rowsPerPage: 10 },
    sortedColumns: [],
    selectedRows: [],
  },
  density: 'standard' as const,
  uniqueField: 'id',
  sortMode: 'client' as const,
  disableRowSelectionOnClick: false,
  cellSelection: false,
  editMode: 'cell',
  pagination: true,
  paginationMode: 'client' as const,
  scrollbarSize: DATA_GRID_DEFAULT_SCROLLBAR_SIZE,
  slots: {},
  slotProps: {},
};
