# Nova Components

This library provides all of the essential building blocks for creating a Nova application.

## Installation

Add the components package as a dependency to your project:

```bash
yarn add @hxnova/react-components
```

You will also need to install the required peer dependencies:

```bash
# With yarn
yarn add @pigment-css/react @hxnova/themes
```

## Usage

In your typescript file, import the component(s) you want to use. You can import them from the package root, or from the specific component subfolder:

```tsx
// Replace ComponentName with the specific core component you want to use
import { ComponentName } from '@hxnova/react-components';
import { ComponentName } from '@hxnova/react-components/ComponentName';
```

You can learn more about the specific components and their usage by viewing the comprehensive [Nova documentation](https://zeroheight.com/9a7698df1/p/174ac9-overview).