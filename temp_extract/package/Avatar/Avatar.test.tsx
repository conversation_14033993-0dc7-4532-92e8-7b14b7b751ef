import * as React from 'react';
import '@testing-library/jest-dom/vitest';
import { render, screen, cleanup, fireEvent } from '@testing-library/react';
import { describe, expect, it, afterEach, vi } from 'vitest';
import { unstable_capitalize as capitalize } from '@mui/utils';
import { Avatar } from './Avatar.tsx';
import PersonIcon from '../internal/svg-icons/Person';
import classes, { AvatarClassKey } from './Avatar.classes.ts';

afterEach(() => {
  cleanup();
});

describe('<Avatar />', () => {
  describe('prop: size', () => {
    it('standard by default', () => {
      const { getByTestId } = render(<Avatar data-testid="root" />);

      expect(getByTestId('root')).toHaveClass(classes.sizeMedium);
    });
    (['small', 'medium', 'large'] as const).forEach((size) => {
      it(`should render ${size}`, () => {
        const { getByTestId } = render(<Avatar data-testid="root" size={size} />);

        expect(getByTestId('root')).toHaveClass(classes[`size${capitalize(size)}` as AvatarClassKey]);
      });
    });
  });

  describe('image avatar', () => {
    it('should render a div containing an img', () => {
      const { container } = render(
        <Avatar
          data-testid="root"
          className="my-avatar"
          src="/fake.png"
          alt="Hello World!"
          data-my-prop="woofAvatar"
        />,
      );
      const avatar = container.firstChild;
      const img = container.querySelector('img');
      expect(avatar).toHaveClass(classes.root);
      expect(avatar).toHaveClass('my-avatar');
      expect(avatar).toHaveAttribute('data-my-prop', 'woofAvatar');
      expect(img).toHaveClass(classes.img);
      expect(img).toHaveAttribute('alt', 'Hello World!');
      expect(img).toHaveAttribute('src', '/fake.png');
    });

    it('should be able to add more props to the image', () => {
      const onError = vi.fn();
      const { container } = render(<Avatar src="/fake.png" slotProps={{ img: { onError } }} />);
      const img = container.querySelector('img');
      fireEvent.error(img as HTMLImageElement);
      expect(onError).toBeCalledTimes(1);
    });
  });

  describe('image avatar with unrendered children', () => {
    it('should render a div containing an img, not children', () => {
      const { container } = render(<Avatar src="/fake.png">MB</Avatar>);
      const imgs = container.querySelectorAll('img');
      const avatar = container.firstChild;
      expect(imgs.length).to.equal(1);
      expect(avatar).toHaveTextContent('');
    });

    it('should be able to add more props to the image', () => {
      const onError = vi.fn();
      const { container } = render(<Avatar src="/fake.png" slotProps={{ img: { onError } }} />);
      const img = container.querySelector('img');
      fireEvent.error(img as HTMLImageElement);
      expect(onError).toBeCalledTimes(1);
    });
  });

  describe('font icon avatar', () => {
    it('should render a div containing an font icon', () => {
      const { container } = render(
        <Avatar>
          <span className="my-icon-font" data-testid="icon">
            icon
          </span>
        </Avatar>,
      );
      const icon = container.firstChild?.firstChild;
      expect(icon).toHaveClass('my-icon-font');
      expect(icon).toHaveTextContent('icon');
    });

    it('should merge user classes & spread custom props to the root node', () => {
      const { container } = render(
        <Avatar className="my-avatar" data-my-prop="woofAvatar">
          <span>icon</span>
        </Avatar>,
      );
      const avatar = container.firstChild;
      expect(avatar).toHaveClass(classes.root);
      expect(avatar).toHaveClass('my-avatar');
      expect(avatar).toHaveAttribute('data-my-prop', 'woofAvatar');
    });
  });

  describe('svg icon avatar', () => {
    it('should render a div containing an svg icon', () => {
      const { container } = render(
        <Avatar>
          <PersonIcon />
        </Avatar>,
      );
      const avatar = container.firstChild;
      const personIcon = (avatar as ChildNode).firstChild;

      expect(personIcon).toHaveAttribute('data-testid', 'PersonIcon');
    });

    it('should merge user classes & spread custom props to the root node', () => {
      const { container } = render(
        <Avatar className="my-avatar" data-my-prop="woofAvatar">
          <PersonIcon />
        </Avatar>,
      );
      const avatar = container.firstChild;
      expect(avatar).toHaveClass(classes.root);
      expect(avatar).toHaveClass('my-avatar');
      expect(avatar).toHaveAttribute('data-my-prop', 'woofAvatar');
    });
  });

  describe('text avatar', () => {
    it('should render a div containing a string', () => {
      const { container } = render(<Avatar>OT</Avatar>);
      const avatar = container.firstChild;
      expect((avatar as ChildNode).firstChild).toHaveTextContent('OT');
    });

    it('should merge user classes & spread custom props to the root node', () => {
      const { container } = render(
        <Avatar className="my-avatar" data-my-prop="woofAvatar">
          OT
        </Avatar>,
      );
      const avatar = container.firstChild;
      expect(avatar).toHaveClass(classes.root);
      expect(avatar).toHaveClass('my-avatar');
      expect(avatar).toHaveAttribute('data-my-prop', 'woofAvatar');
    });
  });

  describe('falsey avatar', () => {
    it('should render with defaultColor class when supplied with a child with falsey value', () => {
      const { container } = render(<Avatar>{0}</Avatar>);
      const avatar = container.firstChild;
      expect((avatar as ChildNode).firstChild).toHaveTextContent('0');
    });

    it('should merge user classes & spread custom props to the root node', () => {
      const { container } = render(
        <Avatar className="my-avatar" data-my-prop="woofAvatar">
          {0}
        </Avatar>,
      );
      const avatar = container.firstChild;
      expect(avatar).toHaveClass(classes.root);
      expect(avatar).toHaveClass('my-avatar');
      expect(avatar).toHaveAttribute('data-my-prop', 'woofAvatar');
    });
  });

  it('should render first letter of alt when src or srcSet are not available', () => {
    const { container } = render(<Avatar className="my-avatar" alt="Hello World!" />);
    const avatar = container.firstChild;
    expect(avatar).toHaveTextContent('H');
  });
});
