import {
  unstable_generateUtilityClasses as generateUtilityClasses,
  unstable_generateUtilityClass as generateUtilityClass,
} from '@mui/utils';

export interface AvatarClasses {
  /** Styles applied to the root element. */
  root: string;
  /** Styles applied to the root element if `size="small"`. */
  sizeSmall: string;
  /** Styles applied to the root element if `size="medium"`. */
  sizeMedium: string;
  /** Styles applied to the root element if `size="large"`. */
  sizeLarge: string;
  /** Styles applied to the img element if either `src` or `srcSet` is defined. */
  img: string;
  /** Styles applied to the fallback icon */
  fallback: string;
  /** Styles applied to the root element if the link is keyboard focused. */
  focusVisible: string;
}

export type AvatarClassKey = keyof AvatarClasses;

export function getAvatarUtilityClass(slot: string): string {
  return generateUtilityClass('NovaAvatar', slot);
}

const avatarClasses: AvatarClasses = generateUtilityClasses('NovaAvatar', [
  'root',
  'sizeSmall',
  'sizeMedium',
  'sizeLarge',
  'img',
  'fallback',
  'focusVisible',
]);

export default avatarClasses;
