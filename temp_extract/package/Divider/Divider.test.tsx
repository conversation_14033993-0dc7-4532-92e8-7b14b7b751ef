import React from 'react';
import '@testing-library/jest-dom/vitest';
import { screen, render } from '@testing-library/react';
import { expect, describe, it } from 'vitest';
import classes from './Divider.classes';
import { Divider } from './Divider';

describe('Nova <Divider />', () => {
  describe('prop: inset', () => {
    it('should add inset class', () => {
      const { container } = render(<Divider variant="inset" />);
      expect(container.firstChild).toHaveClass(classes.inset);
    });
  });

  describe('role', () => {
    it('avoids adding implicit aria semantics', () => {
      const { container } = render(<Divider />);
      expect(container.firstChild).not.toHaveAttribute('role');
    });

    it('adds a proper role if none is specified', () => {
      const { container } = render(<Divider component="div" />);
      expect(container.firstChild).toHaveAttribute('role', 'separator');
    });

    it('overrides the computed role with the provided one', () => {
      const { container } = render(<Divider role="presentation" />);
      expect(container.firstChild).toHaveAttribute('role', 'presentation');
    });
  });
});
