import { OverridableStringUnion, OverrideProps } from '@mui/types';
import { CreateSlotsAndSlotProps, SlotProps } from '../types/slot';
import { SxProps } from '../types/theme';

export type DividerSlot = 'root';

export interface DividerSlots {
  /**
   * The component that renders the root.
   * @default 'hr'
   */
  root?: React.ElementType;
}

export type DividerSlotsAndSlotProps = CreateSlotsAndSlotProps<
  DividerSlots,
  {
    root: SlotProps<'hr', object, DividerOwnerState>;
  }
>;

export interface DividerPropsVariantOverrides {}

export interface DividerTypeMap<P = object, D extends React.ElementType = 'hr'> {
  props: P & {
    /**
     * The content of the component.
     */
    children?: React.ReactNode;
    /**
     * The variant to use.
     * @default 'fullWidth'
     */
    variant?: OverridableStringUnion<'fullWidth' | 'inset', DividerPropsVariantOverrides>;
    /**
     * The component orientation.
     * @default 'horizontal'
     */
    orientation?: 'horizontal' | 'vertical';
    /**
     * The system prop that allows defining system overrides as well as additional CSS styles.
     */
    sx?: SxProps;
  } & DividerSlotsAndSlotProps;
  defaultComponent: D;
}

export type DividerProps<
  D extends React.ElementType = DividerTypeMap['defaultComponent'],
  P = {
    component?: React.ElementType;
  },
> = OverrideProps<DividerTypeMap<P, D>, D>;

export interface DividerOwnerState extends DividerProps {}
