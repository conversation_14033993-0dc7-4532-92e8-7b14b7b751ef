import React from 'react';
import { unstable_composeClasses as composeClasses, unstable_capitalize as capitalize } from '@mui/utils';
import useSlotProps from '@mui/utils/useSlotProps';
import { styled } from '@pigment-css/react';
import { DividerOwnerState, DividerProps } from './Divider.types';
import { getDividerUtilityClass } from './Divider.classes';

const useUtilityClasses = (ownerState: DividerOwnerState) => {
  const { orientation, variant } = ownerState;
  const slots = {
    root: ['root', orientation, variant],
  };
  return composeClasses(slots, getDividerUtilityClass, {});
};

export const DividerRoot = styled('hr')<DividerProps>(({ theme }) => ({
  '--nova-divider-thickness': '1px',
  '--nova-divider-lineColor': theme.vars.palette.outlineVariant,
  margin: 'initial', // reset margin for `hr` tag
  marginInline: 'var(--nova-divider-inset)',
  marginBlock: 'initial',
  position: 'relative',
  alignSelf: 'stretch',
  flexShrink: 0,
  inlineSize: 'initial',
  blockSize: 'var(--nova-divider-thickness)',
  variants: [
    {
      props: { orientation: 'vertical' },
      style: {
        marginInline: 'initial',
        marginBlock: 'var(--nova-divider-inset)',
        inlineSize: 'var(--nova-divider-thickness)',
        blockSize: 'initial',
      },
    },
    {
      props: { variant: 'fullWidth' },
      style: {
        '--nova-divider-inset': '0px',
      },
    },
    {
      props: { variant: 'inset' },
      style: {
        '--nova-divider-inset': '16px',
      },
    },
    {
      props: (props) => props.children === undefined,
      style: {
        border: 'none', // reset the border for `hr` tag
        listStyle: 'none',
        backgroundColor: 'var(--nova-divider-lineColor)',
      },
    },
    {
      props: (props) => props.children !== undefined,
      style: {
        '--nova-divider-gap': '8px',
        '--nova-divider-childPosition': '50%',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        whiteSpace: 'nowrap',
        textAlign: 'center',
        border: 0,
        ...theme.typography.bodySmall,
        '&::before, &::after': {
          display: 'inline-block',
          position: 'relative',
          content: '""',
          backgroundColor: 'var(--nova-divider-lineColor)',
        },
        '&::before': { flexBasis: 'var(--nova-divider-childPosition)' },
        '&::after': { flexBasis: 'calc(100% - var(--nova-divider-childPosition))' },
      },
    },
    {
      props: (props) => props.children !== undefined && props.orientation === 'vertical',
      style: {
        '&::before, &::after': {
          inlineSize: 'var(--nova-divider-thickness)',
          blockSize: 'initial',
        },
        '&::before': {
          marginInlineEnd: 'initial',
          marginBlockEnd: 'min(var(--nova-divider-childPosition) * 999, var(--nova-divider-gap))',
        },
        '&::after': {
          marginInlineStart: 'initial',
          marginBlockStart: 'min((100% - var(--nova-divider-childPosition)) * 999, var(--nova-divider-gap))',
        },
      },
    },
    {
      props: (props) => props.children !== undefined && props.orientation === 'horizontal',
      style: {
        flexDirection: 'row',
        '&::before, &::after': {
          inlineSize: 'initial',
          blockSize: 'var(--nova-divider-thickness)',
        },
        '&::before': {
          marginInlineEnd: 'min(var(--nova-divider-childPosition) * 999, var(--nova-divider-gap))',
          marginBlockEnd: 'initial',
        },
        '&::after': {
          marginInlineStart: 'min((100% - var(--nova-divider-childPosition)) * 999, var(--nova-divider-gap))',
          marginBlockStart: 'initial',
        },
      },
    },
  ],
}));

export const Divider = React.forwardRef(function Divider(props: DividerProps, ref) {
  const {
    className,
    children,
    component = children !== undefined && children !== null ? 'div' : 'hr',
    variant = 'fullWidth',
    orientation = 'horizontal',
    role = component !== 'hr' ? 'separator' : undefined,
    slots = {},
    slotProps = {},
    ...other
  } = props;

  const ownerState = {
    ...props,
    variant,
    role,
    orientation,
    component,
  };

  const classes = useUtilityClasses(ownerState);
  const SlotRoot = slots.root ?? DividerRoot;

  const rootProps = useSlotProps({
    additionalProps: {
      ref,
      as: component,
      role,
      ...(role === 'separator' &&
        orientation === 'vertical' && {
          'aria-orientation': 'vertical',
        }),
    },
    className: [classes.root, className],
    elementType: DividerRoot,
    externalSlotProps: slotProps.root,
    externalForwardedProps: other,
    ownerState,
  });

  return <SlotRoot {...rootProps}>{children}</SlotRoot>;
});
