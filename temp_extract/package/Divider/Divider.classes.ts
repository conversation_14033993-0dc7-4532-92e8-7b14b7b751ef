import {
  unstable_generateUtilityClasses as generateUtilityClasses,
  unstable_generateUtilityClass as generateUtilityClass,
} from '@mui/utils';

export interface DividerClasses {
  /** Styles applied to the root element. */
  root: string;
  /** Styles applied to the root element if `orientation="horizontal"`. */
  horizontal: string;
  /** Styles applied to the root element if `orientation="vertical"`. */
  vertical: string;
  /** Styles applied to the root element if `variant="inset"`. */
  inset: string;
  /** Styles applied to the root element if `variant="fullWidth"`. */
  fullWidth: string;
}

export type DividerClassKey = keyof DividerClasses;

export function getDividerUtilityClass(slot: string): string {
  return generateUtilityClass('NovaDivider', slot);
}

const DividerClasses: DividerClasses = generateUtilityClasses('NovaDivider', [
  'root',
  'horizontal',
  'vertical',
  'fullWidth',
  'inset',
]);

export default DividerClasses;
