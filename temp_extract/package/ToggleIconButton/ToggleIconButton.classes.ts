import {
  unstable_generateUtilityClasses as generateUtilityClasses,
  unstable_generateUtilityClass as generateUtilityClass,
} from '@mui/utils';

export interface ToggleIconButtonClasses {
  /** Styles applied to the root element. */
  root: string;

  /** Styles applied to the ToggleIconButton if `variant="filled"`. */
  filled: string;
  /** Styles applied to the ToggleIconButton if `variant="outlined"`. */
  outlined: string;
  /** Styles applied to the ToggleIconButton if `variant="standard"`. */
  standard: string;
  /** Styles applied to the ToggleIconButton if `variant="neutral"`. */
  neutral: string;

  /** Styles applied to the ToggleIconButton if `selected={true}`. */
  selected: string;
}

export type ToggleIconButtonClassKey = keyof ToggleIconButtonClasses;

export function getToggleIconButtonUtilityClass(slot: string): string {
  return generateUtilityClass('NovaToggleIconButton', slot);
}

const toggleIconButtonClasses: ToggleIconButtonClasses = generateUtilityClasses('NovaToggleIconButton', [
  'root',
  'filled',
  'outlined',
  'standard',
  'neutral',
  'selected',
]);

export default toggleIconButtonClasses;
