'use client';
import React from 'react';
import { styled } from '@pigment-css/react';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import { ToggleIconButtonProps } from './ToggleIconButton.types';
import { getToggleIconButtonUtilityClass } from './ToggleIconButton.classes';
import { ButtonBase } from '../ButtonBase';
import clsx from 'clsx';

const useUtilityClasses = (ownerState: ToggleIconButtonProps) => {
  const { variant, selected } = ownerState;

  const slots = {
    root: ['root', variant, selected && 'selected'],
  };

  return composeClasses(slots, getToggleIconButtonUtilityClass, {});
};

const ToggleIconButtonBase = styled(ButtonBase)<ToggleIconButtonProps>(({ theme }) => ({
  justifyContent: 'center',
  fontSize: 24,
  width: '2.5rem',
  padding: 0,
  variants: [
    // Size Styles
    {
      props: { size: 'small' },
      style: {
        fontSize: 20,
        width: '2rem',
      },
    },
    {
      props: { size: 'large' },
      style: {
        fontSize: 28,
        width: '3rem',
      },
    },
    // Filled Styles
    {
      props: { variant: 'filled', selected: false },
      style: {
        color: theme.vars.palette.primary,
        backgroundColor: theme.vars.palette.surfaceContainerHighest,
        '&:hover': {
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.surfaceContainerHighest}, ${theme.vars.palette.primary} ${theme.vars.palette.stateLayers.hoverPrimary})`,
        },
        '&:focus-visible': {
          outline: `2px solid ${theme.vars.palette.primary}`,
          outlineOffset: 2,
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.surfaceContainerHighest}, ${theme.vars.palette.primary} ${theme.vars.palette.stateLayers.focusPrimary})`,
        },
        '&:active': {
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.surfaceContainerHighest}, ${theme.vars.palette.primary} ${theme.vars.palette.stateLayers.pressPrimary})`,
        },
      },
    },
    {
      props: { variant: 'filled', selected: true },
      style: {
        color: theme.vars.palette.onPrimary,
        backgroundColor: theme.vars.palette.primary,
        '&:hover': {
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.primary}, ${theme.vars.palette.onPrimary} ${theme.vars.palette.stateLayers.hoverOnPrimary})`,
        },
        '&:focus-visible': {
          outline: `2px solid ${theme.vars.palette.primary}`,
          outlineOffset: 2,
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.primary}, ${theme.vars.palette.onPrimary} ${theme.vars.palette.stateLayers.focusOnPrimary})`,
        },
        '&:active': {
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.primary}, ${theme.vars.palette.onPrimary} ${theme.vars.palette.stateLayers.pressOnPrimary})`,
        },
      },
    },
    // Outlined Styles
    {
      props: { variant: 'outlined', selected: false },
      style: {
        borderWidth: 1,
        borderStyle: 'solid',
        borderColor: theme.vars.palette.primary,
        backgroundColor: 'transparent',
        color: theme.vars.palette.primary,
        '&:hover': {
          backgroundColor: `color-mix(in srgb, transparent, ${theme.vars.palette.primary} ${theme.vars.palette.stateLayers.hoverPrimary})`,
        },
        '&:focus-visible': {
          outline: `2px solid ${theme.vars.palette.primary}`,
          outlineOffset: 2,
          backgroundColor: `color-mix(in srgb, transparent, ${theme.vars.palette.primary} ${theme.vars.palette.stateLayers.focusPrimary})`,
        },
        '&:active': {
          backgroundColor: `color-mix(in srgb, transparent, ${theme.vars.palette.primary} ${theme.vars.palette.stateLayers.pressPrimary})`,
        },
      },
    },
    {
      props: { variant: 'outlined', selected: true },
      style: {
        color: theme.vars.palette.onPrimary,
        backgroundColor: theme.vars.palette.primary,
        '&:hover': {
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.primary}, ${theme.vars.palette.onPrimary} ${theme.vars.palette.stateLayers.hoverOnPrimary})`,
        },
        '&:focus-visible': {
          outline: `2px solid ${theme.vars.palette.primary}`,
          outlineOffset: 2,
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.primary}, ${theme.vars.palette.onPrimary} ${theme.vars.palette.stateLayers.focusOnPrimary})`,
        },
        '&:active': {
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.primary}, ${theme.vars.palette.onPrimary} ${theme.vars.palette.stateLayers.pressOnPrimary})`,
        },
      },
    },
    // Standard Styles
    {
      props: { variant: 'neutral' },
      style: {
        color: theme.vars.palette.onSurface,
        '&:hover': {
          backgroundColor: `color-mix(in srgb, transparent, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.hoverOnSurface})`,
        },
        '&:focus-visible': {
          outline: `2px solid ${theme.vars.palette.secondary}`,
          outlineOffset: 2,
          backgroundColor: `color-mix(in srgb, transparent, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.focusOnSurface})`,
        },
        '&:active': {
          backgroundColor: `color-mix(in srgb, transparent, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.pressOnSurface})`,
        },
      },
    },
    {
      props: { variant: 'standard' },
      style: {
        color: theme.vars.palette.primary,
        '&:hover': {
          backgroundColor: `color-mix(in srgb, transparent, ${theme.vars.palette.primary} ${theme.vars.palette.stateLayers.hoverPrimary})`,
        },
        '&:focus-visible': {
          outline: `2px solid ${theme.vars.palette.primary}`,
          outlineOffset: 2,
          backgroundColor: `color-mix(in srgb, transparent, ${theme.vars.palette.primary} ${theme.vars.palette.stateLayers.focusPrimary})`,
        },
        '&:active': {
          backgroundColor: `color-mix(in srgb, transparent, ${theme.vars.palette.primary} ${theme.vars.palette.stateLayers.pressPrimary})`,
        },
      },
    },
    // Disabled Styles
    {
      props: { disabled: true },
      style: {
        backgroundColor: `color-mix(in srgb, ${theme.vars.palette.backgroundDisabled}, ${theme.vars.palette.onBackgroundDisabled} ${theme.vars.palette.stateLayers.disabled})`,
        color: theme.vars.palette.onBackgroundDisabled,
        border: 'none',
      },
    },
    {
      props: { disabled: true, variant: 'outlined' },
      style: {
        backgroundColor: `color-mix(in srgb, transparent, ${theme.vars.palette.onBackgroundDisabled} ${theme.vars.palette.stateLayers.disabled})`,
        border: `1px solid ${theme.vars.palette.outlineDisabled}`,
      },
    },
    {
      props: { disabled: true, variant: 'standard' },
      style: {
        background: 'transparent',
      },
    },
    {
      props: { disabled: true, variant: 'neutral' },
      style: {
        background: 'transparent',
      },
    },
  ],
}));

export const ToggleIconButton = (props: ToggleIconButtonProps) => {
  const defaultProps: Partial<ToggleIconButtonProps> = {
    variant: 'filled',
    selected: false,
  };

  const mergedProps = { ...defaultProps, ...props };
  const classes = useUtilityClasses(mergedProps);

  const { children, className, selected, ...otherProps } = mergedProps;

  const childrenWidthFontSize = React.isValidElement(children)
    ? React.cloneElement(children as React.ReactElement, {
        fontSize: children.props.fontSize ?? 'inherit',
        filled: children.props.filled ?? selected,
      })
    : children;

  return (
    <ToggleIconButtonBase className={clsx(classes.root, className)} selected={selected} {...otherProps}>
      {childrenWidthFontSize}
    </ToggleIconButtonBase>
  );
};
