import React from 'react';
import { render, cleanup } from '@testing-library/react';
import { expect, test, afterEach } from 'vitest';
import { ToggleIconButton } from './ToggleIconButton';

afterEach(() => {
  cleanup();
});

test('by default, should render with the root, filled and sizeMedium classes', async () => {
  const { getByRole } = render(<ToggleIconButton>Icon</ToggleIconButton>);
  const button = getByRole('button');

  expect(button).toHaveClass('NovaToggleIconButton-root');
  expect(button).toHaveClass('NovaToggleIconButton-filled');
  expect(button).toHaveClass('NovaButtonBase-sizeMedium');
});

test('should render a outlined button', () => {
  const { getByRole } = render(<ToggleIconButton variant="outlined">Icon</ToggleIconButton>);
  const button = getByRole('button');

  expect(button).toHaveClass('NovaToggleIconButton-outlined');
});

test('should render a standard button', () => {
  const { getByRole } = render(<ToggleIconButton variant="standard">Icon</ToggleIconButton>);
  const button = getByRole('button');

  expect(button).toHaveClass('NovaToggleIconButton-standard');
});

test('should render a neutral button', () => {
  const { getByRole } = render(<ToggleIconButton variant="neutral">Icon</ToggleIconButton>);
  const button = getByRole('button');

  expect(button).toHaveClass('NovaToggleIconButton-neutral');
});
// render tests for size
test('should render a medium size button', () => {
  const { getByRole } = render(<ToggleIconButton size="medium">Icon</ToggleIconButton>);
  const button = getByRole('button');

  expect(button).toHaveClass('NovaButtonBase-sizeMedium');
});

test('should render a small size button', () => {
  const { getByRole } = render(<ToggleIconButton size="small">Icon</ToggleIconButton>);
  const button = getByRole('button');

  expect(button).toHaveClass('NovaButtonBase-sizeSmall');
});

test('should render a large size button', () => {
  const { getByRole } = render(<ToggleIconButton size="large">Icon</ToggleIconButton>);
  const button = getByRole('button');

  expect(button).toHaveClass('NovaButtonBase-sizeLarge');
});

test('should render a selected button', () => {
  const { getByRole } = render(<ToggleIconButton selected>Icon</ToggleIconButton>);
  const button = getByRole('button');

  expect(button).toHaveClass('Mui-selected');
});

test('should render a disabled button', () => {
  const { getByRole } = render(<ToggleIconButton disabled>Icon</ToggleIconButton>);
  const button = getByRole('button');

  expect(button).toHaveClass('Mui-disabled');
});

test('should render a button with a custom class', () => {
  const { getByRole } = render(<ToggleIconButton className="custom">Icon</ToggleIconButton>);
  const button = getByRole('button');

  expect(button).toHaveClass('custom');
});
