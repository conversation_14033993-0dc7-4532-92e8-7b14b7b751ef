import { ButtonBaseProps } from '../ButtonBase';

export type ToggleIconButtonVariant = 'filled' | 'outlined' | 'standard' | 'neutral';

export interface ToggleIconButtonProps extends ButtonBaseProps {
  /**
   * The style of toggle icon button to use
   * @default 'filled'
   */
  variant?: ToggleIconButtonVariant;
  /**
   * Whether the component is selected
   * @default false
   */
  selected?: boolean;
}
