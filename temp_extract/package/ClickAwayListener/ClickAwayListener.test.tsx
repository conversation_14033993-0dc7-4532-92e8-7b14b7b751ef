import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, expect, it, vi } from 'vitest';
import { ClickAwayListener } from './ClickAwayListener';

describe('ClickAwayListener', () => {
  it('should render the children', () => {
    const children = <span>Hello World!</span>;
    render(<ClickAwayListener onClickAway={() => {}}>{children}</ClickAwayListener>);
    expect(screen.getByText('Hello World!')).toBeInTheDocument();
  });

  it('should be called when clicking away', async () => {
    const handleClickAway = vi.fn();
    render(
      <ClickAwayListener onClickAway={handleClickAway}>
        <span />
      </ClickAwayListener>,
    );

    await userEvent.click(document.body);
    expect(handleClickAway).toBeCalled();
  });

  it('should not be called when clicking inside', async () => {
    const handleClickAway = vi.fn();
    render(
      <ClickAwayListener onClickAway={handleClickAway}>
        <span data-testid="test" />
      </ClickAwayListener>,
    );
    await userEvent.click(screen.getByTestId('test'));
    expect(handleClickAway).not.toHaveBeenCalled();
  });

  it('should be called when preventDefault is `true`', async () => {
    const handleClickAway = vi.fn();
    render(
      <ClickAwayListener onClickAway={handleClickAway}>
        <span />
      </ClickAwayListener>,
    );
    const preventDefault = (event) => event.preventDefault();
    document.body.addEventListener('click', preventDefault);
    await userEvent.click(document.body);
    expect(handleClickAway).toBeCalled();
    document.body.removeEventListener('click', preventDefault);
  });
});
