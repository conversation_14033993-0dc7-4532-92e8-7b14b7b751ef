import { CreateSlotsAndSlotProps, SlotProps } from '../types/slot';
import { OverrideProps } from '@mui/types';
import { ListItemButtonProps } from '../ListItemButton';

export type MenuItemSlot = 'root';

export interface MenuItemSlots {
  /**
   * The component that renders the root.
   * @default 'div'
   */
  root?: React.ElementType;
}

export type MenuItemSlotsAndSlotProps = CreateSlotsAndSlotProps<
  MenuItemSlots,
  {
    root: SlotProps<'div', object, MenuItemOwnerState>;
  }
>;

export interface MenuItemTypeMap<P = object, D extends React.ElementType = 'div'> {
  props: P & MenuItemSlotsAndSlotProps & Omit<ListItemButtonProps, 'slots' | 'slotProps'>;
  defaultComponent: D;
}

export type MenuItemProps<
  D extends React.ElementType = MenuItemTypeMap['defaultComponent'],
  P = {
    component?: React.ElementType;
  },
> = OverrideProps<MenuItemTypeMap<P, D>, D>;

export interface MenuItemOwnerState extends MenuItemProps {
  /**
   * If `true`, the element's focus is visible.
   */
  focusVisible?: boolean;
}
