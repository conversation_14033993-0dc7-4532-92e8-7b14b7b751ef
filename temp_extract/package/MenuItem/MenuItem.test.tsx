import React from 'react';
import '@testing-library/jest-dom/vitest';
import { screen, render, fireEvent } from '@testing-library/react';
import { expect, describe, it, vi } from 'vitest';
import classes from './MenuItem.classes';
import { MenuItem } from './MenuItem';
import { MenuProvider, MenuProviderValue } from '../internal/hooks/useMenu';

const testContext: MenuProviderValue = {
  registerItem: () => ({ id: '0', deregister: () => {} }),
  getItemIndex: () => 0,
  totalSubitemCount: 1,
  dispatch: () => {},
  getItemState: () => ({
    highlighted: false,
    selected: false,
    focusable: false,
  }),
};

function renderMenuItem(item) {
  return render(<MenuProvider value={testContext}>{item}</MenuProvider>);
}

describe('<MenuItem />', () => {
  it('should render a focusable menuitem', () => {
    renderMenuItem(<MenuItem />);
    const menuitem = screen.getByRole('menuitem');

    expect(menuitem).toHaveProperty('tabIndex', -1);
  });

  it('should render with the selected class but not aria-selected when `selected`', () => {
    renderMenuItem(<MenuItem selected />);
    const menuitem = screen.getByRole('menuitem');

    expect(menuitem).toHaveClass(classes.selected);
    expect(menuitem).not.toHaveAttribute('aria-selected');
  });

  it('can have a role of option', () => {
    renderMenuItem(<MenuItem role="option" aria-selected={false} />);

    expect(screen.queryByRole('option')).not.toEqual(null);
  });

  describe('event callbacks', () => {
    const events: Array<keyof typeof fireEvent> = [
      'click',
      'mouseDown',
      'mouseEnter',
      'mouseLeave',
      'mouseUp',
      'touchEnd',
    ];

    events.forEach((eventName) => {
      it(`should fire ${eventName}`, () => {
        const handlerName = `on${eventName[0].toUpperCase()}${eventName.slice(1)}`;
        const handler = vi.fn();
        renderMenuItem(<MenuItem {...{ [handlerName]: handler }} />);

        fireEvent[eventName](screen.getByRole('menuitem'));

        expect(handler).toBeCalledTimes(1);
      });
    });

    it('can be disabled', () => {
      renderMenuItem(<MenuItem disabled />);
      const menuitem = screen.getByRole('menuitem');
      expect(menuitem).toHaveClass(classes.disabled);
    });

    it('can be selected', () => {
      renderMenuItem(<MenuItem selected />);
      const menuitem = screen.getByRole('menuitem');
      expect(menuitem).toHaveClass(classes.selected);
    });
  });
});
