import {
  unstable_generateUtilityClasses as generateUtilityClasses,
  unstable_generateUtilityClass as generateUtilityClass,
} from '@mui/utils';

export interface MenuItemClasses {
  /** Styles applied to the root element. */
  root: string;
  /** Styles applied to the root element if the link is keyboard focused. */
  focusVisible: string;
  /** Styles applied to the inner `component` element if `disabled={true}`. */
  disabled: string;
  /** Styles applied to the root element if `selected={true}`. */
  selected: string;
}

export type MenuItemClassKey = keyof MenuItemClasses;

export function getMenuItemUtilityClass(slot: string): string {
  return generateUtilityClass('NovaMenuItem', slot);
}

const menuItemClasses: MenuItemClasses = generateUtilityClasses('NovaMenuItem', [
  'root',
  'focusVisible',
  'disabled',
  'selected',
]);

export default menuItemClasses;
