'use client';
import * as React from 'react';
import { styled } from '@pigment-css/react';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import useSlotProps from '@mui/utils/useSlotProps';
import useForkRef from '@mui/utils/useForkRef';
import { useMenuItem } from '../internal/hooks/useMenuItem';
import { MenuItemOwnerState, MenuItemProps } from './MenuItem.types';
import { getMenuItemUtilityClass } from './MenuItem.classes';
import { StyledListItemButton } from '../ListItemButton/ListItemButton';
import ListItemStatusContext from '../ListItem/ListItemStatusContext';
import ListItemButtonOrientationContext from '../ListItemButton/ListItemButtonOrientationContext';

const useUtilityClasses = (ownerState: MenuItemOwnerState) => {
  const { disabled, selected, focusVisible } = ownerState;
  const slots = {
    root: ['root', focusVisible && 'focusVisible', disabled && 'disabled', selected && 'selected'],
  };

  const composedClasses = composeClasses(slots, getMenuItemUtilityClass, {});
  return composedClasses;
};

const MenuItemRoot = styled(StyledListItemButton)<MenuItemProps>(({ theme }) => ({
  border: 'none',
  gap: 'var(--nova-listItem-gap)',
  '&:focus-visible': {
    backgroundColor: 'var(--nova-listItemButton-focus-background)',
  },
  variants: [
    {
      props: { selected: true },
      style: {
        backgroundColor: theme.vars.palette.secondaryContainer,
        '&:hover': {
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.surfaceContainerHighest}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.hoverOnSurface})`,
        },
        '&:focus-visible': {
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.surfaceContainerHighest}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.focusOnSurface})`,
        },
        '&:active': {
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.surfaceContainerHighest}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.pressOnSurface})`,
        },
        '--nova-listItem-color': theme.vars.palette.onSecondaryContainer,
        '--nova-listItem-secondaryColor': theme.vars.palette.onSecondaryContainer,
      },
    },
    {
      props: { disabled: true },
      style: {
        backgroundColor: `var(--nova-listItemButton-disabled-background)`,
        '--nova-listItem-color': theme.vars.palette.onBackgroundDisabled,
        '--nova-listItem-secondaryColor': theme.vars.palette.onBackgroundDisabled,
        '&:hover, &:active': {
          backgroundColor: `var(--nova-listItemButton-disabled-background)`,
        },
        outline: 0,
        cursor: 'default',
      },
    },
    {
      props: { selected: true, disabled: true },
      style: {
        backgroundColor: `color-mix(in srgb, ${theme.vars.palette.surfaceContainerHighest}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.disabledOnSurface})`,
        '&:hover, &:active': {
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.surfaceContainerHighest}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.disabledOnSurface})`,
        },
      },
    },
  ],
}));

export const MenuItem = React.forwardRef(function MenuItem(props: MenuItemProps, ref: React.ForwardedRef<Element>) {
  const {
    children,
    disabled: disabledProp,
    selected = false,
    component = 'li',
    orientation = 'horizontal',
    slots = {},
    slotProps = {},
    id,
    ...other
  } = props;

  const menuItemRef = React.useRef<HTMLElement>(null);
  const handleRef = useForkRef(menuItemRef, ref);

  const { getRootProps, disabled, focusVisible } = useMenuItem({
    id,
    disabled: disabledProp,
    rootRef: handleRef,
  });

  const ownerState = {
    ...props,
    disabled,
    selected,
    focusVisible,
  };

  const classes = useUtilityClasses(ownerState);
  const SlotRoot = slots.root ?? MenuItemRoot;

  const rootProps = useSlotProps({
    additionalProps: {
      ref,
      as: component,
    },
    elementType: MenuItemRoot,
    getSlotProps: getRootProps,
    externalSlotProps: slotProps.root,
    externalForwardedProps: other,
    className: classes.root,
    ownerState,
  });
  return (
    <ListItemButtonOrientationContext.Provider value={orientation}>
      <ListItemStatusContext.Provider value={{ disabled, checked: selected }}>
        <SlotRoot {...rootProps}>{children}</SlotRoot>
      </ListItemStatusContext.Provider>
    </ListItemButtonOrientationContext.Provider>
  );
});
