import * as React from 'react';
import { OverrideProps } from '@mui/types';
import { CreateSlotsAndSlotProps, SlotProps } from '../types/slot';
import { SxProps } from '../types/theme';

export interface SegmentedButtonGroupRootSlotPropsOverrides {}

export interface SegmentedButtonGroupSlots {
  /**
   * The component that renders the root.
   * @default 'div'
   */
  root?: React.ElementType;
}

export type RadioGroupSlotsAndSlotProps = CreateSlotsAndSlotProps<
  SegmentedButtonGroupSlots,
  {
    root: SlotProps<'div', SegmentedButtonGroupRootSlotPropsOverrides, SegmentedButtonGroupOwnerState>;
  }
>;

export interface SegmentedButtonGroupTypeMap<P = object, D extends React.ElementType = 'div'> {
  props: P &
    RadioGroupSlotsAndSlotProps & {
      /**
       * If `true`, only allow one of the child SegmentedButton values to be selected.
       * @default false
       */
      exclusive?: boolean;
      /**
       * If `true`, the component is disabled. This implies that all SegmentedButton children will be disabled.
       * @default false
       */
      disabled?: boolean;
      /**
       * Callback fired when the value changes.
       *
       * @param {React.MouseEvent<HTMLElement>} event The event source of the callback.
       * @param {any} value of the selected buttons. When `exclusive` is true
       * this is a single value; when false an array of selected values. If no value
       * is selected and `exclusive` is true the value is null; when false an empty array.
       */
      onChange?: (event: React.MouseEvent<HTMLElement>, value: any) => void;
      /**
       * The component orientation.
       * @default 'horizontal'
       */
      orientation?: 'horizontal' | 'vertical';
      /**
       * How large the SegmentedButtonGroup contents should be.
       * @default 'medium'
       */
      size?: 'small' | 'medium' | 'large';
      /**
       * The currently selected value within the group or an array of selected
       * values when `exclusive` is false.
       *
       * The value must have reference equality with the option in order to be selected.
       */
      value?: any;
      /**
       * The system prop that allows defining system overrides as well as additional CSS styles.
       */
      sx?: SxProps;
    };
  defaultComponent: D;
}

export type SegmentedButtonGroupProps<
  D extends React.ElementType = SegmentedButtonGroupTypeMap['defaultComponent'],
  P = { component?: React.ElementType },
> = OverrideProps<SegmentedButtonGroupTypeMap<P, D>, D>;

export interface SegmentedButtonGroupOwnerState extends SegmentedButtonGroupProps {}
