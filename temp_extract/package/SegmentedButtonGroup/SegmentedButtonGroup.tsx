'use client';

import * as React from 'react';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import useSlotProps from '@mui/utils/useSlotProps';
import { styled } from '@pigment-css/react';
import { getSegmentedButtonGroupUtilityClass } from './SegmentedButtonGroup.classes';
import { SegmentedButtonGroupProps, SegmentedButtonGroupOwnerState } from './SegmentedButtonGroup.types';
import { getValidReactChildren, unstable_capitalize as capitalize } from '@mui/utils';
import SegmentedButtonGroupContext from './SegmentedButtonGroupContext';
import SegmentedButtonGroupButtonContext from './SegmentedButtonGroupButtonContext';

const useUtilityClasses = (ownerState: SegmentedButtonGroupOwnerState) => {
  const { orientation, disabled, size } = ownerState;
  const slots = {
    root: ['root', orientation, size && `size${capitalize(size)}`],
    grouped: ['grouped', `grouped${capitalize(orientation)}`, disabled && 'disabled'],
    firstButton: ['firstButton'],
    lastButton: ['lastButton'],
    middleButton: ['middleButton'],
  };

  return composeClasses(slots, getSegmentedButtonGroupUtilityClass, {});
};

const SegmentedButtonGroupRoot = styled('div')<SegmentedButtonGroupProps>(() => ({
  display: 'inline-flex',
  borderRadius: '0.75rem',
  variants: [
    {
      props: { orientation: 'vertical' },
      style: {
        flexDirection: 'column',
      },
    },
  ],
}));

export const SegmentedButtonGroup = React.forwardRef(function SegmentedButtonGroup(
  props: SegmentedButtonGroupProps,
  ref: React.ForwardedRef<Element>,
) {
  const defaultProps: Partial<SegmentedButtonGroupProps> = {
    size: 'medium',
    exclusive: false,
    disabled: false,
    orientation: 'horizontal',
  };

  const mergedProps = { ...defaultProps, ...props };

  const {
    className,
    component = 'div',
    children,
    value,
    onChange,
    exclusive,
    size,
    disabled,
    orientation,
    slots = {},
    slotProps = {},
    ...rest
  } = mergedProps;

  const classes = useUtilityClasses(mergedProps);
  const SlotRoot = slots.root ?? SegmentedButtonGroupRoot;

  const rootProps = useSlotProps({
    additionalProps: {
      ref,
      role: 'group',
      as: component,
    },
    className: [classes.root, className],
    elementType: SegmentedButtonGroupRoot,
    externalForwardedProps: rest,
    externalSlotProps: slotProps.root,
    ownerState: mergedProps,
  });

  const handleChange = React.useCallback(
    (event, buttonValue) => {
      if (!onChange) return;
      const index = value && value.indexOf(buttonValue);
      let newValue;

      if (value && index >= 0) {
        newValue = value.slice();
        newValue.splice(index, 1);
      } else {
        newValue = value ? value.concat(buttonValue) : [buttonValue];
      }

      onChange(event, newValue);
    },
    [onChange, value],
  );

  const handleExclusiveChange = React.useCallback(
    (event, buttonValue) => {
      if (!onChange) return;
      onChange(event, value === buttonValue ? null : buttonValue);
    },
    [onChange, value],
  );

  const context = React.useMemo(
    () => ({
      className: classes.grouped,
      onChange: exclusive ? handleExclusiveChange : handleChange,
      value,
      size,
      disabled,
      orientation,
    }),
    [classes.grouped, exclusive, handleExclusiveChange, handleChange, value, size, disabled, orientation],
  );

  const validChildren = getValidReactChildren(children);
  const childrenCount = validChildren.length;

  const getButtonPositionClassName = (index) => {
    const isFirstButton = index === 0;
    const isLastButton = index === childrenCount - 1;

    if (isFirstButton && isLastButton) {
      return '';
    }
    if (isFirstButton) {
      return classes.firstButton;
    }
    if (isLastButton) {
      return classes.lastButton;
    }
    return classes.middleButton;
  };

  return (
    <SlotRoot {...rootProps}>
      <SegmentedButtonGroupContext.Provider value={context}>
        {validChildren.map((child, index) => (
          <SegmentedButtonGroupButtonContext.Provider key={index} value={getButtonPositionClassName(index)}>
            {child}
          </SegmentedButtonGroupButtonContext.Provider>
        ))}
      </SegmentedButtonGroupContext.Provider>
    </SlotRoot>
  );
});
