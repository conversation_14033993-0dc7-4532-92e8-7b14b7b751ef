import * as React from 'react';
import type { SegmentedButtonGroupProps } from './SegmentedButtonGroup.types';

interface SegmentedButtonGroupContextValue {
  className?: string;
  onChange?: SegmentedButtonGroupProps['onChange'];
  value?: SegmentedButtonGroupProps['value'];
  size?: SegmentedButtonGroupProps['size'];
  disabled?: SegmentedButtonGroupProps['disabled'];
  orientation?: SegmentedButtonGroupProps['orientation'];
}

const SegmentedButtonGroupContext = React.createContext<SegmentedButtonGroupContextValue>({});

if (process.env.NODE_ENV !== 'production') {
  SegmentedButtonGroupContext.displayName = 'SegmentedButtonGroupContext';
}

export default SegmentedButtonGroupContext;
