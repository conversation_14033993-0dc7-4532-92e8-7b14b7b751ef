import { Locale } from '.';
import { UsePaginationItem } from '../internal/hooks/usePagination';

const locale: Locale = {
  locale: 'hu-HU',
  Autocomplete: {
    clearText: 'Törlés',
    closeText: '<PERSON>z<PERSON>r<PERSON>',
    loadingText: 'Betöltés…',
    noOptionsText: 'Nincsenek lehetőségek',
    openText: 'Megnyitás',
  },
  Pagination: {
    getItemAriaLabel: function (type: UsePaginationItem['type'], page: number | null, selected: boolean): string {
      if (type === 'page' && page !== null) {
        return `${selected ? '' : 'Ugrás a '} ${page}. oldalra`;
      }
      if (type === 'first') {
        return `Ugrás az első oldalra`;
      }
      if (type === 'last') {
        return `Ugrás az utolsó oldalra`;
      }
      if (type === 'next') {
        return `Ugrás a következő oldalra`;
      }

      return `Ugr<PERSON> az előz<PERSON> oldalra`;
    },
  },
  TablePagination: {
    getItemAriaLabel: function (type: 'first' | 'last' | 'next' | 'previous') {
      if (type === 'first') {
        return `Ugrás az első oldalra`;
      }
      if (type === 'last') {
        return `Ugrás az utolsó oldalra`;
      }
      if (type === 'next') {
        return `Ugrás a következő oldalra`;
      }
      return `Ugrás az előző oldalra`;
    },
    renderPageInfo: function (page: number, totalPages: number) {
      return `Oldal ${page + 1} a ${totalPages} -ból/-ből`;
    },
    labelRowsPerPage: 'Elemek oldalanként:',
  },
};

export default locale;
