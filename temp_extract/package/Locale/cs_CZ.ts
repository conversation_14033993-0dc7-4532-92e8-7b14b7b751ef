import { Locale } from '.';
import { UsePaginationItem } from '../internal/hooks/usePagination';

const locale: Locale = {
  locale: 'cs-CZ',
  Autocomplete: {
    clearText: 'Vymazat',
    closeText: 'Zavřít',
    loadingText: 'Načítá se…',
    noOptionsText: '<PERSON><PERSON><PERSON><PERSON> mož<PERSON>ti',
    openText: 'Otevřít',
  },
  Pagination: {
    getItemAriaLabel: function (type: UsePaginationItem['type'], page: number | null, selected: boolean): string {
      if (type === 'page' && page !== null) {
        return `${selected ? '' : 'Přejít na '}stranu ${page}`;
      }
      if (type === 'first') {
        return `Přejít na první stranu`;
      }
      if (type === 'last') {
        return `Přejít na poslední stranu`;
      }
      if (type === 'next') {
        return `<PERSON><PERSON><PERSON><PERSON>t na další stranu`;
      }

      return `<PERSON><PERSON><PERSON><PERSON><PERSON> na předchoz<PERSON> stranu`;
    },
  },
  TablePagination: {
    getItemAriaLabel: function (type: 'first' | 'last' | 'next' | 'previous') {
      if (type === 'first') {
        return `Přejít na první stranu`;
      }
      if (type === 'last') {
        return `Přejít na poslední stranu`;
      }
      if (type === 'next') {
        return `Přejít na další stranu`;
      }
      return `Přejít na předchozí stranu`;
    },
    renderPageInfo: function (page: number, totalPages: number) {
      return `Strana ${page + 1} z ${totalPages}`;
    },
    labelRowsPerPage: 'Položek na stránku:',
  },
};

export default locale;
