import { Locale } from '.';
import { UsePaginationItem } from '../internal/hooks/usePagination';

const locale: Locale = {
  locale: 'sv-SE',
  Autocomplete: {
    clearText: 'Rensa',
    closeText: 'Stäng',
    loadingText: 'Laddar…',
    noOptionsText: 'Inga alternativ',
    openText: 'Öppna',
  },
  Pagination: {
    getItemAriaLabel: function (type: UsePaginationItem['type'], page: number | null, selected: boolean): string {
      if (type === 'page' && page !== null) {
        return `${selected ? '' : 'Gå till '}sidan ${page}`;
      }
      if (type === 'first') {
        return `Gå till första sidan`;
      }
      if (type === 'last') {
        return `Gå till sista sidan`;
      }
      if (type === 'next') {
        return `Gå till nästa sida`;
      }

      return `Gå till föregående sida`;
    },
  },
  TablePagination: {
    getItemAriaLabel: function (type: 'first' | 'last' | 'next' | 'previous') {
      if (type === 'first') {
        return `Gå till första sidan`;
      }
      if (type === 'last') {
        return `Gå till sista sidan`;
      }
      if (type === 'next') {
        return `Gå till nästa sida`;
      }
      return `Gå till föregående sida`;
    },
    renderPageInfo: function (page: number, totalPages: number) {
      return `Sida ${page + 1} av ${totalPages}`;
    },
    labelRowsPerPage: 'Objekt per sida:',
  },
};

export default locale;
