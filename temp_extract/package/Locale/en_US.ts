import { Locale } from '.';
import { UsePaginationItem } from '../internal/hooks/usePagination';

const locale: Locale = {
  locale: 'en-US',
  Autocomplete: {
    clearText: 'Clear',
    closeText: 'Close',
    loadingText: 'Loading…',
    noOptionsText: 'No options',
    openText: 'Open',
  },
  Pagination: {
    getItemAriaLabel: function (type: UsePaginationItem['type'], page: number | null, selected: boolean): string {
      if (type === 'page' && page !== null) {
        return `${selected ? '' : 'Go to '}page ${page}`;
      }
      if (type === 'first') {
        return `Go to first page`;
      }
      if (type === 'last') {
        return `Go to last page`;
      }
      if (type === 'next') {
        return `Go to next page`;
      }

      return `Go to previous page`;
    },
  },
  TablePagination: {
    getItemAriaLabel: function (type: 'first' | 'last' | 'next' | 'previous') {
      if (type === 'first') {
        return `Go to first page`;
      }
      if (type === 'last') {
        return `Go to last page`;
      }
      if (type === 'next') {
        return `Go to next page`;
      }
      return `Go to previous page`;
    },
    renderPageInfo: function (page: number, totalPages: number) {
      return `Page ${page + 1} of ${totalPages}`;
    },
    labelRowsPerPage: 'Items per page:',
  },
};

export default locale;
