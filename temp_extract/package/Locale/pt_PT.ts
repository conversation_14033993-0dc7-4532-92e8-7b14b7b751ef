import { Locale } from '.';
import { UsePaginationItem } from '../internal/hooks/usePagination';

const locale: Locale = {
  locale: 'pt-PT',
  Autocomplete: {
    clearText: 'Limpar',
    closeText: 'Fechar',
    loadingText: 'A carregar…',
    noOptionsText: 'Sem opções',
    openText: 'Abrir',
  },
  Pagination: {
    getItemAriaLabel: function (type: UsePaginationItem['type'], page: number | null, selected: boolean): string {
      if (type === 'page' && page !== null) {
        return `${selected ? '' : 'Ir para '}página ${page}`;
      }
      if (type === 'first') {
        return `Ir para a primeira página`;
      }
      if (type === 'last') {
        return `Ir para a última página`;
      }
      if (type === 'next') {
        return `Ir para a próxima página`;
      }

      return `Ir para a página anterior`;
    },
  },
  TablePagination: {
    getItemAriaLabel: function (type: 'first' | 'last' | 'next' | 'previous') {
      if (type === 'first') {
        return `Ir para a primeira página`;
      }
      if (type === 'last') {
        return `Ir para a última página`;
      }
      if (type === 'next') {
        return `Ir para a próxima página`;
      }
      return `Ir para a página anterior`;
    },
    renderPageInfo: function (page: number, totalPages: number) {
      return `Página ${page + 1} de ${totalPages}`;
    },
    labelRowsPerPage: 'Itens por página:',
  },
};

export default locale;
