import { Locale } from '.';
import { UsePaginationItem } from '../internal/hooks/usePagination';

const locale: Locale = {
  locale: 'pl-PL',
  Autocomplete: {
    clearText: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
    closeText: '<PERSON>amknij',
    loadingText: 'Ł<PERSON>wanie…',
    noOptionsText: '<PERSON>rak opcji',
    openText: 'Otwórz',
  },
  Pagination: {
    getItemAriaLabel: function (type: UsePaginationItem['type'], page: number | null, selected: boolean): string {
      if (type === 'page' && page !== null) {
        return `${selected ? '' : 'Przejd<PERSON> do '}strony ${page}`;
      }
      if (type === 'first') {
        return `Przejdź do pierwszej strony`;
      }
      if (type === 'last') {
        return `Przejdź do ostatniej strony`;
      }
      if (type === 'next') {
        return `Przejd<PERSON> do następnej strony`;
      }

      return `Przej<PERSON><PERSON> do poprzedniej strony`;
    },
  },
  TablePagination: {
    getItemAriaLabel: function (type: 'first' | 'last' | 'next' | 'previous') {
      if (type === 'first') {
        return `Przejdź do pierwszej strony`;
      }
      if (type === 'last') {
        return `Przejdź do ostatniej strony`;
      }
      if (type === 'next') {
        return `Przejdź do następnej strony`;
      }
      return `Przejdź do poprzedniej strony`;
    },
    renderPageInfo: function (page: number, totalPages: number) {
      return `Strona ${page + 1} z ${totalPages}`;
    },
    labelRowsPerPage: 'Elementów na stronę:',
  },
};

export default locale;
