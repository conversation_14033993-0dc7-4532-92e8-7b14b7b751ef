import { AutocompleteLocale } from '../Autocomplete/Autocomplete.types';
import { PaginationLocale } from '../Pagination/Pagination.types';
import { TablePaginationLocale } from '../TablePagination/TablePagination.types';

export interface Locale {
  locale: string;
  Pagination: Required<PaginationLocale>;
  TablePagination: Required<TablePaginationLocale>;
  Autocomplete: Required<AutocompleteLocale>;
}

export { useLanguage, useLocale } from './LocaleProvider';
