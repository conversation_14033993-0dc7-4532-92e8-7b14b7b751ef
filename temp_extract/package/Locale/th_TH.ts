import { Locale } from '.';
import { UsePaginationItem } from '../internal/hooks/usePagination';

const locale: Locale = {
  locale: 'th-TH',
  Autocomplete: {
    clearText: 'ล้าง',
    closeText: 'ปิด',
    loadingText: 'กำลังโหลด…',
    noOptionsText: 'ไม่มีตัวเลือก',
    openText: 'เปิด',
  },
  Pagination: {
    getItemAriaLabel: function (type: UsePaginationItem['type'], page: number | null, selected: boolean): string {
      if (type === 'page' && page !== null) {
        return `${selected ? '' : 'ไปที่ '}หน้า ${page}`;
      }
      if (type === 'first') {
        return `ไปที่หน้าหมายเลขหนึ่ง`;
      }
      if (type === 'last') {
        return `ไปที่หน้าสุดท้าย`;
      }
      if (type === 'next') {
        return `ไปหน้าถัดไป`;
      }

      return `กลับไปหน้าก่อนหน้า`;
    },
  },
  TablePagination: {
    getItemAriaLabel: function (type: 'first' | 'last' | 'next' | 'previous') {
      if (type === 'first') {
        return `ไปที่หน้าหมายเลขหนึ่ง`;
      }
      if (type === 'last') {
        return `ไปที่หน้าสุดท้าย`;
      }
      if (type === 'next') {
        return `ไปหน้าถัดไป`;
      }
      return `กลับไปหน้าก่อนหน้า`;
    },
    renderPageInfo: function (page: number, totalPages: number) {
      return `หน้า ${page + 1} จาก ${totalPages}`;
    },
    labelRowsPerPage: 'รายการต่อหน้า:',
  },
};

export default locale;
