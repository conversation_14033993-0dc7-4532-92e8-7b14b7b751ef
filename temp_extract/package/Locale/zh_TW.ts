import { Locale } from '.';
import { UsePaginationItem } from '../internal/hooks/usePagination';

const locale: Locale = {
  locale: 'zh-TW',
  Autocomplete: {
    clearText: '清除',
    closeText: '關閉',
    loadingText: '載入中…',
    noOptionsText: '沒有選項',
    openText: '打開',
  },
  Pagination: {
    getItemAriaLabel: function (type: UsePaginationItem['type'], page: number | null, selected: boolean): string {
      if (type === 'page' && page !== null) {
        return `${selected ? '' : '前往 '}第 ${page} 頁`;
      }
      if (type === 'first') {
        return `前往第一頁`;
      }
      if (type === 'last') {
        return `前往最後一頁`;
      }
      if (type === 'next') {
        return `前往下一頁`;
      }

      return `前往上一頁`;
    },
  },
  TablePagination: {
    getItemAriaLabel: function (type: 'first' | 'last' | 'next' | 'previous') {
      if (type === 'first') {
        return `前往第一頁`;
      }
      if (type === 'last') {
        return `前往最後一頁`;
      }
      if (type === 'next') {
        return `前往下一頁`;
      }
      return `前往上一頁`;
    },
    renderPageInfo: function (page: number, totalPages: number) {
      return `第 ${page + 1} 頁，共 ${totalPages} 頁`;
    },
    labelRowsPerPage: '每頁條數:',
  },
};

export default locale;
