import { Locale } from '.';
import { UsePaginationItem } from '../internal/hooks/usePagination';

const locale: Locale = {
  locale: 'zh-CN',
  Autocomplete: {
    clearText: '清除',
    closeText: '关闭',
    loadingText: '加载中…',
    noOptionsText: '没有选项',
    openText: '打开',
  },
  Pagination: {
    getItemAriaLabel: function (type: UsePaginationItem['type'], page: number | null, selected: boolean): string {
      if (type === 'page' && page !== null) {
        return `${selected ? '' : '前往 '}第 ${page} 页`;
      }
      if (type === 'first') {
        return `前往第一页`;
      }
      if (type === 'last') {
        return `前往最后一页`;
      }
      if (type === 'next') {
        return `前往下一页`;
      }

      return `前往上一页`;
    },
  },
  TablePagination: {
    getItemAriaLabel: function (type: 'first' | 'last' | 'next' | 'previous') {
      if (type === 'first') {
        return `前往第一页`;
      }
      if (type === 'last') {
        return `前往最后一页`;
      }
      if (type === 'next') {
        return `前往下一页`;
      }
      return `前往上一页`;
    },
    renderPageInfo: function (page: number, totalPages: number) {
      return `第 ${page + 1} 页，共 ${totalPages} 页`;
    },
    labelRowsPerPage: '每页条数:',
  },
};

export default locale;
