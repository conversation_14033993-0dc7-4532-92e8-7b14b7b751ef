import { Locale } from '.';
import { UsePaginationItem } from '../internal/hooks/usePagination';

const locale: Locale = {
  locale: 'ko-KR',
  Autocomplete: {
    clearText: '지우기',
    closeText: '닫기',
    loadingText: '로딩 중…',
    noOptionsText: '옵션이 없습니다',
    openText: '열기',
  },
  Pagination: {
    getItemAriaLabel: function (type: UsePaginationItem['type'], page: number | null, selected: boolean): string {
      if (type === 'page' && page !== null) {
        return `${selected ? '' : '페이지 '} ${page}로 이동`;
      }
      if (type === 'first') {
        return `첫 페이지로 이동`;
      }
      if (type === 'last') {
        return `마지막 페이지로 이동`;
      }
      if (type === 'next') {
        return `다음 페이지로 이동`;
      }

      return `이전 페이지로 이동`;
    },
  },
  TablePagination: {
    getItemAriaLabel: function (type: 'first' | 'last' | 'next' | 'previous') {
      if (type === 'first') {
        return `첫 페이지로 이동`;
      }
      if (type === 'last') {
        return `마지막 페이지로 이동`;
      }
      if (type === 'next') {
        return `다음 페이지로 이동`;
      }
      return `이전 페이지로 이동`;
    },
    renderPageInfo: function (page: number, totalPages: number) {
      return `페이지 ${page + 1} / ${totalPages}`;
    },
    labelRowsPerPage: '페이지당 항목 수:',
  },
};

export default locale;
