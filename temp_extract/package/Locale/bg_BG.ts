import { Locale } from '.';
import { UsePaginationItem } from '../internal/hooks/usePagination';

const locale: Locale = {
  locale: 'bg-BG',
  Autocomplete: {
    clearText: 'Изчисти',
    closeText: 'Затвори',
    loadingText: 'Зареждане…',
    noOptionsText: 'Няма опции',
    openText: 'Отвори',
  },
  Pagination: {
    getItemAriaLabel: function (type: UsePaginationItem['type'], page: number | null, selected: boolean): string {
      if (type === 'page' && page !== null) {
        return `${selected ? '' : 'Отидете на '}страница ${page}`;
      }
      if (type === 'first') {
        return `Отидете на първата страница`;
      }
      if (type === 'last') {
        return `Отидете на последната страница`;
      }
      if (type === 'next') {
        return `Отидете на следващата страница`;
      }

      return `Отидете на предишната страница`;
    },
  },
  TablePagination: {
    getItemAriaLabel: function (type: 'first' | 'last' | 'next' | 'previous') {
      if (type === 'first') {
        return `Отидете на първата страница`;
      }
      if (type === 'last') {
        return `Отидете на последната страница`;
      }
      if (type === 'next') {
        return `Отидете на следващата страница`;
      }
      return `Отидете на предишната страница`;
    },
    renderPageInfo: function (page: number, totalPages: number) {
      return `Страница ${page + 1} от ${totalPages}`;
    },
    labelRowsPerPage: 'Елементи на страница:',
  },
};

export default locale;
