import { Locale } from '.';
import { UsePaginationItem } from '../internal/hooks/usePagination';

const locale: Locale = {
  locale: 'tr-TR',
  Autocomplete: {
    clearText: 'Temizle',
    closeText: 'Kapat',
    loadingText: '<PERSON><PERSON><PERSON><PERSON>yor…',
    noOptionsText: '<PERSON>ç<PERSON> seçenek yok',
    openText: 'Aç',
  },
  Pagination: {
    getItemAriaLabel: function (type: UsePaginationItem['type'], page: number | null, selected: boolean): string {
      if (type === 'page' && page !== null) {
        return `${selected ? '' : 'Sayfaya git '} ${page}`;
      }
      if (type === 'first') {
        return `İlk sayfaya git`;
      }
      if (type === 'last') {
        return `Son sayfaya git`;
      }
      if (type === 'next') {
        return `Sonraki sayfaya git`;
      }

      return `Önceki sayfaya git`;
    },
  },
  TablePagination: {
    getItemAriaLabel: function (type: 'first' | 'last' | 'next' | 'previous') {
      if (type === 'first') {
        return `İlk sayfaya git`;
      }
      if (type === 'last') {
        return `Son sayfaya git`;
      }
      if (type === 'next') {
        return `Sonraki sayfaya git`;
      }
      return `Önceki sayfaya git`;
    },
    renderPageInfo: function (page: number, totalPages: number) {
      return `Sayfa ${page + 1} / ${totalPages}`;
    },
    labelRowsPerPage: 'Sayfa başına öğe sayısı:',
  },
};

export default locale;
