import { Locale } from '.';
import { UsePaginationItem } from '../internal/hooks/usePagination';

const locale: Locale = {
  locale: 'ro-RO',
  Autocomplete: {
    clearText: 'Șterge',
    closeText: 'Închide',
    loadingText: 'Se încarcă…',
    noOptionsText: 'Nici o opțiune',
    openText: 'Deschide',
  },
  Pagination: {
    getItemAriaLabel: function (type: UsePaginationItem['type'], page: number | null, selected: boolean): string {
      if (type === 'page' && page !== null) {
        return `${selected ? '' : 'Mergi la '}pagina ${page}`;
      }
      if (type === 'first') {
        return `Mergi la prima pagină`;
      }
      if (type === 'last') {
        return `Mergi la ultima pagină`;
      }
      if (type === 'next') {
        return `Mergi la pagina următoare`;
      }

      return `Mergi la pagina anterioară`;
    },
  },
  TablePagination: {
    getItemAriaLabel: function (type: 'first' | 'last' | 'next' | 'previous') {
      if (type === 'first') {
        return `Mergi la prima pagină`;
      }
      if (type === 'last') {
        return `Mergi la ultima pagină`;
      }
      if (type === 'next') {
        return `Mergi la pagina următoare`;
      }
      return `Mergi la pagina anterioară`;
    },
    renderPageInfo: function (page: number, totalPages: number) {
      return `Pagina ${page + 1} din ${totalPages}`;
    },
    labelRowsPerPage: 'Elemente pe pagină:',
  },
};

export default locale;
