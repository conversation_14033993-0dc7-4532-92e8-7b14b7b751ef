import * as React from 'react';
import { OverrideProps } from '@mui/types';
import { AvatarProps } from '../Avatar/Avatar.types';
import { CreateSlotsAndSlotProps, SlotProps } from '../types/slot';
import { SxProps } from '../types/theme';
import { ApplyColorInversion } from '../types/colorSystem';

export type AvatarGroupSlot = 'root';

export interface AvatarGroupSlots {
  /**
   * The component that renders the root.
   * @default 'div'
   */
  root?: React.ElementType;
}

export type AvatarGroupSlotsAndSlotProps = CreateSlotsAndSlotProps<
  AvatarGroupSlots,
  {
    root: SlotProps<'div', object, AvatarGroupOwnerState>;
  }
>;

export interface AvatarGroupTypeMap<P = object, D extends React.ElementType = 'div'> {
  props: P &
    Pick<AvatarProps, 'color' | 'size' | 'disabled'> & {
      /**
       * Used to render icon or text elements inside the AvatarGroup if `src` is not set.
       * This can be an element, or just a string.
       */
      children?: React.ReactNode;

      /**
       * The system prop that allows defining system overrides as well as additional CSS styles.
       */
      sx?: SxProps;
    } & AvatarGroupSlotsAndSlotProps;
  defaultComponent: D;
}

export type AvatarGroupProps<
  D extends React.ElementType = AvatarGroupTypeMap['defaultComponent'],
  P = { component?: React.ElementType },
> = OverrideProps<AvatarGroupTypeMap<P, D>, D>;

export interface AvatarGroupOwnerState extends ApplyColorInversion<AvatarGroupProps> {}
