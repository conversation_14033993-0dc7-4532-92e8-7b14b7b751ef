import * as React from 'react';
import { render } from '@testing-library/react';
import { describe, expect, it } from 'vitest';
import { AvatarGroup } from './AvatarGroup';
import { Avatar, avatarClasses } from '../Avatar';

describe('<AvatarGroup />', () => {
  it('provide context to Avatar', () => {
    const { container } = render(
      <AvatarGroup color="info" size="medium">
        <Avatar src="/" />
      </AvatarGroup>,
    );

    const firstChild = container.firstChild;
    if (firstChild === null) {
      return;
    }
    const avatar = container.firstChild?.firstChild?.firstChild;
    expect(avatar).toHaveClass(avatarClasses.sizeMedium);
  });
});
