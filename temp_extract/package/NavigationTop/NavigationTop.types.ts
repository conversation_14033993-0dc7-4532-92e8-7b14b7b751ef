import { HTMLAttributes, ReactNode } from 'react';
import type { ButtonProps } from '../Button';
import { CreateSlotsAndSlotProps, SlotProps } from '../types/slot';
import { IconButtonProps } from '../IconButton';
import { TypographyProps } from '../Typography';
import { TextFieldProps } from '../TextField';

export interface NavigationTopSlots {
  /**
   * The component that renders the page title.
   * @default 'Typography'
   */
  pageTitle?: React.ElementType;
  /**
   * The component that renders the SearchBar.
   * @default 'TextField'
   */
  searchBar?: React.ElementType;
  /**
   * The component that renders the icon action.
   * @default 'IconButton'
   */
  iconAction?: React.ElementType;
}

export type NavigationTopSlotsAndSlotProps = CreateSlotsAndSlotProps<
  NavigationTopSlots,
  {
    pageTitle: SlotProps<'span', TypographyProps, NavigationTopOwnerState>;
    searchBar: SlotProps<'input', TextFieldProps, NavigationTopOwnerState>;
    iconAction: SlotProps<'button', IconButtonProps, NavigationTopOwnerState>;
  }
>;

export interface NavigationTopProps extends HTMLAttributes<HTMLDivElement>, NavigationTopSlotsAndSlotProps {
  /**
   * Product Logo to display in the toolbar
   */
  productLogo?: ReactNode;
  /**
   * Title to display in the toolbar
   */
  pageTitle?: ReactNode;
  /**
   * Callback function executed when the search value changes. If provided, a search bar will be displayed in the toolbar
   * @param value the current text in the search field
   */
  onSearchChange?: (value: string) => void;
  /**
   * Secondary actions to display in the toolbar. These will be presented as icon buttons.
   */
  iconActions?: Array<{
    icon: ReactNode;
    onClick: () => void;
    label: string;
  }>;
  /**
   * Primary actions to display in the toolbar. These will be presented as buttons.
   */
  primaryActions?: ButtonProps[];
  /**
   * User avatar to display at the far right of the toolbar
   */
  userAvatar?: ReactNode;
  /**
   * Whether or not to show a divider below the bar
   * @default true
   */
  divider?: boolean;
}
export interface NavigationTopOwnerState extends NavigationTopProps {}
