import {
  unstable_generateUtilityClasses as generateUtilityClasses,
  unstable_generateUtilityClass as generateUtilityClass,
} from '@mui/utils';

export interface NavigationTopClasses {
  /** Styles applied to the root element. */
  root: string;
  /** Styles applied to the logo element. */
  productLogo: string;
  /** Styles applied to the page title element. */
  pageTitle: string;
  /** Styles applied to the children. */
  navigationContent: string;
  /** Styles applied to the search bar element. */
  searchBar: string;
  /** Styles applied to the icon actions element. */
  iconActions: string;
  /** Styles applied to the primary actions element. */
  primaryActions: string;
  /** Styles applied to the user avatar element. */
  userAvatar: string;
}

export type TabClassKey = keyof NavigationTopClasses;

export function getNavigationTopUtilityClass(slot: string): string {
  return generateUtilityClass('NovaNavigationTop', slot);
}

const navigationTopClasses: NavigationTopClasses = generateUtilityClasses('NovaNavigationTop', [
  'root',
  'productLogo',
  'pageTitle',
  'navigationContent',
  'searchBar',
  'iconActions',
  'primaryActions',
  'userAvatar',
]);

export default navigationTopClasses;
