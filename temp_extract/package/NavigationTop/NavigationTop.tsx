'use client';
import React, { useCallback, useMemo } from 'react';
import { styled } from '@pigment-css/react';
import useSlotProps from '@mui/utils/useSlotProps';
import { NavigationTopProps } from './NavigationTop.types';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import { getNavigationTopUtilityClass } from './NavigationTop.classes';
import { Button } from '../Button';
import { Tooltip } from '../Tooltip';
import { IconButton } from '../IconButton';
import { Search } from '../Search';
import { unstable_debounce as debounce } from '@mui/utils';
import SearchIcon from '../internal/svg-icons/Search';
import { Typography, TypographyProps } from '../Typography';

const useUtilityClasses = (ownerState: NavigationTopProps) => {
  const slots = {
    root: ['root'],
    productLogo: ['productLogo'],
    pageTitle: ['pageTitle'],
    navigationContent: ['navigationContent'],
    searchBar: ['searchBar'],
    iconActions: ['iconActions'],
    primaryActions: ['primaryActions'],
    userAvatar: ['userAvatar'],
  };

  return composeClasses(slots, getNavigationTopUtilityClass, {});
};

// Slots
const NavigationTopProductLogo = styled('div', {
  name: 'NovaNavigationTop',
  slot: 'ProductLogo',
})<NavigationTopProps>(({ theme }) => ({
  marginRight: '0.75rem',
  '& > svg': {
    display: 'block',
  },
}));
const NavigationTopPageTitle = styled(Typography, {
  name: 'NovaNavigationTop',
  slot: 'PageTitle',
})<NavigationTopProps>(({ theme }) => ({
  marginRight: '0.75rem',
  fontWeight: 500,
  color: theme.vars.palette.onSurface,
}));
const NavigationTopContent = styled('div', {
  name: 'NovaNavigationTop',
  slot: 'NavigationContent',
})<NavigationTopProps>(({ theme }) => ({
  marginRight: '0.75rem',
  flex: 1,
}));
const NavigationTopSearchBar = styled(Search, {
  name: 'NovaNavigationTop',
  slot: 'SearchBar',
})<NavigationTopProps>(({ theme }) => ({
  marginRight: '0.75rem',
}));
const NavigationTopIconActions = styled('div', {
  name: 'NovaNavigationTop',
  slot: 'IconActions',
})<NavigationTopProps>(({ theme }) => ({
  marginRight: '0.75rem',
  display: 'flex',
  flexDirection: 'row',
  alignItems: 'center',
  gap: '1rem',
}));
const NavigationTopPrimaryActions = styled('div', {
  name: 'NovaNavigationTop',
  slot: 'PrimaryActions',
})<NavigationTopProps>(({ theme }) => ({
  marginRight: '0.75rem',
  display: 'flex',
  flexDirection: 'row',
  alignItems: 'center',
  gap: '0.75rem',
  flexWrap: 'wrap',
}));
const NavigationTopUserAvatar = styled('div', {
  name: 'NovaNavigationTop',
  slot: 'UserAvatar',
})<NavigationTopProps>(({ theme }) => ({
  flex: '0 0 auto',
}));

const NavigationTopRoot = styled('div')<NavigationTopProps>(({ theme }) => ({
  display: 'flex',
  flexDirection: 'row',
  alignItems: 'center',
  justifyContent: 'flex-start',
  paddingLeft: '3.75rem',
  paddingRight: '3.75rem',
  paddingTop: '1.5rem',
  paddingBottom: '1.5rem',
  '& :last-child': {
    marginRight: 0,
  },

  variants: [
    {
      props: { divider: true },
      style: {
        borderBottom: `1px solid ${theme.vars.palette.outlineVariant}`,
      },
    },
  ],
}));

export const NavigationTop = (props: NavigationTopProps) => {
  const defaultProps: Partial<NavigationTopProps> = {
    primaryActions: [],
    iconActions: [],
    divider: true,
    slots: {},
    slotProps: {},
  };
  const mergedProps = { ...defaultProps, ...props };
  const classes = useUtilityClasses(mergedProps);
  const {
    children,
    onSearchChange,
    primaryActions = [],
    iconActions = [],
    productLogo,
    pageTitle,
    userAvatar,
    divider,
    className,
    slots,
    slotProps,
    ...rest
  } = mergedProps;

  // Slots
  const PageTitleSlot = slots?.pageTitle || NavigationTopPageTitle;
  const SearchBarSlot = slots?.searchBar || NavigationTopSearchBar;
  const IconActionSlot = slots?.iconAction || IconButton;

  // Search State Management
  const [searchTerm, setSearchTerm] = React.useState('');
  const handleOnSearch = useCallback(
    (value: string) => {
      onSearchChange?.(value);
    },
    [onSearchChange],
  );
  const handleOnSearchDebounced = useMemo(() => debounce(handleOnSearch, 300), [handleOnSearch]);

  // Slot Prop Management
  const productLogoSlotProps = useSlotProps({
    elementType: NavigationTopProductLogo,
    externalSlotProps: {},
    ownerState: mergedProps,
    className: classes.productLogo,
  });
  const pageTitleSlotProps = useSlotProps({
    elementType: NavigationTopPageTitle,
    externalSlotProps: slotProps?.pageTitle,
    ownerState: mergedProps,
    className: classes.pageTitle,
    additionalProps: {
      variant: 'labelLarge' as TypographyProps['variant'],
    },
  });
  const navigationContentSlotProps = useSlotProps({
    elementType: NavigationTopContent,
    externalSlotProps: {},
    ownerState: mergedProps,
    className: classes.navigationContent,
  });
  const searchBarSlotProps = useSlotProps({
    elementType: NavigationTopSearchBar,
    externalSlotProps: slotProps?.searchBar,
    ownerState: mergedProps,
    className: classes.searchBar,
    additionalProps: {
      placeholder: 'Search',
      value: searchTerm,
      startDecorator: <SearchIcon fontSize={'inherit'} color="inherit" />,
      // startDecorator: <span>HH</span>,
      onChange: (e: React.ChangeEvent<HTMLInputElement>) => {
        setSearchTerm(e.target.value);
        handleOnSearchDebounced(e.target.value);
      },
    },
  });
  const iconActionsSlotProps = useSlotProps({
    elementType: NavigationTopIconActions,
    externalSlotProps: {},
    ownerState: mergedProps,
    className: classes.iconActions,
  });
  const primaryActionsSlotProps = useSlotProps({
    elementType: NavigationTopPrimaryActions,
    externalSlotProps: {},
    ownerState: mergedProps,
    className: classes.primaryActions,
  });
  const userAvatarSlotProps = useSlotProps({
    elementType: NavigationTopUserAvatar,
    externalSlotProps: {},
    ownerState: mergedProps,
    className: classes.searchBar,
    additionalProps: {},
  });

  return (
    <NavigationTopRoot divider={divider} className={[classes.root, className].join(' ')} {...rest}>
      {productLogo && <NavigationTopProductLogo {...productLogoSlotProps}>{productLogo}</NavigationTopProductLogo>}
      {pageTitle && <PageTitleSlot {...pageTitleSlotProps}>{pageTitle}</PageTitleSlot>}
      <NavigationTopContent {...navigationContentSlotProps}>{children}</NavigationTopContent>

      {onSearchChange && <SearchBarSlot {...searchBarSlotProps} />}

      {iconActions.length > 0 && (
        <NavigationTopIconActions {...iconActionsSlotProps}>
          {iconActions.map((action, index) => (
            <Tooltip title={action.label} key={index}>
              <IconActionSlot variant={'neutral'} onClick={action.onClick} {...slotProps?.iconAction}>
                {action.icon}
              </IconActionSlot>
            </Tooltip>
          ))}
        </NavigationTopIconActions>
      )}
      {primaryActions.length > 0 && (
        <NavigationTopPrimaryActions {...primaryActionsSlotProps}>
          {primaryActions.map((action, index) => (
            <Button key={index} {...action} />
          ))}
        </NavigationTopPrimaryActions>
      )}
      {userAvatar && <NavigationTopUserAvatar {...userAvatarSlotProps}>{userAvatar}</NavigationTopUserAvatar>}
    </NavigationTopRoot>
  );
};
