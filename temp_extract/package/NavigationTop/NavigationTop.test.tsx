import React from 'react';
import '@testing-library/jest-dom/vitest';
import { NavigationTop } from './NavigationTop';
import { screen, render, fireEvent } from '@testing-library/react';
import { expect, describe, it, vi } from 'vitest';
import MetrologyReporting from '@nexusui/branding/MetrologyReporting';
import { Avatar } from '../Avatar';

describe('NavigationTop', () => {
  it('renders NavigationTop component with product logo and name', () => {
    render(<NavigationTop productLogo={<MetrologyReporting height={40} width={40} />} pageTitle="My Product" />);
    expect(screen.getByText('My Product')).toBeInTheDocument();
  });

  it('renders NavigationTop component with icon actions', () => {
    render(
      <NavigationTop
        iconActions={[
          { icon: <div data-testid={'action1'} />, label: 'Menu', onClick: () => {} },
          { icon: <div data-testid={'action2'} />, label: 'Menu2', onClick: () => {} },
          { icon: <div data-testid={'action3'} />, label: 'Menu3', onClick: () => {} },
        ]}
      />,
    );
    expect(screen.getByTestId('action1')).toBeInTheDocument();
    expect(screen.getByTestId('action2')).toBeInTheDocument();
    expect(screen.getByTestId('action3')).toBeInTheDocument();
  });

  it('renders NavigationTop component with primary actions', () => {
    render(
      <NavigationTop
        primaryActions={[
          { children: 'Primary Action', onClick: () => {} },
          { children: 'Other Action', variant: 'outlined', onClick: () => {} },
        ]}
      />,
    );
    expect(screen.getByText('Primary Action')).toBeInTheDocument();
    expect(screen.getByText('Other Action')).toBeInTheDocument();
  });

  it('handles search input change', () => {
    vi.useFakeTimers();
    const handleSearchChange = vi.fn();
    render(<NavigationTop onSearchChange={handleSearchChange} />);
    const searchInput = screen.getByPlaceholderText('Search');
    fireEvent.change(searchInput, { target: { value: 'test' } });
    vi.advanceTimersByTime(350);
    expect(handleSearchChange).toHaveBeenCalledWith('test');
  });

  it('renders NavigationTop component with user avatar', () => {
    render(<NavigationTop userAvatar={<Avatar onClick={() => {}}>AX</Avatar>} />);
    expect(screen.getByText('AX')).toBeInTheDocument();
  });
});
