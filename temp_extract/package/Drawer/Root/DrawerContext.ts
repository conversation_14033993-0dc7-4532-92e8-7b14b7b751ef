import * as React from 'react';
import type { DrawerRootProps, SubNavDrawerContentProps } from './Drawer.types';

interface DrawerContextValue {
  expanded?: DrawerRootProps['expanded'];
  width?: DrawerRootProps['width'];
  showRailLabel?: DrawerRootProps['showRailLabel'];
  activeItem?: DrawerRootProps['activeItem'];
  onItemSelect?: DrawerRootProps['onItemSelect'];
  subNavDrawerContent?: SubNavDrawerContentProps;
  handleSubNavDrawerOpen?: () => void;
  handleSubNavDrawerClose?: () => void;
  handleSetTempSubNavDrawerContent?: (content: SubNavDrawerContentProps) => void;
  handleSetSubNavDrawerContent?: (content: SubNavDrawerContentProps) => void;
  handleRestoreSubNavContent?: () => void;
}
interface SubNavDrawerContextValue {
  isInSubNavDrawer?: boolean;
}

const DrawerContext = React.createContext<DrawerContextValue>({});

export const SubNavDrawerContext = React.createContext<SubNavDrawerContextValue>({});

if (process.env.NODE_ENV !== 'production') {
  DrawerContext.displayName = 'DrawerContext';
  SubNavDrawerContext.displayName = 'SubNavDrawerContext';
}

export default DrawerContext;
