import {
  unstable_generateUtilityClasses as generateUtilityClasses,
  unstable_generateUtilityClass as generateUtilityClass,
} from '@mui/utils';

export interface DrawerClasses {
  /** Class name applied to the root element. */
  root: string;
  /** Class name applied to the root element when open is false. */
  hidden: string;
  /** Class name applied to the backdrop element. */
  backdrop: string;
  /** Class name applied to the root element if `variant="permanent"`. */
  variantPermanent: string;
  /** Class name applied to the root element if `variant="persistent"`. */
  variantPersistent: string;
  /** Class name applied to the root element if `variant="temporary"`. */
  variantTemporary: string;
  /** Class name applied to the root element if `anchor="left"`. */
  anchorLeft: string;
  /** Class name applied to the root element if `anchor="top"`. */
  anchorTop: string;
  /** Class name applied to the root element if `anchor="right"`. */
  anchorRight: string;
  /** Class name applied to the root element if `anchor="bottom"`. */
  anchorBottom: string;
  /** Styles applied to the root element if `expanded=true`. */
  expanded: string;
  /** Styles applied to the root element if `expanded=false`. */
  collapsed: string;
}

export type DrawerClassKey = keyof DrawerClasses;

export function getDrawerUtilityClass(slot: string): string {
  return generateUtilityClass('NovaDrawer', slot);
}

const drawerRootClasses: DrawerClasses = generateUtilityClasses('NovaDrawer', [
  'root',
  'hidden',
  'backdrop',
  'variantPermanent',
  'variantPersistent',
  'variantTemporary',
  'anchorLeft',
  'anchorTop',
  'anchorRight',
  'anchorBottom',
  'expanded',
  'collapsed',
]);

export default drawerRootClasses;
