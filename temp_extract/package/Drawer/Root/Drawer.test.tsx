import '@testing-library/jest-dom/vitest';
import React from 'react';
import { render, screen, cleanup } from '@testing-library/react';
import { describe, expect, it, afterEach } from 'vitest';
import { DrawerRoot } from './Drawer.tsx';
import { DrawerNavItem } from '../NavItem/DrawerNavItem.tsx';

afterEach(() => {
  cleanup();
});

describe('Drawer', () => {
  describe('Render Drawer', () => {
    it('should render with default slot classes', () => {
      render(<DrawerRoot data-testid="NovaDrawer-root" />);
      expect(screen.getByTestId('NovaDrawer-root')).toBeDefined();
      expect(screen.getByTestId('NovaDrawer-root')).toHaveClass('NovaDrawer-root');
    });

    it('should render slot classes in collapsed mode', () => {
      render(<DrawerRoot data-testid="NovaDrawer-root" expanded={false} />);
      expect(screen.getByTestId('NovaDrawer-root')).toHaveClass('NovaDrawer-collapsed');
    });

    it('should change width successfully', () => {
      render(
        <DrawerRoot
          width={300}
          slotProps={{
            container: { 'data-testid': 'NovaDrawer-container' },
          }}
        />,
      );
      expect(screen.getByTestId('NovaDrawer-container')).toBeDefined();
      expect(screen.getByTestId('NovaDrawer-container')).toHaveStyle({ width: 300 });
    });

    it('should render showRailLabel when label defined', () => {
      render(
        <DrawerRoot data-testid="NovaDrawer-root" expanded={false} showRailLabel>
          <DrawerNavItem label="Label" />
        </DrawerRoot>,
      );
      expect(screen.getAllByText('Label')).toHaveLength(2);
    });

    it('should render showRailLabel when railLabel defined', () => {
      render(
        <DrawerRoot data-testid="NovaDrawer-root" expanded={false} showRailLabel>
          <DrawerNavItem label="Label" />
        </DrawerRoot>,
      );
      expect(screen.getAllByText('Label').length).toBeGreaterThan(0);
    });

    it('should slotProps working', () => {
      render(
        <DrawerRoot
          slotProps={{
            container: { 'data-testid': 'NovaDrawer-container' },
          }}
        />,
      );
      expect(screen.getByTestId('NovaDrawer-container')).toBeDefined();
    });

    it('should render with className', () => {
      const { container } = render(
        <DrawerRoot
          data-testid="NovaDrawer-root"
          className="drawer-class"
          slotProps={{
            container: { className: 'container-class' },
          }}
        />,
      );
      expect(screen.getByTestId('NovaDrawer-root')).toHaveClass('drawer-class');
      expect(container.querySelector('.container-class')).toBeDefined();
    });
  });

  describe('prop: variant=temporary', () => {
    it('should render with default slot classes', () => {
      render(<DrawerRoot data-testid="NovaDrawer-root" />);
      expect(screen.getByTestId('NovaDrawer-root')).toHaveClass('NovaDrawer-root');
      expect(screen.getByTestId('NovaDrawer-root')).toHaveClass('NovaDrawer-variantTemporary');
    });
  });
  it('should set the custom className for Modal when variant is temporary', () => {
    const { container } = render(
      <DrawerRoot data-testid="NovaDrawer-root" className="woofDrawer" open variant="temporary">
        <div />
      </DrawerRoot>,
    );
    expect(screen.getByTestId('NovaDrawer-root')).toHaveClass('woofDrawer');
  });

  it('should be closed by default', () => {
    render(
      <DrawerRoot data-testid="NovaDrawer-root">
        <div />
      </DrawerRoot>,
    );
    expect(screen.queryByTestId('NovaDrawer-root')).toHaveClass('Modal-hidden');
  });

  it('should be opened when open=true', () => {
    render(
      <DrawerRoot open>
        <div data-testid="child" />
      </DrawerRoot>,
    );
    expect(screen.getByTestId('child')).toBeDefined();
  });
});

describe('prop: variant=persistent', () => {
  it('should render with default slot classes', () => {
    render(<DrawerRoot data-testid="NovaDrawer-root" variant="persistent" />);
    expect(screen.getByTestId('NovaDrawer-root')).toHaveClass('NovaDrawer-root');
    expect(screen.getByTestId('NovaDrawer-root')).toHaveClass('NovaDrawer-variantPersistent');
  });
});

describe('prop: variant=permanent', () => {
  it('should render with default slot classes', () => {
    render(<DrawerRoot data-testid="NovaDrawer-root" variant="permanent" />);
    expect(screen.getByTestId('NovaDrawer-root')).toHaveClass('NovaDrawer-root');
    expect(screen.getByTestId('NovaDrawer-root')).toHaveClass('NovaDrawer-variantPermanent');
  });
});

describe('Anchor', () => {
  it('Anchor: left', () => {
    render(<DrawerRoot data-testid="NovaDrawer-root" />);
    expect(screen.getByTestId('NovaDrawer-root')).toHaveClass('NovaDrawer-anchorLeft');
  });

  it('Anchor: top', () => {
    render(<DrawerRoot data-testid="NovaDrawer-root" anchor="top" />);
    expect(screen.getByTestId('NovaDrawer-root')).toHaveClass('NovaDrawer-anchorTop');
  });

  it('Anchor: right', () => {
    render(<DrawerRoot data-testid="NovaDrawer-root" anchor="right" />);
    expect(screen.getByTestId('NovaDrawer-root')).toHaveClass('NovaDrawer-anchorRight');
  });

  it('Anchor: bottom', () => {
    render(<DrawerRoot data-testid="NovaDrawer-root" anchor="bottom" />);
    expect(screen.getByTestId('NovaDrawer-root')).toHaveClass('NovaDrawer-anchorBottom');
  });
});
