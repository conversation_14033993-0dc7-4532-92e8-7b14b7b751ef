import * as React from 'react';
import { OverrideProps } from '@mui/types';
import { CreateSlotsAndSlotProps, SlotProps } from '../../types/slot';
import { ModalOwnProps, ModalProps } from '../../internal/components/Modal';

export type DrawerSlot = 'root' | 'backdrop' | 'anchor' | 'container';

export interface DrawerSlots {
  /**
   * The component that renders the root.
   * @default 'div'
   */
  root?: React.ElementType;
  /**
   * The component that renders the backdrop.
   * @default 'div'
   */
  backdrop?: React.ElementType;
  /**
   * The component that renders the content of the drawer.
   * @default 'div'
   */
  container?: React.ElementType;

  /**
   * The component that renders the sub navigation drawer.
   * @default 'div'
   */
  subNavContainer?: React.ElementType;
}

export type DrawerSlotsAndSlotProps = CreateSlotsAndSlotProps<
  DrawerSlots,
  {
    root: SlotProps<'div', object, DrawerOwnerState>;
    backdrop: SlotProps<'div', object, DrawerOwnerState>;
    container: SlotProps<'div', object, DrawerOwnerState>;
    subNavContainer: SlotProps<'div', object, DrawerOwnerState>;
  }
>;

export interface DrawerTypeMap<P = object, D extends React.ElementType = 'div'> {
  props: P &
    Omit<ModalOwnProps, 'open' | 'onClose' | 'keepMounted' | 'children'> &
    DrawerSlotsAndSlotProps & {
      /**
       * Width of the Drawer component.
       * @default '360px'
       */
      width?: React.CSSProperties['width'];
      /**
       * Side from which the drawer will appear.
       * @default 'left'
       */
      anchor?: 'left' | 'top' | 'right' | 'bottom';
      /**
       * Callback fired when the component requests to be closed.
       * The `reason` parameter can optionally be used to control the response to `onClose`.
       *
       * @param {object} event The event source of the callback.
       * @param {string} reason Can be: `"escapeKeyDown"`, `"backdropClick"`.
       */
      onClose?: ModalProps['onClose'];
      /**
       * If `true`, the component is shown.
       * @default false
       */
      open?: boolean;
      /**
       * The variant to use.
       * @default 'temporary'
       */
      variant?: 'permanent' | 'persistent' | 'temporary';
      /**
       * If `false`, the drawer displays in a collapsed mode, showing only icons and titles.
       * @default true
       */
      expanded?: boolean;
      /**
       * `itemId` of the active navigation item.
       */
      activeItem?: string;
      /**
       * Callback fired whenever an item is clicked.
       */
      onItemSelect?: (itemId: string) => void;
      /**
       * If `true`, the labels of navigation items will be shown (navigation rail only).
       * @default false
       */
      showRailLabel?: boolean;
      /**
       * If `true`, the sub navigation drawer header will be shown, displaying the label of the hovered or clicked nav item (navigation rail only).
       * @default true
       */
      showSubNavDrawerHeader?: boolean;
      /**
       * Callback fired when the sub navigation drawer is toggled.
       * @param isOpen - `true` if the sub navigation drawer is opening, `false` if closing.
       */
      onToggleSubNavDrawer?: (isOpen: boolean) => void;
      /**
       * The duration for the transition, in milliseconds.
       * You may specify different durations for different transitions:
       * - enter: Duration for the enter transition.
       * - exit: Duration for the exit transition.
       * @default { enter: 225, exit: 195 }
       */
      transitionDuration?: { enter?: number; exit?: number };
    };
  defaultComponent: D;
}

export type DrawerRootProps<
  D extends React.ElementType = DrawerTypeMap['defaultComponent'],
  P = {
    component?: React.ElementType;
  },
> = OverrideProps<DrawerTypeMap<P, D>, D>;

export interface DrawerOwnerState extends DrawerRootProps {
  isSubNavDrawerOpen?: boolean;
}

export interface SubNavDrawerContentProps {
  header: React.ReactNode;
  children: React.ReactNode;
}
