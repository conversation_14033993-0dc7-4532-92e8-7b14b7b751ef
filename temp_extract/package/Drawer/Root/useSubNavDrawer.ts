'use client';

import * as React from 'react';
import { DrawerRootProps, SubNavDrawerContentProps } from './Drawer.types';

export function useSubNavDrawer(onToggleSubNavDrawer: DrawerRootProps['onToggleSubNavDrawer']) {
  const [isSubNavDrawerOpen, setIsSubNavDrawerOpen] = React.useState(false);
  const [tempSubNavDrawerContent, setTempSubNavDrawerContent] = React.useState(null);
  const [subNavDrawerContent, setSubNavDrawerContent] = React.useState(null);

  const handleSubNavDrawerOpen = () => {
    setIsSubNavDrawerOpen(true);
  };
  const handleSubNavDrawerClose = () => {
    setIsSubNavDrawerOpen(false);
    onToggleSubNavDrawer?.(false);
  };

  const handleSetTempSubNavDrawerContent = (content: SubNavDrawerContentProps) => {
    setTempSubNavDrawerContent(content);
    if (content && onToggleSubNavDrawer) {
      onToggleSubNavDrawer(true);
    }
  };

  const handleSetSubNavDrawerContent = (content: SubNavDrawerContentProps) => {
    setSubNavDrawerContent(content);
  };

  const handleRestoreSubNavContent = () => {
    setSubNavDrawerContent(tempSubNavDrawerContent);
    if (!tempSubNavDrawerContent) {
      handleSubNavDrawerClose();
    }
  };

  return {
    isSubNavDrawerOpen,
    tempSubNavDrawerContent,
    subNavDrawerContent,
    handleSubNavDrawerOpen,
    handleSubNavDrawerClose,
    handleSetTempSubNavDrawerContent,
    handleSetSubNavDrawerContent,
    handleRestoreSubNavContent,
  };
}
