'use client';

import * as React from 'react';
import { styled } from '@pigment-css/react';
import { getDrawerUtilityClass } from './Drawer.classes';
import { OverridableComponent } from '@mui/types';
import { Modal, ModalOwnerState } from '../../internal/components/Modal';
import { DrawerOwnerState, DrawerRootProps, DrawerTypeMap } from './Drawer.types';
import useSlotProps from '@mui/utils/useSlotProps';
import { unstable_capitalize as capitalize, unstable_composeClasses as composeClasses } from '@mui/utils';
import DrawerContext, { SubNavDrawerContext } from './DrawerContext';
import { DrawerBody } from '../Body';
import { DrawerNavGroup } from '../NavGroup';
import { useSubNavDrawer } from './useSubNavDrawer';

const useUtilityClasses = (ownerState: DrawerOwnerState) => {
  const { open, variant, expanded, anchor } = ownerState;

  const slots = {
    root: [
      'root',
      !open && 'hidden',
      variant && `variant${capitalize(variant)}`,
      anchor && `anchor${capitalize(anchor)}`,
      expanded ? 'expanded' : 'collapsed',
    ],
    backdrop: ['backdrop'],
    container: ['container'],
    subNavContainer: ['subNavContainer'],
  };

  return composeClasses(slots, getDrawerUtilityClass, {});
};

const formatWidth = (width: number | string) => (typeof width === 'number' ? `${width}px` : width);

const DrawerModalRoot = styled(Modal, {
  name: 'NovaDrawer',
  slot: 'ModalRoot',
})<ModalOwnerState>(({ theme }) => ({
  position: 'fixed',
  right: 0,
  bottom: 0,
  top: 0,
  left: 0,
  zIndex: 1200,
  transitionProperty: 'visibility',
  transitionDelay: '0s',
  variants: [
    {
      props: { open: false },
      style: {
        visibility: 'hidden',
        transitionDelay: 'var(--nova-drawer-modal-transition-delay)',
      },
    },
  ],
}));

const DrawerDockedRoot = styled('div', {
  name: 'NovaDrawer',
  slot: 'DockedRoot',
})<DrawerOwnerState>(({ theme }) => ({
  flex: '0 0 auto',
}));

const DrawerContainer = styled('div', {
  name: 'NovaDrawer',
  slot: 'Container',
})<DrawerOwnerState>(({ theme }) => ({
  '--nova-drawer-rail-width': '80px',
  '@media (max-width: 599px)': {
    '--nova-drawer-rail-width': '72px',
  },
  boxSizing: 'border-box',
  width: 'var(--nova-drawer-container-width)',
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  flex: '1 0 auto',
  overflowY: 'auto',
  overflowX: 'hidden',
  WebkitOverflowScrolling: 'touch',
  position: 'fixed',
  zIndex: 1200,
  outline: 0,
  backgroundColor: theme.vars.palette.surfaceContainerLow,
  borderWidth: '0',
  borderColor: theme.vars.palette.outlineVariant,
  borderStyle: 'solid',
  transform: 'translateX(-100%)',
  transitionProperty: 'transform, width',
  transitionDuration: 'var(--nova-drawer-transition-duration-enter)',
  transitionTimingFunction: 'cubic-bezier(0.4, 0, 0.6, 1)',
  variants: [
    {
      props: (ownerState) => !ownerState.expanded || !ownerState.open,
      style: {
        transitionDuration: 'var(--nova-drawer-transition-duration-exit)',
      },
    },
    // Left anchor
    {
      props: { anchor: 'left' },
      style: {
        top: 0,
        left: 0,
        borderRightWidth: '1px',
        '@media (max-width: 599px)': {
          borderTopRightRadius: '1rem',
          borderBottomRightRadius: '1rem',
        },
      },
    },
    {
      props: { anchor: 'left', open: false },
      style: {
        transform: 'translateX(-100%)',
      },
    },
    {
      props: { anchor: 'left', open: true },
      style: {
        transform: 'translateX(0)',
      },
    },
    // Right anchor
    {
      props: { anchor: 'right' },
      style: {
        top: 0,
        right: 0,
        borderLeftWidth: '1px',
        '@media (max-width: 599px)': {
          borderTopLeftRadius: '1rem',
          borderBottomLeftRadius: '1rem',
        },
      },
    },
    {
      props: { anchor: 'right', open: false },
      style: {
        transform: 'translateX(100%)',
      },
    },
    {
      props: { anchor: 'right', open: true },
      style: {
        transform: 'translateX(0)',
      },
    },
    // Top anchor
    {
      props: { anchor: 'top' },
      style: {
        width: '100%',
        height: 'var(--nova-drawer-width)',
        top: 0,
        left: 0,
        borderBottomWidth: '1px',
        '@media (max-width: 599px)': {
          borderBottomLeftRadius: '1rem',
          borderBottomRightRadius: '1rem',
        },
      },
    },
    {
      props: { anchor: 'top', open: false },
      style: {
        transform: 'translateY(-100%)',
      },
    },
    {
      props: { anchor: 'top', open: true },
      style: {
        transform: 'translateY(0)',
      },
    },
    // Bottom anchor
    {
      props: { anchor: 'bottom' },
      style: {
        width: '100%',
        height: 'var(--nova-drawer-width)',
        bottom: 0,
        left: 0,
        borderTopWidth: '1px',
        '@media (max-width: 599px)': {
          borderTopLeftRadius: '1rem',
          borderTopRightRadius: '1rem',
        },
      },
    },
    {
      props: { anchor: 'bottom', open: false },
      style: {
        transform: 'translateY(100%)',
      },
    },
    {
      props: { anchor: 'bottom', open: true },
      style: {
        transform: 'translateY(0)',
      },
    },
  ],
}));

const SubNavDrawerContainer = styled(DrawerContainer, {
  name: 'NovaDrawer',
  slot: 'SubNavDrawerContainer',
})<DrawerOwnerState>(({ theme }) => ({
  zIndex: 1199,
  width: 'calc(var(--nova-drawer-rail-width) + 274px)',
  paddingLeft: 'var(--nova-drawer-rail-width)',
  transform: 'translateX(-100%)',
  transitionProperty: 'transform',
  transitionDuration: 'var(--nova-drawer-transition-duration-enter)',
  transitionTimingFunction: 'cubic-bezier(0.4, 0, 0.6, 1)',
  variants: [
    {
      props: { isSubNavDrawerOpen: false },
      style: {
        transitionDuration: 'var(--nova-drawer-transition-duration-exit)',
      },
    },
    // Left anchor
    {
      props: { anchor: 'left' },
      style: {
        top: 0,
        left: 0,
      },
    },
    {
      props: { anchor: 'left', isSubNavDrawerOpen: false },
      style: {
        transform: 'translateX(-100%)',
      },
    },
    {
      props: { anchor: 'left', isSubNavDrawerOpen: true },
      style: {
        transform: 'translateX(0)',
      },
    },
    // Right anchor
    {
      props: { anchor: 'right' },
      style: {
        top: 0,
        right: 0,
        paddingLeft: 0,
        paddingRight: 'var(--nova-drawer-rail-width)',
      },
    },
    {
      props: { anchor: 'right', isSubNavDrawerOpen: false },
      style: {
        transform: 'translateX(100%)',
      },
    },
    {
      props: { anchor: 'right', isSubNavDrawerOpen: true },
      style: {
        transform: 'translateX(0)',
      },
    },
  ],
}));

const Backdrop = styled('div', {
  name: 'NovaDrawer',
  slot: 'Backdrop',
})<DrawerOwnerState>(({ theme }) => ({
  position: 'fixed',
  inset: 0,
  right: 0,
  bottom: 0,
  top: 0,
  left: 0,
  zIndex: -1,
  backgroundColor: 'rgba(0, 0, 0, 0.5)',
  WebkitTapHighlightColor: 'transparent',
  opacity: 0,
  transitionProperty: 'opacity',
  transitionDuration: 'var(--nova-drawer-transition-duration-enter)',
  transitionTimingFunction: 'cubic-bezier(0.4, 0, 0.6, 1)',
  variants: [
    {
      props: { open: true },
      style: {
        opacity: 1,
        transitionDuration: 'var(--nova-drawer-transition-duration-exit)',
      },
    },
  ],
}));

export const DrawerRoot = React.forwardRef(function Drawer(props: DrawerRootProps, ref: React.Ref<HTMLElement>) {
  const {
    className,
    children,
    open: openProp = false,
    activeItem,
    onItemSelect,
    anchor = 'left',
    variant = 'temporary',
    expanded = true,
    showRailLabel = false,
    showSubNavDrawerHeader = true,
    onToggleSubNavDrawer,
    width = '360px',
    slots = {},
    slotProps = {},
    component,
    onClose,
    transitionDuration = { enter: 225, exit: 195 },
    ...other
  } = props;

  const open = variant === 'permanent' ? true : openProp;

  const {
    isSubNavDrawerOpen,
    subNavDrawerContent,
    handleSubNavDrawerOpen,
    handleSubNavDrawerClose,
    handleSetTempSubNavDrawerContent,
    handleSetSubNavDrawerContent,
    handleRestoreSubNavContent,
  } = useSubNavDrawer(onToggleSubNavDrawer);

  const ownerState = {
    ...props,
    anchor,
    open,
    variant,
    expanded,
    isSubNavDrawerOpen,
  };

  const classes = useUtilityClasses(ownerState);

  const BackdropSlot = slots.backdrop ?? Backdrop;
  const RootSlot = slots.root ?? ((variant !== 'temporary' ? DrawerDockedRoot : DrawerModalRoot) as React.ElementType);
  const DrawerContainerSlot = slots.container ?? DrawerContainer;
  const SubNavDrawerContainerSlot = slots.subNavContainer ?? SubNavDrawerContainer;

  const backdropProps = useSlotProps({
    elementType: BackdropSlot,
    externalSlotProps: slotProps.backdrop,
    ownerState,
    className: classes.backdrop,
  });

  const rootSlotProps = useSlotProps({
    elementType: RootSlot,
    externalSlotProps: slotProps.root,
    externalForwardedProps: other,
    additionalProps: {
      ref,
      as: component,
      slots: { backdrop: variant === 'temporary' && BackdropSlot },
      slotProps: { backdrop: variant === 'temporary' && backdropProps },
      keepMounted: variant === 'temporary',
      style: {
        '--nova-drawer-modal-transition-delay': `${transitionDuration.exit}ms`,
        '--nova-drawer-transition-duration-enter': `${transitionDuration.enter}ms`,
        '--nova-drawer-transition-duration-exit': `${transitionDuration.exit}ms`,
      },
      onMouseLeave: handleRestoreSubNavContent,
    },
    ownerState: { ...(ownerState as any) },
    className: [classes.root, className],
  });

  const containerSlotProps = useSlotProps({
    elementType: DrawerContainerSlot,
    externalSlotProps: slotProps.container,
    ownerState,
    className: classes.container,
    additionalProps: {
      style: {
        '--nova-drawer-container-width': open ? (expanded ? formatWidth(width) : 'var(--nova-drawer-rail-width)') : 0,
        '--nova-drawer-width': formatWidth(width),
      },
    },
  });

  const subNavContainerSlotProps = useSlotProps({
    elementType: SubNavDrawerContainerSlot,
    externalSlotProps: slotProps.subNavContainer,
    ownerState,
    className: classes.subNavContainer,
  });

  const context = React.useMemo(
    () => ({
      expanded,
      width,
      showRailLabel,
      activeItem,
      onItemSelect,
      subNavDrawerContent,
      handleSubNavDrawerOpen,
      handleSubNavDrawerClose,
      handleSetTempSubNavDrawerContent,
      handleSetSubNavDrawerContent,
      handleRestoreSubNavContent,
    }),
    [
      expanded,
      width,
      showRailLabel,
      activeItem,
      onItemSelect,
      subNavDrawerContent,
      handleSubNavDrawerOpen,
      handleSubNavDrawerClose,
      handleSetTempSubNavDrawerContent,
      handleSetSubNavDrawerContent,
      handleRestoreSubNavContent,
    ],
  );

  return (
    <>
      <DrawerContext.Provider value={context}>
        <RootSlot {...rootSlotProps} open={open} onClose={onClose}>
          <>
            <DrawerContainerSlot {...containerSlotProps}>{children}</DrawerContainerSlot>
            {!expanded && (
              <SubNavDrawerContext.Provider value={{ isInSubNavDrawer: true }}>
                <SubNavDrawerContainerSlot {...subNavContainerSlotProps}>
                  <DrawerBody>
                    {subNavDrawerContent && (
                      <DrawerNavGroup
                        sx={{ paddingBlock: '1rem' }}
                        header={showSubNavDrawerHeader && subNavDrawerContent.header}
                      >
                        {subNavDrawerContent.children}
                      </DrawerNavGroup>
                    )}
                  </DrawerBody>
                </SubNavDrawerContainerSlot>
              </SubNavDrawerContext.Provider>
            )}
          </>
        </RootSlot>
      </DrawerContext.Provider>
    </>
  );
}) as OverridableComponent<DrawerTypeMap>;
