import '@testing-library/jest-dom/vitest';
import React from 'react';
import { render, screen, cleanup } from '@testing-library/react';
import { describe, expect, it, afterEach } from 'vitest';
import { DrawerNavGroup } from './DrawerNavGroup.tsx';
import DrawerContext from '../Root/DrawerContext.ts';

afterEach(() => {
  cleanup();
});

const WrapperComponent = ({ expanded }) => {
  return (
    <DrawerContext.Provider
      value={{
        expanded,
      }}
    >
      <DrawerNavGroup data-testid="NovaDrawerNavGroup-root" />
    </DrawerContext.Provider>
  );
};

describe('DrawerNavGroup', () => {
  it('should render with default slot classes', () => {
    render(<DrawerNavGroup data-testid="NovaDrawerNavGroup-root" />);
    expect(screen.getByTestId('NovaDrawerNavGroup-root')).toBeDefined();
    expect(screen.getByTestId('NovaDrawerNavGroup-root')).toHaveClass('NovaDrawerNavGroup-root');
  });

  it('should render slot classes in collapsed mode', () => {
    render(<WrapperComponent expanded={false} />);
    expect(screen.getByTestId('NovaDrawerNavGroup-root')).toBeDefined();
    expect(screen.getByTestId('NovaDrawerNavGroup-root')).toHaveClass('NovaDrawerNavGroup-collapsed');
  });

  it('should render header', () => {
    render(<DrawerNavGroup data-testid="NovaDrawerNavGroup-root" header="Header" />);
    expect(screen.getByText('Header')).toBeDefined();
  });

  it('should render custom header', () => {
    render(<DrawerNavGroup data-testid="NovaDrawerNavGroup-root" header={<div>Custom Header</div>} />);
    expect(screen.getByText('Custom Header')).toBeDefined();
  });

  it('should slotProps working', () => {
    render(
      <DrawerNavGroup
        data-testid="NovaDrawerNavGroup-root"
        slotProps={{ header: { 'data-testid': 'header-test-id' } }}
        header="Header"
      />,
    );
    expect(screen.getByTestId('header-test-id')).toBeDefined();
  });
});
