import * as React from 'react';
import { OverrideProps } from '@mui/types';
import { CreateSlotsAndSlotProps, SlotProps } from '../../types/slot';
import { SxProps } from '../../types/theme';
import { TypographyProps } from '../../Typography';

export type DrawerNavGroupSlot = 'root';

export interface DrawerNavGroupSlots {
  /**
   * The component that renders the root.
   * @default 'div'
   */
  root?: React.ElementType;
  /**
   * The component that renders the header.
   */
  header?: React.ElementType;
}

export type DrawerNavGroupSlotsAndSlotProps = CreateSlotsAndSlotProps<
  DrawerNavGroupSlots,
  {
    root: SlotProps<'div', object, DrawerNavGroupOwnerState>;
    header: SlotProps<'span', TypographyProps, DrawerNavGroupOwnerState>;
  }
>;

export interface DrawerNavGroupTypeMap<P = object, D extends React.ElementType = 'div'> {
  props: P &
    DrawerNavGroupSlotsAndSlotProps & {
      /**
       * The header content of the DrawerNavGroup.
       */
      header?: React.ReactNode;
      /**
       * The system prop that allows defining system overrides as well as additional CSS styles.
       */
      sx?: SxProps;
    };
  defaultComponent: D;
}

export type DrawerNavGroupProps<
  D extends React.ElementType = DrawerNavGroupTypeMap['defaultComponent'],
  P = {
    component?: React.ElementType;
  },
> = OverrideProps<DrawerNavGroupTypeMap<P, D>, D>;

export interface DrawerNavGroupOwnerState extends DrawerNavGroupProps {
  expanded?: boolean;
}
