'use client';

import * as React from 'react';
import { styled } from '@pigment-css/react';
import { getDrawerNavGroupUtilityClass } from './DrawerNavGroup.classes';
import { DrawerNavGroupOwnerState, DrawerNavGroupProps } from './DrawerNavGroup.types';
import useSlotProps from '@mui/utils/useSlotProps';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import DrawerContext, { SubNavDrawerContext } from '../Root/DrawerContext';
import { Typography } from '../../Typography';

const useUtilityClasses = (ownerState: DrawerNavGroupOwnerState) => {
  const { expanded } = ownerState;

  const slots = {
    root: ['root', expanded ? 'expanded' : 'collapsed'],
    header: ['header'],
  };

  return composeClasses(slots, getDrawerNavGroupUtilityClass, {});
};

const DrawerNavGroupRoot = styled('ul', {
  name: 'NovaDrawerNavGroup',
  slot: 'Root',
})<DrawerNavGroupOwnerState>(({ theme }) => ({
  listStyle: 'none',
  margin: 0,
  padding: 0,
  outline: 0,
  paddingBlock: '0.25rem',
}));

const DrawerNavGroupHeader = styled(Typography, {
  name: 'NovaDrawerNavGroup',
  slot: 'Header',
})<DrawerNavGroupOwnerState>(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  padding: '0 0.75rem',
  height: 40,
  '@media (max-width: 599px)': {
    height: 32,
  },
  overflow: 'hidden',
  whiteSpace: 'nowrap',
  textOverflow: 'ellipsis',
  opacity: 1,
  color: theme.vars.palette.onSurfaceVariant,
  transitionProperty: 'height, opacity',
  transitionDuration: 'var(--nova-drawer-transition-duration-exit)',
  transitionTimingFunction: 'cubic-bezier(0.4, 0, 0.6, 1)',
  variants: [
    {
      props: { expanded: false },
      style: {
        height: 0,
        opacity: 0,
        transitionDuration: 'var(--nova-drawer-transition-duration-enter)',
      },
    },
  ],
}));

export const DrawerNavGroup = React.forwardRef(function DrawerNavGroup(
  props: DrawerNavGroupProps,
  ref: React.Ref<HTMLElement>,
) {
  const contextValue = React.useContext(DrawerContext);
  const { isInSubNavDrawer } = React.useContext(SubNavDrawerContext);

  const { className, children, slots = {}, slotProps = {}, component, header, ...other } = props;

  const expanded = isInSubNavDrawer ?? contextValue?.expanded;

  const ownerState = { ...props, expanded };

  const classes = useUtilityClasses(ownerState);

  const RootSlot = slots.root ?? DrawerNavGroupRoot;
  const HeaderSlot = slots.header ?? DrawerNavGroupHeader;

  const rootSlotProps = useSlotProps({
    elementType: RootSlot,
    externalSlotProps: slotProps.root,
    externalForwardedProps: other,
    ownerState,
    className: [classes.root, className],
    additionalProps: {
      ref,
      as: component,
    },
  });

  const headerSlotProps = useSlotProps({
    elementType: HeaderSlot,
    externalSlotProps: slotProps.header,
    ownerState,
    className: classes.header,
    additionalProps: {
      variant: 'labelMedium',
    },
  });

  return (
    <RootSlot {...rootSlotProps}>
      {header && <HeaderSlot {...headerSlotProps}>{header}</HeaderSlot>}
      {children}
    </RootSlot>
  );
});
