import {
  unstable_generateUtilityClasses as generateUtilityClasses,
  unstable_generateUtilityClass as generateUtilityClass,
} from '@mui/utils';

export interface DrawerNavGroupClasses {
  /** Class name applied to the root element. */
  root: string;
  /** Class name applied to the header element. */
  header: string;
  /** Styles applied to the root element if `expanded=true`. */
  expanded: string;
  /** Styles applied to the root element if `expanded=false`. */
  collapsed: string;
}

export type DrawerNavGroupClassKey = keyof DrawerNavGroupClasses;

export function getDrawerNavGroupUtilityClass(slot: string): string {
  return generateUtilityClass('NovaDrawerNavGroup', slot);
}

const DrawerNavGroupClasses: DrawerNavGroupClasses = generateUtilityClasses('NovaDrawerNavGroup', [
  'root',
  'header',
  'expanded',
  'collapsed',
]);

export default DrawerNavGroupClasses;
