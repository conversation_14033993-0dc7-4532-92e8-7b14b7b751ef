import '@testing-library/jest-dom/vitest';
import React from 'react';
import { render, screen, cleanup } from '@testing-library/react';
import { describe, expect, it, afterEach } from 'vitest';
import { DrawerBody } from './DrawerBody.tsx';
import DrawerContext from '../Root/DrawerContext.ts';

afterEach(() => {
  cleanup();
});

const WrapperComponent = ({ expanded }) => {
  return (
    <DrawerContext.Provider
      value={{
        expanded,
      }}
    >
      <DrawerBody data-testid="NovaDrawerBody-root" />
    </DrawerContext.Provider>
  );
};

describe('DrawerBody', () => {
  it('should render with default slot classes', () => {
    render(<DrawerBody data-testid="NovaDrawerBody-root" />);
    expect(screen.getByTestId('NovaDrawerBody-root')).toBeDefined();
    expect(screen.getByTestId('NovaDrawerBody-root')).toHaveClass('NovaDrawerBody-root');
  });

  it('should render slot classes in collapsed mode', () => {
    render(<WrapperComponent expanded={false} />);
    expect(screen.getByTestId('NovaDrawerBody-root')).toBeDefined();
    expect(screen.getByTestId('NovaDrawerBody-root')).toHaveClass('NovaDrawerBody-collapsed');
  });
});
