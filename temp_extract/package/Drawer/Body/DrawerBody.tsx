'use client';

import * as React from 'react';
import { styled } from '@pigment-css/react';
import { getDrawerBodyUtilityClass } from './DrawerBody.classes';
import { DrawerBodyOwnerState, DrawerBodyProps } from './DrawerBody.types';
import useSlotProps from '@mui/utils/useSlotProps';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import DrawerContext from '../Root/DrawerContext';

const useUtilityClasses = (ownerState: DrawerBodyOwnerState) => {
  const { expanded } = ownerState;

  const slots = {
    root: ['root', expanded ? 'expanded' : 'collapsed'],
  };

  return composeClasses(slots, getDrawerBodyUtilityClass, {});
};

const DrawerBodyRoot = styled('div', {
  name: 'NovaDrawerBody',
  slot: 'Root',
})<DrawerBodyOwnerState>(({ theme }) => ({
  flex: '1',
  padding: '0 0.75rem',
  '@media (max-width: 599px)': {
    padding: '0 0.5rem',
  },
  overflowX: 'hidden',
  variants: [
    {
      // Hide scrollbar in collapsed mode
      props: { expanded: false },
      style: {
        scrollbarWidth: 'none',
        msOverflowStyle: 'none',
        '::-webkit-scrollbar': {
          display: 'none',
        },
      },
    },
  ],
}));

export const DrawerBody = React.forwardRef(function DrawerBody(props: DrawerBodyProps, ref: React.Ref<HTMLElement>) {
  const contextValue = React.useContext(DrawerContext);

  const { className, children, slots = {}, slotProps = {}, component, ...other } = props;

  const expanded = contextValue?.expanded;

  const ownerState = { ...props, expanded };

  const classes = useUtilityClasses(ownerState);

  const RootSlot = slots.root ?? DrawerBodyRoot;

  const rootSlotProps = useSlotProps({
    elementType: RootSlot,
    externalSlotProps: slotProps.root,
    externalForwardedProps: other,
    ownerState,
    className: [classes.root, className],
    additionalProps: {
      ref,
      as: component,
    },
  });

  return <RootSlot {...rootSlotProps}>{children}</RootSlot>;
});
