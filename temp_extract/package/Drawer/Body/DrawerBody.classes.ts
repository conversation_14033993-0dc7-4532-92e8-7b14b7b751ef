import {
  unstable_generateUtilityClasses as generateUtilityClasses,
  unstable_generateUtilityClass as generateUtilityClass,
} from '@mui/utils';

export interface DrawerBodyClasses {
  /** Class name applied to the root element. */
  root: string;
  /** Styles applied to the root element if `expanded=true`. */
  expanded: string;
  /** Styles applied to the root element if `expanded=false`. */
  collapsed: string;
}

export type DrawerBodyClassKey = keyof DrawerBodyClasses;

export function getDrawerBodyUtilityClass(slot: string): string {
  return generateUtilityClass('NovaDrawerBody', slot);
}

const drawerBodyClasses: DrawerBodyClasses = generateUtilityClasses('NovaDrawerBody', [
  'root',
  'expanded',
  'collapsed',
]);

export default drawerBodyClasses;
