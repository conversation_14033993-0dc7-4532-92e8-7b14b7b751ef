import * as React from 'react';
import { OverrideProps } from '@mui/types';
import { CreateSlotsAndSlotProps, SlotProps } from '../../types/slot';
import { SxProps } from '../../types/theme';

export type DrawerBodySlot = 'root';

export interface DrawerBodySlots {
  /**
   * The component that renders the root.
   * @default 'div'
   */
  root?: React.ElementType;
}

export type DrawerBodySlotsAndSlotProps = CreateSlotsAndSlotProps<
  DrawerBodySlots,
  {
    root: SlotProps<'div', object, DrawerBodyOwnerState>;
  }
>;

export interface DrawerBodyTypeMap<P = object, D extends React.ElementType = 'div'> {
  props: P &
    DrawerBodySlotsAndSlotProps & {
      /**
       * The system prop that allows defining system overrides as well as additional CSS styles.
       */
      sx?: SxProps;
    };
  defaultComponent: D;
}

export type DrawerBodyProps<
  D extends React.ElementType = DrawerBodyTypeMap['defaultComponent'],
  P = {
    component?: React.ElementType;
  },
> = OverrideProps<DrawerBodyTypeMap<P, D>, D>;

export interface DrawerBodyOwnerState extends DrawerBodyProps {
  expanded?: boolean;
}
