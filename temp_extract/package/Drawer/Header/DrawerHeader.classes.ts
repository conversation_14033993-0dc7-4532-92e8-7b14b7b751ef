import {
  unstable_generateUtilityClasses as generateUtilityClasses,
  unstable_generateUtilityClass as generateUtilityClass,
} from '@mui/utils';

export interface DrawerHeaderClasses {
  /** Class name applied to the root element. */
  root: string;
  /** Class name applied to the container element. */
  container: string;
  /** Styles applied to the product logo element.  */
  productLogo: string;
  /** Styles applied to the page title element. */
  pageTitle: string;
  /** Styles applied to the end decorator element. */
  endDecorator: string;
  /** Styles applied to the root element if `expanded=true`. */
  expanded: string;
  /** Styles applied to the root element if `expanded=false`. */
  collapsed: string;
}

export type DrawerHeaderClassKey = keyof DrawerHeaderClasses;

export function getDrawerHeaderUtilityClass(slot: string): string {
  return generateUtilityClass('NovaDrawerHeader', slot);
}

const drawerHeaderClasses: DrawerHeaderClasses = generateUtilityClasses('NovaDrawerHeader', [
  'root',
  'container',
  'productLogo',
  'pageTitle',
  'endDecorator',
  'expanded',
  'collapsed',
]);

export default drawerHeaderClasses;
