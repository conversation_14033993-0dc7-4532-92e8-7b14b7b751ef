import '@testing-library/jest-dom/vitest';
import React from 'react';
import { render, screen, cleanup } from '@testing-library/react';
import { describe, expect, it, afterEach } from 'vitest';
import { DrawerHeader } from './DrawerHeader.tsx';
import DrawerContext from '../Root/DrawerContext.ts';

afterEach(() => {
  cleanup();
});

const WrapperComponent = ({ expanded }) => {
  return (
    <DrawerContext.Provider
      value={{
        expanded,
      }}
    >
      <DrawerHeader data-testid="NovaDrawerHeader-root" />
    </DrawerContext.Provider>
  );
};

describe('DrawerHeader', () => {
  it('should render with default slot classes', () => {
    render(<DrawerHeader data-testid="NovaDrawerHeader-root" />);
    expect(screen.getByTestId('NovaDrawerHeader-root')).toHaveClass('NovaDrawerHeader-root');
  });

  it('should render slot classes in collapsed mode', () => {
    render(<WrapperComponent expanded={false} />);
    expect(screen.getByTestId('NovaDrawerHeader-root')).toHaveClass('NovaDrawerHeader-collapsed');
  });

  it('should render productLogo', () => {
    render(<DrawerHeader productLogo={<div>Product Logo</div>} />);
    expect(screen.getByText('Product Logo')).toBeInTheDocument();
  });

  it('should render pageTitle', () => {
    render(<DrawerHeader pageTitle="Product Name" />);
    expect(screen.getByText('Product Name')).toBeInTheDocument();
  });

  it('should render custom page title', () => {
    render(<DrawerHeader pageTitle={<div>Custom Page Title</div>} />);
    expect(screen.getByText('Custom Page Title')).toBeInTheDocument();
  });

  it('should render endDecorator', () => {
    render(<DrawerHeader endDecorator={<div>End Decorator</div>} />);
    expect(screen.getAllByText('End Decorator').length).toBeGreaterThan(0);
  });

  it('should slotProps working', () => {
    render(
      <DrawerHeader
        slotProps={{
          root: { 'data-testid': 'root' },
          endDecorator: { 'data-testid': 'endDecorator' },
        }}
        endDecorator={<div>End Decorator</div>}
      />,
    );
    expect(screen.getByTestId('root')).toBeInTheDocument();
    expect(screen.getByTestId('endDecorator')).toBeInTheDocument();
  });

  it('should render with className', () => {
    render(<DrawerHeader data-testid="NovaDrawerHeader-root" className="test" />);
    expect(screen.getByTestId('NovaDrawerHeader-root')).toHaveClass('test');
  });
});
