import * as React from 'react';
import { OverrideProps } from '@mui/types';
import { CreateSlotsAndSlotProps, SlotProps } from '../../types/slot';
import { SxProps } from '../../types/theme';
import { TypographyProps } from '../../Typography';

export type DrawerHeaderSlot = 'root' | 'productLogo' | 'pageTitle' | 'endDecorator' | 'railDecorator';

export interface DrawerHeaderSlots {
  /**
   * The component that renders the root.
   * @default 'div'
   */
  root?: React.ElementType;
  /**
   * The component that renders the product logo.
   * @default 'div'
   */
  productLogo?: React.ElementType;
  /**
   * The component that renders the page title.
   * @default 'span'
   */
  pageTitle?: React.ElementType;
  /**
   * The component that renders the end decorator.
   * @default 'div'
   */
  endDecorator?: React.ElementType;
}

export type DrawerHeaderSlotsAndSlotProps = CreateSlotsAndSlotProps<
  DrawerHeaderSlots,
  {
    root: SlotProps<'div', object, DrawerHeaderOwnerState>;
    productLogo: SlotProps<'div', object, DrawerHeaderOwnerState>;
    pageTitle: SlotProps<'span', TypographyProps, DrawerHeaderOwnerState>;
    endDecorator: SlotProps<'div', object, DrawerHeaderOwnerState>;
  }
>;

export interface DrawerHeaderTypeMap<P = object, D extends React.ElementType = 'div'> {
  props: P &
    DrawerHeaderSlotsAndSlotProps & {
      /**
       * A component for the specific product logo. It is recommended to use a product logo component from @nexusui/branding.
       */
      productLogo?: React.ReactNode;
      /**
       * The name of the current product.
       */
      pageTitle?: React.ReactNode;
      /**
       * Element placed after the children.
       */
      endDecorator?: React.ReactNode;
      /**
       * The system prop that allows defining system overrides as well as additional CSS styles.
       */
      sx?: SxProps;
    };
  defaultComponent: D;
}

export type DrawerHeaderProps<
  D extends React.ElementType = DrawerHeaderTypeMap['defaultComponent'],
  P = {
    component?: React.ElementType;
  },
> = OverrideProps<DrawerHeaderTypeMap<P, D>, D>;

export interface DrawerHeaderOwnerState extends DrawerHeaderProps {
  expanded?: boolean;
}
