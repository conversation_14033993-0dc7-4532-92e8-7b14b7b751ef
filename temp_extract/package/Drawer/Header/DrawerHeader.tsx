'use client';

import * as React from 'react';
import { styled } from '@pigment-css/react';
import { getDrawerHeaderUtilityClass } from './DrawerHeader.classes';
import { DrawerHeaderOwnerState, DrawerHeaderProps } from './DrawerHeader.types';
import useSlotProps from '@mui/utils/useSlotProps';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import { Typography } from '../../Typography';
import DrawerContext from '../Root/DrawerContext';

const useUtilityClasses = (ownerState: DrawerHeaderOwnerState) => {
  const { expanded } = ownerState;

  const slots = {
    root: ['root', expanded ? 'expanded' : 'collapsed'],
    productLogo: ['productLogo'],
    pageTitle: ['pageTitle'],
    endDecorator: ['endDecorator'],
    container: ['container'],
  };

  return composeClasses(slots, getDrawerHeaderUtilityClass, {});
};

const DrawerHeaderRoot = styled('div', {
  name: 'NovaDrawerHeader',
  slot: 'Root',
})<DrawerHeaderOwnerState>(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'flex-start',
  flexShrink: 0,
  padding: '1.25rem 0.75rem',
  paddingBottom: '0.25rem',
}));

const DrawerHeaderContainer = styled('div', {
  name: 'NovaDrawerHeader',
  slot: 'Header',
})<DrawerHeaderOwnerState>(({ theme }) => ({
  width: 'calc(var(--nova-drawer-width) - 36px)',
  boxSizing: 'border-box',
  display: 'flex',
  flexDirection: 'row',
  alignItems: 'center',
  gap: '0.75rem',
}));

const DrawerHeaderProductLogo = styled('div', {
  name: 'NovaDrawerHeader',
  slot: 'ProductLogo',
})<DrawerHeaderOwnerState>(({ theme }) => ({
  width: 'calc(var(--nova-drawer-rail-width) - 24px)',
  height: 48,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
}));

const DrawerHeaderPageTitle = styled(Typography, {
  name: 'NovaDrawerHeader',
  slot: 'PageTitle',
})<DrawerHeaderOwnerState>(({ theme }) => ({
  flex: '1 1 auto',
  color: theme.vars.palette.onSurface,
  opacity: 1,
  transitionProperty: 'opacity',
  transitionDuration: 'var(--nova-drawer-transition-duration-exit)',
  transitionTimingFunction: 'cubic-bezier(0.4, 0, 0.6, 1)',
  variants: [
    {
      props: { expanded: false },
      style: {
        opacity: 0,
        transitionDuration: 'var(--nova-drawer-transition-duration-enter)',
      },
    },
  ],
}));

const DrawerHeaderEndDecorator = styled('div', {
  name: 'NovaDrawerHeader',
  slot: 'RailDecorator',
})<DrawerHeaderOwnerState>(({ theme }) => ({
  width: 'calc(var(--nova-drawer-rail-width) - 24px)',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  overflow: 'hidden',
  marginTop: '0.25rem',
}));

export const DrawerHeader = React.forwardRef(function DrawerHeader(
  props: DrawerHeaderProps,
  ref: React.Ref<HTMLElement>,
) {
  const contextValue = React.useContext(DrawerContext);

  const {
    className,
    children,
    slots = {},
    slotProps = {},
    component,
    productLogo,
    pageTitle,
    endDecorator,
    sx,
    ...others
  } = props;

  const expanded = contextValue?.expanded;

  const ownerState = { ...props, expanded };

  const classes = useUtilityClasses(ownerState);

  const RootSlot = slots.root ?? DrawerHeaderRoot;
  const ProductLogoSlot = slots.productLogo ?? DrawerHeaderProductLogo;
  const PageTitleSlot = slots.pageTitle ?? DrawerHeaderPageTitle;
  const EndDecoratorSlot = slots.endDecorator ?? DrawerHeaderEndDecorator;

  const rootSlotProps = useSlotProps({
    elementType: RootSlot,
    externalSlotProps: slotProps.root,
    externalForwardedProps: others,
    ownerState,
    className: [classes.root, className],
    additionalProps: {
      ref,
      as: component,
    },
  });

  const productLogoSlotProps = useSlotProps({
    elementType: ProductLogoSlot,
    externalSlotProps: slotProps.productLogo,
    ownerState,
    className: classes.productLogo,
  });

  const pageTitleSlotProps = useSlotProps({
    elementType: PageTitleSlot,
    externalSlotProps: slotProps.pageTitle,
    ownerState,
    className: classes.pageTitle,
    additionalProps: {
      variant: 'labelMedium',
    },
  });

  const endDecoratorSlotProps = useSlotProps({
    elementType: EndDecoratorSlot,
    externalSlotProps: slotProps.endDecorator,
    ownerState,
    className: classes.endDecorator,
  });

  const containerSlotProps = useSlotProps({
    elementType: DrawerHeaderContainer,
    externalSlotProps: {},
    ownerState,
    className: classes.container,
  });

  return (
    <RootSlot {...rootSlotProps}>
      <DrawerHeaderContainer {...containerSlotProps}>
        {productLogo && <ProductLogoSlot {...productLogoSlotProps}>{productLogo}</ProductLogoSlot>}
        {pageTitle && <PageTitleSlot {...pageTitleSlotProps}>{pageTitle}</PageTitleSlot>}
      </DrawerHeaderContainer>
      {endDecorator && <EndDecoratorSlot {...endDecoratorSlotProps}>{endDecorator}</EndDecoratorSlot>}
    </RootSlot>
  );
});
