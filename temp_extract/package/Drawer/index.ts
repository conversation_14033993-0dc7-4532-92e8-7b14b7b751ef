import { DrawerRoot } from './Root';
import { DrawerHeader } from './Header';
import { DrawerBody } from './Body';
import { DrawerNavGroup } from './NavGroup';
import { DrawerNavItem } from './NavItem';
import { DrawerFooter } from './Footer';

export { DrawerRoot, drawerRootClasses } from './Root';
export { DrawerHeader, drawerHeaderClasses } from './Header';
export { DrawerBody, drawerBodyClasses } from './Body';
export { DrawerNavGroup, drawerNavGroupClasses } from './NavGroup';
export { DrawerNavItem, drawerNavItemClasses } from './NavItem';
export type { DrawerRootProps } from './Root';
export type { DrawerHeaderProps } from './Header';
export type { DrawerBodyProps } from './Body';
export type { DrawerNavGroupProps } from './NavGroup';
export type { DrawerNavItemProps } from './NavItem';
export type { DrawerFooterProps } from './Footer';

export const Drawer = {
  Root: DrawerRoot,
  Header: DrawerHeader,
  Body: DrawerBody,
  NavGroup: DrawerNavGroup,
  NavItem: DrawerNavItem,
  Footer: DrawerFooter,
};
