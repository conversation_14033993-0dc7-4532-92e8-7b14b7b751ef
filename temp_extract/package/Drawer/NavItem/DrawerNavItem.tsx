'use client';

import * as React from 'react';
import { styled } from '@pigment-css/react';
import DrawerNavItemClasses, { getDrawerNavItemUtilityClass } from './DrawerNavItem.classes';
import { DrawerNavItemOwnerState, DrawerNavItemProps } from './DrawerNavItem.types';
import { DrawerNavGroup } from '../NavGroup';
import useSlotProps from '@mui/utils/useSlotProps';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import DrawerContext, { SubNavDrawerContext } from '../Root/DrawerContext';
import { Typography } from '../../Typography';
import { Badge, badgeClasses } from '../../Badge';
import { Tooltip } from '../../Tooltip';
import ExpandMore from '../../internal/svg-icons/ExpandMore';
import ExpandLess from '../../internal/svg-icons/ExpandLess';

const NavLevelContext = React.createContext(0);

const useUtilityClasses = (ownerState: DrawerNavItemOwnerState) => {
  const { expanded, selected, currentLevel } = ownerState;

  const slots = {
    root: [
      'root',
      expanded ? 'expanded' : 'collapsed',
      selected && 'selected',
      currentLevel > 0 && `level${currentLevel}`,
    ],
    container: ['container'],
    label: ['label'],
    railLabel: ['railLabel'],
    startDecorator: ['startDecorator'],
    trailingAction: ['trailingAction'],
    trailingLabel: ['trailingLabel'],
    trailingBadge: ['trailingBadge'],
  };

  return composeClasses(slots, getDrawerNavItemUtilityClass, {});
};

// Recursively finds the first child element.
function findFirstChild(children: React.ReactNode): React.ReactElement | null {
  if (!children) return null;

  const childArray = React.Children.toArray(children);

  for (const child of childArray) {
    if (React.isValidElement(child)) {
      if (!child.props.children) {
        return child;
      }

      const leaf = findFirstChild(child.props.children);
      if (leaf) return leaf;
    }
  }

  return null;
}

// Checks if any child element is selected by recursively searching for the active item.
function hasSelectedChild(children: React.ReactNode, activeId: string): boolean {
  if (!children) return false;

  const childArray = React.Children.toArray(children);

  for (const child of childArray) {
    if (React.isValidElement(child)) {
      if (child.props.itemId === activeId) {
        return true;
      }

      const selected = hasSelectedChild(child.props.children, activeId);
      if (selected) return true;
    }
  }

  return null;
}

const DrawerNavItemRoot = styled(Tooltip, {
  name: 'NovaDrawerNavItem',
  slot: 'Root',
})<DrawerNavItemOwnerState>(({ theme }) => ({
  width: '100%',
  display: 'flex',
  textAlign: 'left',
  textDecoration: 'none',
  flexDirection: 'column',
  justifyContent: 'center',
  alignItems: 'flex-start',
  cursor: 'pointer',
  color: theme.vars.palette.onSurfaceVariant,
  borderRadius: '0.5rem',
  outline: 'none',
  height: 56,
  '@media (max-width: 599px)': {
    height: 48,
  },
  transitionProperty: 'margin',
  transitionDuration: 'var(--nova-drawer-transition-duration-exit)',
  transitionTimingFunction: 'cubic-bezier(0.4, 0, 0.6, 1)',
  '&:focus-visible': {
    backgroundColor: `color-mix(in srgb, ${theme.vars.palette.surfaceContainer}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.focusOnSurface})`,
    outlineOffset: '1px',
    outline: `2px solid ${theme.vars.palette.secondary}`,
  },
  '&:hover': {
    backgroundColor: `color-mix(in srgb, ${theme.vars.palette.surfaceContainer}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.hoverOnSurface})`,
  },
  '&:active': {
    backgroundColor: `color-mix(in srgb, ${theme.vars.palette.surfaceContainer}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.pressOnSurface})`,
  },
  variants: [
    {
      props: { expanded: false },
      style: {
        marginBottom: '0.25rem',
        transitionDuration: 'var(--nova-drawer-transition-duration-enter)',
        '&:last-of-type': {
          marginBottom: 0,
        },
      },
    },
    {
      props: { selected: true },
      style: {
        color: theme.vars.palette.onSecondaryContainer,
        backgroundColor: theme.vars.palette.secondaryContainer,
        '&:hover': {
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.secondaryContainer}, ${theme.vars.palette.secondary} ${theme.vars.palette.stateLayers.hoverSecondary})`,
        },
        '&:active': {
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.secondaryContainer}, ${theme.vars.palette.secondary} ${theme.vars.palette.stateLayers.pressSecondary})`,
        },
      },
    },
  ],
}));

const DrawerNavItemContainer = styled('div', {
  name: 'NovaDrawerNavItem',
  slot: 'Container',
})<DrawerNavItemOwnerState>(({ theme }) => ({
  boxSizing: 'border-box',
  width: '100%',
  padding: '0 1rem',
  display: 'grid',
  alignItems: 'center',
  transitionProperty: 'height',
  transitionDuration: 'var(--nova-drawer-transition-duration-exit)',
  transitionTimingFunction: 'cubic-bezier(0.4, 0, 0.6, 1)',
  variants: [
    {
      props: { expanded: false },
      style: {
        transitionDuration: 'var(--nova-drawer-transition-duration-enter)',
      },
    },
  ],
}));

const DrawerNavItemContent = styled('div', {
  name: 'NovaDrawerNavItem',
  slot: 'Content',
})<DrawerNavItemOwnerState>(({ theme }) => ({
  boxSizing: 'border-box',
  width: 'var(--nova-drawer-nav-item-width)',
  display: 'flex',
  flexDirection: 'row',
  alignItems: 'center',
  gap: '1rem',
}));

const DrawerNavItemTrailingContent = styled('div', {
  name: 'NovaDrawerNavItem',
  slot: 'Trailing',
})<DrawerNavItemOwnerState>(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: '0.5rem',
  fontSize: '1.5rem',
}));

const DrawerNavItemLabel = styled(Typography, {
  name: 'NovaDrawerHeader',
  slot: 'Label',
})<DrawerNavItemOwnerState>(({ theme }) => ({
  flex: '1',
  overflow: 'hidden',
  whiteSpace: 'nowrap',
  textOverflow: 'ellipsis',
  transitionProperty: 'opacity',
  transitionDuration: 'var(--nova-drawer-transition-duration-exit)',
  transitionTimingFunction: 'cubic-bezier(0.4, 0, 0.6, 1)',
  opacity: 1,
  variants: [
    {
      props: { expanded: false },
      style: {
        opacity: 0,
        transitionDuration: 'var(--nova-drawer-transition-duration-enter)',
      },
    },
  ],
}));

const DrawerNavItemTrailingLabel = styled(Typography, {
  name: 'NovaDrawerHeader',
  slot: 'TrailingLabel',
})<DrawerNavItemOwnerState>(({ theme }) => ({}));

const DrawerNavItemStartDecorator = styled('div', {
  name: 'NovaDrawerNavItem',
  slot: 'Icon',
})<DrawerNavItemOwnerState>(({ theme }) => ({
  lineHeight: 0,
  fontSize: 24,
}));

const DrawerNavItemTrailingAction = styled('div', {
  name: 'NovaDrawerNavItem',
  slot: 'TrailingAction',
})<DrawerNavItemOwnerState>(({ theme }) => ({
  lineHeight: 0,
  fontSize: 24,
}));

const DrawerNavItemBadge = styled(Badge, {
  name: 'NovaDrawerNavItem',
  slot: 'Badge',
})<DrawerNavItemOwnerState>(({ theme }) => ({
  [`& .${badgeClasses.badge}`]: {
    position: 'relative',
    left: 'unset',
    bottom: 'unset',
  },
}));

const DrawerNavItemRailLabel = styled(Typography, {
  name: 'NovaDrawerNavItem',
  slot: 'RailLabel',
})<DrawerNavItemOwnerState>(({ theme }) => ({
  height: 0,
  fontSize: 10,
  lineHeight: '14px',
  opacity: 0,
  overflow: 'hidden',
  width: 56,
  whiteSpace: 'nowrap',
  textOverflow: 'ellipsis',
  textAlign: 'center',
  transitionProperty: 'height, opacity',
  transitionDuration: 'var(--nova-drawer-transition-duration-exit)',
  transitionTimingFunction: 'cubic-bezier(0.4, 0, 0.6, 1)',
  variants: [
    {
      props: { expanded: false },
      style: {
        height: 14,
        opacity: 1,
        transitionDuration: 'var(--nova-drawer-transition-duration-enter)',
      },
    },
    {
      props: { expanded: false, showRailLabel: false },
      style: {
        height: 0,
      },
    },
  ],
}));

const Collapse = styled('div')<DrawerNavItemOwnerState>(({ theme }) => ({
  display: 'grid',
  gridTemplateRows: '0fr',
  transitionProperty: 'grid-template-rows',
  transitionDuration: 'var(--nova-drawer-transition-duration-exit)',
  transitionTimingFunction: 'cubic-bezier(0.4, 0, 0.6, 1)',

  variants: [
    {
      props: { expanded: true, subMenuOpen: true },
      style: {
        gridTemplateRows: '1fr',
        transitionDuration: 'var(--nova-drawer-transition-duration-enter)',
      },
    },
  ],
}));

const StyledDrawerNavGroup = styled(DrawerNavGroup)<DrawerNavItemOwnerState>(({ theme }) => ({
  padding: 0,
  overflow: 'hidden',
  [`& .${DrawerNavItemClasses.root}`]: {
    height: 56,
    '@media (max-width: 599px)': {
      height: 48,
    },
  },
  [`& .${DrawerNavItemClasses.container}`]: {
    paddingLeft: 'var(--nova-drawer-nav-item-padding)',
  },
}));

export const DrawerNavItem = React.forwardRef(function DrawerNavItem(
  props: DrawerNavItemProps,
  ref: React.Ref<HTMLElement>,
) {
  const parentLevel = React.useContext(NavLevelContext);
  const currentLevel = parentLevel + 1;

  const {
    className,
    children,
    label,
    trailingLabel,
    startDecorator: startDecoratorProp,
    trailingAction,
    trailingBadge,
    slots = {},
    slotProps = {},
    itemId,
    selected: selectedProp = false,
    component,
    ...other
  } = props;

  const [subMenuOpen, setSubMenuOpen] = React.useState(false);

  const hasChildren = Boolean(children);

  const {
    expanded: expandedContext,
    showRailLabel,
    activeItem,
    onItemSelect,
    subNavDrawerContent,
    handleSubNavDrawerOpen,
    handleSubNavDrawerClose,
    handleSetTempSubNavDrawerContent,
    handleSetSubNavDrawerContent,
    handleRestoreSubNavContent,
  } = React.useContext(DrawerContext);

  const { isInSubNavDrawer } = React.useContext(SubNavDrawerContext);

  const expanded = isInSubNavDrawer ?? expandedContext;

  const selected = React.useMemo(() => {
    return (
      selectedProp ||
      (!hasChildren && itemId && activeItem === itemId) ||
      (!expanded && hasSelectedChild(children, activeItem))
    );
  }, [selectedProp, hasChildren, itemId, activeItem, expanded, children]);

  const startDecorator = React.isValidElement(startDecoratorProp)
    ? React.cloneElement(startDecoratorProp as React.ReactElement, { filled: selected })
    : startDecoratorProp;

  const ownerState = { ...props, expanded, selected, showRailLabel, subMenuOpen, currentLevel };
  const classes = useUtilityClasses(ownerState);

  const RootSlot = slots.root ?? DrawerNavItemRoot;
  const StartDecoratorSlot = slots.startDecorator ?? DrawerNavItemStartDecorator;
  const TrailingActionSlot = slots.trailingAction ?? DrawerNavItemTrailingAction;
  const LabelSlot = slots.label ?? DrawerNavItemLabel;
  const RailLabelSlot = slots.railLabel ?? DrawerNavItemRailLabel;
  const TrailingLabelSlot = slots.trailingLabel ?? DrawerNavItemTrailingLabel;

  const rootSlotProps = useSlotProps({
    elementType: RootSlot,
    externalSlotProps: slotProps.root,
    externalForwardedProps: other,
    ownerState,
    className: [classes.root, className],
    additionalProps: {
      ref,
      as: component,
      role: 'li',
      title: label,
      open: expanded ? false : undefined,
      placement: 'right',
      showArrow: true,
      onClick: () => {
        if (expanded) {
          if (hasChildren) {
            setSubMenuOpen((prev) => !prev);
          } else {
            onItemSelect(itemId);
            handleSetTempSubNavDrawerContent(subNavDrawerContent);
          }
        } else {
          if (!isInSubNavDrawer) {
            handleSetTempSubNavDrawerContent(hasChildren ? { children, header: label } : null);
            if (hasChildren) {
              const node = findFirstChild(children);
              if (node) onItemSelect(node.props.itemId);
            } else {
              onItemSelect(itemId);
              handleSubNavDrawerClose();
            }
          } else {
            hasChildren ? handleSetSubNavDrawerContent({ children, header: label }) : onItemSelect(itemId);
          }
        }
      },
      onMouseEnter: () => {
        if (!isInSubNavDrawer) {
          if (hasChildren) {
            handleSubNavDrawerOpen();
            handleSetSubNavDrawerContent({ children, header: label });
          } else {
            handleRestoreSubNavContent();
          }
        }
      },
    },
  });

  const startDecoratorSlotProps = useSlotProps({
    elementType: StartDecoratorSlot,
    externalSlotProps: slotProps.startDecorator,
    ownerState,
    className: classes.startDecorator,
  });

  const TrailingActionSlotProps = useSlotProps({
    elementType: TrailingActionSlot,
    externalSlotProps: slotProps.trailingAction,
    ownerState,
    className: classes.trailingAction,
  });

  const badgeSlotProps = useSlotProps({
    elementType: DrawerNavItemBadge,
    externalSlotProps: slotProps.trailingBadge,
    ownerState,
    className: classes.trailingBadge,
    additionalProps: {
      ...trailingBadge,
    },
  });

  const containerSlotProps = useSlotProps({
    elementType: DrawerNavItemContainer,
    externalSlotProps: {},
    ownerState,
    className: classes.container,
    additionalProps: {
      style: {
        '--nova-drawer-nav-item-padding': (currentLevel - 1) * 3 + 'rem',
        '--nova-drawer-nav-item-width': `calc(var(--nova-drawer-width) - 56px - ${currentLevel - 1} * 2rem)`,
      },
    },
  });

  const labelSlotProps = useSlotProps({
    elementType: LabelSlot,
    externalSlotProps: slotProps.label,
    ownerState,
    className: classes.label,
  });

  const railLabelSlotProps = useSlotProps({
    elementType: RailLabelSlot,
    externalSlotProps: slotProps.railLabel,
    ownerState,
    className: classes.railLabel,
    additionalProps: {
      variant: 'labelSmall',
    },
  });

  const trailingLabelSlotProps = useSlotProps({
    elementType: TrailingLabelSlot,
    externalSlotProps: slotProps.trailingLabel,
    ownerState,
    className: classes.trailingLabel,
    additionalProps: {
      variant: 'labelMedium',
    },
  });

  const collapseProps = useSlotProps({
    elementType: Collapse,
    externalSlotProps: {},
    ownerState,
  });

  return (
    <NavLevelContext.Provider value={currentLevel}>
      <RootSlot {...rootSlotProps}>
        <DrawerNavItemContainer {...containerSlotProps}>
          <DrawerNavItemContent>
            {startDecorator && (
              <StartDecoratorSlot {...startDecoratorSlotProps}>
                {trailingBadge && !expanded ? <Badge {...trailingBadge}>{startDecorator}</Badge> : startDecorator}
              </StartDecoratorSlot>
            )}
            {label && <LabelSlot {...labelSlotProps}>{label}</LabelSlot>}
            <DrawerNavItemTrailingContent>
              {trailingLabel && <TrailingLabelSlot {...trailingLabelSlotProps}>{trailingLabel}</TrailingLabelSlot>}
              {trailingBadge && <DrawerNavItemBadge {...badgeSlotProps} />}
              {trailingAction && <TrailingActionSlot {...TrailingActionSlotProps}>{trailingAction}</TrailingActionSlot>}
              {Boolean(children) && (
                <>{subMenuOpen ? <ExpandLess fontSize="inherit" /> : <ExpandMore fontSize="inherit" />}</>
              )}
            </DrawerNavItemTrailingContent>
          </DrawerNavItemContent>
        </DrawerNavItemContainer>
        <RailLabelSlot {...railLabelSlotProps}>{showRailLabel ? label : ''}</RailLabelSlot>
      </RootSlot>
      {Boolean(children) && (
        <Collapse {...collapseProps}>
          <StyledDrawerNavGroup>{children}</StyledDrawerNavGroup>
        </Collapse>
      )}
    </NavLevelContext.Provider>
  );
});
