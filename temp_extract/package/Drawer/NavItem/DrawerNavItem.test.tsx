import '@testing-library/jest-dom/vitest';
import React from 'react';
import { render, screen, cleanup } from '@testing-library/react';
import { describe, expect, it, afterEach } from 'vitest';
import { DrawerNavItem } from './DrawerNavItem.tsx';
import DrawerContext from '../Root/DrawerContext.ts';

afterEach(() => {
  cleanup();
});

const WrapperComponent = ({ expanded, showRailLabel = true, label = '' }) => {
  return (
    <DrawerContext.Provider
      value={{
        expanded,
        showRailLabel,
      }}
    >
      <DrawerNavItem label={label} data-testid="NovaDrawerNavItem-root" />
    </DrawerContext.Provider>
  );
};

describe('DrawerNavItem', () => {
  it('should render with default slot classes', () => {
    render(<DrawerNavItem data-testid="NovaDrawerNavItem-root" />);
    expect(screen.getByTestId('NovaDrawerNavItem-root')).toBeDefined();
    expect(screen.getByTestId('NovaDrawerNavItem-root')).toHaveClass('NovaDrawerNavItem-root');
  });

  it('should render slot classes in rail mode', () => {
    render(<WrapperComponent expanded={false} />);
    expect(screen.getByTestId('NovaDrawerNavItem-root')).toHaveClass('NovaDrawerNavItem-collapsed');
  });

  it('should render selected class', () => {
    render(<DrawerNavItem data-testid="NovaDrawerNavItem-root" selected />);
    expect(screen.getByTestId('NovaDrawerNavItem-root')).toHaveClass('Mui-selected');
  });

  it('should render label', () => {
    render(<DrawerNavItem label="Label" />);
    expect(screen.getByText('Label')).toBeDefined();
  });

  it('should render railLabel', () => {
    render(<WrapperComponent expanded={false} showRailLabel label="Rail Label" />);
    expect(screen.getAllByText('Rail Label').length).toBeGreaterThan(0);
  });

  it('should render trailingLabel', () => {
    render(<DrawerNavItem trailingLabel="Trailing Label" />);
    expect(screen.getByText('Trailing Label')).toBeDefined();
  });

  it('should render startDecorator', () => {
    render(<DrawerNavItem startDecorator={<div>Start Decorator</div>} />);
    expect(screen.getByText('Start Decorator')).toBeDefined();
  });

  it('should render trailingAction', () => {
    render(<DrawerNavItem trailingAction={<div>Trailing Action</div>} />);
    expect(screen.getByText('Trailing Action')).toBeDefined();
  });

  it('should render trailingBadge', () => {
    render(
      <DrawerNavItem
        trailingBadge={{
          badgeContent: '99+',
          color: 'error',
          slotProps: { badge: { 'data-testid': 'NovaBadge-content' } },
        }}
      />,
    );

    expect(screen.getByTestId('NovaBadge-content')).toHaveClass('NovaBadge-colorError');
    expect(screen.getByTestId('NovaBadge-content')).toHaveClass('NovaBadge-sizeLarge');
    expect(screen.getByTestId('NovaBadge-content')).toHaveTextContent('99+');
  });

  it('should render small badge in collapsed mode', () => {
    const { container } = render(
      <DrawerContext.Provider
        value={{
          expanded: false,
        }}
      >
        <DrawerNavItem
          trailingBadge={{
            badgeContent: '99+',
            color: 'error',
          }}
        />
      </DrawerContext.Provider>,
    );
    const element = container.querySelector('.NovaBadge-sizeSmall');
    expect(element).toBeDefined();
  });

  it('should render with className', () => {
    render(<DrawerNavItem data-testid="NovaDrawerNavItem-root" className="my-class" />);
    expect(screen.getByTestId('NovaDrawerNavItem-root')).toHaveClass('my-class');
  });

  it('should slotProps working', () => {
    render(
      <DrawerNavItem
        slotProps={{
          label: { 'data-testid': 'NovaDrawerNavItem-label' },
          railLabel: { 'data-testid': 'NovaDrawerNavItem-railLabel' },
          trailingLabel: { 'data-testid': 'NovaDrawerNavItem-trailingLabel' },
          startDecorator: { 'data-testid': 'NovaDrawerNavItem-startDecorator' },
          trailingAction: { 'data-testid': 'NovaDrawerNavItem-trailingAction' },
          trailingBadge: { 'data-testid': 'NovaDrawerNavItem-trailingBadge' },
        }}
        label="Label"
        trailingLabel="Trailing Label"
        startDecorator={<div>Start Decorator</div>}
        trailingAction={<div>Trailing Action</div>}
        trailingBadge={<div>Trailing Badge</div>}
      />,
    );
    expect(screen.getByTestId('NovaDrawerNavItem-label')).toBeDefined();
    expect(screen.getByTestId('NovaDrawerNavItem-trailingLabel')).toBeDefined();
    expect(screen.getByTestId('NovaDrawerNavItem-startDecorator')).toBeDefined();
    expect(screen.getByTestId('NovaDrawerNavItem-trailingAction')).toBeDefined();
    expect(screen.getByTestId('NovaDrawerNavItem-trailingBadge')).toBeDefined();
  });
});
