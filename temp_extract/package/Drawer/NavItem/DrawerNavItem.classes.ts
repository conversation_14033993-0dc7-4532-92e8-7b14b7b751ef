import {
  unstable_generateUtilityClasses as generateUtilityClasses,
  unstable_generateUtilityClass as generateUtilityClass,
} from '@mui/utils';

export interface DrawerNavItemClasses {
  /** Class name applied to the root element. */
  root: string;
  /** Class name applied to the container element. */
  container: string;
  /** Class name applied to the label element. */
  label: string;
  /** Class name applied to the rail label element. */
  railLabel: string;
  /** Class name applied to the start decorator element. */
  startDecorator: string;
  /** Class name applied to the end decorator element. */
  trailingAction: string;
  /** Class name applied to the trailing label element. */
  trailingLabel: string;
  /** Class name applied to the badge element. */
  trailingBadge: string;
  /** Styles applied to the root element if `expanded=true`. */
  expanded: string;
  /** Styles applied to the root element if `expanded=false`. */
  collapsed: string;
}

export type DrawerNavItemClassKey = keyof DrawerNavItemClasses;

export function getDrawerNavItemUtilityClass(slot: string): string {
  return generateUtilityClass('NovaDrawerNavItem', slot);
}

const DrawerNavItemClasses: DrawerNavItemClasses = generateUtilityClasses('NovaDrawerNavItem', [
  'root',
  'container',
  'label',
  'railLabel',
  'startDecorator',
  'trailingLabel',
  'trailingAction',
  'trailingBadge',
  'expanded',
  'collapsed',
]);

export default DrawerNavItemClasses;
