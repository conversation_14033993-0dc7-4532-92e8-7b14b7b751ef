import * as React from 'react';
import { OverrideProps } from '@mui/types';
import { CreateSlotsAndSlotProps, SlotProps } from '../../types/slot';
import { SxProps } from '../../types/theme';
import { BadgeProps } from '../../Badge';
import { TypographyProps } from '../../Typography';

export type DrawerNavItemSlot = 'root' | 'startDecorator' | 'label' | 'endDecorator' | 'badge' | 'railLabel';

export interface DrawerNavItemSlots {
  /**
   * The component that renders the root.
   * @default 'li'
   */
  root?: React.ElementType;
  /**
   * The component that renders the start decorator.
   * @default 'div'
   */
  startDecorator?: React.ElementType;
  /**
   * The component that renders the label.
   * @default 'span'
   */
  label?: React.ElementType;
  /**
   * The component that renders the rail label.
   * @default 'span'
   */
  railLabel?: React.ElementType;
  /**
   * The component that renders the trailing label.
   * @default 'span'
   */
  trailingLabel?: React.ElementType;
  /**
   * The component that renders the end decorator.
   * @default 'div'
   */
  trailingAction?: React.ElementType;
  /**
   * The component that renders the badge.
   * @default 'div'
   */
  trailingBadge?: React.ElementType;
}

export type DrawerNavItemSlotsAndSlotProps = CreateSlotsAndSlotProps<
  DrawerNavItemSlots,
  {
    root: SlotProps<'li', object, DrawerNavItemOwnerState>;
    startDecorator: SlotProps<'div', object, DrawerNavItemOwnerState>;
    label: SlotProps<'span', TypographyProps, DrawerNavItemOwnerState>;
    railLabel: SlotProps<'span', TypographyProps, DrawerNavItemOwnerState>;
    trailingLabel: SlotProps<'span', TypographyProps, DrawerNavItemOwnerState>;
    trailingAction: SlotProps<'div', object, DrawerNavItemOwnerState>;
    trailingBadge: SlotProps<'div', BadgeProps, DrawerNavItemOwnerState>;
  }
>;

export interface DrawerNavItemTypeMap<P = object, D extends React.ElementType = 'div'> {
  props: P &
    DrawerNavItemSlotsAndSlotProps & {
      /**
       * The unique identifier of the DrawerNavItem. item will be selected if the itemId matches the `activeItem`.
       */
      itemId?: string;
      /**
       * Use to apply selected styling.
       * @default false
       */
      selected?: boolean;
      /**
       * Element placed before the children.
       */
      startDecorator?: React.ReactNode;
      /**
       * The label to display in the DrawerNavItem.
       */
      label?: React.ReactNode;
      /**
       * The label to display at the right of the DrawerNavItem.
       */
      trailingLabel?: React.ReactNode;
      /**
       * The Badge to display at the right of the DrawerNavItem.
       */
      trailingBadge?: BadgeProps;
      /**
       * The action to display at the end of the DrawerNavItem.
       */
      trailingAction?: React.ReactNode;
      /**
       * The system prop that allows defining system overrides as well as additional CSS styles.
       */
      sx?: SxProps;
    };
  defaultComponent: D;
}

export type DrawerNavItemProps<
  D extends React.ElementType = DrawerNavItemTypeMap['defaultComponent'],
  P = {
    component?: React.ElementType;
  },
> = OverrideProps<DrawerNavItemTypeMap<P, D>, D>;

export interface DrawerNavItemOwnerState extends DrawerNavItemProps {
  expanded?: boolean;
  subMenuOpen?: boolean;
  currentLevel?: number;
}
