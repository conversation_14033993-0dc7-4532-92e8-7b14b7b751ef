import * as React from 'react';
import { OverrideProps } from '@mui/types';
import { CreateSlotsAndSlotProps, SlotProps } from '../../types/slot';
import { SxProps } from '../../types/theme';

export type DrawerFooterSlot = 'root';

export interface DrawerFooterSlots {
  /**
   * The component that renders the root.
   * @default 'div'
   */
  root?: React.ElementType;
}

export type DrawerFooterSlotsAndSlotProps = CreateSlotsAndSlotProps<
  DrawerFooterSlots,
  {
    root: SlotProps<'div', object, DrawerFooterOwnerState>;
  }
>;

export interface DrawerFooterTypeMap<P = object, D extends React.ElementType = 'div'> {
  props: P &
    DrawerFooterSlotsAndSlotProps & {
      /**
       * The system prop that allows defining system overrides as well as additional CSS styles.
       */
      sx?: SxProps;
    };
  defaultComponent: D;
}

export type DrawerFooterProps<
  D extends React.ElementType = DrawerFooterTypeMap['defaultComponent'],
  P = {
    component?: React.ElementType;
  },
> = OverrideProps<DrawerFooterTypeMap<P, D>, D>;

export interface DrawerFooterOwnerState extends DrawerFooterProps {
  expanded?: boolean;
}
