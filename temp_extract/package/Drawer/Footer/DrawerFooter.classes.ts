import {
  unstable_generateUtilityClasses as generateUtilityClasses,
  unstable_generateUtilityClass as generateUtilityClass,
} from '@mui/utils';

export interface DrawerFooterClasses {
  /** Class name applied to the root element. */
  root: string;
  /** Styles applied to the root element if `expanded=true`. */
  expanded: string;
  /** Styles applied to the root element if `expanded=false`. */
  collapsed: string;
}

export type DrawerFooterClassKey = keyof DrawerFooterClasses;

export function getDrawerFooterUtilityClass(slot: string): string {
  return generateUtilityClass('NovaDrawerFooter', slot);
}

const DrawerFooterClasses: DrawerFooterClasses = generateUtilityClasses('NovaDrawerFooter', [
  'root',
  'expanded',
  'collapsed',
]);

export default DrawerFooterClasses;
