'use client';

import * as React from 'react';
import { styled } from '@pigment-css/react';
import { getDrawerFooterUtilityClass } from './DrawerFooter.classes';
import { DrawerFooterOwnerState, DrawerFooterProps } from './DrawerFooter.types';
import useSlotProps from '@mui/utils/useSlotProps';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import DrawerContext from '../Root/DrawerContext';

const useUtilityClasses = (ownerState: DrawerFooterOwnerState) => {
  const { expanded } = ownerState;

  const slots = {
    root: ['root', expanded ? 'expanded' : 'collapsed'],
  };

  return composeClasses(slots, getDrawerFooterUtilityClass, {});
};

const DrawerFooterRoot = styled('div', {
  name: 'NovaDrawerFooter',
  slot: 'Root',
})<DrawerFooterOwnerState>(({ theme }) => ({
  overflowX: 'hidden',
  padding: '0 0.75rem',
  paddingBottom: '0.75rem',
  '@media (max-width: 599px)': {
    padding: '0 0.5rem',
  },
}));

export const DrawerFooter = React.forwardRef(function DrawerFooter(
  props: DrawerFooterProps,
  ref: React.Ref<HTMLElement>,
) {
  const contextValue = React.useContext(DrawerContext);

  const { className, children, slots = {}, slotProps = {}, component, ...other } = props;

  const expanded = contextValue?.expanded;

  const ownerState = { ...props, expanded };

  const classes = useUtilityClasses(ownerState);

  const RootSlot = slots.root ?? DrawerFooterRoot;

  const rootSlotProps = useSlotProps({
    elementType: RootSlot,
    externalSlotProps: slotProps.root,
    externalForwardedProps: other,
    ownerState,
    className: [classes.root, className],
    additionalProps: {
      ref,
      as: component,
    },
  });

  return <RootSlot {...rootSlotProps}>{children}</RootSlot>;
});
