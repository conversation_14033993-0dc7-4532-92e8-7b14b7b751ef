import '@testing-library/jest-dom/vitest';
import React from 'react';
import { render, screen, cleanup } from '@testing-library/react';
import { describe, expect, it, afterEach } from 'vitest';
import { DrawerFooter } from './DrawerFooter.tsx';
import DrawerContext from '../Root/DrawerContext.ts';

afterEach(() => {
  cleanup();
});

const WrapperComponent = ({ expanded }) => {
  return (
    <DrawerContext.Provider
      value={{
        expanded,
      }}
    >
      <DrawerFooter data-testid="NovaDrawerFooter-root" />
    </DrawerContext.Provider>
  );
};

describe('DrawerFooter', () => {
  it('should render with default slot classes', () => {
    render(<DrawerFooter data-testid="NovaDrawerFooter-root" />);
    expect(screen.getByTestId('NovaDrawerFooter-root')).toBeDefined();
    expect(screen.getByTestId('NovaDrawerFooter-root')).toHaveClass('NovaDrawerFooter-root');
  });

  it('should render slot classes in collapsed mode', () => {
    render(<WrapperComponent expanded={false} />);
    expect(screen.getByTestId('NovaDrawerFooter-root')).toBeDefined();
    expect(screen.getByTestId('NovaDrawerFooter-root')).toHaveClass('NovaDrawerFooter-collapsed');
  });
});
