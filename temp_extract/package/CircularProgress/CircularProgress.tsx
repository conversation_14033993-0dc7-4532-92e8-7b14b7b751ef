'use client';
import * as React from 'react';
import { unstable_composeClasses as composeClasses, unstable_capitalize as capitalize } from '@mui/utils';
import useSlotProps from '@mui/utils/useSlotProps';
import { keyframes, styled } from '@pigment-css/react';
import { CircularProgressOwnerState, CircularProgressProps } from './CircularProgress.types';
import { getCircularProgressUtilityClass } from './CircularProgress.classes';
import { ColorPaletteSystem } from '../types/colorSystem';
import { SystemColor } from '../Alert/Alert.types';

const ARC_DURATION = 1333; // milliseconds
const LINEAR_ROTATE_DURATION = (ARC_DURATION * 360) / 306; // milliseconds

const circularRotateKeyframe = keyframes`
  to {
    transform: rotate(360deg);
  }
`;

const useUtilityClasses = (ownerState: CircularProgressOwnerState) => {
  const { variant, color } = ownerState;

  const slots = {
    root: ['root', variant, color && `color${capitalize(color)}`],
    svg: ['svg'],
    track: ['track'],
    progress: ['progress'],
  };

  return composeClasses(slots, getCircularProgressUtilityClass, {});
};

export const CircularProgressRoot = styled('span')<CircularProgressProps>(({ theme }) => ({
  '--nova-circularProgress-size': '48px',
  '--nova-circularProgress-thickness': 3,
  '--nova-circularProgress-indicatorColor': theme.vars.palette.primary,
  '--nova-circularProgress-trackColor': theme.vars.palette.secondaryContainer,
  '--nova-circularProgress-radius':
    'calc(var(--nova-circularProgress-numberSize) / 2 - var(--nova-circularProgress-thickness) / 2 )',
  '--nova-circularProgress-length': 'calc(2 * 3.1415926535 * var(--nova-circularProgress-radius))',
  '--nova-circularProgress-gap': `calc( var(--nova-circularProgress-thickness) / 4)`,
  '--nova-circularProgress-gap-angle':
    'calc(var(--nova-circularProgress-gap) / var(--nova-circularProgress-radius) * 180 * 1deg)',
  height: 'var(--nova-circularProgress-size)',
  width: 'var(--nova-circularProgress-size)',
  boxSizing: 'border-box',
  display: 'inline-flex',
  justifyContent: 'center',
  alignItems: 'center',
  flexShrink: 0, // prevent from shrinking when CircularProgress is in a flex container.
  position: 'relative',
  variants: [
    {
      props: { color: 'primary' },
      style: {
        '--nova-circularProgress-indicatorColor': theme.vars.palette.primary,
      },
    },
    {
      props: { color: 'inherit' },
      style: {
        '--nova-circularProgress-indicatorColor': 'currentColor',
      },
    },
    {
      props: { color: 'error' },
      style: {
        '--nova-circularProgress-indicatorColor': theme.vars.palette.error,
      },
    },
    ...(['success', 'info', 'warning'] as SystemColor[]).map((color: SystemColor) => ({
      props: { color },
      style: {
        display: 'inline-block',
        '--nova-circularProgress-indicatorColor': theme.vars.palette.system[color as ColorPaletteSystem],
      },
    })),
    {
      props: { variant: 'determinate' },
      style: {
        transition: `transform 300ms cubic-bezier(0.4, 0, 0.2, 1)`,
      },
    },
    {
      props: { variant: 'indeterminate' },
      style: {
        animation: `linear infinite ${circularRotateKeyframe}`,
        animationDuration: `${LINEAR_ROTATE_DURATION}ms`,
      },
    },
  ],
}));

export const CircularProgressSVG = styled('svg')<CircularProgressProps>({
  width: 'inherit',
  height: 'inherit',
  display: 'inherit',
  boxSizing: 'inherit',
  position: 'absolute',
  top: '0px',
  left: '0px',
});

const CircularProgressTrack = styled('circle')<CircularProgressProps>(({ theme }) => ({
  cx: '50%',
  cy: '50%',
  fill: 'none',
  stroke: 'var(--nova-circularProgress-trackColor)',
  strokeWidth: 'var(--nova-circularProgress-thickness)',
  strokeLinecap: 'var(--nova-circularProgress-linecap, round)',
  variants: [
    {
      props: { variant: 'determinate' },
      style: {
        transition:
          'stroke-dasharray 0.3s cubic-bezier(0.4, 0, 0.2, 1), stroke-dashoffset 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
        strokeDasharray: 'max(0, calc(var(--nova-circularProgress-length) - var(--nova-circularProgress-thickness)))',
        strokeDashoffset:
          'max(0, calc((var(--nova-circularProgress-percent) / 100) * var(--nova-circularProgress-length) + var(--nova-circularProgress-thickness) * 2))',
        transform: 'rotate(calc(90deg - var(--nova-circularProgress-gap-angle))) scaleX(-1)',
        transformOrigin: 'center',
      },
    },
    {
      props: (props) => props.value === 0,
      style: {
        strokeDashoffset:
          'calc(((var(--nova-circularProgress-percent) ) / 100) * (var(--nova-circularProgress-length)) + var(--nova-circularProgress-thickness))',
      },
    },
  ],
}));

export const CircularProgressProgress = styled('circle')<CircularProgressProps>(({ theme }) => ({
  cx: '50%',
  cy: '50%',
  fill: 'none',
  stroke: 'var(--nova-circularProgress-indicatorColor)',
  strokeWidth: 'var(--nova-circularProgress-thickness)',
  strokeLinecap: 'var(--nova-circularProgress-linecap, round)',
  strokeDasharray: 'var(--nova-circularProgress-length)',
  strokeDashoffset:
    'calc(((100 - var(--nova-circularProgress-percent) ) / 100) * (var(--nova-circularProgress-length)))',
  variants: [
    {
      props: { variant: 'determinate' },
      style: {
        transition: `stroke-dashoffset 0.3s cubic-bezier(0.4, 0, 0.2, 1)`,
        strokeWidth: 'var(--nova-circularProgress-thickness)',
        transform: 'rotate(calc(-90deg + var(--nova-circularProgress-gap-angle)))',
        transformOrigin: 'center',
      },
    },
    {
      props: { variant: 'indeterminate' },
      style: {
        strokeDasharray:
          'calc(0.3 * var(--nova-circularProgress-numberSize)), calc(4 * var(--nova-circularProgress-numberSize))',
        strokeDashoffset: 0,
      },
    },
    {
      props: (props) => props.value === 100,
      style: {
        strokeDasharray: 'var(--nova-circularProgress-length)',
      },
    },
  ],
}));

export const CircularProgress = React.forwardRef(function CircularProgress(
  props: CircularProgressProps,
  ref: React.ForwardedRef<HTMLSpanElement>,
) {
  const {
    className,
    color = 'primary',
    style,
    size = 48,
    thickness = 3,
    value: rawValue = 0,
    variant = 'indeterminate',
    component = 'span',
    slots = {},
    slotProps = {},
    ...other
  } = props;

  // Clamp value between 0 and 100
  const value = Math.min(100, Math.max(0, rawValue));

  const ownerState = {
    ...props,
    color,
    size,
    thickness,
    value,
    variant,
    component,
  };

  const classes = useUtilityClasses(ownerState);
  const SlotRoot = slots.root ?? CircularProgressRoot;
  const SlotSvg = slots.svg ?? CircularProgressSVG;
  const SlotTrack = slots.track ?? CircularProgressTrack;
  const SlotProgress = slots.progress ?? CircularProgressProgress;

  const rootSlotProps = useSlotProps({
    additionalProps: {
      ref,
      as: component,
      role: 'progressbar',
      style: {
        '--nova-circularProgress-percent': value,
        '--nova-circularProgress-size': typeof size === 'number' ? `${size}px` : size,
        '--nova-circularProgress-numberSize': size,
        '--nova-circularProgress-thickness': thickness,
        width: size,
        height: size,
      },
      ...(value &&
        variant === 'determinate' && {
          'aria-valuemin': 0,
          'aria-valuemax': 100,
          'aria-valuenow': typeof value === 'number' ? Math.round(value) : Math.round(Number(value || 0)),
        }),
    },
    className: [classes.root, className],
    elementType: CircularProgressRoot,
    externalSlotProps: slotProps.root,
    externalForwardedProps: other,
    ownerState,
  });

  const svgSlotProps = useSlotProps({
    additionalProps: {
      viewBox: `${size} ${size}`,
    },
    className: classes.svg,
    elementType: CircularProgressSVG,
    externalSlotProps: slotProps.svg,
    ownerState,
  });

  const trackSlotProps = useSlotProps({
    additionalProps: {
      r: (size - thickness) / 2,
    },
    className: classes.track,
    elementType: CircularProgressTrack,
    externalSlotProps: slotProps.track,
    ownerState,
  });

  const progressSlotProps = useSlotProps({
    additionalProps: {
      r: (size - thickness) / 2,
    },
    className: classes.progress,
    elementType: CircularProgressProgress,
    externalSlotProps: slotProps.progress,
    ownerState,
  });

  return (
    <SlotRoot {...rootSlotProps}>
      <SlotSvg {...svgSlotProps}>
        <SlotTrack {...trackSlotProps} />
        <SlotProgress {...progressSlotProps} />
      </SlotSvg>
    </SlotRoot>
  );
});
