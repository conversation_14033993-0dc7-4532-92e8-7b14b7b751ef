import React from 'react';
import { OverridableStringUnion, OverrideProps } from '@mui/types';
import { CircularProgressClasses } from './CircularProgress.classes';
import { SxProps } from '../types/theme';
import { CreateSlotsAndSlotProps, SlotProps } from '../types/slot';
import { ApplyColorInversion } from '../types/colorSystem';

export type CircularProgressSlot = 'root' | 'svg' | 'progress';
export interface CircularProgressSlots {
  /**
   * The component that renders the root.
   * @default 'span'
   */
  root?: React.ElementType;

  /**
   * The component that renders the svg.
   * @default 'svg'
   */
  svg?: React.ElementType;
  /**
   * The component that renders the track.
   * @default 'circle'
   */
  track?: React.ElementType;
  /**
   * The component that renders the progress.
   * @default 'circle'
   */
  progress?: React.ElementType;
}

export interface CircularProgressPropsColorOverrides {}
export interface CircularProgressPropsSizeOverrides {}
export interface CircularProgressPropsVariantOverrides {}

export type CircularProgressSlotsAndSlotProps = CreateSlotsAndSlotProps<
  CircularProgressSlots,
  {
    root: SlotProps<'span', object, CircularProgressOwnerState>;
    svg: SlotProps<'svg', object, CircularProgressOwnerState>;
    track: SlotProps<'circle', object, CircularProgressOwnerState>;
    progress: SlotProps<'circle', object, CircularProgressOwnerState>;
  }
>;

export interface CircularProgressTypeMap<P = object, D extends React.ElementType = 'span'> {
  props: P &
    CircularProgressSlotsAndSlotProps & {
      /**
       * The color of the component.
       * @default 'primary'
       */
      color?: OverridableStringUnion<
        'primary' | 'error' | 'info' | 'success' | 'warning' | 'inherit',
        CircularProgressPropsColorOverrides
      >;
      /**
       * The size of the component.
       * @default 48
       */
      size?: number;
      /**
       * The system prop that allows defining system overrides as well as additional CSS styles.
       */
      sx?: SxProps;
      /**
       * The thickness of the circle.
       * @default 3
       */
      thickness?: number;
      /**
       * The value of the progress indicator for the determinate variant.
       * Value between 0 and 100.
       * @default 0
       */
      value?: number;
      /**
       * The variant to use.
       * Use indeterminate when there is no progress value.
       * @default 'indeterminate'
       */
      variant?: 'determinate' | 'indeterminate';
    };
  defaultComponent: D;
}

export type CircularProgressProps<
  D extends React.ElementType = CircularProgressTypeMap['defaultComponent'],
  P = { component?: React.ElementType },
> = OverrideProps<CircularProgressTypeMap<P, D>, D>;

export interface CircularProgressOwnerState extends ApplyColorInversion<CircularProgressProps> {}
