import {
  unstable_generateUtilityClasses as generateUtilityClasses,
  unstable_generateUtilityClass as generateUtilityClass,
} from '@mui/utils';

export interface CircularProgressClasses {
  /** Styles applied to the root element. */
  root: string;
  /** Styles applied to the root element if `variant="determinate"`. */
  determinate: string;
  /** Styles applied to the root element if `variant="indeterminate"`. */
  indeterminate: string;
  /** Styles applied to the root element if `color="primary"`. */
  colorPrimary: string;
  /** Styles applied to the root element if `color="error"`. */
  colorError: string;
  /** Styles applied to the root element if `color="info"`. */
  colorInfo: string;
  /** Styles applied to the root element if `color="warning"`. */
  colorWarning: string;
  /** Styles applied to the root element if `color="success"`. */
  colorSuccess: string;
  /** Styles applied to the svg element. */
  svg: string;
  /** Styles applied to the `track` element. */
  track: string;
  /** Styles applied to the `progress` element. */
  progress: string;
}

export function getCircularProgressUtilityClass(slot: string): string {
  return generateUtilityClass('NovaCircularProgress', slot);
}
const circularProgressClasses: CircularProgressClasses = generateUtilityClasses('NovaCircularProgress', [
  'root',
  'determinate',
  'indeterminate',
  'colorPrimary',
  'colorError',
  'colorInfo',
  'colorWarning',
  'colorSuccess',
  'svg',
  'track',
  'progress',
]);

export default circularProgressClasses;
