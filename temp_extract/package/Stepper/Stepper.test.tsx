/// <reference types="@testing-library/jest-dom" />
import React from 'react';
import { expect, test, describe } from 'vitest';
import { render, screen } from '@testing-library/react';
import { Stepper } from './Stepper';
import { Step } from '../Step/Step';
import stepperClasses from './Stepper.classes';

describe('<Stepper />', () => {
  test('renders basic stepper correctly', () => {
    const { container } = render(<Stepper />);
    expect(container.firstChild).toHaveClass(stepperClasses.root);
    expect(container.firstChild).toBeInTheDocument();
    expect(container.firstChild?.nodeName).toBe('OL');
  });

  test('renders with custom component prop', () => {
    const { container } = render(<Stepper component="nav" />);
    expect(container.firstChild?.nodeName).toBe('NAV');
  });

  test('renders children correctly', () => {
    render(
      <Stepper>
        <Step>Step 1</Step>
        <Step>Step 2</Step>
        <Step>Step 3</Step>
      </Stepper>,
    );

    const steps = screen.getAllByRole('listitem');
    expect(steps).toHaveLength(3);
    expect(steps[0]).toHaveTextContent('Step 1');
    expect(steps[1]).toHaveTextContent('Step 2');
    expect(steps[2]).toHaveTextContent('Step 3');
  });

  test('applies data-first-child and data-last-child attributes', () => {
    render(
      <Stepper>
        <Step>First</Step>
        <Step>Middle</Step>
        <Step>Last</Step>
      </Stepper>,
    );

    const steps = screen.getAllByRole('listitem');
    expect(steps[0]).toHaveAttribute('data-first-child');
    expect(steps[1]).not.toHaveAttribute('data-first-child');
    expect(steps[1]).not.toHaveAttribute('data-last-child');
    expect(steps[2]).toHaveAttribute('data-last-child');
  });

  test('renders with horizontal orientation by default', () => {
    const { container } = render(<Stepper />);
    expect(container.firstChild).toHaveClass(stepperClasses.horizontal);
  });

  test('renders with vertical orientation', () => {
    const { container } = render(<Stepper orientation="vertical" />);
    expect(container.firstChild).toHaveClass(stepperClasses.vertical);
  });

  test('applies custom className', () => {
    const { container } = render(<Stepper className="custom-class" />);
    expect(container.firstChild).toHaveClass('custom-class');
  });

  test('forwards ref to root element', () => {
    const ref = React.createRef<HTMLOListElement>();
    render(<Stepper ref={ref} />);
    expect(ref.current).toBeInstanceOf(HTMLOListElement);
  });

  test('handles non-element children gracefully', () => {
    const { container } = render(
      <Stepper>
        <Step>Valid Step</Step>
        {null}
        {undefined}
        {false}
        <Step>Another Valid Step</Step>
      </Stepper>,
    );

    const steps = screen.getAllByRole('listitem');
    expect(steps).toHaveLength(2);
  });

  test('passes slotProps to root', () => {
    const { container } = render(
      <Stepper
        slotProps={{
          root: {
            'data-testid': 'stepper-root',
            className: 'custom-root-class',
          },
        }}
      />,
    );

    expect(screen.getByTestId('stepper-root')).toBeInTheDocument();
    expect(container.firstChild).toHaveClass('custom-root-class');
  });

  test('renders custom slots', () => {
    const CustomRoot = React.forwardRef<HTMLDivElement>((props, ref) => (
      <div ref={ref} data-testid="custom-root" {...props} />
    ));
    CustomRoot.displayName = 'CustomRoot';

    render(
      <Stepper
        slots={{
          root: CustomRoot,
        }}
      />,
    );

    expect(screen.getByTestId('custom-root')).toBeInTheDocument();
  });
});
