'use client';
import * as React from 'react';
import { OverridableComponent } from '@mui/types';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import clsx from 'clsx';
import { styled } from '@pigment-css/react';
import { getStepperUtilityClass } from './Stepper.classes';
import { StepperOwnerState, StepperProps, StepperTypeMap } from './Stepper.types';
import useSlotProps from '@mui/utils/useSlotProps';

const useUtilityClasses = (ownerState: StepperOwnerState) => {
  const { orientation } = ownerState;

  const slots = {
    root: ['root', orientation],
  };

  return composeClasses(slots, getStepperUtilityClass, {});
};

const StepperRoot = styled('ol', {
  name: 'NovaStepper',
  slot: 'Root',
  overridesResolver: (props, styles) => styles.root,
})<StepperOwnerState>(({ theme }) => {
  return {
    '--nova-stepper-indicatorColumn': 'auto',
    '--nova-step-connectorThickness': '1px',
    '--nova-step-indicatorDotSize': '0.375rem',
    '--nova-stepper-verticalGap': '0.75rem',
    '--nova-step-gap': '0.5rem',
    '--nova-step-connectorInset': '0.375rem',
    '--nova-stepIndicator-size': '1.5rem',
    boxSizing: 'border-box',
    display: 'flex',
    margin: 0,
    padding: 0,
    ...theme.typography.bodyMedium,
    variants: [
      {
        props: { orientation: 'vertical' },
        style: { flexDirection: 'column', gap: 'var(--nova-stepper-verticalGap)' },
      },
    ],
  };
});

// eslint-disable-next-line react/display-name
export const Stepper = React.forwardRef<HTMLElement, StepperProps>(function Stepper(props, ref) {
  const {
    className,
    component = 'ol',
    children,
    orientation = 'horizontal',
    slots = {},
    slotProps = {},
    ...other
  } = props;

  const ownerState = {
    ...props,
    component,
    orientation,
  };

  const classes = useUtilityClasses(ownerState);

  const SlotRoot = slots.root ?? StepperRoot;
  const rootProps = useSlotProps({
    elementType: StepperRoot,
    externalSlotProps: slotProps.root,
    externalForwardedProps: other,
    additionalProps: {
      ref,
      as: component,
    },
    ownerState,
    className: clsx(classes.root, className),
  });

  return (
    <SlotRoot {...rootProps}>
      {React.Children.map(children, (child, index) => {
        if (!React.isValidElement(child)) {
          return child;
        }
        const extraProps: Record<string, any> = {};
        if (index === 0) {
          extraProps['data-first-child'] = '';
        }
        if (index === React.Children.count(children) - 1) {
          extraProps['data-last-child'] = '';
        }
        return React.cloneElement(child, extraProps);
      })}
    </SlotRoot>
  );
}) as OverridableComponent<StepperTypeMap>;
