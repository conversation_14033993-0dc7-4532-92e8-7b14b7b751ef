import generateUtilityClasses from '@mui/utils/generateUtilityClasses';
import generateUtilityClass from '@mui/utils/generateUtilityClass';

export interface StepperClasses {
  /** Class name applied to the root element. */
  root: string;
  /** Class name applied to the root element if `orientation="horizontal"`. */
  horizontal: string;
  /** Class name applied to the root element if `orientation="vertical"`. */
  vertical: string;
}

export type StepperClassKey = keyof StepperClasses;

export function getStepperUtilityClass(slot: string): string {
  return generateUtilityClass('NovaStepper', slot);
}

const stepperClasses: StepperClasses = generateUtilityClasses('NovaStepper', ['root', 'horizontal', 'vertical']);

export default stepperClasses;
