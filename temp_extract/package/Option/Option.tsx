'use client';
import * as React from 'react';
import { OverridableComponent } from '@mui/types';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import useSlotProps from '@mui/utils/useSlotProps';
import { OptionOwnerState, OptionProps, OptionTypeMap } from './Option.types';
import { getOptionUtilityClass } from './Option.classes';
import RowListContext from '../List/RowListContext';
import useForkRef from '@mui/utils/useForkRef';
import { useOption } from '../internal/hooks/useOption';
import { StyledListItemButton } from '../ListItemButton/ListItemButton';

const useUtilityClasses = (ownerState: OptionOwnerState) => {
  const { disabled, highlighted, selected } = ownerState;

  const slots = {
    root: ['root', disabled && 'disabled', highlighted && 'highlighted', selected && 'selected'],
  };

  return composeClasses(slots, getOptionUtilityClass, {});
};

// eslint-disable-next-line react/display-name
export const Option = React.forwardRef((props: OptionProps, ref: React.ForwardedRef<Element>) => {
  const { children, component = 'li', disabled = false, value, label, slots = {}, slotProps = {}, ...rest } = props;

  const row = React.useContext(RowListContext);
  const optionRef = React.useRef<HTMLLIElement>(null);
  const combinedRef = useForkRef(optionRef, ref);

  const computedLabel = label ?? (typeof children === 'string' ? children : optionRef.current?.innerText);
  const { getRootProps, selected, highlighted, index } = useOption({
    disabled,
    label: computedLabel,
    value,
    rootRef: combinedRef,
  });

  const ownerState: OptionOwnerState = {
    ...props,
    disabled,
    selected,
    highlighted,
    index,
    component,
    row,
  };

  const classes = useUtilityClasses(ownerState);

  const SlotRoot = slots.root ?? StyledListItemButton;

  const slotRootProps = useSlotProps({
    elementType: SlotRoot,
    externalSlotProps: slotProps.root,
    externalForwardedProps: rest,
    additionalProps: {
      ref: ref,
      as: component,
    },
    getSlotProps: getRootProps,
    ownerState,
    className: classes.root,
  });

  return <SlotRoot {...slotRootProps}>{children}</SlotRoot>;
}) as OverridableComponent<OptionTypeMap>;
