import generateUtilityClasses from '@mui/utils/generateUtilityClasses';
import generateUtilityClass from '@mui/utils/generateUtilityClass';

export interface OptionClasses {
  /** Class name applied to the root element. */
  root: string;
  /** Class name applied to the root element if `disabled={true}`. */
  disabled: string;
  /** Class name applied to the root element if `highlighted={true}`. */
  highlighted: string;
  /** Class name applied to the root element if `selected={true}`. */
  selected: string;
}

export type OptionClassKey = keyof OptionClasses;

export function getOptionUtilityClass(slot: string): string {
  return generateUtilityClass('NovaOption', slot);
}

const optionClasses: OptionClasses = generateUtilityClasses('NovaOption', [
  'root',
  'disabled',
  'highlighted',
  'selected',
]);

export default optionClasses;
