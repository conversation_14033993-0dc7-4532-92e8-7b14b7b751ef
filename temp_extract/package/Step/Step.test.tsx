/// <reference types="@testing-library/jest-dom" />
import React from 'react';
import { expect, test, describe } from 'vitest';
import { render, screen } from '@testing-library/react';
import { Step } from './Step';
import stepClasses from './Step.classes';

describe('<Step />', () => {
  test('renders basic step correctly', () => {
    const { container } = render(<Step>Test Step</Step>);
    expect(container.firstChild).toHaveClass(stepClasses.root);
    expect(container.firstChild).toBeInTheDocument();
    expect(container.firstChild?.nodeName).toBe('LI');
    expect(container.firstChild).toHaveTextContent('Test Step');
  });

  test('renders with custom component prop', () => {
    const { container } = render(<Step component="div">Test Step</Step>);
    expect(container.firstChild?.nodeName).toBe('DIV');
  });

  test('applies active state correctly', () => {
    const { container } = render(<Step active>Active Step</Step>);
    expect(container.firstChild).toHaveClass(stepClasses.active);
  });

  test('applies completed state correctly', () => {
    const { container } = render(<Step completed>Completed Step</Step>);
    expect(container.firstChild).toHaveClass(stepClasses.completed);
  });

  test('applies disabled state correctly', () => {
    const { container } = render(<Step disabled>Disabled Step</Step>);
    expect(container.firstChild).toHaveClass(stepClasses.disabled);
  });

  test('renders with horizontal orientation by default', () => {
    const { container } = render(<Step>Horizontal Step</Step>);
    expect(container.firstChild).toHaveClass(stepClasses.horizontal);
  });

  test('renders with vertical orientation', () => {
    const { container } = render(<Step orientation="vertical">Vertical Step</Step>);
    expect(container.firstChild).toHaveClass(stepClasses.vertical);
  });

  test('applies custom className', () => {
    const { container } = render(<Step className="custom-class">Step</Step>);
    expect(container.firstChild).toHaveClass('custom-class');
  });

  test('forwards ref to root element', () => {
    const ref = React.createRef<HTMLLIElement>();
    render(<Step ref={ref}>Step</Step>);
    expect(ref.current).toBeInstanceOf(HTMLLIElement);
  });

  test('passes slotProps to root', () => {
    render(
      <Step
        slotProps={{
          root: {
            'data-testid': 'step-root',
            className: 'custom-root-class',
          },
        }}
        icon="1"
      >
        Step
      </Step>,
    );

    expect(screen.getByTestId('step-root')).toBeInTheDocument();
    expect(screen.getByTestId('step-root')).toHaveClass('custom-root-class');
    expect(screen.getByText('1')).toBeInTheDocument();
  });

  test('renders custom slots', () => {
    const CustomRoot = React.forwardRef<HTMLDivElement>((props, ref) => (
      <div ref={ref} data-testid="custom-root" {...props} />
    ));
    CustomRoot.displayName = 'CustomRoot';

    render(
      <Step
        slots={{
          root: CustomRoot,
        }}
        icon="1"
      >
        Step
      </Step>,
    );

    expect(screen.getByTestId('custom-root')).toBeInTheDocument();
    expect(screen.getByText('1')).toBeInTheDocument();
  });

  test('renders with icon prop', () => {
    const TestIcon = () => <span data-testid="test-icon">★</span>;
    render(<Step icon={<TestIcon />}>Step with Icon</Step>);

    expect(screen.getByTestId('test-icon')).toBeInTheDocument();
    expect(screen.getByText('★')).toBeInTheDocument();
  });

  test('renders with string icon', () => {
    render(<Step icon="1">Step with Number</Step>);
    expect(screen.getByText('1')).toBeInTheDocument();
  });

  test('combines multiple states correctly', () => {
    const { container } = render(
      <Step active completed disabled orientation="vertical">
        Multi-state Step
      </Step>,
    );

    expect(container.firstChild).toHaveClass(stepClasses.active);
    expect(container.firstChild).toHaveClass(stepClasses.completed);
    expect(container.firstChild).toHaveClass(stepClasses.disabled);
    expect(container.firstChild).toHaveClass(stepClasses.vertical);
  });

  test('renders with custom indicator slot', () => {
    const CustomIndicator = React.forwardRef<HTMLDivElement>((props, ref) => (
      <div ref={ref} data-testid="custom-indicator" {...props} />
    ));
    CustomIndicator.displayName = 'CustomIndicator';

    render(
      <Step
        slots={{
          indicator: CustomIndicator,
        }}
        icon="1"
      >
        Step
      </Step>,
    );

    expect(screen.getByTestId('custom-indicator')).toBeInTheDocument();
    expect(screen.getByText('1')).toBeInTheDocument();
  });

  test('passes slotProps to indicator', () => {
    render(
      <Step
        slotProps={{
          indicator: {
            'data-testid': 'indicator',
            className: 'custom-indicator-class',
          },
        }}
        icon="1"
      >
        Step
      </Step>,
    );

    const indicator = screen.getByTestId('indicator');
    expect(indicator).toBeInTheDocument();
    expect(indicator).toHaveClass('custom-indicator-class');
    expect(indicator).toHaveClass(stepClasses.indicator);
  });

  test('does not render indicator when no icon is provided', () => {
    render(<Step>Step without icon</Step>);

    const indicator = screen.queryByRole('presentation');
    expect(indicator).not.toBeInTheDocument();
  });

  test('indicator inherits step states', () => {
    const { rerender } = render(
      <Step icon="1" active>
        Active Step
      </Step>,
    );

    const getIndicator = () => screen.getByText('1').closest(`.${stepClasses.indicator}`);

    expect(getIndicator()?.closest(`.${stepClasses.active}`)).toBeInTheDocument();

    rerender(
      <Step icon="1" completed>
        Completed Step
      </Step>,
    );
    expect(getIndicator()?.closest(`.${stepClasses.completed}`)).toBeInTheDocument();

    rerender(
      <Step icon="1" disabled>
        Disabled Step
      </Step>,
    );
    expect(getIndicator()?.closest(`.${stepClasses.disabled}`)).toBeInTheDocument();
  });

  test('renders with custom icon component', () => {
    const CustomIcon = () => <span data-testid="custom-icon">★</span>;
    render(<Step icon={<CustomIcon />}>Step with Custom Icon</Step>);

    expect(screen.getByTestId('custom-icon')).toBeInTheDocument();
  });
});
