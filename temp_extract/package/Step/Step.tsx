'use client';
import * as React from 'react';
import { OverridableComponent } from '@mui/types';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import clsx from 'clsx';
import { styled } from '@pigment-css/react';
import stepClasses, { getStepUtilityClass } from './Step.classes';
import { StepOwnerState, StepProps, StepTypeMap } from './Step.types';
import useSlotProps from '@mui/utils/useSlotProps';
import stepperClasses from '../Stepper/Stepper.classes';

const useUtilityClasses = (ownerState: StepOwnerState) => {
  const { orientation, active, completed, disabled } = ownerState;

  const slots = {
    root: ['root', orientation, active && 'active', completed && 'completed', disabled && 'disabled'],
    indicator: ['indicator'],
  };

  return composeClasses(slots, getStepUtilityClass, {});
};

const StepRoot = styled('li', {
  name: 'NovaStep',
  slot: 'Root',
  overridesResolver: (props, styles) => styles.root,
})<StepOwnerState>(({ theme }) => {
  return {
    position: 'relative',
    display: 'flex',
    gridTemplateColumns: 'var(--nova-stepper-indicatorColumn) 1fr', // for vertical stepper. has no effect on horizontal stepper.
    gridAutoFlow: 'dense',
    flex: 'var(--_Step-flex)',
    flexDirection: 'row',
    alignItems: 'var(--_Step-alignItems, center)',
    justifyContent: 'var(--_Step-justify, center)',
    gap: `var(--nova-step-gap)`,
    color: theme.vars.palette.onSurface,
    '& > *': { zIndex: 1, [`&:not(.${stepClasses.indicator})`]: { gridColumn: '2' } },
    '&::after': {
      content: '""',
      display: 'block',
      borderRadius: 'var(--nova-step-connectorRadius)',
      height: `var(--nova-step-connectorThickness)`,
      background: `var(--nova-step-connectorBg, ${theme.vars.palette.outline})`,
      flex: 1,
      marginInlineStart: `calc(var(--nova-step-connectorInset) - var(--nova-step-gap))`,
      marginInlineEnd: `var(--nova-step-connectorInset)`,
      zIndex: 0,
    },
    '&[data-last-child]::after': {
      display: 'none',
    },
    [`.${stepperClasses.horizontal} &:not([data-last-child])`]: {
      '--_Step-flex': 'auto', // requires to be `auto` to make equally connectors.
      [`&.${stepClasses.vertical}`]: {
        '--_Step-flex': 1, // requires to be `1` to make equally connectors.
      },
    },
    [`.${stepperClasses.vertical} &`]: {
      display: 'grid',
      '--_Step-justify': 'flex-start',
      '&::after': {
        gridColumn: '1',
        width: `var(--nova-step-connectorThickness)`,
        height: 'auto',
        margin: `calc(var(--nova-step-connectorInset) - var(--nova-step-gap)) auto calc(var(--nova-step-connectorInset) - var(--nova-stepper-verticalGap))`,
        alignSelf: 'stretch',
      },
    },
    variants: [
      {
        props: { orientation: 'vertical' },
        style: {
          flexDirection: 'column',
          alignItems: 'center',
          [`.${stepperClasses.horizontal} &`]: {
            '&[data-last-child]': {
              '--_Step-flex': 1,
            },
            '&[data-indicator]': {
              '--_Step-justify': 'flex-start',
            },
            '&::after': {
              margin: 0,
              position: 'absolute',
              height: `var(--nova-step-connectorThickness)`,
              zIndex: 0,
              top: `calc(var(--nova-stepIndicator-size) / 2 - var(--nova-step-connectorThickness) / 2)`,
              left: `calc(50% + var(--nova-stepIndicator-size) / 2 + var(--nova-step-connectorInset))`,
              width: `calc(100% - var(--nova-stepIndicator-size) - 2 * var(--nova-step-connectorInset))`,
            },
            [`&:has(.${stepClasses.indicator}:empty)::after`]: {
              '--nova-stepIndicator-size': '0px',
              '--nova-step-connectorInset': '0px',
              top: `calc(50% - var(--nova-step-connectorThickness) / 2)`,
            },
          },
          [`.${stepperClasses.vertical} &`]: {
            display: 'flex',
            alignItems: 'center',
            textAlign: 'center',
            minHeight: '100px',
            '& > *:not(.${stepClasses.indicator})': {
              marginTop: 'var(--nova-step-gap)',
            },
            '&:not([data-last-child])::after': {
              content: '""',
              position: 'absolute',
              width: `var(--nova-step-connectorThickness)`,
              background: `var(--nova-step-connectorBg, ${theme.vars.palette.outline})`,
              left: '50%',
              top: 'calc(100% - 24px)',
              height: '24px',
              transform: 'translateX(-50%)',
            },
          },
        },
      },
    ],
  };
});

const StepIndicator = styled('div', {
  name: 'NovaStep',
  slot: 'Indicator',
  overridesResolver: (props, styles) => styles.indicator,
})<StepOwnerState>(({ theme }) => ({
  '--Icon-fontSize': 'calc(var(--nova-stepIndicator-size, 2rem) / 2)',
  boxSizing: 'border-box',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  ...theme.typography.bodyMedium,
  fontSize: '12px !important',
  borderRadius: '50%',
  width: 'var(--nova-stepIndicator-size, 1.5rem)',
  height: 'var(--nova-stepIndicator-size, 1.5rem)',
  color: theme.vars.palette.onPrimary,
  backgroundColor: theme.vars.palette.surfaceVariant,
  border: `1px solid ${theme.vars.palette.outline}`,
  '& > span': {
    display: 'inline-flex',
    alignItems: 'center',
    justifyContent: 'center',
    paddingTop: '1px',
  },

  [`.${stepClasses.completed} &`]: {
    backgroundColor: theme.vars.palette.primary,
    borderColor: 'transparent',
  },
  [`.${stepClasses.active} &`]: {
    backgroundColor: theme.vars.palette.primary,
    borderColor: theme.vars.palette.primary,
    outline: `1px solid ${theme.vars.palette.onPrimary}`,
    outlineOffset: -2,
  },
  [`.${stepClasses.disabled} &`]: {
    color: theme.vars.palette.onBackgroundDisabled,
    backgroundColor: theme.vars.palette.backgroundDisabled,
    borderColor: theme.vars.palette.outlineDisabled,
  },
}));

// eslint-disable-next-line react/display-name
export const Step = React.forwardRef<HTMLElement, StepProps>(function Step(props, ref) {
  const {
    active = false,
    completed = false,
    className,
    component = 'li',
    children,
    disabled = false,
    orientation = 'horizontal',
    icon,
    slots = {},
    slotProps = {},
    ...other
  } = props;

  const ownerState = {
    ...props,
    active,
    completed,
    component,
    disabled,
    orientation,
  };

  const classes = useUtilityClasses(ownerState);

  const SlotRoot = slots.root ?? StepRoot;
  const rootProps = useSlotProps({
    elementType: StepRoot,
    externalSlotProps: slotProps.root,
    externalForwardedProps: other,
    additionalProps: {
      ref,
      as: component,
      'data-indicator': icon ? '' : undefined,
    },
    ownerState,
    className: clsx(classes.root, className),
  });

  const SlotIndicator = slots.indicator ?? StepIndicator;
  const indicatorProps = useSlotProps({
    elementType: SlotIndicator,
    externalSlotProps: slotProps.indicator,
    ownerState,
    className: classes.indicator,
  });

  return (
    <SlotRoot {...rootProps}>
      {icon && (
        <SlotIndicator {...indicatorProps}>
          {typeof icon === 'string' || typeof icon === 'number' ? <span>{icon}</span> : icon}
        </SlotIndicator>
      )}
      {children}
    </SlotRoot>
  );
}) as OverridableComponent<StepTypeMap>;
