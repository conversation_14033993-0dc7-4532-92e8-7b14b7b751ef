import '@testing-library/jest-dom/vitest';
import { render, screen, cleanup, fireEvent } from '@testing-library/react';
import React from 'react';
import { describe, expect, it, afterEach, vi } from 'vitest';
import { Checkbox } from './Checkbox.tsx';

afterEach(() => {
  cleanup();
});

describe('Checkbox', () => {
  it('should render normal', () => {
    render(<Checkbox data-testid="NovaCheckbox-root" />);
    expect(screen.getByTestId('NovaCheckbox-root')).toBeInTheDocument();
    expect(screen.getByTestId('NovaCheckbox-root')).toHaveClass('NovaCheckbox-root');
    expect(screen.getByTestId('NovaCheckbox-root')).toHaveClass('NovaCheckbox-colorPrimary');
    expect(screen.getByTestId('NovaCheckbox-root')).toHaveClass('NovaCheckbox-sizeMedium');
    expect(screen.getByTestId('NovaIcon-checkboxOutline')).toBeInTheDocument();
    expect(screen.getByRole('checkbox')).toBeInTheDocument();
    expect(screen.getByRole('checkbox')).toHaveClass('NovaCheckbox-input');
    expect(screen.getByRole('checkbox')).not.toBeChecked();
  });

  it('should render error checkbox', () => {
    render(<Checkbox data-testid="NovaCheckbox-root" color="error" />);
    expect(screen.getByTestId('NovaCheckbox-root')).toHaveClass('NovaCheckbox-colorError');
  });

  it('should render small checkbox', () => {
    render(<Checkbox data-testid="NovaCheckbox-root" size="small" />);
    expect(screen.getByTestId('NovaCheckbox-root')).toHaveClass('NovaCheckbox-sizeSmall');
  });

  it('should render large checkbox', () => {
    render(<Checkbox data-testid="NovaCheckbox-root" size="large" />);
    expect(screen.getByTestId('NovaCheckbox-root')).toHaveClass('NovaCheckbox-sizeLarge');
  });

  it('should render defaultChecked checkbox', () => {
    render(<Checkbox data-testid="NovaCheckbox-root" defaultChecked />);
    expect(screen.getByTestId('NovaIcon-checkboxChecked')).toBeInTheDocument();
    expect(screen.getByRole('checkbox')).toBeChecked();
  });

  it('should render checked checkbox', () => {
    render(<Checkbox data-testid="NovaCheckbox-root" checked />);
    expect(screen.getByTestId('NovaCheckbox-root')).toHaveClass('Mui-checked');
    expect(screen.getByRole('checkbox')).toBeChecked();
  });

  it('should render disabled checkbox', () => {
    render(<Checkbox data-testid="NovaCheckbox-root" disabled />);
    expect(screen.getByTestId('NovaCheckbox-root')).toHaveClass('Mui-disabled');
    expect(screen.getByRole('checkbox')).toBeDisabled();
  });

  it('should render indeterminate checkbox', () => {
    render(<Checkbox data-testid="NovaCheckbox-root" indeterminate />);
    expect(screen.getByTestId('NovaCheckbox-root')).toHaveClass('NovaCheckbox-indeterminate');
    expect(screen.getByTestId('NovaIcon-checkboxIndeterminate')).toBeInTheDocument();
  });

  it('should render checkbox with label', () => {
    render(<Checkbox data-testid="NovaCheckbox-root" label="Agree" />);
    expect(screen.getByText('Agree')).toBeInTheDocument();
  });

  it('should render uncontrolled checkbox', () => {
    const mockOnChange = vi.fn();
    render(<Checkbox data-testid="NovaCheckbox-root" onChange={mockOnChange} />);
    expect(screen.getByRole('checkbox')).not.toBeChecked();
    fireEvent.click(screen.getByRole('checkbox'));
    expect(mockOnChange).toBeCalled();
    expect(screen.getByRole('checkbox')).toBeChecked();
  });

  it('should render controlled checkbox', () => {
    const mockOnChange = vi.fn();
    render(<Checkbox data-testid="NovaCheckbox-root" checked={false} onChange={mockOnChange} />);
    expect(screen.getByRole('checkbox')).not.toBeChecked();
    fireEvent.click(screen.getByRole('checkbox'));
    expect(mockOnChange).toBeCalled();
    expect(screen.getByRole('checkbox')).not.toBeChecked();
  });

  it('should slotProps work', () => {
    render(<Checkbox data-testid="NovaCheckbox-root" slotProps={{ input: { 'data-testid': 'checkbox-input' } }} />);
    expect(screen.getByTestId('checkbox-input')).toHaveClass('NovaCheckbox-input');
  });
});
