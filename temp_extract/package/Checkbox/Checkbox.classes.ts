import generateUtilityClasses from '@mui/utils/generateUtilityClasses';
import generateUtilityClass from '@mui/utils/generateUtilityClass';

export interface CheckboxClasses {
  /** Class name applied to the root element. */
  root: string;
  /** Class name applied to the checkbox element. */
  checkbox: string;
  /** Class name applied to the action element. */
  action: string;
  /** Class name applied to the input element. */
  input: string;
  /** Class name applied to the label element. */
  label: string;

  /** State class applied to the root element if `checked={true}`. */
  checked: string;
  /** State class applied to the root element if `disabled={true}`. */
  disabled: string;
  /** State class applied to the root element if `indeterminate={true}`. */
  indeterminate: string;

  /** State class applied to the root element if `color="primary"`. */
  colorPrimary: string;
  /** State class applied to the root element if `color="secondary"`. */
  colorError: string;

  /** Styles applied to the root element if `size="medium"`. */
  sizeMedium: string;
  /** Styles applied to the root element if `size="large"`. */
  sizeLarge: string;
  /** Styles applied to the root element if `size="small"`. */
  sizeSmall: string;

  /** Class name applied to the root element if the switch has visible focus */
  focusVisible: string;
}

export type CheckboxClassKey = keyof CheckboxClasses;

export function getCheckboxUtilityClass(slot: string): string {
  return generateUtilityClass('NovaCheckbox', slot);
}

const checkboxClasses: CheckboxClasses = generateUtilityClasses('NovaCheckbox', [
  'root',
  'checkbox',
  'action',
  'input',
  'label',
  'checked',
  'disabled',
  'indeterminate',
  'colorPrimary',
  'colorError',
  'sizeMedium',
  'sizeLarge',
  'sizeSmall',
  'focusVisible',
]);

export default checkboxClasses;
