import * as React from 'react';
import { OverrideProps } from '@mui/types';
import { CreateSlotsAndSlotProps, SlotProps } from '../types/slot';
import { UseSwitchParameters } from '../internal/hooks/useSwitch';

export interface CheckBoxRootSlotPropsOverrides {}
export interface CheckBoxCheckboxSlotPropsOverrides {}
export interface CheckBoxActionSlotPropsOverrides {}
export interface CheckBoxInputSlotPropsOverrides {}
export interface CheckBoxLabelSlotPropsOverrides {}

export type CheckboxSlot = 'root' | 'checkbox' | 'action' | 'input' | 'label';

export interface CheckboxSlots {
  /**
   * The component that renders the root.
   * @default 'span'
   */
  root?: React.ElementType;
  /**
   * The component that renders the checkbox.
   * @default 'span'
   */
  checkbox?: React.ElementType;
  /**
   * The component that renders the action.
   * @default 'span'
   */
  action?: React.ElementType;
  /**
   * The component that renders the input.
   * @default 'input'
   */
  input?: React.ElementType;
  /**
   * The component that renders the label.
   * @default 'label'
   */
  label?: React.ElementType;
}

export type CheckboxSlotsAndSlotProps = CreateSlotsAndSlotProps<
  CheckboxSlots,
  {
    root: SlotProps<'span', CheckBoxRootSlotPropsOverrides, CheckboxOwnerState>;
    checkbox: SlotProps<'span', CheckBoxCheckboxSlotPropsOverrides, CheckboxOwnerState>;
    action: SlotProps<'span', CheckBoxActionSlotPropsOverrides, CheckboxOwnerState>;
    input: SlotProps<'input', CheckBoxInputSlotPropsOverrides, CheckboxOwnerState>;
    label: SlotProps<'label', CheckBoxLabelSlotPropsOverrides, CheckboxOwnerState>;
  }
>;

export interface CheckboxTypeMap<P = object, D extends React.ElementType = 'span'> {
  props: P &
    UseSwitchParameters &
    CheckboxSlotsAndSlotProps & {
      /**
       * The id of the `input` element.
       */
      id?: string;
      /**
       * The label element next to the checkbox.
       */
      label?: React.ReactNode;
      /**
       * The value of the component. The DOM API casts this to a string.
       * The browser uses "on" as the default value.
       */
      value?: React.AllHTMLAttributes<HTMLInputElement>['value'];
      /**
       * The `name` attribute of the input.
       */
      name?: string;
      /**
       * If `true`, the component appears indeterminate.
       * This does not set the native input element to indeterminate due
       * to inconsistent behavior across browsers.
       * However, we set a `data-indeterminate` attribute on the `input`.
       * @default false
       */
      indeterminate?: boolean;
      /**
       * The size of the Checkbox.
       * @default 'medium'
       */
      size?: 'small' | 'medium' | 'large';
      /**
       * The color of the Checkbox
       */
      color?: 'primary' | 'error';
    };
  defaultComponent: D;
}

export type CheckboxProps<
  D extends React.ElementType = CheckboxTypeMap['defaultComponent'],
  P = {
    component?: React.ElementType;
  },
> = OverrideProps<CheckboxTypeMap<P, D>, D>;

export interface CheckboxOwnerState extends CheckboxProps {
  /**
   * If `true`, the checkbox's focus is visible.
   */
  focusVisible?: boolean;
}
