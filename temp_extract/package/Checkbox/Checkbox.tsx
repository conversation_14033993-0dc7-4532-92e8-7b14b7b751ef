'use client';
import React from 'react';
import {
  unstable_composeClasses as composeClasses,
  unstable_capitalize as capitalize,
  EventHandlers,
} from '@mui/utils';
import useSlotProps from '@mui/utils/useSlotProps';
import { styled } from '@pigment-css/react';
import UncheckIcon from './Icon/CheckboxOutline';
import IndeterminateIcon from './Icon/CheckboxIndeterminate';
import CheckedIcon from './Icon/Checkbox';
import checkboxClasses, { getCheckboxUtilityClass } from './Checkbox.classes';
import { CheckboxOwnerState, CheckboxProps } from './Checkbox.types';
import { useSwitch } from '../internal/hooks/useSwitch';
import FormControlContext from '../FormControl/FormControlContext';

const useUtilityClasses = (ownerState: CheckboxOwnerState) => {
  const { checked, disabled, focusVisible, color, size, indeterminate } = ownerState;

  const slots = {
    root: [
      'root',
      checked && 'checked',
      disabled && 'disabled',
      focusVisible && 'focusVisible',
      indeterminate && 'indeterminate',
      color && `color${capitalize(color)}`,
      size && `size${capitalize(size)}`,
    ],
    checkbox: [
      'checkbox',
      checked && 'checked',
      indeterminate && 'indeterminate',
      focusVisible && 'focusVisible',
      disabled && 'disabled',
    ],
    action: ['action', checked && 'checked', focusVisible && 'focusVisible'],
    input: ['input'],
    label: ['label'],
  };

  return composeClasses(slots, getCheckboxUtilityClass, {});
};

const CheckboxRoot = styled('span')<CheckboxProps>(({ theme }) => ({
  position: 'relative',
  display: 'flex',
  alignItems: 'center',
  padding: 'var(--nova-checkbox-padding)',
  variants: [
    {
      props: { size: 'small' },
      style: {
        lineHeight: '32px',
        '--nova-checkbox-padding': '6px',
      },
    },
    {
      props: { size: 'medium' },
      style: {
        lineHeight: '40px',
        '--nova-checkbox-padding': '8px',
      },
    },
    {
      props: { size: 'large' },
      style: {
        lineHeight: '48px',
        '--nova-checkbox-padding': '10px',
      },
    },
  ],
  [`&.${checkboxClasses.disabled}`]: {
    color: theme.vars.palette.onBackgroundDisabled,
  },
}));

const CheckboxContent = styled('span')<CheckboxProps>(({ theme }) => ({
  display: 'inline-flex',
  verticalAlign: 'middle',
  flexShrink: 0,
  cursor: 'pointer',
  width: 'var(--nova-checkbox-size)',
  height: 'var(--nova-checkbox-size)',
  '--nova-checkbox-size': '1.5rem',
  '--nova-checkbox-state-background': 'transparent',
  '--nova-checkbox-state-size': '0.875rem',
  '--nova-checkbox-state-padding': '0.3125rem',
  '& svg': {
    fontSize: 'var(--nova-checkbox-size)',
  },
  variants: [
    {
      props: { checked: false, disabled: false },
      style: {
        '&:after': {
          position: 'absolute',
          content: '""',
          backgroundColor: 'var(--nova-checkbox-state-background)',
          height: 'var(--nova-checkbox-state-size)',
          width: 'var(--nova-checkbox-state-size)',
          top: 'calc(var(--nova-checkbox-state-padding) + var(--nova-checkbox-padding))',
          left: 'calc(var(--nova-checkbox-state-padding) + var(--nova-checkbox-padding))',
        },
      },
    },
    {
      props: { color: 'error', checked: false, disabled: false },
      style: {
        '& svg': {
          fill: theme.vars.palette.error,
        },
        '&:hover': {
          '--nova-checkbox-state-background': `color-mix(in srgb, transparent, ${theme.vars.palette.error} ${theme.vars.palette.stateLayers.hoverError})`,
        },
        [`&.${checkboxClasses.focusVisible}`]: {
          '--nova-checkbox-state-background': `color-mix(in srgb, transparent, ${theme.vars.palette.error} ${theme.vars.palette.stateLayers.focusError})`,
        },
        '&:active': {
          '--nova-checkbox-state-background': `color-mix(in srgb, transparent, ${theme.vars.palette.error} ${theme.vars.palette.stateLayers.pressError})`,
        },
      },
    },
    {
      props: { color: 'error', checked: true, disabled: false },
      style: {
        fontSize: 'inherit',
        '& svg': {
          fill: theme.vars.palette.error,
        },
        '&:hover': {
          '& svg': {
            fill: theme.vars.palette.onErrorContainer,
          },
        },
        [`&.${checkboxClasses.focusVisible}`]: {
          '& svg': {
            fill: theme.vars.palette.onErrorContainer,
          },
        },
        '&:active': {
          '& svg': {
            fill: theme.vars.palette.onErrorContainer,
          },
        },
      },
    },
    {
      props: { color: 'error', indeterminate: true, disabled: false },
      style: {
        fontSize: 'inherit',
        '& svg': {
          fill: theme.vars.palette.error,
        },
        '&:hover': {
          '& svg': {
            fill: theme.vars.palette.onErrorContainer,
          },
        },
        [`&.${checkboxClasses.focusVisible}`]: {
          '& svg': {
            fill: theme.vars.palette.onErrorContainer,
          },
        },
        '&:active': {
          '& svg': {
            fill: theme.vars.palette.onErrorContainer,
          },
        },
      },
    },
    {
      props: { color: 'primary', checked: false, disabled: false },
      style: {
        '& svg': {
          fill: theme.vars.palette.primary,
        },
        '&:hover': {
          '& svg': {
            fill: theme.vars.palette.addOn.primaryFixedDim,
          },
          '--nova-checkbox-state-background': `color-mix(in srgb, transparent, ${theme.vars.palette.primary} ${theme.vars.palette.stateLayers.hoverPrimary})`,
        },
        [`&.${checkboxClasses.focusVisible}`]: {
          '& svg': {
            fill: theme.vars.palette.addOn.primaryFixedDim,
          },
          '--nova-checkbox-state-background': `color-mix(in srgb, transparent, ${theme.vars.palette.primary} ${theme.vars.palette.stateLayers.focusPrimary})`,
        },
        '&:active': {
          '& svg': {
            fill: theme.vars.palette.addOn.primaryFixedDim,
          },
          '--nova-checkbox-state-background': `color-mix(in srgb, transparent, ${theme.vars.palette.primary} ${theme.vars.palette.stateLayers.pressPrimary})`,
        },
      },
    },
    {
      props: { color: 'primary', checked: true, disabled: false },
      style: {
        '& svg': {
          fill: theme.vars.palette.primary,
        },
        '&:hover': {
          '& svg': {
            fill: theme.vars.palette.addOn.primaryFixedDim,
          },
        },
        [`&.${checkboxClasses.focusVisible}`]: {
          '& svg': {
            fill: theme.vars.palette.addOn.primaryFixedDim,
          },
        },
        '&:active': {
          '& svg': {
            fill: theme.vars.palette.addOn.primaryFixedDim,
          },
        },
      },
    },
    {
      props: { color: 'primary', indeterminate: true, disabled: false },
      style: {
        '& svg': {
          fill: theme.vars.palette.primary,
        },
        '&:hover': {
          '& svg': {
            fill: theme.vars.palette.addOn.primaryFixedDim,
          },
        },
        [`&.${checkboxClasses.focusVisible}`]: {
          '& svg': {
            fill: theme.vars.palette.addOn.primaryFixedDim,
          },
        },
        '&:active': {
          '& svg': {
            fill: theme.vars.palette.addOn.primaryFixedDim,
          },
        },
      },
    },
    {
      props: { size: 'small' },
      style: {
        '--nova-checkbox-size': '20px',
        '--nova-checkbox-state-size': '12px',
        '--nova-checkbox-state-padding': '4px',
        [`&.${checkboxClasses.focusVisible}`]: {
          outline: `1px solid ${theme.vars.palette.secondary}`,
          outlineOffset: '-2px',
        },
      },
    },
    {
      props: { size: 'medium' },
      style: {
        '--nova-checkbox-size': '24px',
        '--nova-checkbox-state-size': '14px',
        '--nova-checkbox-state-padding': '5px',
        [`&.${checkboxClasses.focusVisible}`]: {
          outline: `1px solid ${theme.vars.palette.secondary}`,
          outlineOffset: '-2.5px',
        },
      },
    },
    {
      props: { size: 'large' },
      style: {
        '--nova-checkbox-size': '28px',
        '--nova-checkbox-state-size': '16px',
        '--nova-checkbox-state-padding': '6px',
        [`&.${checkboxClasses.focusVisible}`]: {
          outline: `1px solid ${theme.vars.palette.secondary}`,
          outlineOffset: '-3px',
        },
      },
    },
    {
      props: { disabled: true },
      style: {
        cursor: 'default',
        pointerEvents: 'none',
        userSelect: 'none',
        '& svg': {
          fill: theme.vars.palette.onBackgroundDisabled,
        },
      },
    },
  ],
}));

const CheckboxAction = styled('span')<CheckboxProps>(() => ({
  position: 'absolute',
  zIndex: 1,
  height: '100%',
  width: '100%',
  top: '0px',
  left: '0px',
}));

const CheckboxInput = styled('input')<CheckboxProps>(() => ({
  cursor: 'pointer',
  position: 'absolute',
  opacity: 0,
  width: '100%',
  height: '100%',
  top: '0px',
  left: '0px',
  margin: 0,
  padding: 0,
  zIndex: 1,
}));

const CheckboxLabel = styled('span')<CheckboxProps>(({ theme }) => ({
  color: theme.vars.palette.onSurface,
  ...theme.typography.labelMedium,
  variants: [
    {
      props: { size: 'small' },
      style: {
        marginLeft: '10px',
        fontSize: '14px',
      },
    },
    {
      props: { size: 'medium' },
      style: {
        marginLeft: '12px',
        fontSize: '16px',
      },
    },
    {
      props: { size: 'large' },
      style: {
        marginLeft: '18px',
        fontSize: '18px',
      },
    },
    {
      props: { disabled: true },
      style: {
        color: theme.vars.palette.onBackgroundDisabled,
      },
    },
  ],
}));

// eslint-disable-next-line react/display-name
export const Checkbox = React.forwardRef((props: CheckboxProps, ref: React.ForwardedRef<Element>) => {
  const {
    className,
    component,
    checked: checkedProp,
    label,
    defaultChecked,
    disabled: disabledProp = false,
    indeterminate: indeterminateProp = false,
    id: idOverride,
    name,
    value,
    onBlur,
    onChange,
    onFocus,
    onFocusVisible,
    readOnly,
    required: requiredProp = false,
    color: colorProp = 'primary',
    size: sizeProp = 'medium',
    slots = {},
    slotProps = {},
    ...rest
  } = props;

  const formControl = React.useContext(FormControlContext);

  const id = idOverride || formControl?.htmlFor;

  const inDisabled = props.disabled ?? formControl?.disabled ?? disabledProp;
  const inRequired = props.required ?? formControl?.required ?? requiredProp;
  const size = props.size ?? formControl?.size ?? sizeProp;

  const useCheckboxProps = {
    checked: checkedProp,
    defaultChecked,
    disabled: inDisabled,
    readOnly,
    required: inRequired,
    onBlur: formControl?.onBlur,
    onChange: formControl?.onChange,
    onFocus: formControl?.onFocus,
    onFocusVisible,
  };

  const { getInputProps, checked, disabled, focusVisible } = useSwitch(useCheckboxProps);
  const ownerState = {
    ...props,
    checked,
    disabled,
    focusVisible,
    size,
    required: inRequired,
    color: colorProp,
    indeterminate: indeterminateProp,
  };
  const classes = useUtilityClasses(ownerState);

  const SlotRoot = slots.root ?? CheckboxRoot;
  const SlotCheckbox = slots.checkbox ?? CheckboxContent;
  const SlotAction = slots.action ?? CheckboxAction;
  const SlotInput = slots.input ?? CheckboxInput;
  const SlotLabel = slots.label ?? CheckboxLabel;

  const slotRootProps = useSlotProps({
    elementType: SlotRoot,
    externalSlotProps: slotProps.root,
    externalForwardedProps: rest,
    additionalProps: {
      ref,
      as: component,
    },
    ownerState,
    className: [classes.root, className],
  });

  const slotCheckboxProps = useSlotProps({
    elementType: SlotCheckbox,
    externalSlotProps: slotProps.checkbox,
    externalForwardedProps: {},
    additionalProps: {},
    ownerState,
    className: classes.checkbox,
  });

  const slotActionProps = useSlotProps({
    elementType: SlotAction,
    externalSlotProps: slotProps.action,
    externalForwardedProps: {},
    additionalProps: {},
    ownerState,
    className: classes.action,
  });

  const slotInputProps = useSlotProps({
    elementType: SlotInput,
    externalSlotProps: slotProps.input,
    externalForwardedProps: {},
    additionalProps: {
      id,
      name,
      value,
      role: undefined,
      ...(indeterminateProp && {
        // https://developer.mozilla.org/en-US/docs/Web/Accessibility/ARIA/Attributes/aria-checked#values
        'aria-checked': 'mixed' as const,
      }),
    },
    getSlotProps: (eventHandlers: EventHandlers) => {
      return getInputProps({
        ...eventHandlers,
        onChange,
        onFocus,
        onBlur,
      });
    },
    ownerState,
    className: classes.input,
  });

  const slotLabelProps = useSlotProps({
    elementType: SlotLabel,
    externalSlotProps: slotProps.label,
    externalForwardedProps: {},
    additionalProps: {},
    ownerState,
    className: classes.label,
  });

  let icon = <UncheckIcon />;
  if (indeterminateProp) {
    icon = <IndeterminateIcon />;
  } else if (checked) {
    icon = <CheckedIcon />;
  }

  return (
    <SlotRoot {...slotRootProps}>
      <SlotCheckbox {...slotCheckboxProps}>
        <SlotAction {...slotActionProps}>
          <SlotInput {...slotInputProps} />
        </SlotAction>
        {icon}
      </SlotCheckbox>
      {label && <SlotLabel {...slotLabelProps}>{label}</SlotLabel>}
    </SlotRoot>
  );
});
