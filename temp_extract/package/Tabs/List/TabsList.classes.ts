import {
  unstable_generateUtilityClasses as generateUtilityClasses,
  unstable_generateUtilityClass as generateUtilityClass,
} from '@mui/utils';

export interface TabsListClasses {
  /** Styles applied to the root element. */
  root: string;

  /** Styles applied when `divider="true"`. */
  divider: string;
}

export type TabsListClassKey = keyof TabsListClasses;

export function getTabsListUtilityClass(slot: string): string {
  return generateUtilityClass('NovaTabsList', slot);
}

const tabsListClasses: TabsListClasses = generateUtilityClasses('NovaTabsList', ['root', 'divider']);

export default tabsListClasses;
