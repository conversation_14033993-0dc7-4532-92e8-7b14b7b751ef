'use client';
import { styled } from '@pigment-css/react';
import { BaseTabsListProps, TabsListProps } from './TabsList.types';
import { Tabs as BaseTabs } from '@base-ui-components/react/tabs';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import { getTabsListUtilityClass } from './TabsList.classes';

// Wrapper component to ensure compatibility
const TabsListWrapper = (props: BaseTabsListProps) => <BaseTabs.List {...props} />;

const useUtilityClasses = (ownerState: TabsListProps) => {
  const { divider } = ownerState;

  const slots = {
    root: ['root', divider && 'divider'],
  };

  return composeClasses(slots, getTabsListUtilityClass, {});
};

const TabsListRoot = styled(TabsListWrapper, {
  shouldForwardProp: (prop) => !['divider'].includes(prop),
})<TabsListProps>(({ theme }) => ({
  display: 'flex',
  flexDirection: 'row',
  justifyContent: 'space-between',
  alignItems: 'center',
  position: 'relative',
  width: 'fit-content',

  '&[data-orientation="vertical"]': {
    flexDirection: 'column',
    justifyContent: 'start',
  },
  variants: [
    {
      props: { divider: true },
      style: {
        '&[data-orientation="horizontal"]:after': {
          content: '""',
          position: 'absolute',
          bottom: 0,
          right: 0,
          left: 0,
          height: 2,
          backgroundColor: theme.vars.palette.outlineVariant,
        },
        '&[data-orientation="vertical"]:after': {
          content: '""',
          position: 'absolute',
          bottom: 0,
          top: 0,
          right: 0,
          width: 2,
          backgroundColor: theme.vars.palette.outlineVariant,
        },
      },
    },
  ],
}));

export const TabsList = (props: TabsListProps) => {
  const defaultProps: Partial<TabsListProps> = {};
  const mergedProps = { ...defaultProps, ...props };
  const classes = useUtilityClasses(mergedProps);
  const { className, ...other } = mergedProps;
  return <TabsListRoot className={[classes.root, className].join(' ')} {...other}></TabsListRoot>;
};
