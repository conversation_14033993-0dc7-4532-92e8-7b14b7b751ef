import React from 'react';
import { render, screen, cleanup } from '@testing-library/react';
import { describe, expect, it, afterEach } from 'vitest';
import { TabsRoot } from '../Root';
import { TabsList } from './TabsList';

afterEach(() => {
  cleanup();
});

describe('Tabs', () => {
  it('renders TabsList component', () => {
    render(
      <TabsRoot data-testid={'Tabs'} defaultValue={0}>
        <TabsList data-testid={'TabsList'}></TabsList>
      </TabsRoot>,
    );
    const tabs = screen.getByTestId('TabsList');
    expect(tabs).toBeInTheDocument();
    expect(tabs).toHaveClass('NovaTabsList-root');
    expect(tabs).not.toHaveClass('NovaTabsList-divider');
  });
  it('renders TabsList with divider', () => {
    render(
      <TabsRoot data-testid={'Tabs'} defaultValue={0}>
        <TabsList divider data-testid={'TabsList'}></TabsList>
      </TabsRoot>,
    );
    const tabs = screen.getByTestId('TabsList');
    expect(tabs).toHaveClass('NovaTabsList-root');
    expect(tabs).toHaveClass('NovaTabsList-divider');
  });
});
