import React from 'react';
import { render, screen, cleanup } from '@testing-library/react';
import { describe, expect, it, afterEach } from 'vitest';
import { TabPanel } from './TabPanel';
import { TabsRoot } from '../Root';

afterEach(() => {
  cleanup();
});

describe('TabPanel', () => {
  it('renders TabPanel component', () => {
    render(
      <TabsRoot defaultValue={0}>
        <TabPanel data-testid={'tab1'} value={0}>
          Tab 1
        </TabPanel>
      </TabsRoot>,
    );
    const tab = screen.getByTestId('tab1');
    expect(tab).toBeInTheDocument();
    expect(tab).toHaveTextContent('Tab 1');
    expect(tab).toHaveClass('NovaTabPanel-root');
  });

  it('renders selected TabPanel component', () => {
    render(
      <TabsRoot defaultValue={0}>
        <TabPanel data-testid={'tab1'} value={0}>
          Tab 1
        </TabPanel>
        <TabPanel data-testid={'tab2'} value={1}>
          Tab 2
        </TabPanel>
      </TabsRoot>,
    );
    const tab1 = screen.getByTestId('tab1');
    const tab2 = screen.getByTestId('tab2');
    expect(tab1).toBeInTheDocument();
    expect(tab1).toHaveTextContent('Tab 1');
    expect(tab1.hasAttribute('data-hidden')).toBeFalsy();
    expect(tab2.hasAttribute('data-hidden')).toBeTruthy();
  });
});
