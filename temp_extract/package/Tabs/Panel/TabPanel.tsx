import { BaseTabsPanelProps, TabPanelProps } from './TabPanel.types';
import { Tabs as BaseTabs } from '@base-ui-components/react/tabs';
import { getTabPanelUtilityClass } from './TabPanel.classes';
import { unstable_composeClasses as composeClasses } from '@mui/utils';

const useUtilityClasses = (ownerState: TabPanelProps) => {
  const slots = {
    root: ['root'],
  };

  return composeClasses(slots, getTabPanelUtilityClass, {});
};

export const TabPanel = (props: TabPanelProps) => {
  const { className, ...other } = props;
  const classes = useUtilityClasses(props);
  return <BaseTabs.Panel className={[classes.root, className].join(' ')} {...other} />;
};
