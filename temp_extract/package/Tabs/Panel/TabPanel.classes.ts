import {
  unstable_generateUtilityClasses as generateUtilityClasses,
  unstable_generateUtilityClass as generateUtilityClass,
} from '@mui/utils';

export interface TabPanelClasses {
  /** Styles applied to the root element. */
  root: string;
}

export type TabPanelClassKey = keyof TabPanelClasses;

export function getTabPanelUtilityClass(slot: string): string {
  return generateUtilityClass('NovaTabPanel', slot);
}

const tabPanelClasses: TabPanelClasses = generateUtilityClasses('NovaTabPanel', ['root']);

export default tabPanelClasses;
