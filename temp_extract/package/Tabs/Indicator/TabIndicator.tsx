'use client';
import { styled } from '@pigment-css/react';
import { Tabs as BaseTabs } from '@base-ui-components/react/tabs';
import { BaseTabsIndicatorProps, TabIndicatorProps } from './TabIndicator.types';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import { getTabIndicatorUtilityClass } from './TabIndicator.classes';

// Wrapper component to ensure compatibility
const TabsIndicatorWrapper = (props: BaseTabsIndicatorProps) => <BaseTabs.Indicator {...props} />;

const useUtilityClasses = (ownerState: TabIndicatorProps) => {
  const slots = {
    root: ['root'],
  };

  return composeClasses(slots, getTabIndicatorUtilityClass, {});
};

const TabIndicatorRoot = styled(TabsIndicatorWrapper)<TabIndicatorProps>(({ theme }) => ({
  position: 'absolute',
  zIndex: 1,
  left: 0,
  bottom: 0,
  translate: 'var(--active-tab-left) 0%',
  width: 'var(--active-tab-width)',
  height: 2,
  backgroundColor: theme.vars.palette.primary,
  transitionProperty: 'translate, width, height',
  transitionDuration: '200ms',
  transitionTimingFunction: 'ease-in-out',

  '&[data-orientation="vertical"]': {
    right: 0,
    top: 0,
    left: 'initial',
    bottom: 'initial',
    translate: '0% var(--active-tab-top)',
    height: 'var(--active-tab-height)',
    width: 2,
  },
}));

export const TabIndicator = (props: TabIndicatorProps) => {
  const classes = useUtilityClasses(props);
  const { className, ...rest } = props;

  return <TabIndicatorRoot className={[classes.root, className].join(' ')} {...rest} />;
};
