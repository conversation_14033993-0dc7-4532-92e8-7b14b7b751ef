import React from 'react';
import { render, screen, cleanup } from '@testing-library/react';
import { describe, expect, it, afterEach } from 'vitest';
import { TabsRoot } from '../Root';
import { TabIndicator } from './TabIndicator';
import { TabsList } from '../List';

afterEach(() => {
  cleanup();
});

describe('TabIndicator', () => {
  it('renders Tab Indicator component', () => {
    render(
      <TabsRoot>
        <TabsList>
          <TabIndicator data-testid={'indicator'} />
        </TabsList>
      </TabsRoot>,
    );
    const indicator = screen.getByTestId('indicator');
    expect(indicator).toBeInTheDocument();
    expect(indicator).toHaveClass('NovaTabIndicator-root');
  });
  it('renders Tab Indicator with custom className', () => {
    render(
      <TabsRoot>
        <TabsList>
          <TabIndicator data-testid={'indicator'} className="custom-class" />
        </TabsList>
      </TabsRoot>,
    );
    const indicator = screen.getByTestId('indicator');
    expect(indicator).toHaveClass('custom-class');
  });
});
