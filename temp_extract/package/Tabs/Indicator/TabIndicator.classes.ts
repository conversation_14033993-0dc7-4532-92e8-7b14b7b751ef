import {
  unstable_generateUtilityClasses as generateUtilityClasses,
  unstable_generateUtilityClass as generateUtilityClass,
} from '@mui/utils';

export interface TabIndicatorClasses {
  /** Styles applied to the root element. */
  root: string;
}

export type TabIndicatorClassKey = keyof TabIndicatorClasses;

export function getTabIndicatorUtilityClass(slot: string): string {
  return generateUtilityClass('NovaTabIndicator', slot);
}

const tabIndicatorClasses: TabIndicatorClasses = generateUtilityClasses('NovaTabIndicator', ['root']);

export default tabIndicatorClasses;
