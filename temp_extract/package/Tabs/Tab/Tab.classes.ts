import {
  unstable_generateUtilityClasses as generateUtilityClasses,
  unstable_generateUtilityClass as generateUtilityClass,
} from '@mui/utils';

export interface TabClasses {
  /** Styles applied to the root element. */
  root: string;

  /**
   * Styles applied when `iconPosition="side"`.
   */
  iconPositionSide: string;
  /**
   * Styles applied when `iconPosition="top"`.
   */
  iconPositionTop: string;
}

export type TabClassKey = keyof TabClasses;

export function getTabUtilityClass(slot: string): string {
  return generateUtilityClass('NovaTab', slot);
}

const tabClasses: TabClasses = generateUtilityClasses('NovaTab', ['root', 'iconPositionSide', 'iconPositionTop']);

export default tabClasses;
