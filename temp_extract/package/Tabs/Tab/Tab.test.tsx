import React from 'react';
import { render, screen, cleanup } from '@testing-library/react';
import { describe, expect, it, afterEach } from 'vitest';
import { Tab } from './Tab';
import { TabsRoot } from '../Root';
import { TabsList } from '../List';

afterEach(() => {
  cleanup();
});

describe('Tab', () => {
  it('renders Tab component', () => {
    render(
      <TabsRoot>
        <TabsList>
          <Tab>Tab 1</Tab>
        </TabsList>
      </TabsRoot>,
    );
    const tab = screen.getByRole('tab');
    expect(tab).toBeInTheDocument();
    expect(tab).toHaveTextContent('Tab 1');
    expect(tab).toHaveClass('NovaTab-root');
  });

  it('applies correct selected classes', () => {
    render(
      <TabsRoot defaultValue={0}>
        <TabsList>
          <Tab value={0}>Tab 1</Tab>
          <Tab value={1}>Tab 2</Tab>
        </TabsList>
      </TabsRoot>,
    );
    const tab1 = screen.getAllByRole('tab')[0];
    const tab2 = screen.getAllByRole('tab')[1];
    expect(tab1.hasAttribute('data-selected')).toBeTruthy();
    expect(tab2.hasAttribute('data-selected')).toBeFalsy();
  });

  it('renders with custom icon', () => {
    render(
      <TabsRoot>
        <TabsList>
          <Tab icon={<span data-testid="icon">Icon</span>}>Tab 1</Tab>
        </TabsList>
      </TabsRoot>,
    );
    expect(screen.getByTestId('icon')).toBeInTheDocument();
  });

  it('applies custom className', () => {
    render(
      <TabsRoot>
        <TabsList>
          <Tab className="custom-class">Tab 1</Tab>
        </TabsList>
      </TabsRoot>,
    );
    const tab = screen.getByRole('tab');
    expect(tab).toHaveClass('custom-class');
    expect(tab).toHaveClass('NovaTab-root');
  });
});
