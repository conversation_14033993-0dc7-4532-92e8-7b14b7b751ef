'use client';
import { styled } from '@pigment-css/react';
import { Tabs as BaseTabs } from '@base-ui-components/react/tabs';
import { Typography } from '../../Typography';
import { BaseTabProps, TabProps } from './Tab.types';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import { getTabUtilityClass } from './Tab.classes';

// Wrapper component to ensure compatibility
const TabWrapper = (props: BaseTabProps) => <BaseTabs.Tab {...props} />;

const useUtilityClasses = (ownerState: TabProps) => {
  const slots = {
    root: ['root', ownerState.iconPosition === 'top' ? 'iconPositionTop' : 'iconPositionSide'],
  };

  return composeClasses(slots, getTabUtilityClass, {});
};

const TabRoot = styled(TabWrapper, {
  shouldForwardProp: (prop) => !['iconPosition', 'hasBoth'].includes(prop),
})<TabProps & { hasBoth: boolean }>(({ theme }) => ({
  position: 'relative',
  minHeight: 40,
  gap: theme.vars.sys.size.spaceBetween.horizontal.xs.medium,
  color: theme.vars.palette.onSurfaceVariant,
  cursor: 'pointer',
  paddingLeft: theme.vars.sys.size.padding.leftRight.md.medium,
  paddingRight: theme.vars.sys.size.padding.leftRight.md.medium,
  paddingTop: theme.vars.sys.size.padding.leftRight.xs.medium,
  paddingBottom: theme.vars.sys.size.padding.leftRight.xs.medium,
  backgroundColor: theme.vars.palette.surface,
  width: '100%',
  border: 'none',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  whiteSpace: 'nowrap',

  ['&[data-orientation="vertical"]']: {
    gap: theme.vars.sys.size.spaceBetween.vertical['2xs'].medium,
  },

  // Interaction States
  '&:hover': {
    backgroundColor: `color-mix(in srgb, transparent, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.hoverOnSurface})`,
  },
  '&:active': {
    backgroundColor: `color-mix(in srgb, transparent, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.pressOnSurface})`,
  },
  '&:focus-visible': {
    backgroundColor: `color-mix(in srgb, transparent, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.focusOnSurface})`,
    outline: `2px solid ${theme.vars.palette.secondary}`,
    outlineOffset: 2,
    zIndex: 1,
  },

  // Selected State
  '&[data-selected]': {
    color: theme.vars.palette.primary,
    '&:hover': {
      backgroundColor: `color-mix(in srgb, transparent, ${theme.vars.palette.primary} ${theme.vars.palette.stateLayers.hoverPrimary})`,
    },
    '&:active': {
      backgroundColor: `color-mix(in srgb, transparent, ${theme.vars.palette.primary} ${theme.vars.palette.stateLayers.pressPrimary})`,
    },
    '&:focus-visible': {
      backgroundColor: `color-mix(in srgb, transparent, ${theme.vars.palette.primary} ${theme.vars.palette.stateLayers.focusPrimary})`,
      outline: `2px solid ${theme.vars.palette.secondary}`,
      outlineOffset: 2,
      zIndex: 1,
    },
  },

  variants: [
    {
      props: { disabled: true },
      style: {
        cursor: 'default',
        pointerEvents: 'none',
        userSelect: 'none',
        color: theme.vars.palette.onBackgroundDisabled,
      },
    },
    {
      props: { iconPosition: 'top' },
      style: {
        flexDirection: 'column',
      },
    },
    {
      props: { hasBoth: true, iconPosition: 'side' },
      style: {
        ['&[data-orientation="horizontal"]']: {
          paddingLeft: theme.vars.sys.size.padding.leftRight.lg.medium,
          paddingRight: theme.vars.sys.size.padding.leftRight.lg.medium,
        },
      },
    },
  ],
}));

export const Tab = (props: TabProps) => {
  const defaultProps: Partial<TabProps> = { iconPosition: 'side' };
  const mergedProps = { ...defaultProps, ...props };
  const classes = useUtilityClasses(mergedProps);
  const { icon, children, className, iconPosition, ...rest } = mergedProps;

  return (
    <TabRoot
      className={[classes.root, className].join(' ')}
      iconPosition={iconPosition}
      hasBoth={Boolean(icon) && Boolean(children)}
      {...rest}
    >
      {icon}
      {children && <Typography variant="labelMedium">{children}</Typography>}
    </TabRoot>
  );
};
