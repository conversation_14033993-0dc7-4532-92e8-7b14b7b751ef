import { Tab } from './Tab';
import { TabsList } from './List';
import { TabsRoot } from './Root';
import { TabPanel } from './Panel';
import { TabIndicator } from './Indicator';
export type { TabsRootProps } from './Root';
export type { TabsListProps } from './List';
export type { TabProps } from './Tab';
export type { TabPanelProps } from './Panel';
export type { TabIndicatorProps } from './Indicator';
export { tabClasses } from './Tab';
export { tabsListClasses } from './List';
export { tabsRootClasses } from './Root';
export { tabPanelClasses } from './Panel';
export { tabIndicatorClasses } from './Indicator';

export const Tabs = {
  Root: TabsRoot,
  List: TabsList,
  Tab,
  Indicator: TabIndicator,
  Panel: TabPanel,
};
