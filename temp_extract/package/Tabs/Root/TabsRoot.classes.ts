import {
  unstable_generateUtilityClasses as generateUtilityClasses,
  unstable_generateUtilityClass as generateUtilityClass,
} from '@mui/utils';

export interface TabsRootClasses {
  /** Styles applied to the root element. */
  root: string;
}

export type TabsClassKey = keyof TabsRootClasses;

export function getTabsRootUtilityClass(slot: string): string {
  return generateUtilityClass('NovaTabs', slot);
}

const tabsRootClasses: TabsRootClasses = generateUtilityClasses('NovaTabs', ['root']);

export default tabsRootClasses;
