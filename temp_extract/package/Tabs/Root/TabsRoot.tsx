'use client';
import { styled } from '@pigment-css/react';
import { BaseTabsRootProps, TabsRootProps } from './TabsRoot.types';
import { Tabs as BaseTabs } from '@base-ui-components/react/tabs';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import { getTabsRootUtilityClass } from './TabsRoot.classes';

// Wrapper component to ensure compatibility
const TabsRootWrapper = (props: BaseTabsRootProps) => <BaseTabs.Root {...props} />;

const useUtilityClasses = (ownerState: TabsRootProps) => {
  const slots = {
    root: ['root'],
  };

  return composeClasses(slots, getTabsRootUtilityClass, {});
};

const TabsRootSlot = styled(TabsRootWrapper)<TabsRootProps>(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  '& .NovaTabPanel-root': {
    flex: 1,
    overflow: 'auto',
  },
  variants: [
    {
      props: { orientation: 'vertical' },
      style: {
        flexDirection: 'row',
        '& .NovaTabPanel-root': { height: '100%' },
      },
    },
  ],
}));

export const TabsRoot = (props: TabsRootProps) => {
  const defaultProps: Partial<TabsRootProps> = {};
  const mergedProps = { ...defaultProps, ...props };
  const classes = useUtilityClasses(mergedProps);

  const { className, ...other } = mergedProps;
  return <TabsRootSlot className={[classes.root, className].join(' ')} {...other} />;
};
