import React from 'react';
import { render, screen, cleanup } from '@testing-library/react';
import { describe, expect, it, afterEach } from 'vitest';
import { TabsRoot } from './TabsRoot';

afterEach(() => {
  cleanup();
});

describe('Tabs', () => {
  it('renders Tabs component', () => {
    render(<TabsRoot data-testid={'Tabs'} defaultValue={0}></TabsRoot>);
    const tabs = screen.getByTestId('Tabs');
    expect(tabs).toBeInTheDocument();
    expect(tabs).toHaveClass('NovaTabs-root');
  });
});
