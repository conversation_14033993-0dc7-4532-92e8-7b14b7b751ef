# Changelog (@hxnova/react-components)

## 1.0.0-alpha.8 (2025-05-08)

### Added

- DateField component
- New v3 features for DataGrid (column pinning, cell/row editing)

### Fixed

- Icon styles in Link component
- Error: Cannot set properties of null(setting 'displayName') when using in the Nextjs project.

## 1.0.0-alpha.7 (2025-04-25)

### Fixed

* Correct all components breakpoints based on latest design update.

## 1.0.0-alpha.6 (2025-04-24)

### Added

* Viewport and size related tokens into theme. [#233386](https://dev.azure.com/hexagonmi/MI-Genesis/_git/Nova/pullrequest/233386)
* Box, Stack, Container, Grid layout components. [#238778](https://dev.azure.com/hexagonmi/MI-Genesis/_git/Nova/pullrequest/238778)
* DatePicker component. [#237400](https://dev.azure.com/hexagonmi/MI-Genesis/_git/Nova/pullrequest/237400)
* New features for datagrid - v2. [#236983](https://dev.azure.com/hexagonmi/MI-Genesis/_git/Nova/pullrequest/236983)
* FloatingActionBar component. [#235944](https://dev.azure.com/hexagonmi/MI-Genesis/_git/Nova/pullrequest/235944)
* Separate docs for rail in ZeroHeight. [#233245](https://dev.azure.com/hexagonmi/MI-Genesis/_git/Nova/pullrequest/233245)
* Initial implementation of DataGrid. [#232754](https://dev.azure.com/hexagonmi/MI-Genesis/_git/Nova/pullrequest/232754)

### Changed

* Stepper component according to design's feedback. [#238700](https://dev.azure.com/hexagonmi/MI-Genesis/_git/Nova/pullrequest/238700)
* Adapt to new IconButton changes. [#237900](https://dev.azure.com/hexagonmi/MI-Genesis/_git/Nova/pullrequest/237900)
* Remove @mui/material & @mui/icons-material dependencies from demo app. [#237454](https://dev.azure.com/hexagonmi/MI-Genesis/_git/Nova/pullrequest/237454)
* Slider component according to design's feedback. [#236967](https://dev.azure.com/hexagonmi/MI-Genesis/_git/Nova/pullrequest/236967)
* Remove navigatonIcon from NavigationTop. [#237292](https://dev.azure.com/hexagonmi/MI-Genesis/_git/Nova/pullrequest/237292)
* Icon Button and ToggleIconButton components according to design's feedback. [#236258](https://dev.azure.com/hexagonmi/MI-Genesis/_git/Nova/pullrequest/236258)
* Fix problem of not being able to generating doc for floatingactionbar. [#237422](https://dev.azure.com/hexagonmi/MI-Genesis/_git/Nova/pullrequest/237422)
* Radio component according to design's feedback. [#236563](https://dev.azure.com/hexagonmi/MI-Genesis/_git/Nova/pullrequest/236563)
* Drawer component according to design's feedback. [#237026](https://dev.azure.com/hexagonmi/MI-Genesis/_git/Nova/pullrequest/237026)
* SegmentedButton component according to design's feedback. [#236558](https://dev.azure.com/hexagonmi/MI-Genesis/_git/Nova/pullrequest/236558)
* FloatingActionBar component according to design's feedback. [#236569](https://dev.azure.com/hexagonmi/MI-Genesis/_git/Nova/pullrequest/236569)
* Resolve CssBaseline issue. [#236570](https://dev.azure.com/hexagonmi/MI-Genesis/_git/Nova/pullrequest/236570)
* TextField component according to design's feedback. [#235832](https://dev.azure.com/hexagonmi/MI-Genesis/_git/Nova/pullrequest/235832)
* Dialog component according to design's feedback. [#235886](https://dev.azure.com/hexagonmi/MI-Genesis/_git/Nova/pullrequest/235886)
* Menu component according to design's feedback. [#235933](https://dev.azure.com/hexagonmi/MI-Genesis/_git/Nova/pullrequest/235933)
* Switch component according to design's feedback. [#234925](https://dev.azure.com/hexagonmi/MI-Genesis/_git/Nova/pullrequest/234925)
* Minor alignment updates to NavigationTop, Akkurat font fix. [#234925](https://dev.azure.com/hexagonmi/MI-Genesis/_git/Nova/pullrequest/234925)
* Design feedback issue related to link and avatar. [#233141](https://dev.azure.com/hexagonmi/MI-Genesis/_git/Nova/pullrequest/233141)

## 1.0.0-alpha.5 (2025-03-28)

### Added
* New components:
  * `CssBaseline`
  * `SideSheet`
  * `Stepper`

### Changed
- Added the size prop to TablePagination component

## 1.0.0-alpha.4 (2025-03-21)

### Changed
- Replaced `TextField` with `Search` component in `NavigationTop`
- Fixed styles in `Tabs` component.

## 1.0.0-alpha.3 (2025-03-14)
* New components:
  * `Search`
  * `Card`
  * `Pagination`
  * `ProgressIndicators`

### Changed
- Updated Typography component to pull all styles from theme

## 1.0.0-alpha.2 (2025-02-27)

### Added
* New components:
  * `Tag`
  * `ListSubheader`
  * `MenuList`
  * `ListDivider`
  * `Divider`
* Support both `cjs` and `esm` module


## 1.0.0-alpha.1 (2025-02-13)

### Added
* New components:
    * `Accordion`
    * `AccordionDetails`
    * `AccordionGroup`
    * `AccordionSummary`
    * `Alert`
    * `Avatar`
    * `AvatarGroup`
    * `Badge`
    * `Breadcrumbs`
    * `Button`
    * `Checkbox`
    * `Chip`
    * `ClickAwayListener`
    * `Dialog`
    * `DialogActions`
    * `DialogContent`
    * `DialogHeader`
    * `Drawer`
    * `DrawerBody`
    * `DrawerFooter`
    * `DrawerHeader`
    * `DrawerNavGroup`
    * `DrawerNavItem`
    * `Fab`
    * `FormControl`
    * `FormHelperText`
    * `FormLabel`
    * `IconButton`
    * `Input`
    * `Link`
    * `List`
    * `ListItem`
    * `ListItemButton`
    * `ListItemContent`
    * `ListItemDecorator`
    * `Menu`
    * `MenuItem`
    * `NavigationTop`
    * `Radio`
    * `RadioGroup`
    * `SegmentedButton`
    * `SegmentedButtonGroup`
    * `Slider`
    * `Snackbar`
    * `SvgIcon`
    * `Switch`
    * `Tab`
    * `TabPanel`
    * `Tabs`
    * `TabsList`
    * `TextField`
    * `Textarea`
    * `ToggleIconButton`
    * `Tooltip`
    * `Typography`