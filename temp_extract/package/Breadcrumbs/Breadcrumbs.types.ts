import * as React from 'react';
import { OverrideProps } from '@mui/types';
import { SxProps } from '../types/theme';
import { CreateSlotsAndSlotProps, SlotProps } from '../types/slot';

export type BreadcrumbsSlot = 'root' | 'ol' | 'li' | 'separator';

export interface BreadcrumbsSlots {
  /**
   * The component that renders the root.
   * @default 'nav'
   */
  root?: React.ElementType;
  /**
   * The component that renders the ol.
   * @default 'ol'
   */
  ol?: React.ElementType;
  /**
   * The component that renders the li.
   * @default 'li'
   */
  li?: React.ElementType;
  /**
   * The component that renders the separator.
   * @default 'li'
   */
  separator?: React.ElementType;
}

export type BreadcrumbsSlotsAndSlotProps = CreateSlotsAndSlotProps<
  BreadcrumbsSlots,
  {
    root: SlotProps<'nav', object, BreadcrumbsOwnerState>;
    ol: SlotProps<'ol', object, BreadcrumbsOwnerState>;
    li: SlotProps<'li', object, BreadcrumbsOwnerState>;
    separator: SlotProps<'li', object, BreadcrumbsOwnerState>;
  }
>;

export interface BreadcrumbsTypeMap<P = object, D extends React.ElementType = 'nav'> {
  props: P &
    BreadcrumbsSlotsAndSlotProps & {
      /**
       * The content of the component.
       */
      children?: React.ReactNode;
      /**
       * Custom separator node.
       * @default '/'
       */
      separator?: React.ReactNode;
      /**
       * The system prop that allows defining system overrides as well as additional CSS styles.
       */
      sx?: SxProps;
      /**
       * The component used for the Root slot.
       * Either a string to use a HTML element or a component.
       */
      component?: React.ElementType;
    };
  defaultComponent: D;
}

export type BreadcrumbsProps<
  D extends React.ElementType = BreadcrumbsTypeMap['defaultComponent'],
  P = { component?: React.ElementType },
> = OverrideProps<BreadcrumbsTypeMap<P, D>, D>;

export interface BreadcrumbsOwnerState extends BreadcrumbsProps {}
