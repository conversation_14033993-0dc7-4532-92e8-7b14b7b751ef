import { expect, test, describe } from 'vitest';
import { render, screen } from '@testing-library/react';
import React from 'react';
import { Breadcrumbs } from './Breadcrumbs';
import { Link } from '../Link';
import { Typography } from '../Typography';

describe('Breadcrumbs', () => {
  test('renders basic breadcrumbs component correctly', () => {
    const view = render(
      <Breadcrumbs aria-label="breadcrumbs">
        <Link underline="none" href="#">
          Breadcrumb
        </Link>
        <Typography>Breadcrumb</Typography>
      </Breadcrumbs>,
    );
    expect(view).toBeTruthy();
  });

  test('renders a breadcrumbs with the data-testid property', () => {
    render(
      <Breadcrumbs data-testid="NovaBreadcrumbs-root" aria-label="breadcrumbs">
        <Link underline="none" href="#">
          Breadcrumb
        </Link>
        <Typography>Breadcrumb</Typography>
      </Breadcrumbs>,
    );
    expect(screen.getByTestId('NovaBreadcrumbs-root')).toBeDefined();
  });

  test('renders children correctly', () => {
    render(
      <Breadcrumbs aria-label="breadcrumbs">
        <Link underline="none" href="#">
          Home
        </Link>
        <Typography>Breadcrumb</Typography>
      </Breadcrumbs>,
    );
    expect(screen.getByText('Home')).toBeInTheDocument();
  });

  test('applies custom className correctly', () => {
    render(
      <Breadcrumbs aria-label="breadcrumbs" className="custom-breadcrumbs">
        <Link underline="none" href="#">
          Breadcrumb
        </Link>
        <Typography>Breadcrumb</Typography>
      </Breadcrumbs>,
    );

    expect(screen.getByRole('navigation')).toHaveClass('custom-breadcrumbs');
  });
});
