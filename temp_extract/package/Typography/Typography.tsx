import { styled } from '@pigment-css/react';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import { getTypographyUtilityClass } from './Typography.classes';
import { TypographyProps, VariantMapping } from './Typography.types';
import clsx from 'clsx';

const useUtilityClasses = (ownerState: TypographyProps) => {
  const { variant, noWrap } = ownerState;

  const slots = {
    root: ['root', variant, noWrap && 'noWrap'],
  };

  return composeClasses(slots, getTypographyUtilityClass, {});
};

const defaultVariantMapping: VariantMapping = {
  displayLarge: 'h1',
  displayMedium: 'h2',
  displaySmall: 'h3',
  headlineLarge: 'h4',
  headlineMedium: 'h5',
  headlineSmall: 'h6',
  titleLarge: 'h6',
  titleMedium: 'h6',
  titleSmall: 'h6',
  bodyLarge: 'p',
  bodyMedium: 'p',
  bodySmall: 'p',
  labelLarge: 'span',
  labelMedium: 'span',
  labelSmall: 'span',
  inherit: 'p',
};

const TypographyBase = styled('span')<TypographyProps>(({ theme }) => ({
  margin: 0,
  // Styles based on props
  variants: [
    {
      props: { noWrap: true },
      style: {
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        whiteSpace: 'nowrap',
      },
    },
    // Variant Styles

    {
      props: { variant: 'displayLarge' },
      style: {
        ...theme.typography.displayLarge,
      },
    },
    {
      props: { variant: 'displayMedium' },
      style: {
        ...theme.typography.displayMedium,
      },
    },
    {
      props: { variant: 'displaySmall' },
      style: {
        ...theme.typography.displaySmall,
      },
    },
    {
      props: { variant: 'headlineLarge' },
      style: {
        ...theme.typography.headlineLarge,
      },
    },
    {
      props: { variant: 'headlineMedium' },
      style: {
        ...theme.typography.headlineMedium,
      },
    },
    {
      props: { variant: 'headlineSmall' },
      style: {
        ...theme.typography.headlineSmall,
      },
    },
    {
      props: { variant: 'titleLarge' },
      style: {
        ...theme.typography.titleLarge,
      },
    },
    {
      props: { variant: 'titleMedium' },
      style: {
        ...theme.typography.titleMedium,
      },
    },
    {
      props: { variant: 'titleSmall' },
      style: {
        ...theme.typography.titleSmall,
      },
    },
    {
      props: { variant: 'bodyLarge' },
      style: {
        ...theme.typography.bodyLarge,
      },
    },
    {
      props: { variant: 'bodyMedium' },
      style: {
        ...theme.typography.bodyMedium,
      },
    },
    {
      props: { variant: 'bodySmall' },
      style: {
        ...theme.typography.bodySmall,
      },
    },
    // {
    //   props: { variant: 'bodyXSmall' },
    //   style: {
    //     ...theme.typography.bodyXSmall
    //   },
    // },
    {
      props: { variant: 'labelLarge' },
      style: {
        ...theme.typography.labelLarge,
      },
    },
    {
      props: { variant: 'labelMedium' },
      style: {
        ...theme.typography.labelMedium,
      },
    },
    {
      props: { variant: 'labelSmall' },
      style: {
        ...theme.typography.labelSmall,
      },
    },
    {
      props: { variant: 'inherit' },
      style: {
        letterSpacing: 0,
        fontFamily: theme.typography.fontFamily,
        fontSize: 'inherit',
        lineHeight: 'inherit',
      },
    },
  ],
}));

export const Typography = (props: TypographyProps) => {
  const { as: Component, variantMapping = {}, variant = 'bodyMedium', noWrap, className, children, ...other } = props;
  const mapping = { ...defaultVariantMapping, ...variantMapping };
  const Root = Component ?? mapping[variant];
  const classes = useUtilityClasses(props);

  return (
    <TypographyBase as={Root} ownerState={{ variant, noWrap }} className={clsx(classes.root, className)} {...other}>
      {children}
    </TypographyBase>
  );
};
