import { ElementType, HTMLAttributes } from 'react';

export type TypographyVariant =
  | 'displayLarge'
  | 'displayMedium'
  | 'displaySmall'
  | 'headlineLarge'
  | 'headlineMedium'
  | 'headlineSmall'
  | 'titleLarge'
  | 'titleMedium'
  | 'titleSmall'
  | 'bodyLarge'
  | 'bodyMedium'
  | 'bodySmall'
  | 'labelLarge'
  | 'labelMedium'
  | 'labelSmall';
export type VariantMapping = Partial<{
  displayLarge: ElementType;
  displayMedium: ElementType;
  displaySmall: ElementType;
  headlineLarge: ElementType;
  headlineMedium: ElementType;
  headlineSmall: ElementType;
  titleLarge: ElementType;
  titleMedium: ElementType;
  titleSmall: ElementType;
  bodyLarge: ElementType;
  bodyMedium: ElementType;
  bodySmall: ElementType;
  labelLarge: ElementType;
  labelMedium: ElementType;
  labelSmall: ElementType;
  inherit: ElementType;
}>;
export interface TypographyProps extends HTMLAttributes<HTMLSpanElement> {
  /**
   * The typography style to use
   * @default 'bodyMedium'
   */
  variant?: TypographyVariant | 'inherit';
  /**
   * A mapping of variants to HTML elements to use for that variant
   * @default {
   * {
   * displayLarge: 'h1',
   * displayMedium: 'h2',
   * displaySmall: 'h3',
   * headlineLarge: 'h4',
   * headlineMedium: 'h5',
   * headlineSmall: 'h6',
   * titleLarge: 'h6',
   * titleMedium: 'h6',
   * titleSmall: 'h6',
   * bodyLarge: 'p',
   * bodyMedium: 'p',
   * bodySmall: 'p',
   * labelLarge: 'span',
   * labelMedium: 'span',
   * labelSmall: 'span',
   * };
   * }
   */
  variantMapping?: VariantMapping;
  /**
   * Prevent the component text from wrapping to multiple lines
   */
  noWrap?: boolean;
  /**
   * A component to use to render the element
   */
  as?: ElementType;
}
