import {
  unstable_generateUtilityClasses as generateUtilityClasses,
  unstable_generateUtilityClass as generateUtilityClass,
} from '@mui/utils';

export interface TypographyClasses {
  /** Styles applied to the root element. */
  root: string;

  /** Styles applied to the Button if `variant="displayLarge"`. */
  displayLarge: string;
  /** Styles applied to the Button if `variant="displayMedium"`. */
  displayMedium: string;
  /** Styles applied to the Button if `variant="displaySmall"`. */
  displaySmall: string;
  /** Styles applied to the Button if `variant="headlineLarge"`. */
  headlineLarge: string;
  /** Styles applied to the Button if `variant="headlineMedium"`. */
  headlineMedium: string;
  /** Styles applied to the Button if `variant="headlineSmall"`. */
  headlineSmall: string;
  /** Styles applied to the Button if `variant="titleLarge"`. */
  titleLarge: string;
  /** Styles applied to the Button if `variant="titleMedium"`. */
  titleMedium: string;
  /** Styles applied to the Button if `variant="titleSmall"`. */
  titleSmall: string;
  /** Styles applied to the Button if `variant="bodyLarge"`. */
  bodyLarge: string;
  /** Styles applied to the Button if `variant="bodyMedium"`. */
  bodyMedium: string;
  /** Styles applied to the Button if `variant="bodySmall"`. */
  bodySmall: string;
  /** Styles applied to the Button if `variant="labelLarge"`. */
  labelLarge: string;
  /** Styles applied to the Button if `variant="labelMedium"`. */
  labelMedium: string;
  /** Styles applied to the Button if `variant="labelSmall"`. */
  labelSmall: string;

  /** Styles applied if text wrapping is disabled */
  noWrap: string;
}

export type TypographyClassKey = keyof TypographyClasses;

export function getTypographyUtilityClass(slot: string): string {
  return generateUtilityClass('NovaTypography', slot);
}

const typographyClasses: TypographyClasses = generateUtilityClasses('NovaTypography', [
  'root',
  'displayLarge',
  'displayMedium',
  'displaySmall',
  'headlineLarge',
  'headlineMedium',
  'headlineSmall',
  'titleLarge',
  'titleMedium',
  'titleSmall',
  'bodyLarge',
  'bodyMedium',
  'bodySmall',
  'labelLarge',
  'labelMedium',
  'labelSmall',
  'noWrap',
]);

export default typographyClasses;
