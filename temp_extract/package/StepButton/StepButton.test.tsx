/// <reference types="@testing-library/jest-dom" />
import React from 'react';
import { expect, test, describe, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { StepButton } from './StepButton';
import stepButtonClasses from './StepButton.classes';
import { Step } from '../Step/Step';
import { Stepper } from '../Stepper/Stepper';

describe('<StepButton />', () => {
  test('renders basic step button correctly', () => {
    const { container } = render(<StepButton>Test Button</StepButton>);
    expect(container.firstChild).toHaveClass(stepButtonClasses.root);
    expect(container.firstChild).toBeInTheDocument();
    expect(container.firstChild?.nodeName).toBe('BUTTON');
    expect(container.firstChild).toHaveTextContent('Test Button');
  });

  test('renders with custom component prop', () => {
    const { container } = render(<StepButton component="div">Test Button</StepButton>);
    expect(container.firstChild?.nodeName).toBe('DIV');
  });

  test('handles click events', () => {
    const handleClick = vi.fn();
    render(<StepButton onClick={handleClick}>Click Me</StepButton>);

    fireEvent.click(screen.getByText('Click Me'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  test('renders as button type by default', () => {
    render(<StepButton>Test Button</StepButton>);
    expect(screen.getByRole('button')).toHaveAttribute('type', 'button');
  });

  test('applies custom className', () => {
    const { container } = render(<StepButton className="custom-class">Test Button</StepButton>);
    expect(container.firstChild).toHaveClass('custom-class');
  });

  test('forwards ref to root element', () => {
    const ref = React.createRef<HTMLButtonElement>();
    render(<StepButton ref={ref}>Test Button</StepButton>);
    expect(ref.current).toBeInstanceOf(HTMLButtonElement);
  });

  test('passes slotProps to root', () => {
    render(
      <StepButton
        slotProps={{
          root: {
            'data-testid': 'button-root',
            className: 'custom-root-class',
          },
        }}
      >
        Test Button
      </StepButton>,
    );

    expect(screen.getByTestId('button-root')).toBeInTheDocument();
    expect(screen.getByTestId('button-root')).toHaveClass('custom-root-class');
  });

  test('renders custom slots', () => {
    const CustomRoot = React.forwardRef<HTMLDivElement>((props, ref) => (
      <div ref={ref} data-testid="custom-root" {...props} />
    ));
    CustomRoot.displayName = 'CustomRoot';

    render(
      <StepButton
        slots={{
          root: CustomRoot,
        }}
      >
        Test Button
      </StepButton>,
    );

    expect(screen.getByTestId('custom-root')).toBeInTheDocument();
  });

  test('works within Stepper and Step components', () => {
    render(
      <Stepper>
        <Step>
          <StepButton>Step 1</StepButton>
        </Step>
        <Step>
          <StepButton>Step 2</StepButton>
        </Step>
      </Stepper>,
    );

    expect(screen.getAllByRole('button')).toHaveLength(2);
    expect(screen.getByText('Step 1')).toBeInTheDocument();
    expect(screen.getByText('Step 2')).toBeInTheDocument();
  });

  test('maintains button accessibility attributes', () => {
    render(
      <StepButton aria-label="Step button" disabled>
        Test Button
      </StepButton>,
    );

    const button = screen.getByRole('button');
    expect(button).toHaveAttribute('aria-label', 'Step button');
    expect(button).toBeDisabled();
  });
});
