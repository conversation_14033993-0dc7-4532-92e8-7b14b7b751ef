'use client';
import * as React from 'react';
import { OverridableComponent } from '@mui/types';
import clsx from 'clsx';
import { styled } from '@pigment-css/react';
import stepButtonClasses from './StepButton.classes';
import { StepButtonOwnerState, StepButtonProps, StepButtonTypeMap } from './StepButton.types';
import useSlotProps from '@mui/utils/useSlotProps';
import stepClasses from '../Step/Step.classes';
import stepperClasses from '../Stepper/Stepper.classes';

const StepButtonRoot = styled('button', {
  name: 'NovaStepButton',
  slot: 'Root',
  overridesResolver: (props, styles) => styles.root,
})<StepButtonOwnerState>(({ theme }) => {
  return {
    [`.${stepClasses.indicator}:empty + &`]: {
      '--nova-stepIndicator-size': '0px',
      '--nova-step-gap': '0px',
    },
    [`.${stepClasses.horizontal} &`]: {
      '--_StepButton-alignSelf': 'stretch',
      '--_StepButton-gap': 'var(--nova-step-gap)',
    },
    [`.${stepClasses.horizontal} &::before`]: {
      '--_StepButton-left': 'calc(-1 * (var(--nova-stepIndicator-size) + var(--nova-step-gap)))',
    },
    [`.${stepClasses.vertical} &::before`]: {
      '--_StepButton-top': 'calc(-1 * (var(--nova-stepIndicator-size) + var(--nova-step-gap)))',
    },
    [`.${stepperClasses.vertical} .${stepClasses.vertical} &`]: {
      '--_StepButton-alignItems': 'flex-start',
    },
    [`.${stepperClasses.vertical} &::before`]: {
      '--_StepButton-left': 'calc(-1 * (var(--nova-stepIndicator-size) + var(--nova-step-gap)))',
      '--_StepButton-top': '0px',
    },
    WebkitTapHighlightColor: 'transparent',
    boxSizing: 'border-box',
    border: 'none',
    backgroundColor: 'transparent',
    color: theme.vars.palette.onSurface,
    cursor: 'pointer',
    position: 'relative',
    padding: 0,
    textDecoration: 'none', // prevent user agent underline when used as anchor
    font: 'inherit',
    display: 'inline-flex',
    flexDirection: 'inherit',
    alignItems: 'var(--_StepButton-alignItems, inherit)',
    alignSelf: 'var(--_StepButton-alignSelf)',
    gap: 'var(--_StepButton-gap)',
    '&::before': {
      content: '""',
      display: 'block',
      position: 'absolute',
      top: 'var(--_StepButton-top, 0)',
      right: 0,
      bottom: 0,
      left: 'var(--_StepButton-left, 0)',
    },
  };
});

// eslint-disable-next-line react/display-name
export const StepButton = React.forwardRef<HTMLButtonElement, StepButtonProps>(function StepButton(props, ref) {
  const { className, component = 'button', children, slots = {}, slotProps = {}, ...other } = props;

  const ownerState = {
    ...props,
    component,
  };

  const SlotRoot = slots.root ?? StepButtonRoot;
  const rootProps = useSlotProps({
    elementType: StepButtonRoot,
    externalSlotProps: slotProps.root,
    externalForwardedProps: other,
    additionalProps: {
      ref,
      as: component,
      type: 'button',
    },
    ownerState,
    className: clsx(stepButtonClasses.root, className),
  });

  return <SlotRoot {...rootProps}>{children}</SlotRoot>;
}) as OverridableComponent<StepButtonTypeMap>;
