import generateUtilityClasses from '@mui/utils/generateUtilityClasses';
import generateUtilityClass from '@mui/utils/generateUtilityClass';

export interface StepButtonClasses {
  /** Class name applied to the root element. */
  root: string;
}

export type StepButtonClassKey = keyof StepButtonClasses;

export function getStepButtonUtilityClass(slot: string): string {
  return generateUtilityClass('NovaStepButton', slot);
}

const stepButtonClasses: StepButtonClasses = generateUtilityClasses('NovaStepButton', ['root']);

export default stepButtonClasses;
