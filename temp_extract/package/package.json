{"name": "@hxnova/react-components", "version": "1.0.0-alpha.9", "description": "", "main": "dist/cjs/index.js", "module": "dist/index.js", "types": "dist/index.d.ts", "sideEffects": false, "scripts": {"test": "vitest", "test:ci": "vitest run --coverage", "coverage": "vitest --coverage", "build": "rm -rf dist && pnpm run build:esm && pnpm run build:cjs && pnpm run postbuild", "build:cjs": "tsc -p tsconfig.cjs.json", "build:esm": "tsc -p tsconfig.json", "postbuild": "node ../../scripts/frank-build.js"}, "author": "", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@base-ui-components/react": "1.0.0-alpha.8", "@mui/types": "^7.2.20", "@mui/utils": "^6.1.10", "@mui/system": "^6.1.10", "@popperjs/core": "^2.11.8", "clsx": "^2.1.0", "csstype": "^3.1.3", "dayjs": "^1.10.7"}, "peerDependencies": {"@pigment-css/react": "^0.0.30", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@nexusui/branding": "^2.8.1", "@pigment-css/react": "^0.0.30", "@playwright/test": "^1.43.1", "@testing-library/jest-dom": "^6.6.2", "@testing-library/react": "^16.0.1", "@types/node": "^22.10.2", "@types/react": "^18.2.67", "@types/react-dom": "^18.2.67", "@vitejs/plugin-react": "^4.2.1", "jsdom": "^25.0.1", "playwright": "^1.43.1", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.4.2", "vite": "^6.0.3"}, "homepage": "https://zealous-bay-06c2c6503.5.azurestaticapps.net/", "keywords": ["hexagon", "nova", "hmi", "manufacturing intelligence", "components", "react", "ui"]}