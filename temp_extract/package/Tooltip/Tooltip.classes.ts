import generateUtilityClasses from '@mui/utils/generateUtilityClasses';
import generateUtilityClass from '@mui/utils/generateUtilityClass';

export interface TooltipClasses {
  /** Styles applied to the root element. */
  root: string;
  /** Styles applied to the trigger element. */
  trigger: string;
  /** Styles applied to the portal element. */
  portal: string;
  /** Styles applied to the positioner element. */
  positioner: string;
  /** Styles applied to the popup element. */
  popup: string;
  /** Styles applied to the arrow element. */
  arrow: string;
  /** Styles applied to the root element when the tooltip is open. */
  open: string;
  /** Styles applied to the root element when the tooltip is closed. */
  closed: string;
}

export type TooltipClassKey = keyof TooltipClasses;

export function getTooltipUtilityClass(slot: string): string {
  return generateUtilityClass('NovaTooltip', slot);
}

const tooltipClasses: TooltipClasses = generateUtilityClasses('NovaTooltip', [
  'root',
  'trigger',
  'portal',
  'positioner',
  'popup',
  'arrow',
  'open',
  'closed',
]);

export default tooltipClasses;
