import { expect, test, describe } from 'vitest';
import { render, screen, act } from '@testing-library/react';
import React from 'react';
import { Tooltip } from './Tooltip';
import userEvent from '@testing-library/user-event';

describe('Tooltip', () => {
  test('renders basic tooltip component correctly', () => {
    const view = render(<Tooltip title="test" />);
    expect(view).toBeTruthy();
  });

  test('renders a tooltip with the data-testid property', () => {
    render(<Tooltip title="test" data-testid="NovaTooltip-root" />);
    expect(screen.getByTestId('NovaTooltip-root')).toBeDefined();
  });

  test('renders children correctly', () => {
    render(
      <Tooltip title="Test Tooltip">
        <button>Hover me</button>
      </Tooltip>,
    );
    expect(screen.getByText('Hover me')).toBeInTheDocument();
  });

  test('shows tooltip on hover', async () => {
    render(
      <Tooltip title="Test Tooltip">
        <button>Hover me</button>
      </Tooltip>,
    );

    const trigger = screen.getByText('Hover me');

    // Hover over the trigger
    await act(async () => {
      await userEvent.hover(trigger);
    });

    // Wait for the tooltip to appear
    const tooltip = await screen.findByText('Test Tooltip');
    expect(tooltip).toBeInTheDocument();
  });

  test('hides tooltip on mouse leave', async () => {
    render(
      <Tooltip title="Test Tooltip">
        <button>Hover me</button>
      </Tooltip>,
    );

    const trigger = screen.getByText('Hover me');

    await act(async () => {
      await userEvent.hover(trigger);
      await userEvent.unhover(trigger);
    });

    expect(screen.queryByText('Test Tooltip')).not.toBeInTheDocument();
  });

  test('controlled open state works correctly', () => {
    const { rerender } = render(
      <Tooltip title="Test Tooltip" open={true}>
        <button>Hover me</button>
      </Tooltip>,
    );

    expect(screen.getByText('Test Tooltip')).toBeInTheDocument();

    rerender(
      <Tooltip title="Test Tooltip" open={false}>
        <button>Hover me</button>
      </Tooltip>,
    );

    expect(screen.queryByText('Test Tooltip')).not.toBeInTheDocument();
  });

  test('renders with different sides', () => {
    const { rerender } = render(
      <Tooltip title="Test Tooltip" open={true} placement="top">
        <button>Hover me</button>
      </Tooltip>,
    );

    expect(screen.getByText('Test Tooltip').closest('[data-placement]')).toHaveAttribute('data-placement', 'top');

    rerender(
      <Tooltip title="Test Tooltip" open={true} placement="bottom">
        <button>Hover me</button>
      </Tooltip>,
    );

    expect(screen.getByText('Test Tooltip').closest('[data-placement]')).toHaveAttribute('data-placement', 'bottom');
  });

  test('renders with arrow when showArrow is true', () => {
    render(
      <Tooltip title="Test Tooltip" open={true} showArrow={true}>
        <button>Hover me</button>
      </Tooltip>,
    );

    expect(document.querySelector('.NovaTooltip-arrow')).toBeInTheDocument();
  });

  test('does not render arrow when showArrow is false', () => {
    render(
      <Tooltip title="Test Tooltip" open={true} showArrow={false}>
        <button>Hover me</button>
      </Tooltip>,
    );

    expect(document.querySelector('.NovaTooltip-arrow')).not.toBeInTheDocument();
  });

  test('applies custom className correctly', () => {
    render(
      <Tooltip title="Test Tooltip" open={true} className="custom-tooltip">
        <button>Hover me</button>
      </Tooltip>,
    );

    expect(screen.getByText('Hover me').parentElement).toHaveClass('custom-tooltip');
  });
});
