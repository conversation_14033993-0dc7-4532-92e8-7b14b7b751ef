'use client';
import * as React from 'react';
import { styled } from '@pigment-css/react';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import clsx from 'clsx';
import { Tooltip as BaseTooltip } from '@base-ui-components/react/tooltip';
import { TooltipProps, BaseTriggerProps } from './Tooltip.types';
import { getTooltipUtilityClass } from './Tooltip.classes';
import useSlotProps from '@mui/utils/useSlotProps';

// Wrapper component to ensure compatibility
const TriggerWrapper = (props: BaseTriggerProps) => <BaseTooltip.Trigger {...props} />;

const useUtilityClasses = (ownerState: TooltipProps & { isOpen?: boolean }) => {
  const { isOpen } = ownerState;

  const slots = {
    root: ['root', isOpen && 'open', !isOpen && 'closed'],
    trigger: ['trigger'],
    portal: ['portal'],
    positioner: ['positioner'],
    popup: ['popup'],
    arrow: ['arrow'],
  };

  return composeClasses(slots, getTooltipUtilityClass, {});
};

const StyledPopup = styled('div')<Partial<TooltipProps>>(({ theme }) => ({
  backgroundColor: theme.vars.palette.inverseSurface,
  color: theme.vars.palette.onPrimary,
  padding: `${theme.vars.sys.viewport.spacing.padding.leftRight['2xs']} ${theme.vars.sys.viewport.spacing.padding.leftRight.xs}`,
  borderRadius: 'var(--radius-2xs)',
  fontSize: '0.75rem',
  fontFamily: theme.typography.fontFamily,
  lineHeight: '1rem',
  whiteSpace: 'nowrap',
  boxShadow: 'none',
  pointerEvents: 'none',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'flex-start',
  position: 'relative',
}));

const StyledArrow = styled('div')<Partial<TooltipProps>>(({ theme }) => ({
  position: 'absolute',
  width: '8px',
  height: '8px',
  backgroundColor: 'inherit',
  '&[data-placement="top"]': {
    bottom: 0,
    left: '50%',
    transform: 'translate(-50%, 50%) rotate(45deg)',
  },
  '&[data-placement="bottom"]': {
    top: 0,
    left: '50%',
    transform: 'translate(-50%, -50%) rotate(45deg)',
  },
  '&[data-placement="left"]': {
    top: '50%',
    right: 0,
    transform: 'translate(50%, -50%) rotate(45deg)',
  },
  '&[data-placement="right"]': {
    top: '50%',
    left: 0,
    transform: 'translate(-50%, -50%) rotate(45deg)',
  },
}));

const BaseTooltipTrigger = styled(TriggerWrapper)<Partial<BaseTriggerProps>>(({ theme }) => ({
  backgroundColor: 'transparent',
  border: 'none',
  padding: 0,
}));

// eslint-disable-next-line react/display-name
export const Tooltip = React.forwardRef<HTMLDivElement, TooltipProps>((props, ref) => {
  const {
    children,
    title,
    className,
    open,
    defaultOpen,
    onOpenChange,
    placement = 'top',
    showArrow = false,
    slotProps = {},
    ...other
  } = props;

  const [isOpen, setIsOpen] = React.useState(defaultOpen || slotProps.root?.defaultOpen || false);

  const handleOpenChange = React.useCallback(
    (nextOpen: boolean, event?: Event, reason?: any) => {
      setIsOpen(nextOpen);
      onOpenChange?.(nextOpen, event, reason);
      slotProps.root?.onOpenChange?.(nextOpen, event, reason);
    },
    [onOpenChange, slotProps.root],
  );

  const ownerState = {
    ...props,
    open: open ?? slotProps.root?.open ?? isOpen,
    placement,
    className,
  };

  const classes = useUtilityClasses({ ...props, isOpen });

  const popupProps = useSlotProps({
    elementType: StyledPopup,
    externalSlotProps: {},
    additionalProps: {},
    ownerState,
    className: classes.popup,
  });

  const arrowProps = useSlotProps({
    elementType: StyledArrow,
    externalSlotProps: {},
    additionalProps: {},
    ownerState,
    className: classes.arrow,
  });

  return (
    <BaseTooltip.Provider {...slotProps.provider}>
      <BaseTooltip.Root
        open={open ?? slotProps.root?.open ?? isOpen}
        defaultOpen={defaultOpen ?? slotProps.root?.defaultOpen}
        onOpenChange={handleOpenChange}
        {...slotProps.root}
      >
        <BaseTooltipTrigger
          ref={ref}
          className={clsx(classes.trigger, className)}
          {...slotProps.trigger}
          {...other}
          style={{ ...slotProps.trigger?.style }}
        >
          {children}
        </BaseTooltipTrigger>
        <BaseTooltip.Portal {...slotProps.portal} className={classes.portal}>
          <BaseTooltip.Positioner
            side={placement ?? slotProps.positioner?.side}
            className={clsx(classes.positioner, slotProps.positioner?.className)}
            sideOffset={8}
            align="center"
            alignOffset={0}
            style={{ zIndex: 1500 }}
            {...slotProps.positioner}
          >
            <BaseTooltip.Popup
              {...slotProps.popup}
              className={clsx(classes.popup, slotProps.popup?.className)}
              data-placement={placement}
            >
              <StyledPopup {...popupProps}>
                {title}
                {showArrow && <StyledArrow data-placement={placement} {...arrowProps} />}
              </StyledPopup>
            </BaseTooltip.Popup>
          </BaseTooltip.Positioner>
        </BaseTooltip.Portal>
      </BaseTooltip.Root>
    </BaseTooltip.Provider>
  );
});
