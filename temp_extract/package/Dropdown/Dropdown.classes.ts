import generateUtilityClasses from '@mui/utils/generateUtilityClasses';
import generateUtilityClass from '@mui/utils/generateUtilityClass';

export interface DropdownClasses {
  /** Class name applied to the root element. */
  root: string;
  /** Class name applied to the root element if `disabled={true}`. */
  disabled: string;
  /** Class name applied to the root element if they are keyboard focused. */
  focusVisible: string;
  /** Class name applied to the root element if the listbox is expanded. */
  expanded: string;
  /** Class name applied to the root element if `size="small"`. */
  sizeSmall: string;
  /** Class name applied to the root element if `size="medium"`. */
  sizeMedium: string;
  /** Class name applied to the root element if `size="large"`. */
  sizeLarge: string;
  /** Class name applied to the root element if `multiple={true}`. */
  multiple: string;
  /** Class name applied to the root element if `error={true}`. */
  error: string;
  /** Class name applied to the button element . */
  button: string;
  /** Class name applied to the startDecorator element . */
  startDecorator: string;
  /** Class name applied to the endDecorator element . */
  endDecorator: string;
  /** Class name applied to the indicator element . */
  indicator: string;
  /** Class name applied to the listbox element . */
  listbox: string;
  /** Class name applied to the error icon. */
  errorStateIcon: string;
}

export type DropdownClassKey = keyof DropdownClasses;

export function getDropdownUtilityClass(slot: string): string {
  return generateUtilityClass('NovaDropdown', slot);
}

const dropdownClasses: DropdownClasses = generateUtilityClasses('NovaDropdown', [
  'root',
  'disabled',
  'focusVisible',
  'expanded',
  'sizeSmall',
  'sizeMedium',
  'sizeLarge',
  'multiple',
  'error',
  'button',
  'startDecorator',
  'endDecorator',
  'indicator',
  'listbox',
  'errorStateIcon',
]);

export default dropdownClasses;
