import React from 'react';
import '@testing-library/jest-dom/vitest';
import { screen, render } from '@testing-library/react';
import { expect, describe, it } from 'vitest';
import classes from './ListDivider.classes';
import { List } from '../List';
import { ListDivider } from './ListDivider';

describe('Nova <ListDivider />', () => {
  it('should have role separator', () => {
    render(<ListDivider />);
    expect(screen.getByRole('separator')).toHaveClass(classes.root);
  });

  it('should accept className prop', () => {
    const { container } = render(<ListDivider className="foo-bar" />);
    expect(container.firstChild).toHaveClass('foo-bar');
  });

  describe('aria-orientation', () => {
    it('should not have aria-orientation by default', () => {
      // The
      render(<ListDivider />);
      expect(screen.getByRole('separator')).not.toHaveAttribute('aria-orientation');
    });

    it('should have aria-orientation set to vertical', () => {
      render(
        <List orientation="horizontal">
          <ListDivider />
        </List>,
      );
      expect(screen.getByRole('separator')).toHaveAttribute('aria-orientation', 'vertical');
    });

    it('should not add aria-orientation if role is custom', () => {
      render(
        <List orientation="horizontal">
          <ListDivider role="presentation" />
        </List>,
      );
      expect(screen.getByRole('presentation')).not.toHaveAttribute('aria-orientation');
    });
  });
});
