'use client';
import * as React from 'react';
import { unstable_composeClasses as composeClasses, unstable_capitalize as capitalize } from '@mui/utils';
import { styled } from '@pigment-css/react';
import useSlotProps from '@mui/utils/useSlotProps';
import { DividerRoot } from '../Divider/Divider';
import { ListDividerOwnerState, ListDividerProps } from './ListDivider.types';
import { getListDividerUtilityClass } from './ListDivider.classes';
import RowListContext from '../List/RowListContext';
import ComponentListContext from '../List/ComponentListContext';

const useUtilityClasses = (ownerState: ListDividerOwnerState) => {
  const { orientation, variant } = ownerState;
  const slots = {
    root: ['root', orientation, variant && variant !== 'context' && `${capitalize(variant)}`],
  };

  return composeClasses(slots, getListDividerUtilityClass, {});
};

const ListDividerRoot = styled(DividerRoot as unknown as 'li')<ListDividerOwnerState>(() => ({
  variants: [
    {
      props: { variant: 'context' },
      style: {
        '--nova-divider-inset': 'calc(-1 * var(--nova-list-padding))',
      },
    },
    {
      props: { row: true },
      style: {
        marginInline: 'var(--nova-listDivider-gap)',
      },
    },
    {
      props: (props) => props.row === true && props['data-first-child'] === undefined,
      style: {
        marginInlineStart: 'calc(var(--nova-list-gap) + var(--nova-listDivider-gap))',
      },
    },
    {
      props: { row: true, variant: 'gutter' },
      style: {
        marginBlock: 'var(--nova-listItem-paddingY)',
      },
    },

    {
      props: { row: false, variant: 'gutter' },
      style: {
        marginInlineStart: 'var(--nova-listItem-paddingLeft)',
        marginInlineEnd: 'var(--nova-listItem-paddingRight)',
      },
    },
    {
      props: { row: false, variant: 'startDecorator' },
      style: {
        marginInlineStart: 'var(--nova-listItem-paddingLeft)',
      },
    },
    {
      props: { row: false, variant: 'startContent' },
      style: {
        marginInlineStart: 'calc(var(--nova-listItem-paddingLeft) + var(--nova-listItemDecorator-size))',
      },
    },
  ],
}));

export const ListDivider = React.forwardRef(function ListDivider(
  props: ListDividerProps,
  ref: React.ForwardedRef<Element>,
) {
  const row = React.useContext(RowListContext) || false;
  const listComponent = React.useContext(ComponentListContext);

  const {
    component: componentProp,
    role: roleProp,
    className,
    children,
    variant = 'context',
    orientation: orientationProp,
    slots = {},
    slotProps = {},
    ...other
  } = props;

  const [listElement] = listComponent?.split(':') || ['', ''];
  const component = componentProp || (listElement && !listElement.match(/^(ul|ol|menu)$/) ? 'div' : 'li');
  const role = roleProp || (component === 'li' ? 'separator' : undefined);

  const orientation = orientationProp || (row ? 'vertical' : 'horizontal');
  const ownerState = {
    ...props,
    variant,
    row,
    orientation,
    component,
    role,
  };

  const classes = useUtilityClasses(ownerState);
  const SlotRoot = slots.root ?? ListDividerRoot;

  const rootProps = useSlotProps({
    additionalProps: {
      as: component,
      ref,
      role,
      ...(role === 'separator' &&
        orientation === 'vertical' && {
          // The implicit aria-orientation of separator is 'horizontal'
          // https://developer.mozilla.org/en-US/docs/Web/Accessibility/ARIA/Roles/separator_role
          'aria-orientation': 'vertical',
        }),
    },
    className: [classes.root, className],
    elementType: ListDividerRoot,
    externalSlotProps: slotProps.root,
    externalForwardedProps: other,
    ownerState,
  });

  return <SlotRoot {...rootProps}>{children}</SlotRoot>;
});
