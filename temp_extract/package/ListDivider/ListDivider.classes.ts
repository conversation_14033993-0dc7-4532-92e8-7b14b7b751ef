import {
  unstable_generateUtilityClasses as generateUtilityClasses,
  unstable_generateUtilityClass as generateUtilityClass,
} from '@mui/utils';

export interface ListDividerClasses {
  /** Styles applied to the root element. */
  root: string;
  /** Styles applied to the root element if `variant="gutter"`. */
  gutter: string;
  /** Styles applied to the root element if `variant="startDecorator"`. */
  startDecorator: string;
  /** Styles applied to the root element if `variant="startContent"`. */
  startContent: string;
  /** Styles applied to the root element if `orientation="horizontal"`. */
  horizontal: string;
  /** Styles applied to the root element if `orientation="vertical"`. */
  vertical: string;
}

export type ListDividerClassKey = keyof ListDividerClasses;

export function getListDividerUtilityClass(slot: string): string {
  return generateUtilityClass('NovaListDivider', slot);
}

const listDividerClasses: ListDividerClasses = generateUtilityClasses('NovaListDivider', [
  'root',
  'gutter',
  'startDecorator',
  'startContent',
  'horizontal',
  'vertical',
]);

export default listDividerClasses;
