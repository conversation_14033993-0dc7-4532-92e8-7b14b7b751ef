/// <reference types="@testing-library/jest-dom" />
import React from 'react';
import { expect, test, describe, vi } from 'vitest';
import { render, screen } from '@testing-library/react';
import { Tag } from './Tag';

describe('Tag Component', () => {
  test('renders basic tag component correctly', () => {
    const view = render(<Tag label="Tag label" />);
    expect(view).toBeTruthy();
  });

  test('renders a tag with the data-testid property', () => {
    render(<Tag label="Tag label" data-testid="NovaTag-root" />);
    expect(screen.getByTestId('NovaTag-root')).toBeDefined();
  });

  test('renders with default props', () => {
    render(<Tag label="Default Tag" />);
    const tag = screen.getByText('Default Tag');
    expect(tag).toBeInTheDocument();
    expect(tag).toHaveClass('NovaTag-root');
  });

  test('renders with custom className', () => {
    render(<Tag label="Custom Class Tag" className="custom-class" />);
    const tag = screen.getByText('Custom Class Tag');
    expect(tag).toHaveClass('custom-class');
  });

  describe('variants', () => {
    test.each(['neutral', 'warning', 'info', 'success'] as const)('renders %s variant', (variant) => {
      render(<Tag label={`${variant} Tag`} variant={variant} />);
      const tag = screen.getByText(`${variant} Tag`);
      expect(tag).toHaveClass(`NovaTag-${variant}`);
    });
  });

  describe('intensities', () => {
    test.each(['bold', 'subtle'] as const)('renders %s intensity', (intensity) => {
      render(<Tag label={`${intensity} Tag`} intensity={intensity} />);
      const tag = screen.getByText(`${intensity} Tag`);
      expect(tag).toHaveClass(`NovaTag-${intensity}`);
    });
  });

  test('renders in disabled state', () => {
    render(<Tag label="Disabled Tag" disabled />);
    const tag = screen.getByText('Disabled Tag');
    expect(tag).toHaveClass('Mui-disabled');
  });

  test('forwards ref correctly', () => {
    const ref = React.createRef<HTMLDivElement>();
    render(<Tag label="Ref Tag" ref={ref} />);
    expect(ref.current).toBeInstanceOf(HTMLDivElement);
  });

  test('combines variant and intensity', () => {
    render(<Tag label="Error Subtle Tag" variant="error" intensity="subtle" />);
    const tag = screen.getByText('Error Subtle Tag');
    expect(tag).toHaveClass('Mui-error');
    expect(tag).toHaveClass('NovaTag-subtle');
  });

  test('passes through additional props', () => {
    render(<Tag label="Custom Props Tag" data-testid="custom-tag" title="test title" />);
    const tag = screen.getByTestId('custom-tag');
    expect(tag).toHaveAttribute('title', 'test title');
  });

  test('handles click events when clickable', () => {
    const handleClick = vi.fn();
    render(<Tag label="Clickable Tag" onClick={handleClick} />);
    const tag = screen.getByText('Clickable Tag');

    tag.click();
    expect(handleClick).toHaveBeenCalledTimes(1);
  });
});
