'use client';
import React from 'react';
import { styled } from '@pigment-css/react';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import { Chip } from '../Chip';
import { TagProps } from './Tag.types';
import { getTagUtilityClass } from './Tag.classes';

const useUtilityClasses = (ownerState: TagProps) => {
  const { variant, intensity } = ownerState;

  const slots = {
    root: ['root', variant && variant, intensity && intensity],
  };

  return composeClasses(slots, getTagUtilityClass, {});
};

const TagRoot = styled(Chip)<TagProps>(({ theme }) => ({
  display: 'inline-flex',
  justifyContent: 'center',
  alignItems: 'center',
  borderRadius: '4px',
  height: '18px',
  gap: '4px',
  padding: '0px 8px',
  ...theme.typography.bodySmall,
  fontSize: '12px !important',
  lineHeight: '18px !important',
  borderStyle: 'none',
  variants: [
    // Neutral variant
    {
      props: { variant: 'neutral', intensity: 'bold' },
      style: {
        backgroundColor: theme.vars.palette.secondary,
        color: theme.vars.palette.onPrimary,
        '&[data-clickable="true"]': {
          '&:hover': {
            backgroundColor: `color-mix(in srgb, ${theme.vars.palette.secondary}, ${theme.vars.palette.onPrimary} ${theme.vars.palette.stateLayers.hoverOnPrimary})`,
          },
          '&:active': {
            backgroundColor: `color-mix(in srgb, ${theme.vars.palette.secondary}, ${theme.vars.palette.onPrimary} ${theme.vars.palette.stateLayers.pressOnPrimary})`,
          },
        },
        '&:focus-visible': {
          outline: `2px solid ${theme.vars.palette.secondary}`,
          outlineOffset: 2,
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.secondary}, ${theme.vars.palette.onPrimary} ${theme.vars.palette.stateLayers.focusOnPrimary})`,
        },
      },
    },
    {
      props: { variant: 'neutral', intensity: 'subtle' },
      style: {
        backgroundColor: theme.vars.palette.secondaryContainer,
        color: theme.vars.palette.onSecondaryContainer,
        '&[data-clickable="true"]': {
          '&:hover': {
            backgroundColor: `color-mix(in srgb, ${theme.vars.palette.secondaryContainer}, ${theme.vars.palette.onSecondaryContainer} ${theme.vars.palette.stateLayers.hoverOnSecondaryContainer})`,
          },
          '&:active': {
            backgroundColor: `color-mix(in srgb, ${theme.vars.palette.secondaryContainer}, ${theme.vars.palette.onSecondaryContainer} ${theme.vars.palette.stateLayers.pressOnSecondaryContainer})`,
          },
        },
        '&:focus-visible': {
          outline: `2px solid ${theme.vars.palette.secondary}`,
          outlineOffset: 2,
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.secondaryContainer}, ${theme.vars.palette.onSecondaryContainer} ${theme.vars.palette.stateLayers.focusOnSecondaryContainer})`,
        },
      },
    },

    // Error variant
    {
      props: { variant: 'error', intensity: 'bold' },
      style: {
        backgroundColor: theme.vars.palette.error,
        color: theme.vars.palette.onError,
        '&[data-clickable="true"]': {
          '&:hover': {
            backgroundColor: `color-mix(in srgb, ${theme.vars.palette.error}, ${theme.vars.palette.onError} ${theme.vars.palette.stateLayers.hoverError})`,
          },
          '&:active': {
            backgroundColor: `color-mix(in srgb, ${theme.vars.palette.error}, ${theme.vars.palette.onError} ${theme.vars.palette.stateLayers.pressError})`,
          },
        },
        '&:focus-visible': {
          outline: `2px solid ${theme.vars.palette.error}`,
          outlineOffset: 2,
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.error}, ${theme.vars.palette.onError} ${theme.vars.palette.stateLayers.focusError})`,
        },
      },
    },
    {
      props: { variant: 'error', intensity: 'subtle' },
      style: {
        backgroundColor: theme.vars.palette.errorContainer,
        color: theme.vars.palette.onErrorContainer,
        '&[data-clickable="true"]': {
          '&:hover': {
            backgroundColor: `color-mix(in srgb, ${theme.vars.palette.errorContainer}, ${theme.vars.palette.onErrorContainer} ${theme.vars.palette.stateLayers.hoverError})`,
          },
          '&:active': {
            backgroundColor: `color-mix(in srgb, ${theme.vars.palette.errorContainer}, ${theme.vars.palette.onErrorContainer} ${theme.vars.palette.stateLayers.pressError})`,
          },
        },
        '&:focus-visible': {
          outline: `2px solid ${theme.vars.palette.error}`,
          outlineOffset: 2,
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.errorContainer}, ${theme.vars.palette.onErrorContainer} ${theme.vars.palette.stateLayers.focusError})`,
        },
      },
    },

    // Warning variant
    {
      props: { variant: 'warning', intensity: 'bold' },
      style: {
        backgroundColor: theme.vars.palette.system.warning,
        color: theme.vars.palette.system.onWarning,
        '&[data-clickable="true"]': {
          '&:hover': {
            backgroundColor: `color-mix(in srgb, ${theme.vars.palette.system.warning}, ${theme.vars.palette.system.onWarning} ${theme.vars.palette.stateLayers.hoverOnSurface})`,
          },
          '&:active': {
            backgroundColor: `color-mix(in srgb, ${theme.vars.palette.system.warning}, ${theme.vars.palette.system.onWarning} ${theme.vars.palette.stateLayers.pressOnSurface})`,
          },
        },
        '&:focus-visible': {
          outline: `2px solid ${theme.vars.palette.system.warning}`,
          outlineOffset: 2,
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.system.warning}, ${theme.vars.palette.system.onWarning} ${theme.vars.palette.stateLayers.focusOnSurface})`,
        },
      },
    },
    {
      props: { variant: 'warning', intensity: 'subtle' },
      style: {
        backgroundColor: theme.vars.palette.system.warningContainer,
        color: theme.vars.palette.system.onWarningContainer,
        '&[data-clickable="true"]': {
          '&:hover': {
            backgroundColor: `color-mix(in srgb, ${theme.vars.palette.system.warningContainer}, ${theme.vars.palette.system.onWarningContainer} ${theme.vars.palette.stateLayers.hoverOnSurface})`,
          },
          '&:active': {
            backgroundColor: `color-mix(in srgb, ${theme.vars.palette.system.warningContainer}, ${theme.vars.palette.system.onWarningContainer} ${theme.vars.palette.stateLayers.pressOnSurface})`,
          },
        },
        '&:focus-visible': {
          outline: `2px solid ${theme.vars.palette.system.warning}`,
          outlineOffset: 2,
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.system.warningContainer}, ${theme.vars.palette.system.onWarningContainer} ${theme.vars.palette.stateLayers.focusOnSurface})`,
        },
      },
    },

    // Info variant
    {
      props: { variant: 'info', intensity: 'bold' },
      style: {
        backgroundColor: theme.vars.palette.system.info,
        color: theme.vars.palette.system.onInfo,
        '&[data-clickable="true"]': {
          '&:hover': {
            backgroundColor: `color-mix(in srgb, ${theme.vars.palette.system.info}, ${theme.vars.palette.system.onInfo} ${theme.vars.palette.stateLayers.hoverOnSurface})`,
          },
          '&:active': {
            backgroundColor: `color-mix(in srgb, ${theme.vars.palette.system.info}, ${theme.vars.palette.system.onInfo} ${theme.vars.palette.stateLayers.pressOnSurface})`,
          },
        },
        '&:focus-visible': {
          outline: `2px solid ${theme.vars.palette.system.info}`,
          outlineOffset: 2,
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.system.info}, ${theme.vars.palette.system.onInfo} ${theme.vars.palette.stateLayers.focusOnSurface})`,
        },
      },
    },
    {
      props: { variant: 'info', intensity: 'subtle' },
      style: {
        backgroundColor: theme.vars.palette.system.infoContainer,
        color: theme.vars.palette.system.onInfoContainer,
        '&[data-clickable="true"]': {
          '&:hover': {
            backgroundColor: `color-mix(in srgb, ${theme.vars.palette.system.infoContainer}, ${theme.vars.palette.system.onInfoContainer} ${theme.vars.palette.stateLayers.hoverOnSurface})`,
          },
          '&:active': {
            backgroundColor: `color-mix(in srgb, ${theme.vars.palette.system.infoContainer}, ${theme.vars.palette.system.onInfoContainer} ${theme.vars.palette.stateLayers.pressOnSurface})`,
          },
        },
        '&:focus-visible': {
          outline: `2px solid ${theme.vars.palette.system.info}`,
          outlineOffset: 2,
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.system.infoContainer}, ${theme.vars.palette.system.onInfoContainer} ${theme.vars.palette.stateLayers.focusOnSurface})`,
        },
      },
    },

    // Success variant
    {
      props: { variant: 'success', intensity: 'bold' },
      style: {
        backgroundColor: theme.vars.palette.system.success,
        color: theme.vars.palette.system.onSuccess,
        '&[data-clickable="true"]': {
          '&:hover': {
            backgroundColor: `color-mix(in srgb, ${theme.vars.palette.system.success}, ${theme.vars.palette.system.onSuccess} ${theme.vars.palette.stateLayers.hoverOnSurface})`,
          },
          '&:active': {
            backgroundColor: `color-mix(in srgb, ${theme.vars.palette.system.success}, ${theme.vars.palette.system.onSuccess} ${theme.vars.palette.stateLayers.pressOnSurface})`,
          },
        },
        '&:focus-visible': {
          outline: `2px solid ${theme.vars.palette.system.success}`,
          outlineOffset: 2,
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.system.success}, ${theme.vars.palette.system.onSuccess} ${theme.vars.palette.stateLayers.focusOnSurface})`,
        },
      },
    },
    {
      props: { variant: 'success', intensity: 'subtle' },
      style: {
        backgroundColor: theme.vars.palette.system.successContainer,
        color: theme.vars.palette.system.onSuccessContainer,
        '&[data-clickable="true"]': {
          '&:hover': {
            backgroundColor: `color-mix(in srgb, ${theme.vars.palette.system.successContainer}, ${theme.vars.palette.system.onSuccessContainer} ${theme.vars.palette.stateLayers.hoverOnSurface})`,
          },
          '&:active': {
            backgroundColor: `color-mix(in srgb, ${theme.vars.palette.system.successContainer}, ${theme.vars.palette.system.onSuccessContainer} ${theme.vars.palette.stateLayers.pressOnSurface})`,
          },
        },
        '&:focus-visible': {
          outline: `2px solid ${theme.vars.palette.system.success}`,
          outlineOffset: 2,
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.system.successContainer}, ${theme.vars.palette.system.onSuccessContainer} ${theme.vars.palette.stateLayers.focusOnSurface})`,
        },
      },
    },

    // Disabled state
    {
      props: { disabled: true },
      style: {
        backgroundColor: `color-mix(in srgb, ${theme.vars.palette.backgroundDisabled}, ${theme.vars.palette.onBackgroundDisabled} ${theme.vars.palette.stateLayers.disabled})`,
        color: theme.vars.palette.onBackgroundDisabled,
        pointerEvents: 'none',
      },
    },
  ],
}));

// eslint-disable-next-line react/display-name
export const Tag = React.forwardRef<HTMLDivElement, TagProps>((props, ref) => {
  const { className, variant = 'neutral', intensity = 'bold', onClick, ...other } = props;
  const ownerState = { ...props, variant, intensity };
  const classes = useUtilityClasses(ownerState);

  return (
    <TagRoot
      ref={ref}
      className={[classes.root, className].join(' ')}
      variant={variant}
      intensity={intensity}
      data-clickable={Boolean(onClick)}
      onClick={onClick}
      {...other}
    />
  );
});
