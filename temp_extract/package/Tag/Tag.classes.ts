import generateUtilityClasses from '@mui/utils/generateUtilityClasses';
import generateUtilityClass from '@mui/utils/generateUtilityClass';

export interface TagClasses {
  /** Styles applied to the root element. */
  root: string;
  /** Styles applied to the root element if variant="neutral". */
  neutral: string;
  /** Styles applied to the root element if variant="error". */
  error: string;
  /** Styles applied to the root element if variant="warning". */
  warning: string;
  /** Styles applied to the root element if variant="info". */
  info: string;
  /** Styles applied to the root element if variant="success". */
  success: string;
  /** Styles applied to the root element if style="bold". */
  bold: string;
  /** Styles applied to the root element if style="subtle". */
  subtle: string;
}

export type TagClassKey = keyof TagClasses;

export function getTagUtilityClass(slot: string): string {
  return generateUtilityClass('NovaTag', slot);
}

const tagClasses: TagClasses = generateUtilityClasses('NovaTag', [
  'root',
  'neutral',
  'error',
  'warning',
  'info',
  'success',
  'bold',
  'subtle',
]);

export default tagClasses;
