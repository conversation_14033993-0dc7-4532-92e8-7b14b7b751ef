'use client';

import * as React from 'react';
import { styled } from '@pigment-css/react';
import useSlotProps from '@mui/utils/useSlotProps';
import {
  unstable_capitalize as capitalize,
  unstable_composeClasses as composeClasses,
  unstable_useForkRef as useForkRef,
} from '@mui/utils';
import type {
  AutocompleteOwnerState,
  AutocompleteProps,
  AutocompleteRenderGroupParams,
  AutocompleteRenderValueGetItemProps,
} from './Autocomplete.types';
import inputClasses from '../Input/Input.classes';
import iconButtonClasses from '../IconButton/IconButton.classes';
import autocompleteClasses, { getAutocompleteUtilityClass } from './Autocomplete.classes';
import { useAutocomplete, createFilterOptions } from '../internal/hooks/useAutocomplete';
import { Chip } from '../Chip';
import { IconButton } from '../IconButton';
import { Menu } from '../Menu';
import { MenuItem } from '../MenuItem';
import { ListSubheader } from '../ListSubheader';
import { ListItem } from '../ListItem';
import CloseOutlined from '../internal/svg-icons/CloseOutlined';
import KeyboardArrowDown from '../internal/svg-icons/KeyboardArrowDown';
import clsx from 'clsx';
import { useLocale } from '../Locale';
import enUS from '../Locale/en_US';

function hasValue(value: unknown) {
  return value != null && !(Array.isArray(value) && value.length === 0) && value !== '';
}

type OwnerState = Omit<AutocompleteOwnerState<any, any, any, any>, 'onChange' | 'defaultValue'>;

const useUtilityClasses = (ownerState: OwnerState) => {
  const { expanded, fullWidth, focused, hasClearIcon, hasPopupIcon, popupOpen, size, filled, multiple } = ownerState;

  const slots = {
    root: [
      'root',
      expanded && 'expanded',
      fullWidth && 'fullWidth',
      focused && 'focused',
      hasClearIcon && 'hasClearIcon',
      hasPopupIcon && 'hasPopupIcon',
      filled && 'filled',
      multiple && 'multiple',
    ],
    inputRoot: ['inputRoot'],
    tag: ['tag', size && `tagSize${capitalize(size)}`],
    clearIndicator: ['clearIndicator'],
    popupIndicator: ['popupIndicator', popupOpen && 'popupIndicatorOpen'],
    listbox: ['listbox'],
    loading: ['loading'],
    noOptions: ['noOptions'],
    option: ['option'],
    groupLabel: ['groupLabel'],
  };
  return composeClasses(slots, getAutocompleteUtilityClass, {});
};

const AutocompleteRoot = styled('div')<OwnerState>(({ theme }) => ({
  '--Icon-color': 'currentColor',
  display: 'inline-flex',
  [`&.${autocompleteClasses.focused} .${autocompleteClasses.clearIndicator}`]: {
    visibility: 'visible',
  },
  /* Avoid double tap issue on iOS */
  '@media (pointer: fine)': {
    [`&:hover .${autocompleteClasses.clearIndicator}`]: {
      visibility: 'visible',
    },
  },
  [`& .${inputClasses.root}`]: {
    [`& .${autocompleteClasses.tag}`]: {
      marginBlock: theme.vars.sys.size.spaceBetween.vertical['2xs'].medium,
      maxWidth: 'calc(100% - 6px)',
    },
  },
  [`& .${inputClasses.startDecorator}, & .${inputClasses.endDecorator}`]: {
    color: theme.vars.palette.onSurfaceVariant,
    [`& .${iconButtonClasses.root}`]: {
      color: theme.vars.palette.onSurfaceVariant,
    },
  },
  variants: [
    {
      props: { popupOpen: true },
      style: {
        [`& .${inputClasses.endDecorator} .${autocompleteClasses.popupIndicator}`]: {
          transform: 'rotate(180deg) scale(0.9)',
        },
      },
    },
    {
      props: { fullWidth: true },
      style: {
        width: '100%',
      },
    },
    {
      props: { filled: true },
      style: {
        [`& .${inputClasses.startDecorator}, & .${inputClasses.endDecorator}`]: {
          color: theme.vars.palette.onSurface,
          [`& .${iconButtonClasses.root}`]: {
            color: theme.vars.palette.onSurface,
          },
        },
      },
    },
  ],
}));

const AutocompleteClearIndicator = styled(IconButton)<OwnerState>(({ theme }) => ({
  visibility: 'hidden',
}));

const AutocompletePopupIndicator = styled(IconButton)<OwnerState>(({ theme }) => ({}));

const AutocompleteMenuItem = styled(MenuItem)(({ theme }) => ({
  '&.Mui-focused, &.Mui-focusVisible': {
    backgroundColor: `color-mix(in srgb, ${theme.vars.palette.backgroundStates}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.focusOnSurface})`,
  },
  '&[aria-selected="true"]': {
    backgroundColor: theme.vars.palette.secondaryContainer,
    '&.Mui-focused, &.Mui-focusVisible': {
      backgroundColor: `color-mix(in srgb, ${theme.vars.palette.surfaceContainerHighest}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.focusOnSurface})`,
    },
  },
}));

export { createFilterOptions };

export const Autocomplete = React.forwardRef(function Autocomplete<
  Value,
  Multiple extends boolean | undefined = false,
  DisableClearable extends boolean | undefined = false,
  FreeSolo extends boolean | undefined = false,
>(props: AutocompleteProps<Value, Multiple, DisableClearable, FreeSolo>, ref: React.ForwardedRef<HTMLDivElement>) {
  const autoCompleteLocale = useLocale('Autocomplete');
  const {
    autoComplete = false,
    autoHighlight = false,
    autoSelect = false,
    blurOnSelect = false,
    className,
    clearIcon = <CloseOutlined />,
    clearOnBlur = !props.freeSolo,
    clearOnEscape = false,
    clearText = enUS.Autocomplete.clearText,
    closeText = enUS.Autocomplete.closeText,
    defaultValue = props.multiple ? [] : null,
    disableClearable = false,
    disableCloseOnSelect = false,
    disabled = false,
    disabledItemsFocusable = false,
    disableListWrap = false,
    disablePortal = false,
    filterOptions,
    filterSelectedOptions = false,
    forcePopupIcon = 'auto',
    freeSolo = false,
    fullWidth = false,
    getLimitTagsText = (more: number) => `+${more}`,
    getOptionDisabled,
    getOptionKey,
    getOptionLabel: getOptionLabelProp,
    isOptionEqualToValue,
    groupBy,
    handleHomeEndKeys = !props.freeSolo,
    id: idProp,
    includeInputInList = false,
    inputValue: inputValueProp,
    limitTags = -1,
    loading = false,
    loadingText = enUS.Autocomplete.loadingText,
    multiple = false,
    noOptionsText = enUS.Autocomplete.noOptionsText,
    onChange,
    onClose,
    onHighlightChange,
    onInputChange,
    onOpen,
    open,
    openOnFocus = false,
    openText = enUS.Autocomplete.openText,
    options,
    popupIcon = <KeyboardArrowDown />,
    readOnly = false,
    renderGroup: renderGroupProp,
    renderInput,
    renderOption: renderOptionProp,
    renderValue,
    selectOnFocus = !props.freeSolo,
    size = 'medium',
    slots = {},
    slotProps = {},
    value: valueProp,
    ...other
  } = { ...autoCompleteLocale, ...props };

  const {
    getRootProps,
    getInputProps,
    getInputLabelProps,
    getPopupIndicatorProps,
    getClearProps,
    getItemProps,
    getListboxProps,
    getOptionProps,
    value,
    dirty,
    expanded,
    id,
    popupOpen,
    focused,
    focusedItem,
    anchorEl,
    setAnchorEl,
    inputValue,
    groupedOptions,
  } = useAutocomplete({ ...props, componentName: 'Autocomplete' });

  const hasClearIcon = !disableClearable && !disabled && dirty && !readOnly;
  const hasPopupIcon = (!freeSolo || forcePopupIcon === true) && forcePopupIcon !== false;

  const rootRef = useForkRef(ref, setAnchorEl as () => void);

  const { onMouseDown: handleInputMouseDown = () => {} } = getInputProps();
  const { ref: listboxRef, ...otherListboxProps } = getListboxProps() as {
    ref: React.Ref<HTMLUListElement>;
  } & React.HTMLAttributes<HTMLUListElement>;

  const defaultGetOptionLabel = (option: any) => option.label ?? option;
  const getOptionLabel = getOptionLabelProp || defaultGetOptionLabel;
  const filled = hasValue(value) || hasValue(inputValue);

  const ownerState = {
    ...props,
    disablePortal,
    expanded,
    focused,
    hasClearIcon,
    hasPopupIcon,
    inputFocused: focusedItem === -1,
    popupOpen,
    filled,
  };

  const classes = useUtilityClasses(ownerState as OwnerState);

  const SlotRoot = slots.root ?? AutocompleteRoot;
  const SlotListbox = slots.listbox ?? Menu;
  const SlotDefaultRenderOption = slots.option ?? AutocompleteMenuItem;
  const SlotGroupLabel = slots.groupLabel ?? ListSubheader;
  const SlotClearIndicator = slots.clearIndicator ?? AutocompleteClearIndicator;
  const SlotPopupIndicator = slots.popupIndicator ?? AutocompletePopupIndicator;
  const SlotLoading = slots.loading ?? ListItem;
  const SlotNoOptions = slots.noOptions ?? ListItem;
  const SlotLimitTag = slots.limitTag ?? 'span';

  const slotRootProps = useSlotProps({
    elementType: SlotRoot,
    externalSlotProps: slotProps.root,
    ownerState,
    getSlotProps: () => getRootProps(other),
    className: clsx(className, classes.root),
    additionalProps: {
      ref: rootRef,
    },
  });

  const slotListboxProps = useSlotProps({
    elementType: SlotListbox,
    externalSlotProps: slotProps.listbox,
    ownerState,
    className: classes.listbox,
    additionalProps: {
      anchorEl,
      disablePortal,
      style: { width: anchorEl ? anchorEl.clientWidth : undefined },
      open: popupOpen,
      ref: listboxRef,
      ...otherListboxProps,
    },
  });

  const slotDefaultRenderOptionProps = useSlotProps({
    elementType: SlotDefaultRenderOption,
    externalSlotProps: slotProps.option,
    ownerState,
    className: classes.option,
  });

  const slotGroupLabelProps = useSlotProps({
    elementType: SlotGroupLabel,
    externalSlotProps: slotProps.groupLabel,
    ownerState,
    className: classes.groupLabel,
  });

  const slotClearIndicatorProps = useSlotProps({
    elementType: SlotClearIndicator,
    externalSlotProps: slotProps.clearIndicator,
    ownerState,
    getSlotProps: getClearProps,
    className: classes.clearIndicator,
    additionalProps: {
      type: 'button',
      size,
      'aria-label': clearText,
      title: clearText,
      variant: 'standard',
    },
  });

  const slotPopupIndicatorProps = useSlotProps({
    elementType: SlotPopupIndicator,
    externalSlotProps: slotProps.popupIndicator,
    ownerState,
    getSlotProps: getPopupIndicatorProps,
    className: classes.popupIndicator,
    additionalProps: {
      type: 'button',
      size,
      disabled,
      'aria-label': popupOpen ? closeText : openText,
      title: popupOpen ? closeText : openText,
      variant: 'standard',
    },
  });

  const slotLoadingProps = useSlotProps({
    elementType: SlotLoading,
    externalSlotProps: slotProps.loading,
    ownerState,
    className: classes.loading,
    additionalProps: {
      role: 'presentation',
    },
  });

  const slotNoOptionsProps = useSlotProps({
    elementType: SlotNoOptions,
    externalSlotProps: slotProps.noOptions,
    ownerState,
    className: classes.noOptions,
    additionalProps: {
      role: 'presentation',
    },
  });

  const slotLimitTagProps = useSlotProps({
    elementType: SlotLimitTag,
    externalSlotProps: slotProps.limitTag,
    ownerState,
    className: classes.tag,
  });

  const getCustomizedItemProps = (params: { index: number }) => ({
    key: params.index,
    className: classes.tag,
    disabled,
    ...getItemProps(params),
  });

  const renderSelectedItems = () => {
    if (!value) return null;

    if (renderValue) {
      return renderValue(value, getCustomizedItemProps as AutocompleteRenderValueGetItemProps<Multiple>, ownerState);
    }

    if (multiple && Array.isArray(value)) {
      return value.map((option: Value, index: number) => {
        const { key, ...customItemProps } = getCustomizedItemProps({ index });
        return (
          <Chip
            key={key}
            label={getOptionLabel(option)}
            endIcon={<CloseOutlined sx={{ cursor: 'pointer' }} onClick={customItemProps.onDelete} />}
            size={size === 'large' ? 'medium' : 'small'}
            {...customItemProps}
            {...slotProps.chip}
          />
        );
      });
    }

    return null;
  };

  const renderLimitTags = (items: React.ReactNode[]) => {
    if (limitTags <= -1 || !Array.isArray(items)) return items;

    const more = items.length - limitTags;
    if (focused || more <= 0) return items;

    const limitedItems = items.slice(0, limitTags);
    limitedItems.push(
      <SlotLimitTag key={items.length} {...slotLimitTagProps}>
        {getLimitTagsText(more)}
      </SlotLimitTag>,
    );
    return limitedItems;
  };

  const renderInputContent = () => {
    const items = renderSelectedItems();
    const limitedItems = renderLimitTags(items as React.ReactNode[]);

    return {
      id,
      disabled,
      readOnly,
      size,
      fullWidth: true,
      className: classes.inputRoot,
      children: limitedItems,
      onMouseDown: (event: React.MouseEvent) => {
        if (event.target === event.currentTarget) {
          handleInputMouseDown(event as React.MouseEvent<HTMLInputElement>);
        }
      },
      ...((hasClearIcon || hasPopupIcon) && {
        endDecorator: (
          <React.Fragment>
            {hasClearIcon && <SlotClearIndicator {...slotClearIndicatorProps}>{clearIcon}</SlotClearIndicator>}
            {hasPopupIcon && <SlotPopupIndicator {...slotPopupIndicatorProps}>{popupIcon}</SlotPopupIndicator>}
          </React.Fragment>
        ),
      }),
      slotProps: inputSlotProps,
    };
  };

  const defaultRenderGroup = (params: AutocompleteRenderGroupParams) => (
    <React.Fragment key={params.key}>
      <SlotGroupLabel {...slotGroupLabelProps}>{params.group}</SlotGroupLabel>
      {params.children}
    </React.Fragment>
  );
  const renderGroup = renderGroupProp || defaultRenderGroup;

  const defaultRenderOption = (
    props2: React.HTMLAttributes<HTMLLIElement> & { key: string | number },
    option: Value,
  ) => {
    const { key, ...otherProps } = props2;
    return (
      <SlotDefaultRenderOption key={key} {...otherProps} {...slotDefaultRenderOptionProps}>
        {getOptionLabel(option)}
      </SlotDefaultRenderOption>
    );
  };
  const renderOption = renderOptionProp || defaultRenderOption;

  const renderListOption = (option: Value, index: number) => {
    const optionProps = getOptionProps({ option, index });
    return renderOption(
      { ...optionProps, className: classes.option },
      option,
      {
        selected: optionProps['aria-selected'] === 'true',
        index,
        inputValue,
      },
      ownerState,
    );
  };

  const inputSlotProps = {
    label: {
      ...getInputLabelProps(),
    },
    htmlInput: {
      ...getInputProps(),
    },
  };

  return (
    <React.Fragment>
      <SlotRoot {...slotRootProps}>{renderInput(renderInputContent())}</SlotRoot>
      {anchorEl && (
        <React.Fragment>
          {loading && groupedOptions.length === 0 && (
            <SlotListbox {...slotListboxProps}>
              <SlotLoading {...slotLoadingProps}>{loadingText}</SlotLoading>
            </SlotListbox>
          )}
          {groupedOptions.length === 0 && !freeSolo && !loading && (
            <SlotListbox {...slotListboxProps}>
              <SlotNoOptions
                {...slotNoOptionsProps}
                onMouseDown={(event: React.MouseEvent) => {
                  // Prevent input blur when interacting with the "no options" content
                  event.preventDefault();
                }}
              >
                {noOptionsText}
              </SlotNoOptions>
            </SlotListbox>
          )}
          {groupedOptions.length > 0 && (
            <SlotListbox {...slotListboxProps}>
              {groupedOptions.map((option: any, index: number) => {
                if (groupBy) {
                  return renderGroup({
                    key: option.key,
                    group: option.group,
                    children: option.options.map((option2: Value, index2: number) =>
                      renderListOption(option2, option.index + index2),
                    ),
                  });
                }
                return renderListOption(option, index);
              })}
            </SlotListbox>
          )}
        </React.Fragment>
      )}
    </React.Fragment>
  );
}) as AutocompleteComponent;

interface AutocompleteComponent {
  <
    T,
    Multiple extends boolean | undefined = undefined,
    DisableClearable extends boolean | undefined = undefined,
    FreeSolo extends boolean | undefined = undefined,
  >(
    props: AutocompleteProps<T, Multiple, DisableClearable, FreeSolo>,
  ): React.JSX.Element;
}
