import * as React from 'react';
import { OverrideProps } from '@mui/types';
import { SxProps } from '../../types/theme';
import { CreateSlotsAndSlotProps, SlotProps } from '../../types/slot';

export type AccordionSummarySlot = 'root' | 'button' | 'indicator';

export interface AccordionSummarySlots {
  /**
   * The component that renders the root.
   * @default 'div'
   */
  root?: React.ElementType;
  /**
   * The component that renders the button.
   * @default 'button'
   */
  button?: React.ElementType;
  /**
   * The component that renders the indicator.
   * @default 'span'
   */
  indicator?: React.ElementType;
}

export type AccordionSummarySlotsAndSlotProps = CreateSlotsAndSlotProps<
  AccordionSummarySlots,
  {
    root: SlotProps<'div', object, AccordionSummaryOwnerState>;
    button: SlotProps<'button', object, AccordionSummaryOwnerState>;
    indicator: SlotProps<'span', object, AccordionSummaryOwnerState>;
  }
>;

export interface AccordionSummaryTypeMap<P = object, D extends React.ElementType = 'div'> {
  props: P & {
    /**
     * Used to render icon or text elements inside the AccordionSummary if `src` is not set.
     * This can be an element, or just a string.
     */
    children?: React.ReactNode;
    /**
     * The component used for the Root slot.
     * Either a string to use a HTML element or a component.
     */
    component?: React.ElementType;
    /**
     * The indicator element to display.
     * @default <KeyboardArrowDown />
     */
    indicator?: React.ReactNode;
    /**
     * The system prop that allows defining system overrides as well as additional CSS styles.
     */
    sx?: SxProps;
  } & AccordionSummarySlotsAndSlotProps;
  defaultComponent: D;
}

export type AccordionSummaryProps<
  D extends React.ElementType = AccordionSummaryTypeMap['defaultComponent'],
  P = { component?: React.ElementType },
> = OverrideProps<AccordionSummaryTypeMap<P, D>, D>;

export interface AccordionSummaryOwnerState extends AccordionSummaryProps {
  /**
   * If `true`, the accordion is disabled.
   */
  disabled: boolean;
  /**
   * The expanded state of the accordion.
   */
  expanded: boolean;
}
