/// <reference types="@testing-library/jest-dom" />
import React from 'react';
import { expect, test, describe, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { AccordionGroup } from '../Group';
import { AccordionItem } from '../Item';
import { AccordionSummary } from './AccordionSummary';
import { AccordionDetails } from '../Details';
import accordionGroupClasses from '../Group/AccordionGroup.classes';
import accordionSummaryClasses from './AccordionSummary.classes';

describe('AccordionSummary', () => {
  test('renders basic accordion summary component correctly', () => {
    const view = render(<AccordionSummary />);
    expect(view).toBeTruthy();
  });

  test('renders a accordion summary with the data-testid property', () => {
    render(<AccordionSummary data-testid="root" />);
    expect(screen.getByTestId('root')).toBeDefined();
  });

  test('handles click events and toggles accordion', () => {
    const onClickMock = vi.fn();
    render(
      <AccordionGroup>
        <AccordionItem onClick={onClickMock}>
          <AccordionSummary data-testid="summary">Summary Content</AccordionSummary>
          <AccordionDetails>Details Content</AccordionDetails>
        </AccordionItem>
      </AccordionGroup>,
    );

    const summary = screen.getByTestId('summary');
    const button = summary.querySelector('button');
    expect(button).toBeDefined();

    fireEvent.click(button!);
    expect(onClickMock).toHaveBeenCalled();
  });

  test('applies correct density styles from AccordionGroup context', () => {
    const { rerender } = render(
      <AccordionGroup data-testid="AccordionGroup" density="compact">
        <AccordionItem>
          <AccordionSummary>Summary</AccordionSummary>
        </AccordionItem>
      </AccordionGroup>,
    );

    expect(screen.getByTestId('AccordionGroup')).toHaveClass(`${accordionGroupClasses.densityCompact}`);

    rerender(
      <AccordionGroup data-testid="AccordionGroup" density="comfortable">
        <AccordionItem>
          <AccordionSummary>Summary</AccordionSummary>
        </AccordionItem>
      </AccordionGroup>,
    );

    expect(screen.getByTestId('AccordionGroup')).toHaveClass(`${accordionGroupClasses.densityComfortable}`);
  });

  test('renders with custom indicator', () => {
    const CustomIndicator = () => <span data-testid="custom-indicator">↓</span>;
    render(
      <AccordionItem>
        <AccordionSummary indicator={<CustomIndicator />}>Summary</AccordionSummary>
      </AccordionItem>,
    );

    expect(screen.getByTestId('custom-indicator')).toBeInTheDocument();
  });

  test('handles disabled state correctly', () => {
    render(
      <AccordionGroup>
        <AccordionItem disabled>
          <AccordionSummary data-testid="summary">Disabled Summary</AccordionSummary>
        </AccordionItem>
      </AccordionGroup>,
    );

    const summary = screen.getByTestId('summary');
    const button = summary.querySelector('button');
    expect(button).toHaveAttribute('disabled');
    expect(summary).toHaveClass(accordionSummaryClasses.disabled);
  });

  test('applies custom slot props correctly', () => {
    const customClass = 'custom-summary-class';
    render(
      <AccordionSummary
        data-testid="summary"
        slotProps={{
          root: {
            className: customClass,
          },
        }}
      >
        Summary
      </AccordionSummary>,
    );

    expect(screen.getByTestId('summary')).toHaveClass(customClass);
  });

  test('handles expanded state correctly', () => {
    render(
      <AccordionGroup>
        <AccordionItem defaultExpanded>
          <AccordionSummary data-testid="summary">Summary</AccordionSummary>
          <AccordionDetails>Details</AccordionDetails>
        </AccordionItem>
      </AccordionGroup>,
    );

    const summary = screen.getByTestId('summary');
    expect(summary).toHaveClass(accordionSummaryClasses.expanded);
    expect(summary.querySelector('button')).toHaveAttribute('aria-expanded', 'true');
  });
});
