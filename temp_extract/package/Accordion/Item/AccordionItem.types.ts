import * as React from 'react';
import { OverrideProps } from '@mui/types';
import { SxProps } from '../../types/theme';
import { CreateSlotsAndSlotProps, SlotProps } from '../../types/slot';

export type AccordionItemSlot = 'root';

export interface AccordionItemSlots {
  /**
   * The component that renders the root.
   * @default 'div'
   */
  root?: React.ElementType;
}

export type AccordionItemSlotsAndSlotProps = CreateSlotsAndSlotProps<
  AccordionItemSlots,
  {
    root: SlotProps<'div', object, AccordionItemOwnerState>;
  }
>;

export interface AccordionItemTypeMap<ExtraProps = object, Tag extends React.ElementType = 'div'> {
  props: ExtraProps & {
    /**
     * The id to be used in the AccordionDetails which is controlled by the AccordionSummary.
     * If not provided, the id is autogenerated.
     */
    accordionId?: string;
    /**
     * Used to render icon or text elements inside the AccordionItem if `src` is not set.
     * This can be an element, or just a string.
     */
    children?: React.ReactNode;
    /**
     * If `true`, expands the accordion by default.
     * @default false
     */
    defaultExpanded?: boolean;
    /**
     * The component used for the Root slot.
     * Either a string to use a HTML element or a component.
     */
    component?: React.ElementType;
    /**
     * If `true`, the component is disabled.
     * @default false
     */
    disabled?: boolean;
    /**
     * If `true`, expands the accordion, otherwise collapse it.
     * Setting this prop enables control over the accordion.
     */
    expanded?: boolean;
    /**
     * Callback fired when the expand/collapse state is changed.
     *
     * @param {React.SyntheticEvent} event The event source of the callback. **Warning**: This is a generic event not a change event.
     * @param {boolean} expanded The `expanded` state of the accordion.
     */
    onChange?: (event: React.SyntheticEvent, expanded: boolean) => void;
    /**
     * The system prop that allows defining system overrides as well as additional CSS styles.
     */
    sx?: SxProps;
  } & AccordionItemSlotsAndSlotProps;
  defaultComponent: Tag;
}

export type AccordionItemProps<
  Tag extends React.ElementType = AccordionItemTypeMap['defaultComponent'],
  ExtraProps = { component?: React.ElementType },
> = OverrideProps<AccordionItemTypeMap<ExtraProps, Tag>, Tag>;

export interface AccordionItemOwnerState extends AccordionItemProps {}
