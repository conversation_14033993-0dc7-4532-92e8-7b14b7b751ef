import generateUtilityClasses from '@mui/utils/generateUtilityClasses';
import generateUtilityClass from '@mui/utils/generateUtilityClass';

export interface AccordionItemClasses {
  /** Class name applied to the root element. */
  root: string;
  /** Class name applied to the root element if `expanded` is true. */
  expanded: string;
  /** Class name applied to the root element if `disabled` is true. */
  disabled: string;
}

export type AccordionItemClassKey = keyof AccordionItemClasses;

export function getAccordionItemUtilityClass(slot: string): string {
  return generateUtilityClass('NovaAccordionItem', slot);
}

const accordionItemClasses: AccordionItemClasses = generateUtilityClasses('NovaAccordionItem', [
  'root',
  'expanded',
  'disabled',
]);

export default accordionItemClasses;
