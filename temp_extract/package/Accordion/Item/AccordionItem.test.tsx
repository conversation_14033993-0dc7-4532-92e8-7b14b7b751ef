/// <reference types="@testing-library/jest-dom" />
import React from 'react';
import { expect, test, describe, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { AccordionItem } from './AccordionItem';
import { AccordionSummary } from '../Summary';
import accordionClasses from './AccordionItem.classes';

describe('Accordion', () => {
  test('renders basic accordion component correctly', () => {
    const view = render(
      <AccordionItem>
        <AccordionSummary>Header</AccordionSummary>
      </AccordionItem>,
    );
    expect(view).toBeTruthy();
  });

  test('renders a accordion with the data-testid property', () => {
    render(
      <AccordionItem data-testid="NovaAccordionItem-root">
        <AccordionSummary>Header</AccordionSummary>
      </AccordionItem>,
    );
    expect(screen.getByTestId('NovaAccordionItem-root')).toBeDefined();
  });

  test('should render and not be controlled', () => {
    const { container } = render(
      <AccordionItem>
        <AccordionSummary>Header</AccordionSummary>
      </AccordionItem>,
    );
    expect(container.firstChild).not.toHaveClass(accordionClasses.expanded);
  });

  test('should handle defaultExpanded prop', () => {
    const { container } = render(
      <AccordionItem defaultExpanded>
        <AccordionSummary>Header</AccordionSummary>
      </AccordionItem>,
    );
    expect(container.firstChild).toHaveClass(accordionClasses.expanded);
  });

  test('should render the summary and collapse elements', () => {
    const { getByRole, getByText } = render(
      <AccordionItem>
        <AccordionSummary>Summary</AccordionSummary>
      </AccordionItem>,
    );
    expect(getByText('Summary')).toBeVisible();
    expect(getByRole('button')).toHaveAttribute('aria-expanded', 'false');
  });

  test('should be disabled', () => {
    const { getByRole } = render(
      <AccordionItem disabled>
        <AccordionSummary>Summary</AccordionSummary>
      </AccordionItem>,
    );
    expect(getByRole('button')).toHaveClass(accordionClasses.disabled);
  });

  test('should call onChange when clicking the summary element', () => {
    const handleChange = vi.fn();
    const { getByText } = render(
      <AccordionItem onChange={handleChange}>
        <AccordionSummary>Header</AccordionSummary>
      </AccordionItem>,
    );
    fireEvent.click(getByText('Header'));
    expect(handleChange).toBeCalled();
  });

  test('when disabled should have the disabled class', () => {
    const { container } = render(
      <AccordionItem disabled>
        <AccordionSummary>Header</AccordionSummary>
      </AccordionItem>,
    );
    expect(container.firstChild).toHaveClass(accordionClasses.disabled);
  });

  test('should handle controlled expansion state', () => {
    const handleChange = vi.fn();
    const { getByText, container } = render(
      <AccordionItem expanded={true} onChange={handleChange}>
        <AccordionSummary>Header</AccordionSummary>
      </AccordionItem>,
    );

    expect(container.firstChild).toHaveClass(accordionClasses.expanded);
    fireEvent.click(getByText('Header'));
    expect(handleChange).toHaveBeenCalledTimes(1);
  });

  test('should not trigger onChange when disabled and clicked', () => {
    const handleChange = vi.fn();
    const { getByText } = render(
      <AccordionItem disabled onChange={handleChange}>
        <AccordionSummary>Header</AccordionSummary>
      </AccordionItem>,
    );

    fireEvent.click(getByText('Header'));
    expect(handleChange).not.toHaveBeenCalled();
  });

  test('should maintain proper ARIA attributes', () => {
    const { getByRole } = render(
      <AccordionItem>
        <AccordionSummary>Header</AccordionSummary>
      </AccordionItem>,
    );

    const button = getByRole('button');
    expect(button).toHaveAttribute('aria-expanded', 'false');
    fireEvent.click(button);
    expect(button).toHaveAttribute('aria-expanded', 'true');
  });

  test('should handle custom className', () => {
    const { container } = render(
      <AccordionItem className="custom-class">
        <AccordionSummary>Header</AccordionSummary>
      </AccordionItem>,
    );

    expect(container.firstChild).toHaveClass('custom-class');
  });

  test('should handle multiple expansions and collapses', () => {
    const { getByText, container } = render(
      <AccordionItem>
        <AccordionSummary>Header</AccordionSummary>
      </AccordionItem>,
    );

    expect(container.firstChild).not.toHaveClass(accordionClasses.expanded);

    fireEvent.click(getByText('Header'));
    expect(container.firstChild).toHaveClass(accordionClasses.expanded);

    fireEvent.click(getByText('Header'));
    expect(container.firstChild).not.toHaveClass(accordionClasses.expanded);

    fireEvent.click(getByText('Header'));
    expect(container.firstChild).toHaveClass(accordionClasses.expanded);
  });

  test('should handle focus management', () => {
    const { getByRole } = render(
      <AccordionItem>
        <AccordionSummary>Header</AccordionSummary>
      </AccordionItem>,
    );

    const button = getByRole('button');
    button.focus();
    expect(document.activeElement).toBe(button);

    fireEvent.keyDown(button, { key: 'Tab' });
    expect(document.activeElement).toBe(button);
  });
});
