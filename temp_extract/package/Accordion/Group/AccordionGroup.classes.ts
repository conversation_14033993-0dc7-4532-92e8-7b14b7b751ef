import generateUtilityClasses from '@mui/utils/generateUtilityClasses';
import generateUtilityClass from '@mui/utils/generateUtilityClass';

export interface AccordionGroupClasses {
  /** Class name applied to the root element. */
  root: string;
  /** Class name applied to the root element if `density="standard"`. */
  densityStandard: string;
  /** Class name applied to the root element if `density="compact"`. */
  densityCompact: string;
  /** Class name applied to the root element if `density="comfortable"`. */
  densityComfortable: string;
}

export type AccordionGroupClassKey = keyof AccordionGroupClasses;

export function getAccordionGroupUtilityClass(slot: string): string {
  return generateUtilityClass('NovaAccordionGroup', slot);
}

const accordionGroupClasses: AccordionGroupClasses = generateUtilityClasses('NovaAccordionGroup', [
  'root',
  'densityStandard',
  'densityCompact',
  'densityComfortable',
]);

export default accordionGroupClasses;
