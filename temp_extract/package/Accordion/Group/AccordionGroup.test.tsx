/// <reference types="@testing-library/jest-dom" />
import React from 'react';
import { expect, test, describe } from 'vitest';
import { render, screen } from '@testing-library/react';
import { AccordionGroup } from './AccordionGroup';
import { AccordionItem } from '../Item';
import { AccordionSummary } from '../Summary';
import { AccordionDetails } from '../Details';

describe('AccordionGroup', () => {
  test('renders basic accordion group component correctly', () => {
    const view = render(<AccordionGroup />);
    expect(view).toBeTruthy();
  });

  test('renders a accordion group with the data-testid property', () => {
    render(<AccordionGroup data-testid="root" />);
    expect(screen.getByTestId('root')).toBeDefined();
  });

  test('renders multiple accordions correctly', () => {
    render(
      <AccordionGroup>
        <AccordionItem>
          <AccordionSummary>Header 1</AccordionSummary>
          <AccordionDetails data-testid="details">Content 1</AccordionDetails>
        </AccordionItem>
        <AccordionItem>
          <AccordionSummary>Header 2</AccordionSummary>
          <AccordionDetails data-testid="details">Content 2</AccordionDetails>
        </AccordionItem>
      </AccordionGroup>,
    );

    expect(screen.getAllByRole('button')).toHaveLength(2);
    expect(screen.getAllByTestId('details')).toHaveLength(2);
  });

  test('applies different density classes correctly', () => {
    const { rerender } = render(<AccordionGroup data-testid="root" density="compact" />);
    expect(screen.getByTestId('root')).toHaveClass('NovaAccordionGroup-densityCompact');

    rerender(<AccordionGroup data-testid="root" density="comfortable" />);
    expect(screen.getByTestId('root')).toHaveClass('NovaAccordionGroup-densityComfortable');

    rerender(<AccordionGroup data-testid="root" density="standard" />);
    expect(screen.getByTestId('root')).toHaveClass('NovaAccordionGroup-densityStandard');
  });

  test('renders with custom component prop', () => {
    render(<AccordionGroup component="section" data-testid="root" />);
    const element = screen.getByTestId('root');
    expect(element.tagName.toLowerCase()).toBe('section');
  });

  test('accepts and applies custom slot props', () => {
    const customClass = 'custom-root-class';
    render(
      <AccordionGroup
        data-testid="root"
        slotProps={{
          root: {
            className: customClass,
          },
        }}
      />,
    );

    const element = screen.getByTestId('root');
    expect(element).toHaveClass(customClass);
  });

  test('handles nested accordions correctly', () => {
    render(
      <AccordionGroup data-testid="outer-group">
        <AccordionItem>
          <AccordionSummary>Outer Header</AccordionSummary>
          <AccordionDetails>
            <AccordionGroup data-testid="inner-group">
              <AccordionItem>
                <AccordionSummary>Inner Header</AccordionSummary>
                <AccordionDetails>Inner Content</AccordionDetails>
              </AccordionItem>
            </AccordionGroup>
          </AccordionDetails>
        </AccordionItem>
      </AccordionGroup>,
    );

    expect(screen.getByTestId('outer-group')).toBeInTheDocument();
    expect(screen.getByTestId('inner-group')).toBeInTheDocument();
  });
});
