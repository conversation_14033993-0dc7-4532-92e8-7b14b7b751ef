import * as React from 'react';
import { OverrideProps } from '@mui/types';
import { SxProps } from '../../types/theme';
import { CreateSlotsAndSlotProps, SlotProps } from '../../types/slot';

export type AccordionGroupSlot = 'root';

export interface AccordionGroupSlots {
  /**
   * The component that renders the root.
   * @default 'div'
   */
  root?: React.ElementType;
}

export type AccordionGroupSlotsAndSlotProps = CreateSlotsAndSlotProps<
  AccordionGroupSlots,
  {
    root: SlotProps<'div', object, AccordionGroupOwnerState>;
  }
>;

export interface AccordionGroupTypeMap<P = object, D extends React.ElementType = 'div'> {
  props: P & {
    /**
     * Used to render icon or text elements inside the AccordionGroup if `src` is not set.
     * This can be an element, or just a string.
     */
    children?: React.ReactNode;
    /**
     * If `true`, the divider between accordions will be hidden.
     * @default false
     */
    disableDivider?: boolean;
    /**
     * The component used for the Root slot.
     * Either a string to use a HTML element or a component.
     */
    component?: React.ElementType;
    /**
     * The size of the component.
     * @default 'standard'
     */
    density?: 'standard' | 'compact' | 'comfortable';
    /**
     * The CSS transition for the Accordion details.
     * @default '0.2s ease'
     */
    transition?: string | { initial: string; expanded: string };
    /**
     * The system prop that allows defining system overrides as well as additional CSS styles.
     */
    sx?: SxProps;
  } & AccordionGroupSlotsAndSlotProps;
  defaultComponent: D;
}

export type AccordionGroupProps<
  D extends React.ElementType = AccordionGroupTypeMap['defaultComponent'],
  P = { component?: React.ElementType },
> = OverrideProps<AccordionGroupTypeMap<P, D>, D>;

export interface AccordionGroupOwnerState extends AccordionGroupProps {}
