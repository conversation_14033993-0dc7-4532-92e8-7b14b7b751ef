import { AccordionGroup } from './Group';
import { AccordionItem } from './Item';
import { AccordionSummary } from './Summary';
import { AccordionDetails } from './Details';

export { AccordionGroup, accordionGroupClasses } from './Group';
export { AccordionItem, accordionItemClasses } from './Item';
export { AccordionSummary, accordionSummaryClasses } from './Summary';
export { AccordionDetails, accordionDetailsClasses } from './Details';

export type { AccordionGroupProps } from './Group';
export type { AccordionItemProps } from './Item';
export type { AccordionSummaryProps } from './Summary';
export type { AccordionDetailsProps } from './Details';

export const Accordion = {
  Group: AccordionGroup,
  Item: AccordionItem,
  Summary: AccordionSummary,
  Details: AccordionDetails,
};
