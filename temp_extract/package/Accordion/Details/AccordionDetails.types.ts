import * as React from 'react';
import { OverrideProps } from '@mui/types';
import { SxProps } from '../../types/theme';
import { CreateSlotsAndSlotProps, SlotProps } from '../../types/slot';

export type AccordionDetailsSlot = 'root' | 'content';

export interface AccordionDetailsSlots {
  /**
   * The component that renders the root.
   * @default 'div'
   */
  root?: React.ElementType;
  /**
   * The component that renders the content.
   * @default 'div'
   */
  content?: React.ElementType;
}

export type AccordionDetailsSlotsAndSlotProps = CreateSlotsAndSlotProps<
  AccordionDetailsSlots,
  {
    root: SlotProps<'div', object, AccordionDetailsOwnerState>;
    content: SlotProps<'div', object, AccordionDetailsOwnerState>;
  }
>;

export interface AccordionDetailsTypeMap<P = object, D extends React.ElementType = 'div'> {
  props: P & {
    /**
     * Used to render icon or text elements inside the AccordionDetails if `src` is not set.
     * This can be an element, or just a string.
     */
    children?: React.ReactNode;
    /**
     * The component used for the Root slot.
     * Either a string to use a HTML element or a component.
     */
    component?: React.ElementType;
    /**
     * The system prop that allows defining system overrides as well as additional CSS styles.
     */
    sx?: SxProps;
  } & AccordionDetailsSlotsAndSlotProps;
  defaultComponent: D;
}

export type AccordionDetailsProps<
  D extends React.ElementType = AccordionDetailsTypeMap['defaultComponent'],
  P = { component?: React.ElementType },
> = OverrideProps<AccordionDetailsTypeMap<P, D>, D>;

export interface AccordionDetailsOwnerState extends AccordionDetailsProps {
  /**
   * The size of the component.
   * @default 'standard'
   */
  density?: 'standard' | 'compact' | 'comfortable';
  /**
   * If `true`, the accordion details is disabled.
   */
  disabled: boolean;
  /**
   * The expanded state of the accordion.
   */
  expanded: boolean;
}
