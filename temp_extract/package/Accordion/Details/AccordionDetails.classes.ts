import generateUtilityClasses from '@mui/utils/generateUtilityClasses';
import generateUtilityClass from '@mui/utils/generateUtilityClass';

export interface AccordionDetailsClasses {
  /** Class name applied to the root element. */
  root: string;
  /** Class name applied to the content element. */
  content: string;
  /** Class name applied when the accordion is disabled. */
  disabled: string;
  /** Class name applied to the root element when expanded. */
  expanded: string;
}

export type AccordionDetailsClassKey = keyof AccordionDetailsClasses;

export function getAccordionDetailsUtilityClass(slot: string): string {
  return generateUtilityClass('NovaAccordionDetails', slot);
}

const accordionDetailsClasses: AccordionDetailsClasses = generateUtilityClasses('NovaAccordionDetails', [
  'root',
  'content',
  'disabled',
  'expanded',
]);

export default accordionDetailsClasses;
