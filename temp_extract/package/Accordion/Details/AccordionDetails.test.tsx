/* eslint-disable react/display-name */
/// <reference types="@testing-library/jest-dom" />
import React from 'react';
import { expect, test, describe, vi } from 'vitest';
import { render, screen } from '@testing-library/react';
import { AccordionItem } from '../Item';
import { AccordionSummary } from '../Summary';
import { AccordionDetails } from '../Details';
import accordionDetailsClasses from './AccordionDetails.classes';

describe('AccordionDetails', () => {
  test('renders basic accordion component correctly', () => {
    const view = render(
      <AccordionDetails>
        <AccordionSummary>Header</AccordionSummary>
      </AccordionDetails>,
    );
    expect(view).toBeTruthy();
  });

  test('renders a accordion with the data-testid property', () => {
    render(
      <AccordionDetails data-testid="NovaAccordion-root">
        <AccordionSummary>Header</AccordionSummary>
      </AccordionDetails>,
    );
    expect(screen.getByTestId('NovaAccordion-root')).toBeDefined();
  });

  test('[initial] interactive content should have tab index -1', () => {
    render(
      <AccordionItem>
        <AccordionDetails>
          <a href="/foo" data-testid="link">
            Hello
          </a>
          <input data-testid="textbox" />
        </AccordionDetails>
      </AccordionItem>,
    );

    expect(screen.getByTestId('link')).to.have.property('tabIndex', -1);
    expect(screen.getByTestId('textbox')).to.have.property('tabIndex', -1);
  });

  test('[expanded] interactive content should not have tab index 0', () => {
    render(
      <AccordionItem expanded>
        <AccordionDetails>
          <a href="/foo" data-testid="link">
            Hello
          </a>
          <input data-testid="textbox" />
        </AccordionDetails>
      </AccordionItem>,
    );

    expect(screen.getByTestId('link')).to.have.property('tabIndex', 0);
    expect(screen.getByTestId('textbox')).to.have.property('tabIndex', 0);
  });
});
test('applies expanded class when expanded is true', () => {
  render(
    <AccordionItem expanded>
      <AccordionDetails>
        <div>Content</div>
      </AccordionDetails>
    </AccordionItem>,
  );

  const detailsElement = screen.getByRole('region');
  expect(detailsElement).toHaveClass(accordionDetailsClasses.expanded);
});

test('does not apply expanded class when expanded is false', () => {
  render(
    <AccordionItem>
      <AccordionDetails data-testid="details">
        <div>Content</div>
      </AccordionDetails>
    </AccordionItem>,
  );

  const detailsElement = screen.getByTestId('details');
  expect(detailsElement).not.toHaveClass(accordionDetailsClasses.expanded);
});

test('renders custom root component when component prop is provided', () => {
  render(
    <AccordionDetails component="section" data-testid="details">
      <div>Content</div>
    </AccordionDetails>,
  );

  const detailsElement = screen.getByTestId('details');
  expect(detailsElement.tagName.toLowerCase()).toBe('section');
});

test('renders custom root slot when slots.root is provided', () => {
  const CustomRoot = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>((props, ref) => (
    <div ref={ref} {...props} data-testid="custom-root" />
  ));

  render(
    <AccordionDetails slots={{ root: CustomRoot }}>
      <div>Content</div>
    </AccordionDetails>,
  );

  expect(screen.getByTestId('custom-root')).toBeInTheDocument();
});

test('renders custom content slot when slots.content is provided', () => {
  const CustomContent = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>((props, ref) => (
    <div ref={ref} {...props} data-testid="custom-content" />
  ));

  render(
    <AccordionDetails slots={{ content: CustomContent }}>
      <div>Content</div>
    </AccordionDetails>,
  );

  expect(screen.getByTestId('custom-content')).toBeInTheDocument();
});

test('applies custom slotProps to root slot', () => {
  render(
    <AccordionDetails slotProps={{ root: { 'data-testid': 'custom-root-props' } }}>
      <div>Content</div>
    </AccordionDetails>,
  );

  expect(screen.getByTestId('custom-root-props')).toBeInTheDocument();
});

test('applies custom slotProps to content slot', () => {
  render(
    <AccordionDetails slotProps={{ content: { 'data-testid': 'custom-content-props' } }}>
      <div>Content</div>
    </AccordionDetails>,
  );

  expect(screen.getByTestId('custom-content-props')).toBeInTheDocument();
});
