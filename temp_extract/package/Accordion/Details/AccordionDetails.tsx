'use client';
import * as React from 'react';
import { OverridableComponent } from '@mui/types';
import { unstable_composeClasses as composeClasses, unstable_useForkRef as useForkRef } from '@mui/utils';
import { styled } from '@pigment-css/react';
import accordionDetailsClasses, { getAccordionDetailsUtilityClass } from './AccordionDetails.classes';
import { AccordionDetailsOwnerState, AccordionDetailsTypeMap } from './AccordionDetails.types';
import useSlotProps from '@mui/utils/useSlotProps';
import AccordionItemContext from '../Context/AccordionItemContext';
import AccordionGroupContext from '../Context/AccordionGroupContext';

const useUtilityClasses = (ownerState: AccordionDetailsOwnerState) => {
  const { disabled, expanded } = ownerState;
  const slots = {
    root: ['root', disabled && 'disabled', expanded && 'expanded'],
    content: ['content', disabled && 'disabled', expanded && 'expanded'],
  };

  return composeClasses(slots, getAccordionDetailsUtilityClass, {});
};

const AccordionDetailsRoot = styled('div', {
  name: 'NovaAccordionDetails',
  slot: 'Root',
  overridesResolver: (props, styles) => styles.root,
})<AccordionDetailsOwnerState>(({ theme }) => ({
  width: '100%',
  overflow: 'hidden',
  borderRadius: 'var(--nova-accordionDetails-radius)',
  display: 'grid',
  gridTemplateRows: '1fr',
  marginInline: 'calc(-1 * var(--nova-listItem-paddingLeft)) calc(-1 * var(--nova-listItem-paddingRight))',
  transition: 'var(--nova-accordionDetails-transition)',
  [`&:not(.${accordionDetailsClasses.expanded})`]: {
    gridTemplateRows: '0fr',
  },
  variants: [
    {
      props: { disabled: true },
      style: {
        color: theme.vars.palette.onSurface,
        backgroundColor: theme.vars.palette.backgroundDisabled,
      },
    },
  ],
}));

/**
 * The content slot is required because the root slot is a CSS Grid, it needs a child.
 */
const AccordionDetailsContent = styled('div', {
  name: 'NovaAccordionDetails',
  slot: 'Content',
  overridesResolver: (props, styles) => styles.root,
})<AccordionDetailsOwnerState>({
  display: 'flex',
  flexDirection: 'column',
  overflow: 'hidden',
  paddingInlineStart: '17px',
  paddingInlineEnd: '17px',
  paddingBlockStart: '20px',
  paddingBlockEnd: '20px',
  transition: 'var(--nova-accordionDetails-transition)',
  [`&:not(.${accordionDetailsClasses.expanded})`]: {
    paddingBlock: 0,
  },
  variants: [
    {
      props: { density: 'compact' },
      style: {
        paddingBlockStart: '16px',
        paddingBlockEnd: '16px',
      },
    },
    {
      props: { density: 'comfortable' },
      style: {
        paddingBlockStart: '24px',
        paddingBlockEnd: '24px',
      },
    },
  ],
});

// eslint-disable-next-line react/display-name
export const AccordionDetails = React.forwardRef(function AccordionDetails(props, ref) {
  const { component = 'div', children, slots = {}, slotProps = {}, ...other } = props;

  const { accordionId, disabled = false, expanded = false } = React.useContext(AccordionItemContext);
  const { density } = React.useContext(AccordionGroupContext);
  const rootRef = React.useRef<HTMLElement>(null);
  const handleRef = useForkRef(rootRef, ref);

  React.useEffect(() => {
    if (rootRef.current) {
      const elements = rootRef.current.querySelectorAll(
        'a, button, input, textarea, select, details, [tabindex]:not([tabindex="-1"])',
      );

      elements.forEach((elm) => {
        const currentTabIndex = elm.getAttribute('tabindex');
        const prevTabIndex = elm.getAttribute('data-prev-tabindex');

        if (expanded) {
          // Restore the previous tabindex if it exists, or remove it if it was "unset"
          if (prevTabIndex === 'unset') {
            elm.removeAttribute('tabindex');
          } else if (prevTabIndex !== null) {
            elm.setAttribute('tabindex', prevTabIndex);
          }
          elm.removeAttribute('data-prev-tabindex');
        } else {
          // If element has no data-prev-tabindex, store the current tabindex or "unset"
          if (prevTabIndex === null) {
            elm.setAttribute('data-prev-tabindex', currentTabIndex || 'unset');
          }
          elm.setAttribute('tabindex', '-1');
        }
      });
    }
  }, [expanded]);

  const externalForwardedProps = { ...other, component, slots, slotProps };

  const ownerState = {
    ...props,
    component,
    density,
    expanded,
    disabled,
    nesting: true,
  };

  const classes = useUtilityClasses(ownerState);

  const SlotRoot = slots.root ?? AccordionDetailsRoot;
  const rootProps = useSlotProps({
    elementType: AccordionDetailsRoot,
    externalSlotProps: slotProps.root,
    externalForwardedProps,
    additionalProps: {
      ref: handleRef,
      as: component,
      id: `${accordionId}-details`,
      'aria-labelledby': `${accordionId}-summary`,
      role: 'region',
      hidden: expanded ? undefined : true,
      tabIndex: disabled ? -1 : 0,
    },
    ownerState,
    className: classes.root,
  });

  const SlotContent = slots.content ?? AccordionDetailsContent;
  const contentProps = useSlotProps({
    elementType: AccordionDetailsContent,
    externalSlotProps: slotProps.content,
    ownerState,
    className: classes.content,
  });

  return (
    <SlotRoot {...rootProps}>
      <SlotContent {...contentProps}>{children}</SlotContent>
    </SlotRoot>
  );
}) as OverridableComponent<AccordionDetailsTypeMap>;
