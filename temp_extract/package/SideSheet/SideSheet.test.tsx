import * as React from 'react';
import '@testing-library/jest-dom/vitest';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { expect, describe, it, vi, beforeEach } from 'vitest';
import { SideSheet, SideSheetHeader, SideSheetContent, SideSheetFooter } from './SideSheet';
import classes from './SideSheet.classes';

// Mock sub-components for easier testing
const MockComponent = React.forwardRef<HTMLDivElement>((props: React.HTMLAttributes<HTMLDivElement>, ref) => (
  <div ref={ref} {...props} />
));
MockComponent.displayName = 'MockComponent';

describe('<SideSheet />', () => {
  const TestContent = () => <div data-testid="side-sheet-content">Side Sheet Content</div>;

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the children when open', () => {
    render(
      <SideSheet open>
        <TestContent />
      </SideSheet>,
    );

    expect(screen.getByTestId('side-sheet-content')).toBeInTheDocument();
  });

  describe('CSS classes', () => {
    it('applies default classes to the root element', () => {
      render(
        <SideSheet open>
          <TestContent />
        </SideSheet>,
      );

      const root = screen.getByRole('sideSheet');
      expect(root).toHaveClass(classes.root);
      expect(root).toHaveClass(classes.anchorRight);
      expect(root).toHaveClass(classes.open);
    });

    it('applies default classes to the container element', () => {
      render(
        <SideSheet open>
          <TestContent />
        </SideSheet>,
      );

      const container = screen.getByRole('sideSheet').querySelector(`.${classes.container}`);
      expect(container).toHaveClass(classes.container);
    });

    it('applies anchor specific classes', () => {
      const { rerender } = render(
        <SideSheet open anchor="left">
          <TestContent />
        </SideSheet>,
      );

      let root = screen.getByRole('sideSheet');
      expect(root).toHaveClass(classes.anchorLeft);
      expect(root).not.toHaveClass(classes.anchorRight);

      rerender(
        <SideSheet open anchor="right">
          <TestContent />
        </SideSheet>,
      );

      root = screen.getByRole('sideSheet');
      expect(root).toHaveClass(classes.anchorRight);
      expect(root).not.toHaveClass(classes.anchorLeft);
    });

    it('applies open state class', () => {
      render(
        <SideSheet open>
          <TestContent />
        </SideSheet>,
      );

      const root = screen.getByRole('sideSheet');
      expect(root).toHaveClass(classes.open);
    });

    it('merges custom className with default classes', () => {
      render(
        <SideSheet open className="custom-class">
          <TestContent />
        </SideSheet>,
      );

      const root = screen.getByRole('sideSheet');
      expect(root).toHaveClass('custom-class');
      expect(root).toHaveClass(classes.root);
    });
  });

  describe('default props', () => {
    it('applies default props correctly', () => {
      render(
        <SideSheet open>
          <TestContent />
        </SideSheet>,
      );

      const sideSheet = screen.getByRole('sideSheet');

      // Default anchor is 'right'
      expect(sideSheet).toHaveClass(classes.anchorRight);

      // Default width is 300px
      expect(sideSheet).toHaveStyle('--nova-sideSheet-width: 300px');
    });
  });

  describe('prop: anchor', () => {
    it('renders with left anchor', () => {
      render(
        <SideSheet open anchor="left">
          <TestContent />
        </SideSheet>,
      );

      const sideSheet = screen.getByRole('sideSheet');
      expect(sideSheet).toHaveClass(classes.anchorLeft);
    });

    it('renders with right anchor (default)', () => {
      render(
        <SideSheet open>
          <TestContent />
        </SideSheet>,
      );

      const sideSheet = screen.getByRole('sideSheet');
      expect(sideSheet).toHaveClass(classes.anchorRight);
    });
  });

  describe('modal behavior', () => {
    it('renders as a modal with sheet role', () => {
      render(
        <SideSheet open>
          <TestContent />
        </SideSheet>,
      );

      expect(screen.getByRole('sideSheet')).toBeInTheDocument();
    });

    it('calls onClose when clicking on the backdrop', async () => {
      const handleClose = vi.fn();
      render(
        <SideSheet open onClose={handleClose}>
          <TestContent />
        </SideSheet>,
      );
      // The backdrop is part of the Modal component
      const backdrop = screen.getByRole('sideSheet').querySelector('Modal-backdrop');
      if (backdrop) {
        fireEvent.click(backdrop);
        await waitFor(() => {
          expect(handleClose).toHaveBeenCalledTimes(1);
        });
      }
    });

    it('has the proper ARIA attributes', () => {
      render(
        <SideSheet open>
          <TestContent />
        </SideSheet>,
      );

      const sideSheet = screen.getByRole('sideSheet');
      expect(sideSheet).toHaveAttribute('aria-modal', 'true');
    });

    it('does not call onClose when pressing non-Escape keys', () => {
      const handleClose = vi.fn();
      render(
        <SideSheet open onClose={handleClose}>
          <TestContent />
        </SideSheet>,
      );

      fireEvent.keyDown(document, { key: 'Enter' });
      expect(handleClose).not.toHaveBeenCalled();
    });

    it('is accessible with keyboard navigation', () => {
      render(
        <SideSheet open>
          <SideSheetHeader tabIndex={0} data-testid="header">
            Header
          </SideSheetHeader>
          <SideSheetContent tabIndex={0} data-testid="content">
            Content
          </SideSheetContent>
          <SideSheetFooter tabIndex={0} data-testid="footer">
            Footer
          </SideSheetFooter>
        </SideSheet>,
      );

      const header = screen.getByTestId('header');
      const content = screen.getByTestId('content');
      const footer = screen.getByTestId('footer');

      expect(header).toHaveAttribute('tabIndex', '0');
      expect(content).toHaveAttribute('tabIndex', '0');
      expect(footer).toHaveAttribute('tabIndex', '0');
    });
  });

  describe('prop: width', () => {
    it('applies custom width', () => {
      render(
        <SideSheet open width={500}>
          <TestContent />
        </SideSheet>,
      );

      const sideSheet = screen.getByRole('sideSheet');
      expect(sideSheet).toHaveStyle('--nova-sideSheet-width: 500px');
    });

    it('accepts string width value', () => {
      render(
        <SideSheet open width="400px">
          <TestContent />
        </SideSheet>,
      );

      const sideSheet = screen.getByRole('sideSheet');
      expect(sideSheet).toHaveStyle('--nova-sideSheet-width: 400px');
    });

    it('handles percentage width values', () => {
      render(
        <SideSheet open width="50%">
          <TestContent />
        </SideSheet>,
      );

      const sideSheet = screen.getByRole('sideSheet');
      expect(sideSheet).toHaveStyle('--nova-sideSheet-width: 50%');
    });

    it('handles other CSS units for width', () => {
      render(
        <SideSheet open width="30rem">
          <TestContent />
        </SideSheet>,
      );

      const sideSheet = screen.getByRole('sideSheet');
      expect(sideSheet).toHaveStyle('--nova-sideSheet-width: 30rem');
    });
  });

  describe('prop: component', () => {
    it('renders with custom component', () => {
      render(
        <SideSheet open component="section">
          <TestContent />
        </SideSheet>,
      );

      const root = document.querySelector('section[role="sideSheet"]');
      expect(root).not.toBeNull();
    });
  });

  describe('prop: slots and slotProps', () => {
    it('uses custom slots', () => {
      const CustomRoot = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
        ({ children, ...props }, ref) => (
          <div data-testid="custom-root" ref={ref} {...props}>
            {children}
          </div>
        ),
      );
      CustomRoot.displayName = 'CustomRoot';

      const CustomContainer = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
        ({ children, ...props }, ref) => (
          <div data-testid="custom-container" ref={ref} {...props}>
            {children}
          </div>
        ),
      );
      CustomContainer.displayName = 'CustomContainer';

      render(
        <SideSheet
          open
          slots={{
            root: CustomRoot,
            container: CustomContainer,
          }}
        >
          <TestContent />
        </SideSheet>,
      );

      expect(screen.getByTestId('custom-root')).toBeInTheDocument();
      expect(screen.getByTestId('custom-container')).toBeInTheDocument();
    });

    it('passes slotProps to slots', () => {
      const rootProps = { 'data-testid': 'root-with-props', className: 'custom-root-class' };
      const containerProps = { 'data-testid': 'container-with-props', className: 'custom-container-class' };

      render(
        <SideSheet
          open
          slotProps={{
            root: rootProps,
            container: containerProps,
          }}
        >
          <TestContent />
        </SideSheet>,
      );

      expect(screen.getByTestId('root-with-props')).toBeInTheDocument();
      expect(screen.getByTestId('container-with-props')).toBeInTheDocument();
      expect(screen.getByTestId('root-with-props')).toHaveClass('custom-root-class');
      expect(screen.getByTestId('container-with-props')).toHaveClass('custom-container-class');
    });

    it('merges className from slotProps with default classes', () => {
      render(
        <SideSheet
          open
          slotProps={{
            root: { className: 'custom-root-class' },
          }}
        >
          <TestContent />
        </SideSheet>,
      );

      const root = screen.getByRole('sideSheet');
      expect(root).toHaveClass('custom-root-class');
      expect(root).toHaveClass(classes.root);
    });
  });

  describe('SideSheet sub-components', () => {
    it('renders SideSheetHeader properly', () => {
      render(
        <SideSheet open>
          <SideSheetHeader data-testid="side-sheet-header">Header Content</SideSheetHeader>
          <TestContent />
        </SideSheet>,
      );

      expect(screen.getByTestId('side-sheet-header')).toBeInTheDocument();
      expect(screen.getByTestId('side-sheet-header')).toHaveTextContent('Header Content');
    });

    it('renders SideSheetContent properly', () => {
      render(
        <SideSheet open>
          <SideSheetContent data-testid="side-sheet-content-container">
            <div>Content</div>
          </SideSheetContent>
        </SideSheet>,
      );

      expect(screen.getByTestId('side-sheet-content-container')).toBeInTheDocument();
      expect(screen.getByTestId('side-sheet-content-container')).toHaveTextContent('Content');
    });

    it('renders SideSheetFooter properly', () => {
      render(
        <SideSheet open>
          <SideSheetFooter data-testid="side-sheet-footer">Footer Content</SideSheetFooter>
        </SideSheet>,
      );

      expect(screen.getByTestId('side-sheet-footer')).toBeInTheDocument();
      expect(screen.getByTestId('side-sheet-footer')).toHaveTextContent('Footer Content');
    });

    it('checks styling of sub-components', () => {
      render(
        <SideSheet open>
          <SideSheetHeader data-testid="side-sheet-header">Header</SideSheetHeader>
          <SideSheetContent data-testid="side-sheet-content-container">Content</SideSheetContent>
          <SideSheetFooter data-testid="side-sheet-footer">Footer</SideSheetFooter>
        </SideSheet>,
      );

      // Check that the components have appropriate styling
      const header = screen.getByTestId('side-sheet-header');
      const content = screen.getByTestId('side-sheet-content-container');
      const footer = screen.getByTestId('side-sheet-footer');

      // Verify the components are in the correct order (DOM hierarchy)
      const container = screen.getByRole('sideSheet').querySelector(`.${classes.container}`);
      expect(container?.firstChild).toBe(header);
      expect(container?.lastChild).toBe(footer);
      expect(header.nextSibling).toBe(content);
      expect(content.nextSibling).toBe(footer);
    });

    it('passes props to sub-components', () => {
      const headerProps = { 'data-custom': 'header-prop', className: 'custom-header' };
      const contentProps = { 'data-custom': 'content-prop', className: 'custom-content' };
      const footerProps = { 'data-custom': 'footer-prop', className: 'custom-footer' };

      render(
        <SideSheet open>
          <SideSheetHeader data-testid="header" {...headerProps}>
            Header
          </SideSheetHeader>
          <SideSheetContent data-testid="content" {...contentProps}>
            Content
          </SideSheetContent>
          <SideSheetFooter data-testid="footer" {...footerProps}>
            Footer
          </SideSheetFooter>
        </SideSheet>,
      );

      expect(screen.getByTestId('header')).toHaveAttribute('data-custom', 'header-prop');
      expect(screen.getByTestId('header')).toHaveClass('custom-header');

      expect(screen.getByTestId('content')).toHaveAttribute('data-custom', 'content-prop');
      expect(screen.getByTestId('content')).toHaveClass('custom-content');

      expect(screen.getByTestId('footer')).toHaveAttribute('data-custom', 'footer-prop');
      expect(screen.getByTestId('footer')).toHaveClass('custom-footer');
    });
  });
});
