'use client';
import generateUtilityClasses from '@mui/utils/generateUtilityClasses';
import generateUtilityClass from '@mui/utils/generateUtilityClass';

export interface SideSheetClasses {
  /** Styles applied to the root element. */
  root: string;
  /** Styles applied to the container element. */
  container: string;
  /** Styles applied to the root element when anchored to the left. */
  anchorLeft: string;
  /** Styles applied to the root element when anchored to the right. */
  anchorRight: string;
  /** Styles applied to the root element when open. */
  open: string;
}

export type SideSheetClassKey = keyof SideSheetClasses;

export function getSideSheetUtilityClass(slot: string): string {
  return generateUtilityClass('NovaSideSheet', slot);
}

const sideSheetClasses: SideSheetClasses = generateUtilityClasses('NovaSideSheet', [
  'root',
  'container',
  'anchorLeft',
  'anchorRight',
  'open',
]);

export default sideSheetClasses;
