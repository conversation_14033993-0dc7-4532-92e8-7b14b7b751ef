import {
  unstable_generateUtilityClasses as generateUtilityClasses,
  unstable_generateUtilityClass as generateUtilityClass,
} from '@mui/utils';

export interface AlertClasses {
  /** Styles applied to the root element. */
  root: string;

  /** Styles applied to the root element if `color="primary"`. */
  colorPrimary: string;
  /** Styles applied to the root element if `color="error"`. */
  colorError: string;
  /** Styles applied to the root element if `color="info"`. */
  colorInfo: string;
  /** Styles applied to the root element if `color="warning"`. */
  colorWarning: string;
  /** Styles applied to the root element if `color="success"`. */
  colorSuccess: string;

  /** Styles applied to the root element if `intensity="bold"`. */
  intensityBold: string;
  /** Styles applied to the root element if `intensity="subtle"`. */
  intensitySubtle: string;

  /** Class name applied to the root element if `orientation="horizontal"`. */
  horizontal: string;
  /** Class name applied to the root element if `orientation="vertical"`. */
  vertical: string;

  /** Styles applied to the startDecorator element */
  startDecorator: string;
  /** Styles applied to the content element */
  content: string;
  /** Styles applied to the message element */
  message: string;
  /** Styles applied to the action element */
  action: string;
  /** Styles applied to the close icon element */
  closeIcon: string;
}

export type AlertClassKey = keyof AlertClasses;

export function getAlertUtilityClass(slot: string): string {
  return generateUtilityClass('NovaAlert', slot);
}

const alertClasses: AlertClasses = generateUtilityClasses('NovaAlert', [
  'root',
  'colorPrimary',
  'colorError',
  'colorInfo',
  'colorWarning',
  'colorSuccess',
  'intensityBold',
  'intensitySubtle',
  'horizontal',
  'vertical',
  'startDecorator',
  'content',
  'message',
  'action',
  'closeIcon',
]);

export default alertClasses;
