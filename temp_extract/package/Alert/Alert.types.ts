import { OverrideProps } from '@mui/types';
import { CreateSlotsAndSlotProps, SlotProps } from '../types/slot';
import { SxProps } from '../types/theme';
import { IconButtonProps } from '../IconButton';

export type SystemColor = 'primary' | 'error' | 'info' | 'warning' | 'success';

export type AlertSlot = 'slot' | 'startDecorator' | 'action' | 'closeButton';

export interface AlertSlots {
  /**
   * The component that renders the root.
   * @default 'div'
   */
  root?: React.ElementType;
  /**
   * The component that renders the start decorator.
   * @default 'span'
   */
  startDecorator?: React.ElementType;
  /**
   * The component that renders the action.
   * @default 'span'
   */
  action?: React.ElementType;
  /**
   * The component that renders the close button.
   * @default 'button'
   */
  closeButton?: React.ElementType;
}

export type AlertSlotsAndSlotProps = CreateSlotsAndSlotProps<
  AlertSlots,
  {
    root: SlotProps<'div', object, AlertOwnerState>;
    startDecorator: SlotProps<'span', object, AlertOwnerState>;
    action: SlotProps<'span', object, AlertOwnerState>;
    closeButton: SlotProps<'button', IconButtonProps, AlertOwnerState>;
  }
>;

export interface AlertTypeMap<P = object, D extends React.ElementType = 'div'> {
  props: P &
    AlertSlotsAndSlotProps & {
      /**
       * The system color of Alert
       * @default 'primary'
       */
      color?: SystemColor;
      /**
       * The intensity of Alert
       * @default 'bold'
       */
      intensity?: 'bold' | 'subtle';
      /**
       * The content direction flow, if `vertical`, the endDecorator will be placed below the text.
       * @default 'horizontal'
       */
      orientation?: 'vertical' | 'horizontal';
      /**
       * Element placed before the children.
       */
      startDecorator?: React.ReactNode;
      /**
       * The action of alert. It will be presented as a button.
       */
      action?: {
        onClick: () => void;
        label: string;
      };
      /**
       * Callback fired when the component requests to be closed.
       * When provided, a close icon button is displayed that triggers the callback when clicked.
       * @param {React.SyntheticEvent} event The event source of the callback.
       */
      onClose?: (event: React.SyntheticEvent) => void;
      /**
       * The system prop that allows defining system overrides as well as additional CSS styles.
       */
      sx?: SxProps;
    };
  defaultComponent: D;
}

export type AlertProps<
  D extends React.ElementType = AlertTypeMap['defaultComponent'],
  P = { component?: React.ElementType },
> = OverrideProps<AlertTypeMap<P, D>, D>;

export interface AlertOwnerState extends AlertProps {}
