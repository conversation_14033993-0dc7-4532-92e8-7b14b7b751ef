import '@testing-library/jest-dom/vitest';
import { render, screen, cleanup } from '@testing-library/react';
import React from 'react';
import { describe, expect, it, afterEach } from 'vitest';
import { FormLabel } from './FormLabel';

afterEach(() => {
  cleanup();
});

describe('FormLabel', () => {
  it('should render normal', () => {
    render(<FormLabel data-testid="NovaFormLabel-root" />);
    expect(screen.getByTestId('NovaFormLabel-root')).toHaveClass('NovaFormLabel-root');
  });

  it('should render error state', () => {
    render(<FormLabel data-testid="NovaFormLabel-root" error />);
    expect(screen.getByTestId('NovaFormLabel-root')).toHaveClass('Mui-error');
  });

  it('should render disabled state', () => {
    render(<FormLabel data-testid="NovaFormLabel-root" disabled />);
    expect(screen.getByTestId('NovaFormLabel-root')).toHaveClass('Mui-disabled');
  });

  it('should render medium size', () => {
    render(<FormLabel data-testid="NovaFormLabel-root" size="medium" />);
    expect(screen.getByTestId('NovaFormLabel-root')).toHaveClass('NovaFormLabel-sizeMedium');
  });

  it('should render small size', () => {
    render(<FormLabel data-testid="NovaFormLabel-root" size="small" />);
    expect(screen.getByTestId('NovaFormLabel-root')).toHaveClass('NovaFormLabel-sizeSmall');
  });

  it('should render large size', () => {
    render(<FormLabel data-testid="NovaFormLabel-root" size="large" />);
    expect(screen.getByTestId('NovaFormLabel-root')).toHaveClass('NovaFormLabel-sizeLarge');
  });
});
