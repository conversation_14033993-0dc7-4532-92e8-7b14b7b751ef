import * as React from 'react';
import { OverrideProps } from '@mui/types';
import { CreateSlotsAndSlotProps, SlotProps } from '../types/slot';

export type FormLabelSlot = 'root' | 'asterisk';

export interface FormLabelSlots {
  /**
   * The component that renders the root.
   * @default 'label'
   */
  root?: React.ElementType;
  /**
   * The component that renders the asterisk.
   * @default 'span'
   */
  asterisk?: React.ElementType;
}

export type FormLabelSlotsAndSlotProps = CreateSlotsAndSlotProps<
  FormLabelSlots,
  {
    root: SlotProps<'label', object, FormLabelOwnerState>;
    asterisk: SlotProps<'span', object, FormLabelOwnerState>;
  }
>;

export interface FormLabelTypeMap<P = object, D extends React.ElementType = 'label'> {
  props: P &
    FormLabelSlotsAndSlotProps & {
      /**
       * The content of the component.
       */
      children?: React.ReactNode;
      /**
       * The asterisk is added if required=`true`
       */
      required?: boolean;
      /**
       * If `true`, the label should be displayed in a disabled state.
       */
      disabled?: boolean;
      /**
       * If `true`, the label is displayed in an error state.
       */
      error?: boolean;
      /**
       * The size of the component.
       * @default 'medium'
       */
      size?: 'small' | 'medium' | 'large';
    };
  defaultComponent: D;
}

export type FormLabelProps<
  D extends React.ElementType = FormLabelTypeMap['defaultComponent'],
  P = {
    component?: React.ElementType;
  },
> = OverrideProps<FormLabelTypeMap<P, D>, D>;

export interface FormLabelOwnerState extends FormLabelProps {}
