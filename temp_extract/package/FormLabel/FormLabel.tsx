'use client';
import * as React from 'react';
import { OverridableComponent } from '@mui/types';
import { unstable_composeClasses as composeClasses, unstable_capitalize as capitalize } from '@mui/utils';
import useSlotProps from '@mui/utils/useSlotProps';
import { styled } from '@pigment-css/react';
import { FormLabelOwnerState, FormLabelProps, FormLabelTypeMap } from './FormLabel.types';
import { getFormLabelUtilityClass } from './FormLabel.classes';
import FormControlContext from '../FormControl/FormControlContext';
import { Typography } from '../Typography';

const useUtilityClasses = (ownerState: FormLabelOwnerState) => {
  const { disabled, size, error } = ownerState;

  const slots = {
    root: ['root', disabled && 'disabled', error && 'error', size && `size${capitalize(size)}`],
    asterisk: ['asterisk'],
  };

  return composeClasses(slots, getFormLabelUtilityClass, {});
};

export const FormLabelRoot = styled(Typography)<FormLabelProps>(({ theme }) => ({
  display: 'flex',
  gap: '4px',
  alignItems: 'center',
  flexWrap: 'wrap',
  userSelect: 'none',
  color: theme.vars.palette.onSurface,
  variants: [
    {
      props: { disabled: true },
      style: {
        color: theme.vars.palette.onBackgroundDisabled,
      },
    },
    {
      props: { size: 'small' },
      style: {
        fontSize: '14px',
        lineHeight: '18px',
      },
    },
    {
      props: { size: 'medium' },
      style: {
        fontSize: '16px',
        lineHeight: '20px',
      },
    },
    {
      props: { size: 'large' },
      style: {
        fontSize: '18px',
        lineHeight: '24px',
      },
    },
  ],
}));

export const AsteriskComponent = styled('span')(({ theme }) => ({
  fontSize: 'inherit',
  lineHeight: 'inherit',
}));

// eslint-disable-next-line react/display-name
export const FormLabel = React.forwardRef((props: FormLabelProps, ref: React.ForwardedRef<Element>) => {
  const {
    disabled: disabledProp = false,
    error: errorProp = false,
    required: requiredProp = false,
    size: sizeProp = 'medium',
    children,
    component = 'label',
    htmlFor,
    id,
    slots = {},
    slotProps = {},
    ...rest
  } = props;

  const formControl = React.useContext(FormControlContext);
  const required = props.required ?? formControl?.required ?? requiredProp;
  const disabled = props.disabled ?? formControl?.disabled ?? disabledProp;
  const size = props.size ?? formControl?.size ?? sizeProp;
  const error = props.error ?? formControl?.error ?? errorProp;

  const ownerState = {
    ...props,
    required,
    disabled,
    size,
    error,
  };

  const classes = useUtilityClasses(ownerState);

  const SlotRoot = slots.root ?? FormLabelRoot;
  const SlotAsterisk = slots.asterisk ?? AsteriskComponent;

  const slotRootProps = useSlotProps({
    elementType: SlotRoot,
    externalSlotProps: slotProps.root,
    externalForwardedProps: rest,
    additionalProps: {
      htmlFor: htmlFor ?? formControl?.htmlFor,
      id: id ?? formControl?.labelId,
      ref,
      as: component,
    },
    ownerState,
    className: classes.root,
  });

  const slotAsteriskProps = useSlotProps({
    elementType: SlotAsterisk,
    externalSlotProps: slotProps.asterisk,
    additionalProps: { 'aria-hidden': true },
    ownerState,
    className: classes.asterisk,
  });

  return (
    <SlotRoot {...slotRootProps}>
      {children}
      {required && <SlotAsterisk {...slotAsteriskProps}>&thinsp;{'*'}</SlotAsterisk>}
    </SlotRoot>
  );
}) as OverridableComponent<FormLabelTypeMap>;
