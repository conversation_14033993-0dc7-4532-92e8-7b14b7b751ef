import generateUtilityClasses from '@mui/utils/generateUtilityClasses';
import generateUtilityClass from '@mui/utils/generateUtilityClass';

export interface FormLabelClasses {
  /** Class name applied to the root element. */
  root: string;
  /** Class name applied to the asterisk element. */
  asterisk: string;

  /** Class name applied to the root element if `disabled={true}`. */
  disabled: string;
  /** State class applied to the root element if `error={true}`. */
  error: string;

  /** Styles applied to the root element if `size="small"`. */
  sizeSmall: string;
  /** Styles applied to the root element if `size="medium"`. */
  sizeMedium: string;
  /** Styles applied to the root element if `size="large"`. */
  sizeLarge: string;
}

export type FormLabelClassKey = keyof FormLabelClasses;

export function getFormLabelUtilityClass(slot: string): string {
  return generateUtilityClass('NovaFormLabel', slot);
}

const formLabelClasses: FormLabelClasses = generateUtilityClasses('NovaFormLabel', [
  'root',
  'asterisk',
  'disabled',
  'error',

  'sizeSmall',
  'sizeMedium',
  'sizeLarge',
]);

export default formLabelClasses;
