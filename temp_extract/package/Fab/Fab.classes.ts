import {
  unstable_generateUtilityClasses as generateUtilityClasses,
  unstable_generateUtilityClass as generateUtilityClass,
} from '@mui/utils';

export interface FabClasses {
  /** Styles applied to the root element. */
  root: string;
}

export type FabClassKey = keyof FabClasses;

export function getFabUtilityClass(slot: string): string {
  return generateUtilityClass('NovaFab', slot);
}

const fabClasses: FabClasses = generateUtilityClasses('NovaFab', ['root']);

export default fabClasses;
