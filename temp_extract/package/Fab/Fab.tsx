'use client';

import { styled } from '@pigment-css/react';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import { FabProps } from './Fab.types';
import { getFabUtilityClass } from './Fab.classes';
import { ButtonBase } from '../ButtonBase';
import clsx from 'clsx';

const useUtilityClasses = () => {
  const slots = {
    root: ['root'],
  };

  return composeClasses(slots, getFabUtilityClass, {});
};

const FabBase = styled(ButtonBase)<FabProps>(({ theme }) => ({
  backgroundColor: theme.vars.palette.primaryContainer,
  color: theme.vars.palette.onPrimaryContainer,
  boxShadow: theme.shadows[3],
  borderRadius: '999px',
  padding: '0 0.5rem',
  gap: '0.25rem',
  '&:hover': {
    backgroundColor: `color-mix(in srgb, ${theme.vars.palette.primaryContainer}, ${theme.vars.palette.onPrimary} ${theme.vars.palette.stateLayers.hoverOnPrimary})`,
    boxShadow: theme.shadows[4],
  },
  '&:focus-visible': {
    outline: `2px solid ${theme.vars.palette.primary}`,
    outlineOffset: 2,
    backgroundColor: `color-mix(in srgb, ${theme.vars.palette.primaryContainer}, ${theme.vars.palette.onPrimary} ${theme.vars.palette.stateLayers.focusOnPrimary})`,
  },
  '&:active': {
    backgroundColor: `color-mix(in srgb, ${theme.vars.palette.primaryContainer}, ${theme.vars.palette.onPrimary} ${theme.vars.palette.stateLayers.pressOnPrimary})`,
  },
  variants: [
    {
      props: { size: 'large' },
      style: {
        padding: '0 0.75rem',
        gap: '0.5rem',
      },
    },
  ],
}));

const FabLabel = styled('span')(() => ({
  display: 'inline-block',
  paddingInline: '0.25rem',
}));

export const Fab = (props: FabProps) => {
  const defaultProps: Partial<FabProps> = {
    size: 'medium',
  };
  const mergedProps = { ...defaultProps, ...props };
  const classes = useUtilityClasses();

  const { children, className, ...otherProps } = mergedProps;

  return (
    <FabBase className={clsx(classes.root, className)} {...otherProps}>
      {children && <FabLabel>{children}</FabLabel>}
    </FabBase>
  );
};
