import React from 'react';
import { render, screen, cleanup } from '@testing-library/react';
import { expect, test, afterEach } from 'vitest';
import { Fab } from './Fab';

afterEach(() => {
  cleanup();
});

test('by default, should render with the root and sizeMedium slot classes', async () => {
  const { getByRole } = render(<Fab>Icon</Fab>);
  const button = getByRole('button');

  expect(button).toHaveClass('NovaFab-root');
  expect(button).toHaveClass('NovaButtonBase-sizeMedium');
});

test('should render a small size slot class', () => {
  const { getByRole } = render(<Fab size="small">Icon</Fab>);
  const button = getByRole('button');

  expect(button).toHaveClass('NovaButtonBase-sizeSmall');
});

test('should render a medium size slot class', () => {
  const { getByRole } = render(<Fab size="medium">Icon</Fab>);
  const button = getByRole('button');

  expect(button).toHaveClass('NovaButtonBase-sizeMedium');
});

test('should render a large size slot class', () => {
  const { getByRole } = render(<Fab size="large">Label</Fab>);
  const button = getByRole('button');

  expect(button).toHaveClass('NovaButtonBase-sizeLarge');
});

test('should render with startIcon slot class', () => {
  render(<Fab startIcon={<span data-testid={'startIcon'}>Icon</span>}>Label</Fab>);
  const icon = screen.getByTestId('startIcon');
  expect(icon).toBeInTheDocument();
});

test('should render with endIcon slot class', () => {
  render(<Fab endIcon={<span data-testid={'endIcon'}>Icon</span>}>Label</Fab>);
  const icon = screen.getByTestId('endIcon');
  expect(icon).toBeInTheDocument();
});
