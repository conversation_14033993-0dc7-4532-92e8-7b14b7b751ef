'use client';
import * as React from 'react';
import {
  ActionWithContext,
  ControllableReducerAction,
  ControllableReducerParameters,
  StateChangeCallback,
  StateComparers,
} from './useControllableReducer.types';

function areEqual(a: any, b: any): boolean {
  return a === b;
}

const EMPTY_OBJECT = {};
const NOOP = () => {};

/**
 * Gets the current state augmented with controlled values from the outside.
 * If a state item has a corresponding controlled value, it will be used instead of the internal state.
 */
function getControlledState<State>(internalState: State, controlledProps: Partial<State>) {
  const augmentedState = { ...internalState };
  (Object.keys(controlledProps) as (keyof State)[]).forEach((key) => {
    if (controlledProps[key] !== undefined) {
      (augmentedState as Record<keyof State, any>)[key] = controlledProps[key];
    }
  });

  return augmentedState;
}

interface UseStateChangeDetectionParameters<State extends object> {
  /**
   * The next state returned by the reducer.
   */
  nextState: State;
  /**
   * The initial state.
   */
  initialState: State;
  /**
   * Comparers for each state item. If a state item has a corresponding comparer, it will be used to determine if the state has changed.
   */
  stateComparers: StateComparers<State>;
  /**
   * The function called when the state has changed.
   */
  onStateChange: StateChangeCallback<State>;
  /**
   * The external props used to override the state items.
   */
  controlledProps: Partial<State>;
  /**
   * The last action that was dispatched.
   */
  lastActionRef: React.MutableRefObject<ControllableReducerAction | null>;
}

/**
 * Defines an effect that compares the next state with the previous state and calls
 * the `onStateChange` callback if the state has changed.
 * The comparison is done based on the `stateComparers` parameter.
 */
function useStateChangeDetection<State extends object>(parameters: UseStateChangeDetectionParameters<State>) {
  const { nextState, initialState, stateComparers, onStateChange, controlledProps, lastActionRef } = parameters;

  const internalPreviousStateRef = React.useRef<State>(initialState);

  React.useEffect(() => {
    if (lastActionRef.current === null) {
      // Detect changes only if an action has been dispatched.
      return;
    }

    const previousState = getControlledState(internalPreviousStateRef.current, controlledProps);

    (Object.keys(nextState) as (keyof State)[]).forEach((key) => {
      // go through all state keys and compare them with the previous state
      const stateComparer = stateComparers[key] ?? areEqual;

      const nextStateItem = nextState[key];
      const previousStateItem = previousState[key];

      if (
        (previousStateItem == null && nextStateItem != null) ||
        (previousStateItem != null && nextStateItem == null) ||
        (previousStateItem != null && nextStateItem != null && !stateComparer(nextStateItem, previousStateItem))
      ) {
        onStateChange?.(
          lastActionRef.current!.event ?? null,
          key,
          nextStateItem,
          lastActionRef.current!.type ?? '',
          nextState,
        );
      }
    });

    internalPreviousStateRef.current = nextState;
    lastActionRef.current = null;
  }, [internalPreviousStateRef, nextState, lastActionRef, onStateChange, stateComparers, controlledProps]);
}

/**
 * The alternative to `React.useReducer` that lets you control the state from the outside.
 *
 * It can be used in an uncontrolled mode, similar to `React.useReducer`, or in a controlled mode, when the state is controlled by the props.
 * It also supports partially controlled state, when some state items are controlled and some are not.
 *
 * The controlled state items are provided via the `controlledProps` parameter.
 * When a reducer action is dispatched, the internal state is updated with the new values.
 * A change event (`onStateChange`) is then triggered (for each changed state item) if the new state is different from the previous state.
 * This event can be used to update the controlled values.
 *
 * The comparison of the previous and next states is done using the `stateComparers` parameter.
 * If a state item has a corresponding comparer, it will be used to determine if the state has changed.
 * This is useful when the state item is an object and you want to compare only a subset of its properties or if it's an array and you want to compare its contents.
 *
 * An additional feature is the `actionContext` parameter. It allows you to add additional properties to every action object,
 * similarly to how React context is implicitly available to every component.
 *
 * @template State - The type of the state calculated by the reducer.
 * @template Action - The type of the actions that can be dispatched.
 * @template ActionContext - The type of the additional properties that will be added to every action object.
 *
 * @ignore - internal hook.
 */
export function useControllableReducer<
  State extends object,
  Action extends ControllableReducerAction,
  ActionContext = undefined,
>(parameters: ControllableReducerParameters<State, Action, ActionContext>): [State, (action: Action) => void] {
  const lastActionRef: React.MutableRefObject<Action | null> = React.useRef<Action>(null);

  const {
    reducer,
    initialState,
    controlledProps = EMPTY_OBJECT,
    stateComparers = EMPTY_OBJECT,
    onStateChange = NOOP,
    actionContext,
    componentName = '',
  } = parameters;

  const controlledPropsRef = React.useRef(controlledProps);

  if (process.env.NODE_ENV !== 'production') {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    React.useEffect(() => {
      Object.keys(controlledProps).forEach((key) => {
        if (
          (controlledPropsRef.current as Record<string, unknown>)[key] !== undefined &&
          (controlledProps as Record<string, unknown>)[key] === undefined
        ) {
          console.error(
            `useControllableReducer: ${
              componentName ? `The ${componentName} component` : 'A component'
            } is changing a controlled prop to be uncontrolled: ${key}`,
          );
        }

        if (
          (controlledPropsRef.current as Record<string, unknown>)[key] === undefined &&
          (controlledProps as Record<string, unknown>)[key] !== undefined
        ) {
          console.error(
            `useControllableReducer: ${
              componentName ? `The ${componentName} component` : 'A component'
            } is changing an uncontrolled prop to be controlled: ${key}`,
          );
        }
      });
    }, [controlledProps, componentName]);
  }

  // The reducer that is passed to React.useReducer is wrapped with a function that augments the state with controlled values.
  const reducerWithControlledState = React.useCallback(
    (state: State, action: ActionWithContext<Action, ActionContext>) => {
      lastActionRef.current = action;
      const controlledState = getControlledState(state, controlledProps);
      const newState = reducer(controlledState, action);
      return newState;
    },
    [controlledProps, reducer],
  );

  const [nextState, dispatch] = React.useReducer(reducerWithControlledState, initialState);

  // The action that is passed to dispatch is augmented with the actionContext.
  const dispatchWithContext = React.useCallback(
    (action: Action) => {
      dispatch({
        ...action,
        context: actionContext,
      } as ActionWithContext<Action, ActionContext>);
    },
    [actionContext],
  );

  useStateChangeDetection<State>({
    nextState,
    initialState,
    stateComparers: stateComparers ?? EMPTY_OBJECT,
    onStateChange: onStateChange ?? NOOP,
    controlledProps,
    lastActionRef,
  });

  return [getControlledState(nextState, controlledProps), dispatchWithContext];
}
