import generateUtilityClasses from '@mui/utils/generateUtilityClasses';
import generateUtilityClass from '@mui/utils/generateUtilityClass';

export interface ListItemButtonClasses {
  /** Class name applied to the root element. */
  root: string;
  /** Class name applied to the root element, if `orientation="vertical"`. */
  vertical: string;
  /** Class name applied to the root element, if `orientation="horizontal"`. */
  horizontal: string;
  /** State class applied to the `component`'s `focusVisibleClassName` prop. */
  focusVisible: string;
  /** State class applied to the inner `component` element if `disabled={true}`. */
  disabled: string;
  /** State class applied to the root element if `selected={true}`. */
  selected: string;
}

export type ListItemButtonClassKey = keyof ListItemButtonClasses;

export function getListItemButtonUtilityClass(slot: string): string {
  return generateUtilityClass('NovaListItemButton', slot);
}

const listItemButtonClasses: ListItemButtonClasses = generateUtilityClasses('NovaListItemButton', [
  'root',
  'horizontal',
  'vertical',
  'focusVisible',
  'disabled',
  'selected',
]);

export default listItemButtonClasses;
