'use client';
import * as React from 'react';
import { unstable_composeClasses as composeClasses, unstable_useForkRef as useForkRef } from '@mui/utils';
import clsx from 'clsx';
import { styled } from '@pigment-css/react';
import listItemButtonClasses, { getListItemButtonUtilityClass } from './ListItemButton.classes';
import { ListItemButtonOwnerState, ExtendListItemButton, ListItemButtonTypeMap } from './ListItemButton.types';
import ListItemButtonOrientationContext from './ListItemButtonOrientationContext';
import ListItemStatusContext from '../ListItem/ListItemStatusContext';
import listItemClasses from '../ListItem/ListItem.classes';
import { useButton } from '../internal/hooks/useButton';
import useSlotProps from '@mui/utils/useSlotProps';

const useUtilityClasses = (ownerState: ListItemButtonOwnerState) => {
  const { disabled, focusVisible, focusVisibleClassName, selected } = ownerState;

  const slots = {
    root: ['root', disabled && 'disabled', focusVisible && 'focusVisible', selected && 'selected'],
  };

  const composedClasses = composeClasses(slots, getListItemButtonUtilityClass, {});

  if (focusVisible && focusVisibleClassName) {
    composedClasses.root += ` ${focusVisibleClassName}`;
  }

  return composedClasses;
};

export const StyledListItemButton = styled('div')<ListItemButtonOwnerState>(({ theme }) => ({
  '--nova-icon-margin': 'initial', // reset the icon's margin.
  '--nova-listItemButton-hover-background': `color-mix(in srgb, ${theme.vars.palette.backgroundStates}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.hoverOnSurface})`,
  '--nova-listItemButton-focus-background': `color-mix(in srgb, ${theme.vars.palette.backgroundStates}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.focusOnSurface})`,
  '--nova-listItemButton-active-background': `color-mix(in srgb, ${theme.vars.palette.backgroundStates}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.pressOnSurface})`,
  '--nova-listItemButton-disabled-background': `color-mix(in srgb, ${theme.vars.palette.backgroundDisabled}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.disabledOnSurface})`,
  '--nova-listItemButton-selected-background': theme.vars.palette.secondaryContainer,
  WebkitTapHighlightColor: 'transparent',
  boxSizing: 'border-box',
  position: 'relative',
  font: 'inherit',
  display: 'flex',
  flexDirection: 'row',
  alignItems: 'center',
  alignSelf: 'stretch', // always stretch itself to fill the parent (List|ListItem)
  gap: 'var(--nova-listItem-gap)',
  textAlign: 'initial',
  textDecoration: 'initial', // reset native anchor tag
  backgroundColor: 'initial', // reset button background
  cursor: 'pointer',
  // In some cases, ListItemButton is a child of ListItem so the margin needs to be controlled by the ListItem. The value is negative to account for the ListItem's padding
  marginInline: 'var(--nova-listItemButton-marginInline)',
  marginBlock: 'var(--nova-listItemButton-marginBlock)',
  // account for the border width, so that all of the ListItemButtons content aligned horizontally
  paddingBlock: 'calc(var(--nova-listItem-paddingY) - var(--nova-variant-borderWidth, 0px))',
  // account for the border width, so that all of the ListItemButtons content aligned vertically
  paddingInlineStart:
    'calc(var(--nova-listItem-paddingLeft) + var(--nova-listItem-startActionWidth, var(--nova-unstable_startActionWidth, 0px)))', // --internal variable makes it possible to customize the actionWidth from the top List
  paddingInlineEnd:
    'calc(var(--nova-listItem-paddingRight) + var(--nova-listItem-endActionWidth, var(--nova-unstable_endActionWidth, 0px)))', // --internal variable makes it possible to customize the actionWidth from the top List
  minBlockSize: 'var(--nova-listItem-minHeight)',
  border: '1px solid transparent', // use `transparent` as a placeholder to prevent the button from jumping when switching to `outlined` variant
  borderRadius: 'var(--nova-listItem-radius)',
  flex: 'var(--nova-unstable_ListItem-flex, none)', // prevent children from shrinking when the List's height is limited.
  fontSize: 'inherit', // prevent user agent style when component="button"
  lineHeight: 'inherit', // prevent user agent style when component="button"
  minInlineSize: 0,
  '&:focus-visible': {
    zIndex: 1,
    outlineOffset: '-2px',
    outline: `2px solid ${theme.vars.palette.secondary}`,
    backgroundColor: `color-mix(in srgb, ${theme.vars.palette.surfaceContainer}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.pressOnSurface})`,
  },
  [`.${listItemClasses.root} > &`]: {
    '--nova-unstable_ListItem-flex': '1 0 0%', // grow to fill the available space of ListItem
  },
  [`&.${listItemButtonClasses.selected}`]: {
    backgroundColor: 'var(--nova-listItemButton-selected-background)',
    '--nova-icon-color': 'currentColor',
    '&:hover': {
      backgroundColor: `color-mix(in srgb, ${theme.vars.palette.surfaceContainerHighest}, ${theme.vars.palette.onSecondaryContainer} ${theme.vars.palette.stateLayers.hoverOnSurface})`,
    },
    '&:active': {
      backgroundColor: `color-mix(in srgb, ${theme.vars.palette.surfaceContainerHighest}, ${theme.vars.palette.onSecondaryContainer} ${theme.vars.palette.stateLayers.pressOnSurface})`,
    },
  },
  [`&:not(.${listItemButtonClasses.selected}, [aria-selected="true"])`]: {
    '&:hover': {
      backgroundColor: 'var(--nova-listItemButton-hover-background)',
    },
    '&:active': {
      backgroundColor: 'var(--nova-listItemButton-active-background)',
    },
  },
  [`&.${listItemButtonClasses.disabled}`]: {
    color: theme.vars.palette.onBackgroundDisabled,
    backgroundColor: `var(--nova-listItemButton-disabled-background)`,
    '&:hover, &:active': {
      backgroundColor: `var(--nova-listItemButton-disabled-background)`,
    },
    outline: 0,
    cursor: 'default',
  },
  variants: [
    {
      props: { orientation: 'vertical' },
      style: {
        flexDirection: 'column',
        justifyContent: 'center',
      },
    },
    {
      props: (props) => props['data-first-child'] === undefined && props.row === true,
      style: {
        marginInlineStart: 'var(--nova-list-gap)',
        marginBlockStart: undefined,
      },
    },
    {
      props: (props) => props['data-first-child'] === undefined && props.row !== true,
      style: {
        marginInlineStart: undefined,
        marginBlockStart: 'var(--nova-list-gap)',
      },
    },
  ],
}));

const ListItemButtonRoot = styled(StyledListItemButton, {
  name: 'NovaListItemButton',
  slot: 'Root',
  overridesResolver: (props, styles) => styles.root,
})(({ theme }) => ({
  variants: [
    {
      props: (props) => props.row !== true,
      style: {
        [`&.${listItemButtonClasses.selected}`]: {
          fontWeight: theme.typography.bodyMedium,
        },
      },
    },
  ],
}));

export const ListItemButton = React.forwardRef(function ListItemButton(props: any, ref: any) {
  const {
    children,
    className,
    action,
    component = 'div',
    orientation = 'horizontal',
    role,
    selected = false,
    disabled: disabledProp,
    slots = {},
    slotProps = {},
    ...other
  } = props;

  const statusContext = React.useContext(ListItemStatusContext);

  const disabled = props.disabled || statusContext?.disabled || disabledProp;
  const buttonRef = React.useRef<HTMLElement>(null);
  const handleRef = useForkRef(buttonRef, ref);

  const { focusVisible, setFocusVisible, getRootProps } = useButton({
    ...props,
    rootRef: handleRef,
  });

  React.useImperativeHandle(
    action,
    () => ({
      focusVisible: () => {
        setFocusVisible(true);
        buttonRef.current?.focus();
      },
    }),
    [setFocusVisible],
  );

  const ownerState = {
    ...props,
    component,
    focusVisible,
    orientation,
    selected,
    disabled,
  };

  const classes = useUtilityClasses(ownerState);
  const externalForwardedProps = { ...other, component, slots, slotProps };

  const SlotRoot = slots.root ?? ListItemButtonRoot;
  const rootProps = useSlotProps({
    className: clsx(classes.root, className),
    elementType: ListItemButtonRoot,
    externalSlotProps: slotProps.root,
    externalForwardedProps,
    additionalProps: {
      ref,
      as: component,
    },
    ownerState,
    getSlotProps: getRootProps,
  });

  return (
    <ListItemButtonOrientationContext.Provider value={orientation}>
      <ListItemStatusContext.Provider value={{ checked: selected, disabled }}>
        <SlotRoot {...rootProps} role={role ?? rootProps.role}>
          {children}
        </SlotRoot>
      </ListItemStatusContext.Provider>
    </ListItemButtonOrientationContext.Provider>
  );
}) as unknown as ExtendListItemButton<ListItemButtonTypeMap>;
