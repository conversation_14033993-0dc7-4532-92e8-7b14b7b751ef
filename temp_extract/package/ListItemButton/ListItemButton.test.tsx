/// <reference types="@testing-library/jest-dom" />
import React from 'react';
import { expect, test, describe, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { ListItemButton } from './ListItemButton';
import listItemButtonClasses from './ListItemButton.classes';

describe('ListItemButton', () => {
  test('renders basic listItemButton component correctly', () => {
    const view = render(<ListItemButton />);
    expect(view).toBeTruthy();
  });

  test('renders a listItemButton with the data-testid property', () => {
    render(<ListItemButton data-testid="NovaListItemButton-root" />);
    expect(screen.getByTestId('NovaListItemButton-root')).toBeDefined();
  });

  test('renders children correctly', () => {
    render(<ListItemButton>Test Content</ListItemButton>);
    expect(screen.getByText('Test Content')).toBeInTheDocument();
  });

  test('applies selected class when selected prop is true', () => {
    render(<ListItemButton selected data-testid="button" />);
    expect(screen.getByTestId('button')).toHaveClass(listItemButtonClasses.selected);
  });

  test('applies disabled class and attributes when disabled prop is true', () => {
    render(<ListItemButton disabled data-testid="button" />);
    const button = screen.getByTestId('button');
    expect(button).toHaveClass(listItemButtonClasses.disabled);
    expect(button).toHaveAttribute('aria-disabled', 'true');
  });

  test('handles click events when not disabled', () => {
    const handleClick = vi.fn();
    render(<ListItemButton onClick={handleClick} data-testid="button" />);
    fireEvent.click(screen.getByTestId('button'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  test('does not handle click events when disabled', () => {
    const handleClick = vi.fn();
    render(<ListItemButton disabled onClick={handleClick} data-testid="button" />);
    fireEvent.click(screen.getByTestId('button'));
    expect(handleClick).not.toHaveBeenCalled();
  });

  test('renders with custom component', () => {
    render(<ListItemButton component="span" data-testid="button" />);
    expect(screen.getByTestId('button').tagName).toBe('SPAN');
  });

  test('should render with the selected class', () => {
    const { getByRole } = render(<ListItemButton selected />);
    expect(getByRole('button')).toHaveClass(listItemButtonClasses.selected);
  });

  test('applies custom className along with default classes', () => {
    render(<ListItemButton className="custom-class" data-testid="button" />);
    const button = screen.getByTestId('button');
    expect(button).toHaveClass('custom-class');
    expect(button).toHaveClass(listItemButtonClasses.root);
  });

  test('forwards ref correctly', () => {
    const ref = React.createRef<HTMLDivElement>();
    render(<ListItemButton ref={ref} />);
    expect(ref.current).toBeInstanceOf(HTMLDivElement);
  });
});
