import React from 'react';
import '@testing-library/jest-dom/vitest';
import { screen, render } from '@testing-library/react';
import { expect, describe, it, vi } from 'vitest';
import { ListSubheader } from './ListSubheader';
import classes from './ListSubheader.classes';
import ListSubheaderContext from './ListSubheaderContext';

describe('Nova <ListSubheader />', () => {
  it('should have root className', () => {
    const { container } = render(<ListSubheader />);
    expect(container.firstChild).toHaveClass(classes.root);
  });

  it('should accept className prop', () => {
    const { container } = render(<ListSubheader className="foo-bar" />);
    expect(container.firstChild).toHaveClass('foo-bar');
  });

  it('should call dispatch context with the generated id', () => {
    const dispatch = vi.fn();
    const { container } = render(
      <ListSubheaderContext.Provider value={dispatch}>
        <ListSubheader />
      </ListSubheaderContext.Provider>,
    );
    const firstChild: any = container.firstChild;
    expect(dispatch).toHaveBeenCalledWith(firstChild?.id);
  });
});
