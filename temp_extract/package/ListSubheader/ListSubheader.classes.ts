import {
  unstable_generateUtilityClasses as generateUtilityClasses,
  unstable_generateUtilityClass as generateUtilityClass,
} from '@mui/utils';

export interface ListSubheaderClasses {
  /** Styles applied to the root element. */
  root: string;
  /** Styles applied to the root element, if sticky={true}. */
  sticky: string;
}

export type ListSubheaderClassKey = keyof ListSubheaderClasses;

export function getListSubheaderUtilityClass(slot: string): string {
  return generateUtilityClass('NovaListSubheader', slot);
}

const listSubheaderClasses: ListSubheaderClasses = generateUtilityClasses('NovaListSubheader', ['root', 'sticky']);

export default listSubheaderClasses;
