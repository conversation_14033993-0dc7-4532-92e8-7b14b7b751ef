import React from 'react';
import { unstable_composeClasses as composeClasses, unstable_useId as useId } from '@mui/utils';
import useSlotProps from '@mui/utils/useSlotProps';
import { styled } from '@pigment-css/react';
import { ListSubheaderOwnerState, ListSubheaderProps } from './ListSubheader.types';
import { getListSubheaderUtilityClass } from './ListSubheader.classes';
import ListSubheaderContext from './ListSubheaderContext';

const useUtilityClasses = (ownerState: ListSubheaderOwnerState) => {
  const { sticky } = ownerState;
  const slots = {
    root: ['root', sticky && 'sticky'],
  };

  return composeClasses(slots, getListSubheaderUtilityClass, {});
};

const ListSubheaderRoot = styled('div')<ListSubheaderProps>(({ theme }) => ({
  boxSizing: 'border-box',
  display: 'flex',
  alignItems: 'center',
  marginInline: 'var(--nova-listItem-marginInline)',
  paddingBlock: 'var(--nova-listItem-paddingY)',
  paddingInlineStart: 'var(--nova-listItem-paddingLeft)',
  paddingInlineEnd: 'var(--nova-listItem-paddingRight)',
  minBlockSize: 'var(--nova-listItem-minHeight)',
  ...theme.typography.bodySmall,
  fontSize: 'max(0.75em, 0.625rem)',
  textTransform: 'uppercase',
  letterSpacing: '0.1em',
  color: theme.vars.palette.onSurfaceVariant,
  variants: [
    {
      props: { sticky: true },
      style: {
        position: 'sticky',
        top: 'var(--nova-listItem-stickyTop, 0px)', // integration with Menu and Select.
        zIndex: 1,
        background: `var(--nova-listItem-stickyBackground, ${theme.vars.palette.surfaceContainer})`,
      },
    },
  ],
}));

export const ListSubheader = React.forwardRef(function ListSubheader(
  props: ListSubheaderProps,
  ref: React.ForwardedRef<Element>,
) {
  const { component, className, children, id: idOverride, slots = {}, slotProps = {}, ...other } = props;
  const id = useId(idOverride);
  const setSubheaderId = React.useContext(ListSubheaderContext);

  React.useEffect(() => {
    if (setSubheaderId) {
      setSubheaderId(id || '');
    }
  }, [setSubheaderId, id]);

  const ownerState = {
    ...props,
    id,
  };

  const classes = useUtilityClasses(ownerState);
  const SlotRoot = slots.root ?? ListSubheaderRoot;

  const rootProps = useSlotProps({
    additionalProps: {
      ref,
      as: component,
      id,
    },
    className: [classes.root, className],
    elementType: ListSubheaderRoot,
    externalSlotProps: slotProps.root,
    externalForwardedProps: other,
    ownerState,
  });

  return <SlotRoot {...rootProps}>{children}</SlotRoot>;
});
