import * as React from 'react';
import { OverrideProps } from '@mui/types';
import { CreateSlotsAndSlotProps, SlotProps } from '../types/slot';
import { SxProps } from '../types/theme';

export type ListSubheaderSlot = 'root';

export interface ListSubheaderSlots {
  /**
   * The component that renders the root.
   * @default 'div'
   */
  root?: React.ElementType;
}

export type ListSubheaderSlotsAndSlotProps = CreateSlotsAndSlotProps<
  ListSubheaderSlots,
  {
    root: SlotProps<'div', object, ListSubheaderOwnerState>;
  }
>;

export interface ListSubheaderVariantOverrides {}
export interface ListSubheaderColorOverrides {}

export interface ListSubheaderTypeMap<P = object, D extends React.ElementType = 'div'> {
  props: P & {
    /**
     * The content of the component.
     */
    children?: React.ReactNode;

    /**
     * The system prop that allows defining system overrides as well as additional CSS styles.
     */
    sx?: SxProps;
    /**
     * If `true`, the component has sticky position (with top = 0).
     * @default false
     */
    sticky?: boolean;
  } & ListSubheaderSlotsAndSlotProps;
  defaultComponent: D;
}

export type ListSubheaderProps<
  D extends React.ElementType = ListSubheaderTypeMap['defaultComponent'],
  P = {
    component?: React.ElementType;
  },
> = OverrideProps<ListSubheaderTypeMap<P, D>, D>;

export interface ListSubheaderOwnerState extends ListSubheaderProps {}
