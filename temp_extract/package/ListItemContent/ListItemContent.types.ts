import { CreateSlotsAndSlotProps, SlotProps } from '../types/slot';
import { OverrideProps } from '@mui/types';
import { SxProps } from '../types/theme';
import { TypographyProps } from '../Typography';

export type ListItemContentSlot = 'root';

export interface ListItemContentSlots {
  /**
   * The component that renders the root.
   * @default 'div'
   */
  root?: React.ElementType;
  /**
   * The component that renders the primary slot.
   * @default Typography
   */
  primary?: React.ElementType;
  /**
   * The component that renders the secondary slot.
   * @default Typography
   */
  secondary?: React.ElementType;
}

export type ListItemContentSlotsAndSlotProps = CreateSlotsAndSlotProps<
  ListItemContentSlots,
  {
    root: SlotProps<'div', object, ListItemContentOwnerState>;
    primary: SlotProps<React.ElementType<TypographyProps>, object, ListItemContentOwnerState>;
    secondary: SlotProps<React.ElementType<TypographyProps>, object, ListItemContentOwnerState>;
  }
>;

export interface ListItemContentTypeMap<P = object, D extends React.ElementType = 'div'> {
  props: P &
    ListItemContentSlotsAndSlotProps & {
      /**
       * If `true`, the children are indented.
       * This should be used if there is no left avatar or left icon.
       * @default false
       */
      inset?: boolean;
      /**
       * The main content element.
       */
      primary?: React.ReactNode;
      /**
       * The secondary content element.
       */
      secondary?: React.ReactNode;
      /**
       * The content of the component.
       */
      children?: React.ReactNode;
      /**
       * The system prop that allows defining system overrides as well as additional CSS styles.
       */
      sx?: SxProps;
    };
  defaultComponent: D;
}

export type ListItemContentProps<
  D extends React.ElementType = ListItemContentTypeMap['defaultComponent'],
  P = {
    component?: React.ElementType;
  },
> = OverrideProps<ListItemContentTypeMap<P, D>, D>;

export interface ListItemContentOwnerState extends ListItemContentProps {}
