import React from 'react';
import '@testing-library/jest-dom/vitest';
import { render, screen } from '@testing-library/react';
import { expect, describe, it } from 'vitest';
import classes from './ListItemContent.classes';
import { ListItemContent } from './ListItemContent';
import { Typography, typographyClasses } from '../Typography';

describe('<ListItemItem />', () => {
  it('should have root className', () => {
    const { container } = render(<ListItemContent />);
    expect(container.firstChild).toHaveClass(classes.root);
  });

  it('should accept className prop', () => {
    const { container } = render(<ListItemContent className="foo-bar" />);
    expect(container.firstChild).toHaveClass('foo-bar');
  });

  describe('prop: primary', () => {
    it('should render primary text', () => {
      const { container } = render(<ListItemContent primary="This is the primary text" />);
      expect(container).toHaveTextContent('This is the primary text');
    });

    it('should use the primary node', () => {
      const primaryRef: any = React.createRef();
      const primary = <span ref={primaryRef} />;
      const { container } = render(<ListItemContent primary={primary} />);
      expect(container.querySelector('div')).toContain(primaryRef.current);
    });

    it('should use the children prop as primary node', () => {
      const primaryRef: any = React.createRef();
      const primary = <span ref={primaryRef} />;
      const { container } = render(<ListItemContent>{primary}</ListItemContent>);
      expect(container.querySelector('div')).toContain(primaryRef.current);
    });

    it('should read 0 as primary', () => {
      const { container } = render(<ListItemContent primary={0} />);
      expect(container).toHaveTextContent('0');
    });
  });

  describe('prop: secondary', () => {
    it('should render secondary text', () => {
      const ref: any = React.createRef();
      const { container } = render(<ListItemContent secondary="This is the secondary text" ref={ref} />);
      expect(container).toHaveTextContent('This is the secondary text');
    });

    it('should use the secondary node', () => {
      const secondaryRef: any = React.createRef();
      const secondary = <span ref={secondaryRef} />;
      const { container } = render(<ListItemContent secondary={secondary} />);
      expect(container.querySelector('div')).toContain(secondaryRef.current);
    });

    it('should read 0 as secondary', () => {
      const { container } = render(<ListItemContent secondary={0} />);
      expect(container).toHaveTextContent('0');
    });
  });

  it('should not re-wrap the <Typography> element', () => {
    const primary = <Typography>This is the primary text</Typography>;
    const secondary = <Typography>This is the secondary text</Typography>;
    const { container } = render(<ListItemContent primary={primary} secondary={secondary} />);
    expect(container).toHaveTextContent('This is the primary text');
    expect(container).toHaveTextContent('This is the secondary text');
  });
});
