'use client';
import * as React from 'react';
import { styled } from '@pigment-css/react';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import useSlotProps from '@mui/utils/useSlotProps';
import { ListItemContentOwnerState, ListItemContentProps } from './ListItemContent.types';
import { getListItemContentUtilityClass } from './ListItemContent.classes';
import { Typography } from '../Typography';

const useUtilityClasses = (ownerState: ListItemContentOwnerState) => {
  const { inset } = ownerState;

  const slots = {
    root: ['root', inset && 'inset'],
    primary: ['primary'],
    secondary: ['secondary'],
  };

  return composeClasses(slots, getListItemContentUtilityClass, {});
};

const ListItemContentRoot = styled('div')<ListItemContentProps>(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'center',
  flex: '1 1 auto',
  minWidth: 0,
  gap: 'var(--nova-listItemContent-gap)',
}));

const PrimaryRoot = styled(Typography)<ListItemContentProps>(({ theme }) => ({
  color: 'var(--nova-listItem-color)',
  overflow: 'hidden',
  textOverflow: 'ellipsis',
  whiteSpace: 'nowrap',
  lineHeight: '20px',
}));

const SecondaryRoot = styled(Typography)<ListItemContentProps>(({ theme }) => ({
  color: 'var(--nova-listItem-secondaryColor)',
  overflow: 'hidden',
  textOverflow: 'ellipsis',
  whiteSpace: 'nowrap',
}));

export const ListItemContent = React.forwardRef(function ListItemContent(
  props: ListItemContentProps,
  ref: React.ForwardedRef<Element>,
) {
  const {
    children,
    component,
    className,
    primary: primaryProp,
    secondary: secondaryProp,
    slots = {},
    slotProps = {},
    ...other
  } = props;

  let primary = primaryProp != null ? primaryProp : children;
  let secondary = secondaryProp;
  const ownerState = {
    ...props,
    primary: !!primary,
    secondary: !!secondary,
  };

  const classes = useUtilityClasses(ownerState);

  const PrimarySlot = slots.primary ?? PrimaryRoot;
  const SecondarySlot = slots.secondary ?? SecondaryRoot;

  const primaryProps = useSlotProps({
    className: classes.primary,
    elementType: PrimarySlot,
    externalSlotProps: slotProps.primary,
    ownerState,
  });

  const secondaryProps = useSlotProps({
    className: classes.secondary,
    externalSlotProps: slotProps.secondary,
    elementType: SecondarySlot,
    ownerState,
  });

  const SlotRoot = slots.root ?? ListItemContentRoot;
  const rootProps = useSlotProps({
    additionalProps: {
      ref,
      as: component,
    },
    className: [classes.root, className],
    elementType: ListItemContentRoot,
    externalSlotProps: slotProps.root,
    externalForwardedProps: other,
    ownerState,
  });

  if (primary != null) {
    primary = (
      <PrimarySlot variant={'labelMedium'} {...primaryProps}>
        {primary}
      </PrimarySlot>
    );
  }

  if (secondary != null) {
    secondary = (
      <SecondarySlot variant="bodySmall" {...secondaryProps}>
        {secondary}
      </SecondarySlot>
    );
  }

  return (
    <SlotRoot {...rootProps}>
      {primary}
      {secondary}
    </SlotRoot>
  );
});
