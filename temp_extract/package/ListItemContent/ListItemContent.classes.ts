import {
  unstable_generateUtilityClasses as generateUtilityClasses,
  unstable_generateUtilityClass as generateUtilityClass,
} from '@mui/utils';

export interface ListItemContentClasses {
  /** Styles applied to the root element. */
  root: string;
  /** Styles applied to the Typography component if dense. */
  dense: string;
  /** Styles applied to the primary `Typography` component. */
  primary: string;
  /** Styles applied to the secondary `Typography` component. */
  secondary: string;
}

export type ListItemContentClassKey = keyof ListItemContentClasses;

export function getListItemContentUtilityClass(slot: string): string {
  return generateUtilityClass('NovaListItemContent', slot);
}

const listItemContentClasses: ListItemContentClasses = generateUtilityClasses('NovaListItemContent', [
  'root',
  'dense',
  'primary',
  'secondary',
]);

export default listItemContentClasses;
