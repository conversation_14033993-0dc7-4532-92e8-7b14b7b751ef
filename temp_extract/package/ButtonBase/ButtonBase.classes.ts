import {
  unstable_generateUtilityClasses as generateUtilityClasses,
  unstable_generateUtilityClass as generateUtilityClass,
} from '@mui/utils';

export interface ButtonBaseClasses {
  /** Styles applied to the root element. */
  root: string;

  /** Styles applied to the Button if `size="small"`. */
  sizeSmall: string;
  /** Styles applied to the Button if `size="medium"`. */
  sizeMedium: string;
  /** Styles applied to the Button if `size="large"`. */
  sizeLarge: string;

  /** Styles applied to the Button startIcon element. */
  iconStart: string;
  /** Styles applied to the Button endIcon element. */
  iconEnd: string;
  /** Styles applied to the Button if `fullWidth={true}`. */
  fullWidth: string;

  /** Styles applied to the Button if `disabled={true}` */
  disabled: string;
}

export type ButtonBaseClassKey = keyof ButtonBaseClasses;

export function getButtonBaseUtilityClass(slot: string): string {
  return generateUtilityClass('NovaButtonBase', slot);
}

const buttonBaseClasses: ButtonBaseClasses = generateUtilityClasses('NovaButtonBase', [
  'root',
  'sizeSmall',
  'sizeMedium',
  'sizeLarge',
  'iconStart',
  'iconEnd',
  'fullWidth',
  'disabled',
]);

export default buttonBaseClasses;
