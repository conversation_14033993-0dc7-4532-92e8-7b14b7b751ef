import { SlotComponentProps } from '@mui/utils';
import { ReactNode } from 'react';
import { PolymorphicProps } from '../utils/PolymorphicComponent';
import { Simplify } from '@mui/types';
import { UseButtonParameters, UseButtonRootSlotProps } from '../internal/hooks/useButton';

export interface ButtonActions {
  focusVisible(): void;
}

export interface ButtonRootSlotPropsOverrides {}

export interface ButtonOwnProps extends Omit<UseButtonParameters, 'rootRef'> {
  /**
   * A ref for imperative actions. It currently only supports `focusVisible()` action.
   */
  action?: React.Ref<ButtonActions>;
  children?: React.ReactNode;
  className?: string;
  /**
   * The props used for each slot inside the Button.
   * @default {}
   */
  slotProps?: {
    root?: SlotComponentProps<'button', ButtonRootSlotPropsOverrides, ButtonOwnerState>;
  };
  /**
   * The components used for each slot inside the Button.
   * Either a string to use a HTML element or a component.
   * @default {}
   */
  slots?: ButtonSlots;
  /**
   * The HTML element that is ultimately rendered, for example 'button' or 'a'
   * @default 'button'
   */
  rootElementName?: keyof HTMLElementTagNameMap;
}

export interface ButtonSlots {
  /**
   * The component that renders the root.
   * @default props.href || props.to ? 'a' : 'button'
   */
  root?: React.ElementType;
}

export type ButtonBaseProps<RootComponentType extends React.ElementType = ButtonTypeMap['defaultComponent']> =
  PolymorphicProps<ButtonTypeMap<object, RootComponentType>, RootComponentType> & {
    /**
     * The overall size of the button.
     * @default 'medium'
     */
    size?: 'small' | 'medium' | 'large';
    /**
     * An optional icon to show at the start of the button
     */
    startIcon?: ReactNode;
    /**
     * An optional icon to show at the end of the button
     */
    endIcon?: ReactNode;
    /**
     * If `true`, the button will take up the full width of its container.
     * @default false
     */
    fullWidth?: boolean;
  };

export interface ButtonTypeMap<AdditionalProps = object, RootComponentType extends React.ElementType = 'button'> {
  props: ButtonOwnProps & AdditionalProps;
  defaultComponent: RootComponentType;
}

export type ButtonOwnerState = Simplify<
  ButtonOwnProps & {
    active: boolean;
    focusVisible: boolean;
  }
>;

export type ButtonRootSlotProps = Simplify<
  UseButtonRootSlotProps & {
    ownerState: ButtonOwnerState;
    className?: string;
    children?: React.ReactNode;
  }
>;
