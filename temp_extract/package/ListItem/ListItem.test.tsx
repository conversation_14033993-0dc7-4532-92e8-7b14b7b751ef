import React from 'react';
import '@testing-library/jest-dom/vitest';
import { screen, render } from '@testing-library/react';
import { expect, describe, it } from 'vitest';
import classes from './ListItem.classes';
import { List } from '../List';
import { ListItem } from './ListItem';
import { ListSubheader } from '../ListSubheader';

describe('<ListItemItem />', () => {
  it('should have root className', () => {
    const { container } = render(<ListItem />);
    expect(container.firstChild).toHaveClass(classes.root);
  });

  it('should accept className prop', () => {
    const { container } = render(<ListItem className="foo-bar" />);
    expect(container.firstChild).toHaveClass('foo-bar');
  });

  it('should have sticky classes', () => {
    const { container } = render(<ListItem sticky />);
    expect(container.firstChild).toHaveClass(classes.sticky);
  });

  it('overridable role', () => {
    render(
      <List role="menu">
        <ListItem role="menuitem">Foo</ListItem>
      </List>,
    );
    expect(screen.getByText('Foo')).toHaveAttribute('role', 'menuitem');
  });

  it('can be disabled', () => {
    render(<ListItem role="listItem" disabled />);
    const listItem = screen.getByRole('listItem');
    expect(listItem).toHaveClass(classes.disabled);
  });

  describe('NestedList', () => {
    it('the nested list should be labelledby the subheader', () => {
      const { getByRole, getByTestId } = render(
        <ListItem nested>
          <ListSubheader data-testid="subheader">Subheader</ListSubheader>
          <List />
        </ListItem>,
      );

      const subheader = getByTestId('subheader');

      expect(getByRole('list')).toHaveAttribute('aria-labelledby', subheader.id);
    });

    it('the aria-labelledby can be overridden', () => {
      const { getByRole } = render(
        <ListItem nested>
          <ListSubheader data-testid="subheader">Subheader</ListSubheader>
          <List aria-labelledby={undefined} />
        </ListItem>,
      );

      expect(getByRole('list')).not.toHaveAttribute('aria-labelledby');
    });

    it('the nested list should not be labelled without the subheader', () => {
      const { getByRole } = render(
        <ListItem nested>
          <List />
        </ListItem>,
      );

      expect(getByRole('list')).not.toHaveAttribute('aria-labelledby');
    });
  });
});
