import { CreateSlotsAndSlotProps, SlotProps } from '../types/slot';
import { OverrideProps } from '@mui/types';
import { SxProps } from '../types/theme';

export type ListItemSlot = 'root';

export interface ListItemSlots {
  /**
   * The component that renders the root.
   * @default 'li'
   */
  root?: React.ElementType;
}

export type ListItemSlotsAndSlotProps = CreateSlotsAndSlotProps<
  ListItemSlots,
  {
    root: SlotProps<'li', object, ListItemOwnerState>;
  }
>;

export interface ListItemTypeMap<P = object, D extends React.ElementType = 'div'> {
  props: P &
    ListItemSlotsAndSlotProps & {
      /**
       * The content of the component.
       */
      children?: React.ReactNode;
      /**
       * Whether the component should ignore user interaction.
       * @default false
       */
      disabled?: boolean;
      /**
       * If `true`, the component can contain NestedList.
       * @default false
       */
      nested?: boolean;
      /**
       * If `true`, the component has sticky position (with top = 0).
       * @default false
       */
      sticky?: boolean;
      /**
       * The system prop that allows defining system overrides as well as additional CSS styles.
       */
      sx?: SxProps;
    };
  defaultComponent: D;
}

export type ListItemProps<
  D extends React.ElementType = ListItemTypeMap['defaultComponent'],
  P = {
    component?: React.ElementType;
  },
> = OverrideProps<ListItemTypeMap<P, D>, D>;

export interface ListItemOwnerState extends ListItemProps {
  /**
   * If `true`, the element is rendered in a horizontal list.
   * @internal
   */
  row: boolean;
  /**
   * If `true`, the element is rendered in a wrapped list.
   * @internal
   */
  wrap: boolean;
  /**
   * If `true`, the element is rendered in a nested list item.
   */
  nesting: boolean | string;
  /**
   * @internal
   * The internal prop for controlling CSS margin of the element.
   */
  'data-first-child'?: boolean;
}
