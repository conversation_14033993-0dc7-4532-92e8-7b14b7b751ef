'use client';
import React from 'react';
import { styled } from '@pigment-css/react';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import useSlotProps from '@mui/utils/useSlotProps';
import { ListItemOwnerState, ListItemProps } from './ListItem.types';
import { getListItemUtilityClass } from './ListItem.classes';
import NestedListContext from '../List/NestedListContext';
import RowListContext from '../List/RowListContext';
import WrapListContext from '../List/WrapListContext';
import ListSubheaderContext from '../ListSubheader/ListSubheaderContext';
import GroupListContext from '../List/GroupListContext';
import isMuiElement from '@mui/utils/isMuiElement';
import ListItemStatusContext from './ListItemStatusContext';

const useUtilityClasses = (ownerState: ListItemOwnerState) => {
  const { disabled, nested, nesting, sticky } = ownerState;
  const slots = {
    root: ['root', nested && 'nested', nesting && 'nesting', sticky && 'sticky', disabled && 'disabled'],
  };

  const composedClasses = composeClasses(slots, getListItemUtilityClass, {});
  return composedClasses;
};

export const ListItemRoot = styled('li')<ListItemProps>(({ theme }) => ({
  display: 'flex',
  flex: 'none', // prevent children from shrinking when the List's height is limited.
  justifyContent: 'flex-start',
  position: 'relative',
  textDecoration: 'none',
  textAlign: 'left',
  width: '100%',
  boxSizing: 'border-box',
  color: 'var(--nova-listItem-color)',
  borderRadius: 'var(--nova-listItem-radius)',
  minHeight: 'var(--nova-listItem-minHeight)',
  '--nova-listItemButton-marginInline': `calc(-1 * var(--nova-listItem-paddingLeft)) calc(-1 * var(--nova-listItem-paddingRight))`,
  '--nova-listItemButton-marginBlock': 'calc(-1 * var(--nova-listItem-paddingY))',
  alignItems: 'center',
  gap: 'var(--nova-listItem-gap)',
  marginInline: 'var(--nova-listItem-marginInline)',
  paddingBlockStart: 'var(--nova-listItem-paddingY)',
  marginBlockStart: 'var(--nova-list-gap)',
  marginInlineStart: undefined,
  paddingBlockEnd: 'var(--nova-listItem-paddingY)',
  paddingInlineStart: 'var(--nova-listItem-paddingLeft)',
  paddingInlineEnd: 'var(--nova-listItem-paddingRight)',
  variants: [
    {
      props: { disabled: true },
      style: {
        backgroundColor: `color-mix(in srgb, ${theme.vars.palette.backgroundDisabled}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.disabled})`,
        color: theme.vars.palette.onBackgroundDisabled,
        '--nova-listItem-color': theme.vars.palette.onBackgroundDisabled,
        '--nova-listItem-secondaryColor': theme.vars.palette.onBackgroundDisabled,
      },
    },
    {
      props: { row: true, 'data-first-child': undefined },
      style: {
        marginInlineStart: 'var(--nova-list-gap)',
        marginBlockStart: undefined,
      },
    },
    {
      props: { row: true, wrap: true },
      style: {
        marginInlineStart: 'var(--nova-list-gap)',
        marginBlockStart: 'var(--nova-list-gap)',
      },
    },
    {
      props: { nested: true },
      style: {
        // add negative margin to NestedList equal to this ListItem padding
        '--nova-nestedListItem-marginRight': 'calc(-1 * var(--nova-listItem-paddingRight))',
        '--nova-nestedListItem-marginLeft': 'calc(-1 * var(--nova-listItem-paddingLeft))',
        '--nova-nestedListItem-paddingLeft': `calc(var(--nova-listItem-paddingLeft) + var(--nova-list-nestedInsetStart))`,
        '--nova-listItemButton-marginBlock': '0px',
        '--nova-listItemButton-marginInline':
          'calc(-1 * var(--nova-listItem-paddingLeft)) calc(-1 * var(--nova-listItem-paddingRight))',
        '--nova-listItem--marginInline': `calc(-1 * var(--nova-listItem-paddingLeft)) calc(-1 * var(--nova-listItem-paddingRight))`,
        flexDirection: 'column',
        paddingBlockStart: 0,
        paddingBlockEnd: 0,
        gap: 0,
      },
    },
    {
      props: { sticky: true },
      style: {
        position: 'sticky',
        top: 'var(--nova-listItem-stickyTop, 0px)', // integration with Menu and Select.
        zIndex: 1,
        background: `var(--nova-listItem-stickyBackground, ${theme.vars.palette.surfaceContainer})`,
      },
    },
  ],
}));

export const ListItem = React.forwardRef(function ListItem(props: ListItemProps, ref: React.ForwardedRef<Element>) {
  const group = React.useContext(GroupListContext);
  const row = React.useContext(RowListContext);
  const wrap = React.useContext(WrapListContext);
  const nesting = React.useContext(NestedListContext);

  const {
    children,
    disabled,
    nested = false,
    component = 'li',
    role: roleProp,
    slots = {},
    slotProps = {},
    ...other
  } = props;

  const [subheaderId, setSubheaderId] = React.useState('');

  let role = group === 'menu' ? 'none' : undefined;
  if (roleProp) {
    role = roleProp;
  }

  const ownerState = {
    ...props,
    disabled,
    nested,
    nesting,
    row,
    wrap,
    component,
    role,
  };

  const classes = useUtilityClasses(ownerState);
  const SlotRoot = slots.root ?? ListItemRoot;

  const rootProps = useSlotProps({
    additionalProps: {
      ref,
      as: component,
      role,
    },
    className: classes.root,
    elementType: ListItemRoot,
    externalSlotProps: slotProps.root,
    externalForwardedProps: other,
    ownerState,
  });

  return (
    <ListSubheaderContext.Provider value={setSubheaderId}>
      <NestedListContext.Provider value={nested ? subheaderId || true : false}>
        <ListItemStatusContext.Provider value={{ disabled }}>
          <SlotRoot {...rootProps}>
            {React.Children.map(children, (child, index) =>
              React.isValidElement(child)
                ? React.cloneElement(child, {
                    // to let ListItem knows when to apply margin(Inline|Block)Start
                    ...(index === 0 && { 'data-first-child': '' }),
                    ...(isMuiElement(child, ['ListItem']) && {
                      // The ListItem of ListItem should not be 'li'
                      component: (child.props as ListItemProps)?.component || 'div',
                    }),
                  })
                : child,
            )}
          </SlotRoot>
        </ListItemStatusContext.Provider>
      </NestedListContext.Provider>
    </ListSubheaderContext.Provider>
  );
});
