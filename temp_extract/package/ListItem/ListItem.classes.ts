import {
  unstable_generateUtilityClasses as generateUtilityClasses,
  unstable_generateUtilityClass as generateUtilityClass,
} from '@mui/utils';

export interface ListItemClasses {
  /** Styles applied to the root element. */
  root: string;
  /** Styles applied to the inner `component` element if `disabled={true}`. */
  disabled: string;
  /** Styles applied to the root element, if `nested={true}`. */
  nested: string;
  /** Styles applied to the root element, if it is under a nested list item. */
  nesting: string;
  /** Styles applied to the root element, if `sticky={true}`. */
  sticky: string;
}

export type ListItemClassKey = keyof ListItemClasses;

export function getListItemUtilityClass(slot: string): string {
  return generateUtilityClass('NovaListItem', slot);
}

const listItemClasses: ListItemClasses = generateUtilityClasses('NovaListItem', [
  'root',
  'disabled',
  'nested',
  'nesting',
  'sticky',
]);

export default listItemClasses;
