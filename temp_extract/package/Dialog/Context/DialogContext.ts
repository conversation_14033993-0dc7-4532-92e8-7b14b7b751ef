import * as React from 'react';
import { DialogRootProps } from '../Root/DialogRoot.types';

export type DialogContextValue = Pick<DialogRootProps, 'onClose'> & {
  labelledBy?: string;
  describedBy?: string;
};

export const DialogContext = React.createContext<DialogContextValue>({});

if (process.env.NODE_ENV !== 'production') {
  DialogContext.displayName = 'DialogContext';
}

export function useDialogContext(): DialogContextValue {
  return React.useContext(DialogContext);
}

export default useDialogContext;
