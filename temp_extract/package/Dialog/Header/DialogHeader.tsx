'use client';
import * as React from 'react';
import { OverridableComponent } from '@mui/types';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import useSlotProps from '@mui/utils/useSlotProps';
import { styled } from '@pigment-css/react';
import { DialogHeaderOwnerState, DialogHeaderProps, DialogHeaderTypeMap } from './DialogHeader.types';
import { getDialogHeaderUtilityClass } from './DialogHeader.classes';
import { Typography } from '../../Typography';
import useDialogContext from '../Context/DialogContext';
import clsx from 'clsx';

const useUtilityClasses = () => {
  const slots = {
    root: ['root'],
    icon: ['icon'],
    headline: ['headline'],
    supportingText: ['supportingText'],
  };

  return composeClasses(slots, getDialogHeaderUtilityClass, {});
};

const DialogHeaderRoot = styled('div')<DialogHeaderOwnerState>(({ theme }) => ({
  padding: '16px 24px',
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'center',
  alignItems: 'flex-start',
  gap: '8px',
  alignSelf: 'stretch',
  '@media (max-width: 599px)': {
    padding: '12px 16px',
    gap: '4px',
  },
  variants: [
    {
      props: (ownerState) => Boolean(ownerState.icon),
      style: {
        alignItems: 'center',
      },
    },
  ],
}));

const DialogIcon = styled('div')(({ theme }) => ({
  color: theme.vars.palette.secondary,
  display: 'flex',
}));

const DialogHeadline = styled(Typography)(({ theme }) => ({
  color: theme.vars.palette.onSurface,
  fontWeight: 400,
}));

const DialogHeaderSupportingText = styled(Typography)(({ theme }) => ({
  color: theme.vars.palette.onSurfaceVariant,
  fontWeight: 400,
  overflowY: 'auto',
}));

// eslint-disable-next-line react/display-name
export const DialogHeader = React.forwardRef((props: DialogHeaderProps, ref: React.ForwardedRef<Element>) => {
  const { icon, supportingText, children, component, slots = {}, slotProps = {}, ...rest } = props;
  const { labelledBy, describedBy } = useDialogContext();

  const classes = useUtilityClasses();

  const SlotRoot = slots.root ?? DialogHeaderRoot;

  const slotRootProps = useSlotProps({
    elementType: SlotRoot,
    externalSlotProps: slotProps.root,
    externalForwardedProps: rest,
    additionalProps: {
      ref: ref,
      as: component,
    },
    ownerState: props,
    className: classes.root,
  });

  return (
    <SlotRoot {...slotRootProps}>
      {icon && <DialogIcon className={classes.icon}>{icon}</DialogIcon>}
      {children && typeof children === 'string' ? (
        <DialogHeadline
          id={labelledBy}
          variant={'titleMedium'}
          {...slotProps?.headline}
          className={clsx(classes.headline, slotProps?.headline?.className)}
        >
          {children}
        </DialogHeadline>
      ) : (
        children
      )}
      {supportingText && typeof supportingText === 'string' ? (
        <DialogHeaderSupportingText
          id={describedBy}
          variant={'bodySmall'}
          {...slotProps?.supportingText}
          className={clsx(classes.supportingText, slotProps?.supportingText?.className)}
        >
          {supportingText}
        </DialogHeaderSupportingText>
      ) : (
        supportingText
      )}
    </SlotRoot>
  );
}) as OverridableComponent<DialogHeaderTypeMap>;
