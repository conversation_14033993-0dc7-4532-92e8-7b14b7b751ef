import '@testing-library/jest-dom/vitest';
import { render, screen, cleanup } from '@testing-library/react';
import React from 'react';
import { describe, expect, it, afterEach } from 'vitest';
import { DialogHeader } from './DialogHeader';

afterEach(() => {
  cleanup();
});

describe('DialogHeader', () => {
  it('should render normal', () => {
    render(<DialogHeader data-testid="NovaDialogHeader-root">Dialog title</DialogHeader>);
    expect(screen.getByTestId('NovaDialogHeader-root')).toHaveClass('NovaDialogHeader-root');
    expect(screen.getByText('Dialog title')).toBeInTheDocument();
  });

  it('should render icon', () => {
    render(<DialogHeader icon={<div>Icon</div>}>Dialog title</DialogHeader>);
    expect(screen.getByText('Icon')).toBeInTheDocument();
  });

  it('should render supporting text', () => {
    render(<DialogHeader supportingText={'This is supporting text'}>Dialog title</DialogHeader>);
    expect(screen.getByText('This is supporting text')).toBeInTheDocument();
  });

  it('should slotProps working', () => {
    render(
      <DialogHeader
        slotProps={{
          headline: { 'data-testid': 'NovaDialog-headline' },
          supportingText: { 'data-testid': 'NovaDialog-supportingText' },
        }}
        supportingText={'This is supporting text'}
      >
        Dialog title
      </DialogHeader>,
    );
    expect(screen.getByTestId('NovaDialog-headline')).toHaveTextContent('Dialog title');
    expect(screen.getByTestId('NovaDialog-supportingText')).toHaveTextContent('This is supporting text');
  });
});
