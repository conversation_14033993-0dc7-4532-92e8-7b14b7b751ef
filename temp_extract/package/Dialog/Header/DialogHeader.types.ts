import * as React from 'react';
import { OverrideProps } from '@mui/types';
import { SlotProps, CreateSlotsAndSlotProps } from '../../types/slot';
import { TypographyProps } from '../../Typography';

export type DialogHeaderSlot = 'root';

export interface DialogHeaderSlots {
  /**
   * The component that renders the root.
   * @default 'div'
   */
  root?: React.ElementType;
}

export type DialogHeaderSlotsAndSlotProps = CreateSlotsAndSlotProps<
  DialogHeaderSlots,
  {
    root: SlotProps<'div', object, DialogHeaderOwnerState>;
    headline: TypographyProps;
    supportingText: TypographyProps;
  }
>;

export interface DialogHeaderTypeMap<P = object, D extends React.ElementType = 'div'> {
  props: P & {
    /**
     * Icon for dialog header.
     */
    icon?: React.ReactNode;
    /**
     * Supporting text for dialog header.
     */
    supportingText?: React.ReactNode;
  } & DialogHeaderSlotsAndSlotProps;
  defaultComponent: D;
}

export type DialogHeaderProps<
  D extends React.ElementType = DialogHeaderTypeMap['defaultComponent'],
  P = {
    component?: React.ElementType;
  },
> = OverrideProps<DialogHeaderTypeMap<P, D>, D>;

export interface DialogHeaderOwnerState extends DialogHeaderProps {}
