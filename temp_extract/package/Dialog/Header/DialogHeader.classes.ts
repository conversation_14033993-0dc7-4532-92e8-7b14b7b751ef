import generateUtilityClasses from '@mui/utils/generateUtilityClasses';
import generateUtilityClass from '@mui/utils/generateUtilityClass';

export interface DialogHeaderClasses {
  /** Class name applied to the root element. */
  root: string;
  /** Class name applied to the icon element. */
  icon: string;
  /** Class name applied to the headline element. */
  headline: string;
  /** Class name applied to the supporting text element. */
  supportingText: string;
}

export type DialogHeaderClassKey = keyof DialogHeaderClasses;

export function getDialogHeaderUtilityClass(slot: string): string {
  return generateUtilityClass('NovaDialogHeader', slot);
}

const dialogHeaderClasses: DialogHeaderClasses = generateUtilityClasses('NovaDialogHeader', [
  'root',
  'icon',
  'headline',
  'supportingText',
]);

export default dialogHeaderClasses;
