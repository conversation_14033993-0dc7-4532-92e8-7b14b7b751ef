import * as React from 'react';
import { OverrideProps } from '@mui/types';
import { SlotProps, CreateSlotsAndSlotProps } from '../../types/slot';

export type DialogContentSlot = 'root';

export interface DialogContentSlots {
  /**
   * The component that renders the root.
   * @default 'div'
   */
  root?: React.ElementType;
}

export type DialogContentSlotsAndSlotProps = CreateSlotsAndSlotProps<
  DialogContentSlots,
  {
    root: SlotProps<'div', object, DialogContentOwnerState>;
  }
>;

export interface DialogContentTypeMap<P = object, D extends React.ElementType = 'div'> {
  props: P & {
    /**
     * Display the top divider.
     * @default false
     */
    topDivider?: boolean;
    /**
     * Display the bottom divider.
     * @default false
     */
    bottomDivider?: boolean;
  } & DialogContentSlotsAndSlotProps;
  defaultComponent: D;
}

export type DialogContentProps<
  D extends React.ElementType = DialogContentTypeMap['defaultComponent'],
  P = {
    component?: React.ElementType;
  },
> = OverrideProps<DialogContentTypeMap<P, D>, D>;

export interface DialogContentOwnerState extends DialogContentProps {}
