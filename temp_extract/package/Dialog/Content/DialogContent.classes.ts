import generateUtilityClasses from '@mui/utils/generateUtilityClasses';
import generateUtilityClass from '@mui/utils/generateUtilityClass';

export interface DialogContentClasses {
  /** Class name applied to the root element. */
  root: string;
}

export type DialogContentClassKey = keyof DialogContentClasses;

export function getDialogContentUtilityClass(slot: string): string {
  return generateUtilityClass('NovaDialogContent', slot);
}

const dialogContentClasses: DialogContentClasses = generateUtilityClasses('NovaDialogContent', ['root']);

export default dialogContentClasses;
