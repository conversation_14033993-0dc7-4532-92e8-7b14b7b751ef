'use client';
import * as React from 'react';
import { OverridableComponent } from '@mui/types';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import useSlotProps from '@mui/utils/useSlotProps';
import { styled } from '@pigment-css/react';
import { DialogContentOwnerState, DialogContentProps, DialogContentTypeMap } from './DialogContent.types';
import { getDialogContentUtilityClass } from './DialogContent.classes';

const useUtilityClasses = () => {
  const slots = {
    root: ['root'],
  };

  return composeClasses(slots, getDialogContentUtilityClass, {});
};

const DialogContentRoot = styled('div')<DialogContentOwnerState>(({ theme }) => ({
  padding: '16px 24px',
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'flex-start',
  overflowY: 'auto',
  '@media (max-width: 599px)': {
    padding: '12px 16px',
  },
  variants: [
    {
      props: { topDivider: true },
      style: {
        borderTop: `1px solid`,
        borderTopColor: theme.vars.palette.outlineVariant,
      },
    },
    {
      props: { bottomDivider: true },
      style: {
        borderBottom: `1px solid`,
        borderBottomColor: theme.vars.palette.outlineVariant,
      },
    },
  ],
}));

// eslint-disable-next-line react/display-name
export const DialogContent = React.forwardRef((props: DialogContentProps, ref: React.ForwardedRef<Element>) => {
  const { children, component, slots = {}, slotProps = {}, ...rest } = props;

  const classes = useUtilityClasses();

  const SlotRoot = slots.root ?? DialogContentRoot;

  const slotRootProps = useSlotProps({
    elementType: SlotRoot,
    externalSlotProps: slotProps.root,
    externalForwardedProps: rest,
    additionalProps: {
      ref: ref,
      as: component,
    },
    ownerState: props,
    className: classes.root,
  });

  return <SlotRoot {...slotRootProps}>{children}</SlotRoot>;
}) as OverridableComponent<DialogContentTypeMap>;
