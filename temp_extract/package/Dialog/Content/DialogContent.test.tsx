import '@testing-library/jest-dom/vitest';
import { render, screen, cleanup } from '@testing-library/react';
import React from 'react';
import { describe, expect, it, afterEach } from 'vitest';
import { DialogContent } from './DialogContent';

afterEach(() => {
  cleanup();
});

describe('DialogContent', () => {
  it('should render normal', () => {
    render(<DialogContent data-testid="NovaDialogContent-root">Hello world</DialogContent>);
    expect(screen.getByTestId('NovaDialogContent-root')).toHaveClass('NovaDialogContent-root');
    expect(screen.getByText('Hello world')).toBeInTheDocument();
  });
});
