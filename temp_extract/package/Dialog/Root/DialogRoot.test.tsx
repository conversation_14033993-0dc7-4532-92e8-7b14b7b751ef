import '@testing-library/jest-dom/vitest';
import { render, screen, cleanup, fireEvent } from '@testing-library/react';
import React from 'react';
import { describe, expect, it, afterEach, vi } from 'vitest';
import { DialogRoot } from './DialogRoot';
import { DialogHeader } from '../Header';
import { DialogContent } from '../Content';
import { DialogActions } from '../Actions';
import { Button } from '../../Button';

afterEach(() => {
  cleanup();
});

describe('DialogRoot', () => {
  it('should render normal', () => {
    render(<DialogRoot data-testid="NovaDialogRoot-root" open />);
    expect(screen.getByTestId('NovaDialogRoot-root')).toHaveClass('NovaDialogRoot-root');
    expect(screen.getByRole('dialog')).toBeInTheDocument();
  });

  it('should render full dialog', () => {
    render(
      <DialogRoot data-testid="NovaDialogRoot-root" open>
        <DialogHeader data-testid="NovaDialogHeader-root">Dialog title</DialogHeader>
        <DialogContent data-testid="NovaDialogContent-root">Dialog content</DialogContent>
        <DialogActions data-testid="NovaDialogActions-root">
          <Button>Ok</Button>
        </DialogActions>
      </DialogRoot>,
    );
    expect(screen.getByTestId('NovaDialogHeader-root')).toBeInTheDocument();
    expect(screen.getByTestId('NovaDialogContent-root')).toBeInTheDocument();
    expect(screen.getByTestId('NovaDialogActions-root')).toBeInTheDocument();
    expect(screen.getByText('Dialog title')).toBeInTheDocument();
    expect(screen.getByText('Dialog content')).toBeInTheDocument();
    expect(screen.getByText('Ok')).toBeInTheDocument();
  });

  it('should render fullWidth', () => {
    render(
      <DialogRoot data-testid="NovaDialogRoot-root" open fullWidth>
        <DialogHeader>Dialog title</DialogHeader>
        <DialogContent>Dialog content</DialogContent>
        <DialogActions>
          <Button>Ok</Button>
        </DialogActions>
      </DialogRoot>,
    );
    expect(screen.getByRole('dialog')).toHaveClass('NovaDialogRoot-paper');
    expect(screen.getByRole('dialog')).toHaveClass('NovaDialogRoot-fullWidth');
    expect(screen.getByRole('dialog')).toHaveClass('NovaDialogRoot-widthXs');
  });

  it('should render maxWidth', () => {
    render(
      <DialogRoot data-testid="NovaDialogRoot-root" open maxWidth="md">
        <DialogHeader>Dialog title</DialogHeader>
        <DialogContent>Dialog content</DialogContent>
        <DialogActions>
          <Button>Ok</Button>
        </DialogActions>
      </DialogRoot>,
    );
    expect(screen.getByRole('dialog')).toHaveClass('NovaDialogRoot-widthMd');
  });

  it('should render fullScreen', () => {
    render(
      <DialogRoot data-testid="NovaDialogRoot-root" open fullScreen>
        <DialogHeader>Dialog title</DialogHeader>
        <DialogContent>Dialog content</DialogContent>
        <DialogActions>
          <Button>Ok</Button>
        </DialogActions>
      </DialogRoot>,
    );
    expect(screen.getByRole('dialog')).toHaveClass('NovaDialogRoot-fullScreen');
  });

  it('should trigger onClose', () => {
    const mockOnClose = vi.fn();
    render(
      <DialogRoot
        data-testid="NovaDialogRoot-root"
        open
        fullScreen
        onClose={mockOnClose}
        slotProps={{ backdrop: { 'data-testid': 'backdrop' } as any }}
      >
        <DialogHeader>Dialog title</DialogHeader>
        <DialogContent>Dialog content</DialogContent>
        <DialogActions>
          <Button>Ok</Button>
        </DialogActions>
      </DialogRoot>,
    );
    expect(screen.getByRole('dialog')).toHaveClass('NovaDialogRoot-fullScreen');
    fireEvent.click(screen.getByTestId('backdrop'));
    expect(mockOnClose).toHaveBeenCalled();
  });
});
