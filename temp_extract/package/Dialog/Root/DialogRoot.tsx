'use client';
import * as React from 'react';
import { OverridableComponent } from '@mui/types';
import {
  unstable_composeClasses as composeClasses,
  unstable_capitalize as capitalize,
  unstable_useId as useId,
} from '@mui/utils';
import useSlotProps from '@mui/utils/useSlotProps';
import { styled } from '@pigment-css/react';
import { DialogRootOwnerState, DialogRootProps, DialogRootTypeMap } from './DialogRoot.types';
import { getDialogRootUtilityClass } from './DialogRoot.classes';
import { Modal } from '../../internal/components/Modal';
import { DialogContext } from '../Context/DialogContext';
import { dialogContentClasses } from '../Content';
import { dialogHeaderClasses } from '../Header';

const useUtilityClasses = (ownerState: DialogRootOwnerState) => {
  const { maxWidth, fullWidth, fullScreen } = ownerState;
  const slots = {
    root: ['root'],
    container: ['container'],
    paper: ['paper', `width${capitalize(String(maxWidth))}`, fullWidth && 'fullWidth', fullScreen && 'fullScreen'],
  };

  return composeClasses(slots, getDialogRootUtilityClass, {});
};

const DialogRootSlot = styled(Modal)<DialogRootOwnerState>(({ theme }) => ({
  position: 'fixed',
  zIndex: 1300,
  right: 0,
  bottom: 0,
  top: 0,
  left: 0,
  '@media print': {
    // Use !important to override the Modal inline-style.
    position: 'absolute !important',
  },
}));

const DialogContainer = styled('div')<DialogRootOwnerState>(({ theme }) => ({
  height: '100%',
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  outline: 0,
  '@media print': {
    height: 'auto',
  },
}));

const DialogPaper = styled('div')<DialogRootOwnerState>(({ theme }) => ({
  margin: 32,
  position: 'relative',
  overflowY: 'auto',
  borderRadius: '16px',
  backgroundColor: theme.vars.palette.surfaceContainerHigh,
  border: '1px solid',
  borderColor: theme.vars.palette.outlineVariant,
  '@media print': {
    overflowY: 'visible',
    boxShadow: 'none',
  },
  display: 'flex',
  flexDirection: 'column',
  maxHeight: 'calc(100% - 64px)',
  [`&:not(:has(.${dialogContentClasses.root})) .${dialogHeaderClasses.root}`]: {
    overflowY: 'auto',
    height: '100%',
  },
  variants: [
    {
      props: (ownerState) => !ownerState.maxWidth,
      style: {
        maxWidth: 'calc(100% - 64px)',
      },
    },
    {
      props: { maxWidth: 'xs' },
      style: {
        maxWidth: '375px',
      },
    },
    {
      props: { maxWidth: 'sm' },
      style: {
        maxWidth: '600px',
      },
    },
    {
      props: { maxWidth: 'md' },
      style: {
        maxWidth: '840px',
      },
    },
    {
      props: { maxWidth: 'lg' },
      style: {
        maxWidth: '1200px',
      },
    },
    {
      props: { maxWidth: 'xl' },
      style: {
        maxWidth: '1600px',
      },
    },
    {
      props: { fullWidth: true },
      style: {
        width: 'calc(100% - 64px)',
      },
    },
    {
      props: { fullScreen: true },
      style: {
        margin: 0,
        width: '100%',
        maxWidth: '100%',
        height: '100%',
        maxHeight: 'none',
        borderRadius: 0,
      },
    },
  ],
}));

const DialogBackdrop = styled('div')<DialogRootOwnerState>(({ theme }) => ({
  zIndex: -1,
  position: 'fixed',
  right: 0,
  bottom: 0,
  top: 0,
  left: 0,
  backgroundColor: 'rgba(0, 0, 0, 0.16)',
  WebkitTapHighlightColor: 'transparent',
}));

// eslint-disable-next-line react/display-name
export const DialogRoot = React.forwardRef((props: DialogRootProps, ref: React.ForwardedRef<Element>) => {
  const {
    'aria-describedby': ariaDescribedbyProp,
    'aria-labelledby': ariaLabelledbyProp,
    'aria-modal': ariaModal = true,
    fullScreen: fullScreenProp = false,
    fullWidth: fullWidthProp = false,
    maxWidth: maxWithProp = 'xs',
    children,
    component,
    className,
    slots = {},
    slotProps = {},
    onClick,
    onClose,
    open,
    ...rest
  } = props;

  const backdropClick = React.useRef<boolean | null>(null);
  const handleMouseDown = (event: React.MouseEvent<HTMLDivElement>) => {
    // We don't want to close the dialog when clicking the dialog content.
    // Make sure the event starts and ends on the same DOM element.
    backdropClick.current = event.target === event.currentTarget;
  };
  const handleBackdropClick = (event: React.MouseEvent<HTMLDivElement>) => {
    if (onClick) {
      onClick(event);
    }

    // Ignore the events not coming from the "backdrop".
    if (!backdropClick.current) {
      return;
    }

    backdropClick.current = null;

    if (onClose) {
      onClose(event, 'backdropClick');
    }
  };

  const describedBy = useId(ariaDescribedbyProp);
  const labelledBy = useId(ariaLabelledbyProp);

  const ownerState = {
    ...props,
    fullScreen: fullScreenProp,
    fullWidth: fullWidthProp,
    maxWidth: maxWithProp,
  };

  const classes = useUtilityClasses(ownerState);

  const slotRootProps = useSlotProps({
    elementType: DialogRootSlot,
    externalSlotProps: rest,
    additionalProps: {
      ref,
      as: component,
    },
    ownerState,
    className: [classes.root, className],
  });

  const slotContainerProps = useSlotProps({
    elementType: DialogContainer,
    externalSlotProps: {},
    ownerState,
    className: classes.container,
  });

  const slotPaperProps = useSlotProps({
    elementType: DialogPaper,
    externalSlotProps: {},
    additionalProps: {
      role: 'dialog',
      'aria-describedby': describedBy,
      'aria-labelledby': labelledBy,
      'aria-modal': ariaModal,
    },
    ownerState,
    className: classes.paper,
  });

  return (
    <DialogRootSlot
      closeAfterTransition
      slots={{
        backdrop: DialogBackdrop,
        ...slots,
      }}
      slotProps={slotProps}
      onClose={onClose}
      onClick={handleBackdropClick}
      open={open}
      {...slotRootProps}
    >
      <DialogContainer onMouseDown={handleMouseDown} {...slotContainerProps}>
        <DialogPaper {...slotPaperProps}>
          <DialogContext.Provider value={{ labelledBy, describedBy, onClose }}>{children} </DialogContext.Provider>
        </DialogPaper>
      </DialogContainer>
    </DialogRootSlot>
  );
}) as OverridableComponent<DialogRootTypeMap>;
