import generateUtilityClasses from '@mui/utils/generateUtilityClasses';
import generateUtilityClass from '@mui/utils/generateUtilityClass';

export interface DialogRootClasses {
  /** Styles applied to the root element. */
  root: string;
  /** Styles applied to the container element. */
  container: string;
  /** Styles applied to the Paper component. */
  paper: string;
  /** Styles applied to the Paper component if `maxWidth=false`. */
  widthFalse: string;
  /** Styles applied to the Paper component if `maxWidth="xs"`. */
  widthXs: string;
  /** Styles applied to the Paper component if `maxWidth="sm"`. */
  widthSm: string;
  /** Styles applied to the Paper component if `maxWidth="md"`. */
  widthMd: string;
  /** Styles applied to the Paper component if `maxWidth="lg"`. */
  widthLg: string;
  /** Styles applied to the Paper component if `maxWidth="xl"`. */
  widthXl: string;
  /** Styles applied to the Paper component if `fullWidth={true}`. */
  fullWidth: string;
  /** Styles applied to the Paper component if `fullScreen={true}`. */
  fullScreen: string;
}

export type DialogRootClassKey = keyof DialogRootClasses;

export function getDialogRootUtilityClass(slot: string): string {
  return generateUtilityClass('NovaDialogRoot', slot);
}

const dialogRootClasses: DialogRootClasses = generateUtilityClasses('NovaDialogRoot', [
  'root',
  'container',
  'paper',
  'widthFalse',
  'widthXs',
  'widthSm',
  'widthMd',
  'widthLg',
  'widthXl',
  'fullWidth',
  'fullScreen',
]);

export default dialogRootClasses;
