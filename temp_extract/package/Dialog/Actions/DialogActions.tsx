'use client';
import * as React from 'react';
import { OverridableComponent } from '@mui/types';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import useSlotProps from '@mui/utils/useSlotProps';
import { styled } from '@pigment-css/react';
import { DialogActionsOwnerState, DialogActionsProps, DialogActionsTypeMap } from './DialogActions.types';
import { getDialogActionsUtilityClass } from './DialogActions.classes';

const useUtilityClasses = () => {
  const slots = {
    root: ['root'],
  };

  return composeClasses(slots, getDialogActionsUtilityClass, {});
};

const DialogActionsRoot = styled('div')<DialogActionsOwnerState>(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  padding: '24px',
  justifyContent: 'flex-end',
  flex: '0 0 auto',
  gap: '8px',
  '@media (max-width: 599px)': {
    padding: '12px 16px',
    gap: '4px',
  },
}));

// eslint-disable-next-line react/display-name
export const DialogActions = React.forwardRef((props: DialogActionsProps, ref: React.ForwardedRef<Element>) => {
  const { children, component, slots = {}, slotProps = {}, ...rest } = props;

  const classes = useUtilityClasses();

  const SlotRoot = slots.root ?? DialogActionsRoot;

  const slotRootProps = useSlotProps({
    elementType: SlotRoot,
    externalSlotProps: slotProps.root,
    externalForwardedProps: rest,
    additionalProps: {
      ref,
      as: component,
    },
    ownerState: props,
    className: classes.root,
  });

  return <SlotRoot {...slotRootProps}>{children}</SlotRoot>;
}) as OverridableComponent<DialogActionsTypeMap>;
