import * as React from 'react';
import { OverrideProps } from '@mui/types';
import { SlotProps, CreateSlotsAndSlotProps } from '../../types/slot';

export type DialogActionsSlot = 'root';

export interface DialogActionsSlots {
  /**
   * The component that renders the root.
   * @default 'div'
   */
  root?: React.ElementType;
}

export type DialogActionsSlotsAndSlotProps = CreateSlotsAndSlotProps<
  DialogActionsSlots,
  {
    root: SlotProps<'div', object, DialogActionsOwnerState>;
  }
>;

export interface DialogActionsTypeMap<P = object, D extends React.ElementType = 'div'> {
  props: P & DialogActionsSlotsAndSlotProps;
  defaultComponent: D;
}

export type DialogActionsProps<
  D extends React.ElementType = DialogActionsTypeMap['defaultComponent'],
  P = {
    component?: React.ElementType;
  },
> = OverrideProps<DialogActionsTypeMap<P, D>, D>;

export interface DialogActionsOwnerState extends DialogActionsProps {}
