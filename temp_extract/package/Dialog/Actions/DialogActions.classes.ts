import generateUtilityClasses from '@mui/utils/generateUtilityClasses';
import generateUtilityClass from '@mui/utils/generateUtilityClass';

export interface DialogActionsClasses {
  /** Class name applied to the root element. */
  root: string;
}

export type DialogActionsClassKey = keyof DialogActionsClasses;

export function getDialogActionsUtilityClass(slot: string): string {
  return generateUtilityClass('NovaDialogActions', slot);
}

const dialogActionsClasses: DialogActionsClasses = generateUtilityClasses('NovaDialogActions', ['root']);

export default dialogActionsClasses;
