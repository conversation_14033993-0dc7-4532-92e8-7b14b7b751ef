import '@testing-library/jest-dom/vitest';
import { render, screen, cleanup } from '@testing-library/react';
import React from 'react';
import { describe, expect, it, afterEach } from 'vitest';
import { DialogActions } from './DialogActions';
import { Button } from '../../Button';

afterEach(() => {
  cleanup();
});

describe('DialogActions', () => {
  it('should render normal', () => {
    render(
      <DialogActions data-testid="NovaDialogActions-root">
        <Button>Primary Button</Button>
        <Button>Secondary Button</Button>
      </DialogActions>,
    );
    expect(screen.getByTestId('NovaDialogActions-root')).toHaveClass('NovaDialogActions-root');
    expect(screen.getAllByRole('button')).toHaveLength(2);
  });
});
