import { DialogRoot } from './Root';
import { DialogActions } from './Actions';
import { DialogContent } from './Content';
import { DialogHeader } from './Header';

export { DialogRoot, dialogRootClasses } from './Root';
export { DialogActions, dialogActionsClasses } from './Actions';
export { DialogContent, dialogContentClasses } from './Content';
export { DialogHeader, dialogHeaderClasses } from './Header';

export type { DialogRootProps } from './Root';
export type { DialogActionsProps } from './Actions';
export type { DialogContentProps } from './Content';
export type { DialogHeaderProps } from './Header';

export const Dialog = {
  Root: DialogRoot,
  Actions: DialogActions,
  Content: DialogContent,
  Header: DialogHeader,
};
