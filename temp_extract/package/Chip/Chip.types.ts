import { HTMLAttributes, ReactElement } from 'react';

export interface ChipProps extends Omit<HTMLAttributes<HTMLDivElement>, 'children'> {
  /**
   * The text label to show in the chip
   */
  label: string;
  /**
   * Whether or not the chip is selected/active
   */
  selected?: boolean;
  /**
   * Optional icon to show before the label
   */
  startIcon?: ReactElement;
  /** Optional icon to show after the label */
  endIcon?: ReactElement;
  /**
   * The size of the chip
   * @default 'medium'
   */
  size?: 'small' | 'medium' | 'large';
  /**
   * Whether or not the chip is disabled
   * @default false
   */
  disabled?: boolean;
}
