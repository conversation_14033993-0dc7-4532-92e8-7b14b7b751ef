import generateUtilityClasses from '@mui/utils/generateUtilityClasses';
import generateUtilityClass from '@mui/utils/generateUtilityClass';

export interface ChipClasses {
  /** Styles applied to the root element. */
  root: string;

  /** Styles applied to the Button if `size="small"`. */
  sizeSmall: string;
  /** Styles applied to the Button if `size="medium"`. */
  sizeMedium: string;
  /** Styles applied to the Button if `size="large"`. */
  sizeLarge: string;

  /** Styles applied to the Button startIcon element. */
  iconStart: string;
  /** Styles applied to the Button endIcon element. */
  iconEnd: string;

  /** Styles applied to the Button if `disabled={true}` */
  disabled: string;
  /** Styles applied to the Button if `selected={true}` */
  selected: string;
}

export type ChipClassKey = keyof ChipClasses;

export function getChipUtilityClass(slot: string): string {
  return generateUtilityClass('NovaChip', slot);
}

const chipClasses: ChipClasses = generateUtilityClasses('NovaChip', [
  'root',
  'sizeSmall',
  'sizeMedium',
  'sizeLarge',
  'iconStart',
  'iconEnd',
  'disabled',
  'selected',
]);

export default chipClasses;
