import generateUtilityClasses from '@mui/utils/generateUtilityClasses';
import generateUtilityClass from '@mui/utils/generateUtilityClass';

export interface ContainerClasses {
  /** Class name applied to the root element. */
  root: string;
  /** Class name applied to the root element if `maxWidth = 'xs'`. */
  maxWidthXs: string;
  /** Class name applied to the root element if `maxWidth = 'sm'`. */
  maxWidthSm: string;
  /** Class name applied to the root element if `maxWidth = 'md'`. */
  maxWidthMd: string;
  /** Class name applied to the root element if `maxWidth = 'lg'`. */
  maxWidthLg: string;
  /** Class name applied to the root element if `maxWidth = 'xl'`. */
  maxWidthXl: string;
  /** Class name applied to the root element if `fixed = {true}`. */
  fixed: string;
  /** Class name applied to the root element if `disableGutters = {true}`. */
  disableGutters: string;
}

export type ContainerClassKey = keyof ContainerClasses;

export function getContainerUtilityClass(slot: string): string {
  return generateUtilityClass('NovaContainer', slot);
}

const containerClasses: ContainerClasses = generateUtilityClasses('NovaContainer', [
  'root',
  'maxWidthXs',
  'maxWidthSm',
  'maxWidthMd',
  'maxWidthLg',
  'maxWidthXl',
  'fixed',
  'disableGutters',
]);

export default containerClasses;
