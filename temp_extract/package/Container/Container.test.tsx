import '@testing-library/jest-dom/vitest';
import { render, screen, cleanup } from '@testing-library/react';
import React from 'react';
import { describe, expect, it, afterEach } from 'vitest';
import { Container } from './Container';

afterEach(() => {
  cleanup();
});

describe('Container', () => {
  it('should render normal', () => {
    render(<Container data-testid="NovaContainer-root" />);
    expect(screen.getByTestId('NovaContainer-root')).toHaveClass('NovaContainer-root');
  });
});
