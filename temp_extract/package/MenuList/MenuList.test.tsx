import React from 'react';
import '@testing-library/jest-dom/vitest';
import { render } from '@testing-library/react';
import { expect, describe, it } from 'vitest';
import { unstable_capitalize as capitalize } from '@mui/utils';
import classes, { MenuListClassKey } from './MenuList.classes';
import { MenuList } from './MenuList';

describe('<MenuList />', () => {
  it('should have root className', () => {
    const { container } = render(<MenuList />);
    expect(container.firstChild).toHaveClass(classes.root);
    expect(container.firstChild).toHaveClass(classes.densityStandard);
  });

  it('should accept className prop', () => {
    const { container } = render(<MenuList className="foo-bar" />);
    expect(container.firstChild).toHaveClass('foo-bar');
  });

  describe('prop: density', () => {
    (['standard', 'compact', 'comfortable'] as const).forEach((size) => {
      it('standard by default', () => {
        const { getByTestId } = render(<MenuList data-testid="root" />);
        expect(getByTestId('root')).toHaveClass(classes.densityStandard);
      });
      it(`should render ${size}`, () => {
        const { getByTestId } = render(<MenuList data-testid="root" density={size} />);
        expect(getByTestId('root')).toHaveClass(classes[`density${capitalize(size)}` as MenuListClassKey]);
      });
    });
  });
});
