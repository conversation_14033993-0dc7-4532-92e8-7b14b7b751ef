import { CreateSlotsAndSlotProps, SlotProps } from '../types/slot';
import { OverrideProps } from '@mui/types';
import { SxProps } from '../types/theme';

export type MenuListSlot = 'root';

export interface MenuListSlots {
  /**
   * The component that renders the root.
   * @default 'ul'
   */
  root?: React.ElementType;
}

export type MenuListSlotsAndSlotProps = CreateSlotsAndSlotProps<
  MenuListSlots,
  {
    root: SlotProps<'ul', object, MenuListOwnerState>;
  }
>;

export interface MenuListTypeMap<P = object, D extends React.ElementType = 'ul'> {
  props: P &
    MenuListSlotsAndSlotProps & {
      /**
       * The `density` attribute for the MenuListItem
       * @default 'standard'
       */
      density?: 'standard' | 'compact' | 'comfortable';
      /**
       * The system prop that allows defining system overrides as well as additional CSS styles.
       */
      sx?: SxProps;
      /**
       * Function called when the items displayed in the menu change.
       */
      onItemsChange?: (items: string[]) => void;
    };
  defaultComponent: D;
}

export type MenuListProps<
  D extends React.ElementType = MenuListTypeMap['defaultComponent'],
  P = {
    component?: React.ElementType;
  },
> = OverrideProps<MenuListTypeMap<P, D>, D>;

export interface MenuListOwnerState extends MenuListProps {}
