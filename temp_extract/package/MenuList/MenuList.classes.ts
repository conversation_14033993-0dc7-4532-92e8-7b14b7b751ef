import {
  unstable_generateUtilityClasses as generateUtilityClasses,
  unstable_generateUtilityClass as generateUtilityClass,
} from '@mui/utils';

export interface MenuListClasses {
  /** Styles applied to the root element. */
  root: string;
  /** Styles applied to the root element if `density="standard"`. */
  densityStandard: string;
  /** Styles applied to the root element if `density="compact"`. */
  densityCompact: string;
  /** Styles applied to the root element if `density="comfortable"`. */
  densityComfortable: string;
}

export type MenuListClassKey = keyof MenuListClasses;

export function getMenuListUtilityClass(slot: string): string {
  return generateUtilityClass('NovaMenuList', slot);
}

const menuListClasses: MenuListClasses = generateUtilityClasses('NovaMenuList', [
  'root',
  'densityStandard',
  'densityCompact',
  'densityComfortable',
]);

export default menuListClasses;
