'use client';
import * as React from 'react';
import { styled } from '@pigment-css/react';
import { unstable_composeClasses as composeClasses, unstable_capitalize as capitalize } from '@mui/utils';
import useSlotProps from '@mui/utils/useSlotProps';
import { MenuListOwnerState, MenuListProps } from './MenuList.types';
import { getMenuListUtilityClass } from './MenuList.classes';
import { ListRoot as StyledList } from '../List/List';
import { useMenu, MenuProvider } from '../internal/hooks/useMenu';
import ListProvider from '../List/ListProvider';
import GroupListContext from '../List/GroupListContext';

const useUtilityClasses = (ownerState: MenuListOwnerState) => {
  const { density } = ownerState;
  const slots = {
    root: ['root', density && `density${capitalize(density)}`],
  };

  const composedClasses = composeClasses(slots, getMenuListUtilityClass, {});
  return composedClasses;
};

const MenuListRoot = styled(StyledList)<MenuListProps>(({ theme }) => ({
  borderRadius: '8px',
  border: `1px solid ${theme.vars.palette.outlineVariant}`,
  background: theme.vars.palette.surfaceContainer,
  overflow: 'auto',
  '--nova-listItem-color': theme.vars.palette.onSurface,
  '--nova-listItem-secondaryColor': theme.vars.palette.onSurfaceVariant,
  '--nova-listItem-paddingY': '8px',
  '--nova-listItem-paddingX': '16px',
  '--nova-listItem-minHeight': '56px',
  '--nova-listItem-gap': '8px',
  '--nova-listItem-paddingLeft': 'var(--nova-listItem-paddingX)',
  '--nova-listItem-paddingRight': 'var(--nova-listItem-paddingX)',
  '--nova-listDivider-gap': '8px',
  '--nova-list-paddingBlock': '8px',
  paddingBlock: 'var(--nova-list-paddingBlock)',
  paddingInline: 'var(--nova-list-paddingInline)',
  '--nova-listItem-stickyBackground': theme.vars.palette.surfaceContainer,
  '--nova-listItem-stickyTop': 'calc(var(--nova-list-padding, var(--nova-listDivider-gap)) * -1)', // negative amount of the List's padding block  ...scopedVariables,
  variants: [
    {
      props: { density: 'compact' },
      style: {
        '--nova-listItem-paddingY': '4px',
        '--nova-listItem-paddingX': '16px',
        '--nova-listItem-minHeight': '48px',
      },
    },
    {
      props: { density: 'standard' },
      style: {
        '--nova-listItem-paddingY': '8px',
        '--nova-listItem-paddingX': '16px',
        '--nova-listItem-minHeight': '56px',
      },
    },
    {
      props: { density: 'comfortable' },
      style: {
        fontFamily: theme.typography.fontFamily,
        '--nova-listItem-paddingY': '12px',
        '--nova-listItem-paddingX': '24px',
        '--nova-listItem-minHeight': '64px',
      },
    },
  ],
}));

export const MenuList = React.forwardRef(function MenuList(props: MenuListProps, ref: React.ForwardedRef<Element>) {
  const {
    children,
    id: idProp,
    density = 'standard',
    component,
    onItemsChange,
    slots = {},
    slotProps = {},
    ...other
  } = props;

  const { contextValue: menuContextValue, getListboxProps } = useMenu({
    listboxRef: ref,
    id: idProp,
    onItemsChange,
  });

  const ownerState = {
    ...props,
    density,
    nesting: false,
    row: false,
  };

  const classes = useUtilityClasses(ownerState);
  const SlotRoot = slots.root ?? MenuListRoot;
  const rootProps = useSlotProps({
    additionalProps: {
      ref,
      as: component,
    },
    className: classes.root,
    elementType: MenuListRoot,
    externalSlotProps: slotProps.root,
    getSlotProps: getListboxProps,
    externalForwardedProps: other,
    ownerState,
  });
  return (
    <SlotRoot {...rootProps}>
      <MenuProvider value={menuContextValue}>
        <GroupListContext.Provider value="menu">
          <ListProvider nested>{children}</ListProvider>
        </GroupListContext.Provider>
      </MenuProvider>
    </SlotRoot>
  );
});
