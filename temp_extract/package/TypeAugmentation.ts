/* eslint-disable @typescript-eslint/no-namespace */

import type { ExtendTheme } from '@pigment-css/react/theme';
import { NovaPigmentTheme, SxProps } from './types/theme';

declare module '@pigment-css/react/theme' {
  interface ThemeTokens extends NovaPigmentTheme {}

  interface ThemeArgs {
    theme: ExtendTheme<{
      colorScheme: 'light' | 'dark';
      tokens: ThemeTokens;
    }>;
  }
}

// Enable the sx prop on all HTML elements
declare global {
  namespace React {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    interface HTMLAttributes<T> {
      sx?: SxProps;
      ownerState?: any;
    }
    interface CSSProperties {
      [key: `--${string}`]: string | number;
    }
  }
}
