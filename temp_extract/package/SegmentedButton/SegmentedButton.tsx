'use client';

import * as React from 'react';
import { styled } from '@pigment-css/react';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import { SegmentedButtonOwnerState, SegmentedButtonProps } from './SegmentedButton.types';
import { getSegmentedButtonUtilityClass } from './SegmentedButton.classes';
import { segmentedButtonGroupClasses } from '../SegmentedButtonGroup';
import { ButtonBase } from '../ButtonBase';
import SegmentedButtonGroupContext from '../SegmentedButtonGroup/SegmentedButtonGroupContext';
import SegmentedButtonGroupButtonContext from '../SegmentedButtonGroup/SegmentedButtonGroupButtonContext';
import clsx from 'clsx';

// Determine if the segmented button value matches, or is contained in, the
// candidate group value.
export default function isValueSelected(value, candidate) {
  if (candidate === undefined || value === undefined) {
    return false;
  }
  if (Array.isArray(candidate)) {
    return candidate.includes(value);
  }
  return value === candidate;
}

const useUtilityClasses = (ownerState: SegmentedButtonOwnerState) => {
  const { selected, orientation } = ownerState;

  const slots = {
    root: ['root', selected && 'selected', orientation && `orientation-${orientation}`],
  };

  return composeClasses(slots, getSegmentedButtonUtilityClass, {});
};

const SegmentedButtonBase = styled(ButtonBase)<SegmentedButtonOwnerState>(({ theme }) => ({
  borderWidth: 1,
  borderStyle: 'solid',
  borderColor: theme.vars.palette.outline,
  color: theme.vars.palette.onSurface,
  '&:hover': {
    backgroundColor: `color-mix(in srgb, transparent, ${theme.vars.palette.secondary} ${theme.vars.palette.stateLayers.hoverSecondary})`,
  },
  '&:focus-visible': {
    outline: `2px solid ${theme.vars.palette.secondary}`,
    outlineOffset: 2,
    backgroundColor: `color-mix(in srgb, transparent, ${theme.vars.palette.secondary} ${theme.vars.palette.stateLayers.focusSecondary})`,
  },
  '&:active': {
    backgroundColor: `color-mix(in srgb, transparent, ${theme.vars.palette.secondary} ${theme.vars.palette.stateLayers.pressSecondary})`,
  },
  variants: [
    {
      props: { orientation: 'horizontal' },
      style: {
        // first SegmentedButton
        [`&.${segmentedButtonGroupClasses.firstButton}`]: {
          borderTopRightRadius: 0,
          borderBottomRightRadius: 0,
        },
        // middle SegmentedButtons
        [`&.${segmentedButtonGroupClasses.middleButton}`]: {
          borderLeft: 'none',
          borderRadius: 0,
        },
        // last SegmentedButton
        [`&.${segmentedButtonGroupClasses.lastButton}`]: {
          borderLeftWidth: 0,
          borderTopLeftRadius: 0,
          borderBottomLeftRadius: 0,
        },
      },
    },
    {
      props: { orientation: 'vertical' },
      style: {
        // first SegmentedButton
        [`&.${segmentedButtonGroupClasses.firstButton}`]: {
          borderBottomLeftRadius: 0,
          borderBottomRightRadius: 0,
        },
        // middle SegmentedButtons
        [`&.${segmentedButtonGroupClasses.middleButton}`]: {
          borderTop: 'none',
          borderRadius: 0,
        },
        // last SegmentedButton
        [`&.${segmentedButtonGroupClasses.lastButton}`]: {
          borderTop: 'none',
          borderTopLeftRadius: 0,
          borderTopRightRadius: 0,
        },
      },
    },
    {
      props: { selected: true },
      style: {
        backgroundColor: theme.vars.palette.secondaryContainer,
        color: theme.vars.palette.onSecondaryContainer,
        '&:hover': {
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.secondaryContainer}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.hoverOnSurface})`,
        },
        '&:focus-visible': {
          outline: `2px solid ${theme.vars.palette.secondary}`,
          outlineOffset: 2,
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.secondaryContainer}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.focusOnSurface})`,
        },
        '&:active': {
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.secondaryContainer}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.pressOnSurface})`,
        },
      },
    },
    {
      props: { disabled: true },
      style: {
        backgroundColor: `color-mix(in srgb, ${theme.vars.palette.backgroundDisabled}, ${theme.vars.palette.onBackgroundDisabled} ${theme.vars.palette.stateLayers.disabled})`,
        color: theme.vars.palette.onBackgroundDisabled,
        border: `1px solid ${theme.vars.palette.outlineDisabled}`,
      },
    },
  ],
}));

export const SegmentedButton = (props: SegmentedButtonProps) => {
  const { value: contextValue, orientation, ...contextProps } = React.useContext(SegmentedButtonGroupContext);
  const segmentedButtonGroupButtonContextPositionClassName = React.useContext(SegmentedButtonGroupButtonContext);

  const contextSelected = isValueSelected(props.value, contextValue);

  const mergedProps = { selected: contextSelected, orientation, ...contextProps, ...props };

  const {
    children,
    className,
    disabled = false,
    onClick,
    onChange,
    selected,
    size = 'medium',
    value,
    ...others
  } = mergedProps;

  const classes = useUtilityClasses({ selected, orientation });

  const handleChange = (event: React.MouseEvent<HTMLButtonElement>) => {
    if (onClick) {
      onClick(event, value);
      if (event.defaultPrevented) {
        return;
      }
    }

    if (onChange) {
      onChange(event, value);
    }
  };

  const positionClassName = segmentedButtonGroupButtonContextPositionClassName || '';

  return (
    <SegmentedButtonBase
      className={clsx(classes.root, className, positionClassName)}
      disabled={disabled}
      onClick={handleChange}
      onChange={onChange}
      selected={selected}
      size={size}
      value={value}
      orientation={orientation}
      {...others}
    >
      {children}
    </SegmentedButtonBase>
  );
};
