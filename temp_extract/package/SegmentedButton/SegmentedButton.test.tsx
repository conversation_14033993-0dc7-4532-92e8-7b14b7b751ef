import * as React from 'react';
import { render, screen, cleanup, fireEvent } from '@testing-library/react';
import { describe, expect, it, afterEach, vi } from 'vitest';
import { SegmentedButton } from './SegmentedButton';

afterEach(() => {
  cleanup();
});

describe('<SegmentedButton />', () => {
  it('should render with default slot classes', () => {
    render(<SegmentedButton>Click Me</SegmentedButton>);
    const button = screen.getByRole('button');
    expect(button).toHaveClass('NovaSegmentedButton-root');
    expect(button).toHaveClass('NovaButtonBase-sizeMedium');
  });

  it('should render with disabled slot class', () => {
    render(<SegmentedButton disabled>Click Me</SegmentedButton>);
    const button = screen.getByRole('button');
    expect(button).toHaveClass('Mui-disabled');
  });

  it('should render with small size slot class', () => {
    render(<SegmentedButton size="small">Click Me</SegmentedButton>);
    const button = screen.getByRole('button');
    expect(button).toHaveClass('NovaButtonBase-sizeSmall');
  });

  it('should render with large size slot class', () => {
    render(<SegmentedButton size="large">Click Me</SegmentedButton>);
    const button = screen.getByRole('button');
    expect(button).toHaveClass('NovaButtonBase-sizeLarge');
  });

  it('should render with startIcon slot class', () => {
    render(<SegmentedButton startIcon={<span data-testid={'startIcon'}>Icon</span>}>Click Me</SegmentedButton>);
    const icon = screen.getByTestId('startIcon');
    expect(icon).toBeInTheDocument();
  });

  it('should render with endIcon slot class', () => {
    render(<SegmentedButton endIcon={<span data-testid={'endIcon'}>Icon</span>}>Click Me</SegmentedButton>);
    const icon = screen.getByTestId('endIcon');
    expect(icon).toBeInTheDocument();
  });

  it('should render with selected slot class', () => {
    render(<SegmentedButton selected>Click Me</SegmentedButton>);
    const button = screen.getByRole('button');
    expect(button).toHaveClass('Mui-selected');
  });

  it('should render with custom slot class', () => {
    render(<SegmentedButton className="custom-class">Click Me</SegmentedButton>);
    const button = screen.getByRole('button');
    expect(button).toHaveClass('custom-class');
  });

  it('should pass `slotProps` down to slots', () => {
    const { container } = render(
      <SegmentedButton data-testid="root-segmented-button" slotProps={{ root: { className: 'custom-root' } }}>
        Click Me
      </SegmentedButton>,
    );
    expect(screen.getByTestId('root-segmented-button')).toBeVisible();
    expect(container.querySelector('.custom-root')).toHaveClass('NovaSegmentedButton-root');
  });

  it('handles click event', () => {
    const onClick = vi.fn();
    render(<SegmentedButton onClick={onClick}>Click Me</SegmentedButton>);
    const button = screen.getByRole('button');
    fireEvent.click(button);
    expect(onClick).toHaveBeenCalled();
  });

  it('handle onChange event', () => {
    const onChange = vi.fn();
    render(<SegmentedButton onChange={onChange} />);
    const button = screen.getByRole('button');
    fireEvent.click(button);
    expect(onChange).toHaveBeenCalled();
  });
});
