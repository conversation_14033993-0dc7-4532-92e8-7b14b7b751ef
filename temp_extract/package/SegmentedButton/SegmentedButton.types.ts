import { ButtonBaseProps } from '../ButtonBase';

export interface SegmentedButtonProps extends Omit<ButtonBaseProps, 'onChange' | 'onClick'> {
  /**
   * If `true`, the button is rendered in an active state.
   * @default false
   */
  selected?: boolean;
  /**
   * Callback fired when the state changes.
   *
   * @param {React.MouseEvent<HTMLElement>} event The event source of the callback.
   * @param {any} value of the selected button.
   */
  onChange?: (event: React.MouseEvent<HTMLElement>, value: any) => void;
  /**
   * Callback fired when the button is clicked.
   *
   * @param {React.MouseEvent<HTMLElement>} event The event source of the callback.
   * @param {any} value of the selected button.
   */
  onClick?: (event: React.MouseEvent<HTMLElement>, value: any) => void;
  /**
   * The value to associate with the button when selected in a SegmentedButtonGroup.
   */
  value?: any;
}

export interface SegmentedButtonOwnerState extends SegmentedButtonProps {
  orientation?: 'horizontal' | 'vertical';
}
