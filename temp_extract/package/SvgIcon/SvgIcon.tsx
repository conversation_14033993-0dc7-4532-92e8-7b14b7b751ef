'use client';
import * as React from 'react';
import { unstable_capitalize as capitalize, unstable_composeClasses as composeClasses } from '@mui/utils';
import { OverridableComponent } from '@mui/types';
import { SvgIconProps, SvgIconTypeMap, SvgIconOwnerState } from './SvgIcon.types';
import { getSvgIconUtilityClass } from './SvgIcon.classes';
import { styled } from '@pigment-css/react';
import useSlotProps from '@mui/utils/useSlotProps';
import { TypographyVariant } from '../Typography/Typography.types';
import { ColorPaletteProp, ColorPaletteSystem } from '../types/colorSystem';

const useUtilityClasses = (ownerState: SvgIconOwnerState) => {
  const { color, size } = ownerState;

  const slots = {
    root: ['root', color && color !== 'inherit' && `color${capitalize(color)}`, size && `size${capitalize(size)}`],
  };

  return composeClasses(slots, getSvgIconUtilityClass, {});
};

const SvgIconRoot = styled('svg')<SvgIconProps>(({ theme }) => ({
  userSelect: 'none',
  color: `var(--Icon-color, ${theme.vars.palette.onSurfaceVariant})`,
  width: '1em',
  height: '1em',
  fontSize: '1rem', // 16px
  display: 'inline-block',
  fill: 'currentColor',
  flexShrink: 0,
  variants: [
    ...(
      [
        'displayLarge',
        'displayMedium',
        'displaySmall',
        'headlineLarge',
        'headlineMedium',
        'headlineSmall',
        'titleLarge',
        'titleMedium',
        'titleSmall',
        'bodyLarge',
        'bodyMedium',
        'bodySmall',
        'labelLarge',
        'labelMedium',
        'labelSmall',
      ] as TypographyVariant[]
    ).map((fontSize: TypographyVariant) => ({
      props: { fontSize },
      style: {
        ...theme.typography[fontSize],
      },
    })),
    {
      props: { size: 'small' },
      style: {
        '@media (min-width: 0px)': {
          fontSize: 16,
        },
        '@media (min-width: 600px)': {
          fontSize: 16,
        },
        '@media (min-width: 1200px)': {
          fontSize: 16,
        },
        '@media (min-width: 1600px)': {
          fontSize: 16,
        },
      },
    },
    {
      props: { size: 'medium' },
      style: {
        '@media (min-width: 0px)': {
          fontSize: 16,
        },
        '@media (min-width: 600px)': {
          fontSize: 16,
        },
        '@media (min-width: 1200px)': {
          fontSize: 20,
        },
        '@media (min-width: 1600px)': {
          fontSize: 20,
        },
      },
    },
    {
      props: { size: 'large' },
      style: {
        '@media (min-width: 0px)': {
          fontSize: 20,
        },
        '@media (min-width: 600px)': {
          fontSize: 20,
        },
        '@media (min-width: 1200px)': {
          fontSize: 24,
        },
        '@media (min-width: 1600px)': {
          fontSize: 24,
        },
      },
    },
    {
      props: { fontSize: 'inherit' },
      style: {
        fontSize: 'inherit',
      },
    },
    {
      props: { color: 'inherit' },
      style: {
        color: 'inherit',
      },
    },
    ...(['warning', 'info', 'success'] as ColorPaletteProp[]).map((color: ColorPaletteProp) => ({
      props: { color },
      style: {
        backgroundColor: theme.vars.palette.system[color as ColorPaletteSystem],
        color: theme.vars.palette.system[`${capitalize(color)}` as ColorPaletteSystem],
      },
    })),
    {
      props: { color: 'error' },
      style: {
        backgroundColor: theme.vars.palette.errorContainer,
        color: theme.vars.palette.onErrorContainer,
      },
    },
  ],
}));

export const SvgIcon = React.forwardRef(function SvgIcon(props: SvgIconProps, ref: React.ForwardedRef<SVGElement>) {
  const {
    children,
    className,
    color,
    component = 'svg',
    size = 'medium',
    fontSize,
    htmlColor,
    inheritViewBox = false,
    titleAccess,
    viewBox = '0 0 24 24',
    slots = {},
    slotProps = {},
    ...other
  } = props;

  const hasSvgAsChild = React.isValidElement(children) && children.type === 'svg';

  const ownerState = {
    ...props,
    color,
    component,
    size,
    fontSize,
    inheritViewBox,
    viewBox,
    hasSvgAsChild,
  };

  const classes = useUtilityClasses(ownerState);
  const SlotRoot = slots.root ?? SvgIconRoot;

  const rootProps = useSlotProps({
    className: [classes.root, className],
    elementType: SvgIconRoot,
    externalSlotProps: slotProps.root,
    externalForwardedProps: other,
    ownerState,
    additionalProps: {
      ref,
      as: component,
      color: htmlColor,
      focusable: false,
      ...(titleAccess && { role: 'img' }),
      ...(!titleAccess && { 'aria-hidden': true }),
      ...(!inheritViewBox && { viewBox }),
      ...(hasSvgAsChild && (children.props as Omit<React.SVGProps<SVGSVGElement>, 'ref'>)),
    },
  });

  return (
    <SlotRoot {...rootProps}>
      {hasSvgAsChild ? (children.props as React.SVGProps<SVGSVGElement>).children : children}
      {titleAccess ? <title>{titleAccess}</title> : null}
    </SlotRoot>
  );
}) as OverridableComponent<SvgIconTypeMap>;
