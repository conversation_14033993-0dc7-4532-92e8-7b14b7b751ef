import * as React from 'react';
import { expect, it, describe, beforeAll, afterEach } from 'vitest';
import { render, cleanup } from '@testing-library/react';
import { unstable_capitalize as capitalize } from '@mui/utils';
import { SvgIcon } from './SvgIcon';
import { SvgIconProps } from './SvgIcon.types';
import classes, { SvgIconClassKey } from './SvgIcon.classes';

afterEach(() => {
  cleanup();
});

describe('<SvgIcon />', () => {
  let path: any;

  beforeAll(() => {
    path = <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z" data-testid="test-path" />;
  });

  it('renders children by default', () => {
    const { container, queryByTestId } = render(<SvgIcon>{path}</SvgIcon>);

    expect(queryByTestId('test-path')).not.to.equal(null);
    expect(container.firstChild).toHaveAttribute('aria-hidden', 'true');
  });

  it('renders children of provided svg and merge the props', () => {
    const { container } = render(
      <SvgIcon>
        <svg viewBox="0 0 48 48" strokeWidth="1.5">
          {path}
        </svg>
      </SvgIcon>,
    );

    expect(container.firstChild).toHaveAttribute('viewBox', '0 0 48 48');
    expect(container.firstChild).toHaveAttribute('stroke-width', '1.5');
  });

  describe('prop: titleAccess', () => {
    it('should be able to make an icon accessible', () => {
      const { container, queryByText } = render(<SvgIcon titleAccess="Network">{path}</SvgIcon>);

      expect(queryByText('Network')).not.to.equal(null);
      expect(container.firstChild).not.toHaveAttribute('aria-hidden');
    });
  });

  describe('prop: color', () => {
    it('should render with the user and SvgIcon classes', () => {
      const { container } = render(<SvgIcon className="meow">{path}</SvgIcon>);
      expect(container.firstChild).toHaveClass('meow');
    });

    (['neutral', 'error', 'info', 'warning'] as const).forEach((color) => {
      it(`should render ${color}`, () => {
        const { container } = render(<SvgIcon color={color}>{path}</SvgIcon>);

        expect(container.firstChild).toHaveClass(classes[`color${capitalize(color)}` as SvgIconClassKey]);
      });
    });
  });

  describe('prop: size', function test() {
    it('should render with `medium` by default', () => {
      const { container } = render(<SvgIcon>{path}</SvgIcon>);

      expect(container.firstChild).toHaveClass(classes.sizeMedium);
    });
  });

  describe('prop: inheritViewBox', () => {
    function CustomSvg(props: SvgIconProps) {
      return (
        <svg viewBox="-4 -4 24 24" {...props}>
          {path}
        </svg>
      );
    }

    it('should render with the default viewBox if neither inheritViewBox nor viewBox are provided', () => {
      const { container } = render(<SvgIcon component={CustomSvg} />);
      expect(container.firstChild).toHaveAttribute('viewBox', '0 0 24 24');
    });

    it('should render with given viewBox if inheritViewBox is not provided', () => {
      const { container } = render(<SvgIcon component={CustomSvg} viewBox="0 0 30 30" />);
      expect(container.firstChild).toHaveAttribute('viewBox', '0 0 30 30');
    });

    it("should use the custom component's viewBox if true", () => {
      const { container } = render(<SvgIcon component={CustomSvg} inheritViewBox />);
      expect(container.firstChild).toHaveAttribute('viewBox', '-4 -4 24 24');
    });
  });
});
