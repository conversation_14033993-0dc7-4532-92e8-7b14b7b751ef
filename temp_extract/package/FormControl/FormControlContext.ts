import * as React from 'react';
import { FormControlProps } from './FormControl.types';

/**
 * @internal
 */
export type FormControlContextValue =
  | undefined
  | (Pick<FormControlProps, 'error' | 'disabled' | 'required' | 'size' | 'fullWidth' | 'value' | 'onChange'> & {
      /**
       * id for FormLabel.
       */
      labelId: string;
      /**
       * id for Input.
       */
      htmlFor: string | undefined;
      /**
       * If `true`, the form element has some value.
       */
      filled: boolean;
      /**
       * If `true`, the form element is focused and not disabled.
       */
      focused: boolean;
      /**
       * Callback fired when the form element has lost focus.
       */
      onBlur: () => void;
      /**
       * Call<PERSON> fired when the form element receives focus.
       */
      onFocus: () => void;
    });

const FormControlContext = React.createContext<FormControlContextValue>(undefined);

if (process.env.NODE_ENV !== 'production') {
  FormControlContext.displayName = 'FormControlContext';
}

export function useFormControlContext(): FormControlContextValue | undefined {
  return React.useContext(FormControlContext);
}

export default FormControlContext;
