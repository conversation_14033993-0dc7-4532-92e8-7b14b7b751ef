import '@testing-library/jest-dom/vitest';
import { render, screen, cleanup, fireEvent } from '@testing-library/react';
import React, { useEffect } from 'react';
import { describe, expect, it, afterEach, vi } from 'vitest';
import { FormControl } from './FormControl';
import { useFormControlContext } from './FormControlContext';

afterEach(() => {
  cleanup();
});

describe('FormControl', () => {
  it('should render normal', () => {
    render(<FormControl data-testid="NovaFormControl-root" />);
    expect(screen.getByTestId('NovaFormControl-root')).toHaveClass('NovaFormControl-root');
    expect(screen.getByTestId('NovaFormControl-root')).toHaveClass('NovaFormControl-sizeMedium');
  });

  it('should render error state', () => {
    render(<FormControl data-testid="NovaFormControl-root" error />);
    expect(screen.getByTestId('NovaFormControl-root')).toHaveClass('Mui-error');
  });

  it('should render disabled state', () => {
    render(<FormControl data-testid="NovaFormControl-root" disabled />);
    expect(screen.getByTestId('NovaFormControl-root')).toHaveClass('Mui-disabled');
  });

  it('should render fullWidth state', () => {
    render(<FormControl data-testid="NovaFormControl-root" fullWidth />);
    expect(screen.getByTestId('NovaFormControl-root')).toHaveClass('NovaFormControl-fullWidth');
  });

  it('should render medium size', () => {
    render(<FormControl data-testid="NovaFormControl-root" size="medium" />);
    expect(screen.getByTestId('NovaFormControl-root')).toHaveClass('NovaFormControl-sizeMedium');
  });

  it('should render small size', () => {
    render(<FormControl data-testid="NovaFormControl-root" size="small" />);
    expect(screen.getByTestId('NovaFormControl-root')).toHaveClass('NovaFormControl-sizeSmall');
  });

  it('should render large size', () => {
    render(<FormControl data-testid="NovaFormControl-root" size="large" />);
    expect(screen.getByTestId('NovaFormControl-root')).toHaveClass('NovaFormControl-sizeLarge');
  });

  it('should FormControlContext work well', () => {
    const onChange = vi.fn();
    function TestComponent() {
      const context = useFormControlContext();
      return <input value={context!.value as string} onChange={context!.onChange} />;
    }
    render(
      <FormControl data-testid="NovaFormControl-root" onChange={onChange}>
        <TestComponent />
      </FormControl>,
    );
    expect(screen.getByRole('textbox')).to.have.property('value', '');

    fireEvent.change(screen.getByRole('textbox'), { target: { value: '12' } });
    expect(onChange).toBeCalled();
  });

  it('should FormControlContext work well', () => {
    const onChangeVi = vi.fn();
    function TestComponent({ onContextChange }: any) {
      const context = useFormControlContext();
      useEffect(() => {
        onContextChange?.(context);
      }, [context, onContextChange]);
      return <input value={context!.value as string} onChange={context!.onChange} />;
    }
    render(
      <FormControl data-testid="NovaFormControl-root" error disabled fullWidth required size="large">
        <TestComponent onContextChange={onChangeVi} />
      </FormControl>,
    );
    expect(onChangeVi).toBeCalledWith(
      expect.objectContaining({
        error: true,
        disabled: true,
        fullWidth: true,
        size: 'large',
        value: '',
        filled: false,
        focused: false,
      }),
    );

    fireEvent.change(screen.getByRole('textbox'), { target: { value: '12' } });

    expect(onChangeVi).toHaveBeenLastCalledWith(expect.objectContaining({ filled: true }));
  });
});
