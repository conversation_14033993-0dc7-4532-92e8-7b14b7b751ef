import * as React from 'react';
import { OverrideProps } from '@mui/types';
import { SlotProps, CreateSlotsAndSlotProps } from '../types/slot';

export type FormControlSlot = 'root';
export type NativeFormControlElement = HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement;

export interface FormControlSlots {
  /**
   * The component that renders the root.
   * @default 'div'
   */
  root?: React.ElementType;
}

export type FormControlSlotsAndSlotProps = CreateSlotsAndSlotProps<
  FormControlSlots,
  {
    root: SlotProps<'div', object, FormControlOwnerState>;
  }
>;

export interface FormControlTypeMap<P = object, D extends React.ElementType = 'div'> {
  props: P & {
    /**
     * The content of the component.
     */
    children?: React.ReactNode;
    /**
     * If `true`, the children are in disabled state.
     * @default false
     */
    disabled?: boolean;
    /**
     * If `true`, the children will indicate an error.
     * @default false
     */
    error?: boolean;
    /**
     * If `true`, the user must specify a value for the input before the owning form can be submitted.
     * If `true`, the asterisk appears on the FormLabel.
     * @default false
     */
    required?: boolean;
    /**
     * The size of the component.
     * @default 'medium'
     */
    size?: 'small' | 'medium' | 'large';
    /**
     * If `true`, the component will take up the full width of its container.
     * @default false
     */
    fullWidth?: boolean;
    defaultValue?: unknown;
    /**
     * The value of the form element.
     */
    value?: unknown;
    /**
     * Callback fired when the form element's value is modified.
     */
    onChange?: React.ChangeEventHandler<NativeFormControlElement>;
  } & FormControlSlotsAndSlotProps;
  defaultComponent: D;
}

export type FormControlProps<
  D extends React.ElementType = FormControlTypeMap['defaultComponent'],
  P = { component?: React.ElementType },
> = OverrideProps<FormControlTypeMap<P, D>, D>;

export interface FormControlOwnerState extends FormControlProps {}
