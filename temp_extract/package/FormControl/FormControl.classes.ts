import generateUtilityClasses from '@mui/utils/generateUtilityClasses';
import generateUtilityClass from '@mui/utils/generateUtilityClass';

export interface FormControlClasses {
  /** Class name applied to the root element. */
  root: string;
  /** State class applied to the root element if `error={true}`. */
  error: string;
  /** Class name applied to the root element if `disabled={true}`. */
  disabled: string;
  /** Class name applied to the root element if `fullWidth={true}`. */
  fullWidth: string;

  /** Styles applied to the root element if `size="small"`. */
  sizeSmall: string;
  /** Styles applied to the root element if `size="medium"`. */
  sizeMedium: string;
  /** Styles applied to the root element if `size="large"`. */
  sizeLarge: string;
}

export type FormControlClassKey = keyof FormControlClasses;

export function getFormControlUtilityClass(slot: string): string {
  return generateUtilityClass('NovaFormControl', slot);
}

const formControlClasses: FormControlClasses = generateUtilityClasses('NovaFormControl', [
  'root',
  'error',
  'disabled',
  'fullWidth',
  'sizeSmall',
  'sizeMedium',
  'sizeLarge',
]);

export default formControlClasses;
