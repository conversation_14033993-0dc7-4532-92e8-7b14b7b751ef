'use client';
import * as React from 'react';
import { OverridableComponent } from '@mui/types';
import {
  unstable_useId as useId,
  unstable_capitalize as capitalize,
  unstable_composeClasses as composeClasses,
} from '@mui/utils';
import useSlotProps from '@mui/utils/useSlotProps';
import useControlled from '@mui/utils/useControlled';
import { styled } from '@pigment-css/react';
import FormControlContext from './FormControlContext';
import { getFormControlUtilityClass } from './FormControl.classes';
import {
  FormControlProps,
  FormControlOwnerState,
  FormControlTypeMap,
  NativeFormControlElement,
} from './FormControl.types';

function hasValue(value: unknown) {
  return value != null && !(Array.isArray(value) && value.length === 0) && value !== '';
}

const useUtilityClasses = (ownerState: FormControlOwnerState) => {
  const { disabled, error, size, fullWidth } = ownerState;
  const slots = {
    root: [
      'root',
      disabled && 'disabled',
      error && 'error',
      fullWidth && 'fullWidth',
      size && `size${capitalize(size)}`,
    ],
  };

  return composeClasses(slots, getFormControlUtilityClass, {});
};

export const FormControlRoot = styled('div')<FormControlProps>(({ theme }) => ({
  display: 'inline-flex',
  flexDirection: 'column',
  position: 'relative',
  gap: '4px',
  variants: [
    {
      props: { fullWidth: true },
      style: {
        width: '100%',
      },
    },
    {
      props: { size: 'large' },
      style: { gap: '8px' },
    },
  ],
}));

// eslint-disable-next-line react/display-name
export const FormControl = React.forwardRef((props: FormControlProps, ref: React.ForwardedRef<Element>) => {
  const defaultProps = {
    disabled: false,
    required: false,
    error: false,
    size: 'medium' as const,
    fullWidth: false,
  };
  const overrideProps = { ...defaultProps, ...props };
  const {
    id: idOverride,
    className,
    component,
    disabled,
    required,
    error,
    size,
    fullWidth,
    value: incomingValue,
    defaultValue,
    onChange,
    slots = {},
    slotProps = {},
    ...rest
  } = overrideProps;

  const id = useId(idOverride);
  const [focusedState, setFocused] = React.useState(false);
  const focused = focusedState && !disabled;
  const [value, setValue] = useControlled({
    controlled: incomingValue,
    default: defaultValue,
    name: 'FormControl',
    state: 'value',
  });

  const filled = hasValue(value);

  const classes = useUtilityClasses(overrideProps);

  const SlotRoot = slots.root ?? FormControlRoot;

  const slotRootProps = useSlotProps({
    elementType: SlotRoot,
    externalSlotProps: slotProps.root,
    externalForwardedProps: rest,
    additionalProps: {
      ref,
      as: component,
    },
    ownerState: overrideProps,
    className: [classes.root, className],
  });

  const formControlContextValue = React.useMemo(
    () => ({
      filled,
      disabled,
      required,
      error,
      size,
      fullWidth,
      htmlFor: id,
      labelId: `${id}-label`,
      focused,
      onBlur: () => {
        setFocused(false);
      },
      onChange: (event: React.ChangeEvent<NativeFormControlElement>) => {
        setValue(event.target.value);
        onChange?.(event);
      },
      onFocus: () => {
        setFocused(true);
      },
      value: value ?? '',
    }),
    [filled, disabled, required, error, size, fullWidth, id, focused, value, setValue, onChange],
  );

  return (
    <FormControlContext.Provider value={formControlContextValue}>
      <SlotRoot {...slotRootProps} />
    </FormControlContext.Provider>
  );
}) as OverridableComponent<FormControlTypeMap>;
