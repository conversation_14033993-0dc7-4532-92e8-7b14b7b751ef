import { CreateSlotsAndSlotProps, SlotProps } from '../types/slot';
import { OverrideProps } from '@mui/types';
import { SxProps } from '../types/theme';

export type ListItemDecoratorSlot = 'root';

export interface ListItemDecoratorSlots {
  /**
   * The component that renders the root.
   * @default 'span'
   */
  root?: React.ElementType;
}

export type ListItemDecoratorSlotsAndSlotProps = CreateSlotsAndSlotProps<
  ListItemDecoratorSlots,
  {
    root: SlotProps<'span', object, ListItemDecoratorOwnerState>;
  }
>;

export interface ListItemDecoratorTypeMap<P = object, D extends React.ElementType = 'span'> {
  props: P &
    ListItemDecoratorSlotsAndSlotProps & {
      /**
       * The content of the component.
       */
      children?: React.ReactNode;
      /**
       * The system prop that allows defining system overrides as well as additional CSS styles.
       */
      sx?: SxProps;
    };
  defaultComponent: D;
}

export type ListItemDecoratorProps<
  D extends React.ElementType = ListItemDecoratorTypeMap['defaultComponent'],
  P = {
    component?: React.ElementType;
  },
> = OverrideProps<ListItemDecoratorTypeMap<P, D>, D>;

export interface ListItemDecoratorOwnerState extends ListItemDecoratorProps {}
