import {
  unstable_generateUtilityClasses as generateUtilityClasses,
  unstable_generateUtilityClass as generateUtilityClass,
} from '@mui/utils';

export interface ListItemDecoratorClasses {
  /** Styles applied to the root element. */
  root: string;
}

export type ListItemDecoratorClassKey = keyof ListItemDecoratorClasses;

export function getListItemDecoratorUtilityClass(slot: string): string {
  return generateUtilityClass('NovaListItemDecorator', slot);
}

const listItemDecoratorClasses: ListItemDecoratorClasses = generateUtilityClasses('NovaListItemDecorator', ['root']);

export default listItemDecoratorClasses;
