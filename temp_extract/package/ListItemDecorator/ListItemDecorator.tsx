'use client';
import * as React from 'react';
import { styled } from '@pigment-css/react';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import useSlotProps from '@mui/utils/useSlotProps';
import { ListItemDecoratorOwnerState, ListItemDecoratorProps } from './ListItemDecorator.types';
import { getListItemDecoratorUtilityClass } from './ListItemDecorator.classes';
import ListItemStatusContext from '../ListItem/ListItemStatusContext';

const useUtilityClasses = (ownerState: ListItemDecoratorOwnerState) => {
  const slots = {
    root: ['root'],
  };

  const composedClasses = composeClasses(slots, getListItemDecoratorUtilityClass, {});
  return composedClasses;
};

const ListItemDecoratorRoot = styled('span')<ListItemDecoratorProps>(({ theme }) => ({
  boxSizing: 'border-box',
  display: 'inline-flex',
  alignItems: 'center',
  minWidth: 'var(--nova-listItemDecorator-size)',
  justifyContent: 'center',
  color: 'var(--nova-listItem-secondaryColor)',
  gap: 'var(--nova-listItemDecorator-gap)',
  fontSize: 'var(--nova-listItem-secondary-fontsize)',
}));

export const ListItemDecorator = React.forwardRef(function ListItemDecorator(
  props: ListItemDecoratorProps,
  ref: React.ForwardedRef<Element>,
) {
  const { children, component, className, slots = {}, slotProps = {}, ...other } = props;

  const { disabled = false, checked = false } = React.useContext(ListItemStatusContext);

  const ownerState = {
    ...props,
  };

  const classes = useUtilityClasses(ownerState);
  const SlotRoot = slots.root ?? ListItemDecoratorRoot;
  const rootProps = useSlotProps({
    additionalProps: {
      ref,
      as: component,
    },
    className: [classes.root, className],
    elementType: ListItemDecoratorRoot,
    externalSlotProps: slotProps.root,
    externalForwardedProps: other,
    ownerState,
  });

  return (
    <SlotRoot {...rootProps}>
      {React.Children.map(children, (child) =>
        React.isValidElement(child) ? React.cloneElement(child, { disabled } as Record<string, boolean>) : child,
      )}
    </SlotRoot>
  );
});
