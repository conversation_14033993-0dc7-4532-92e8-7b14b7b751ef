import React from 'react';
import '@testing-library/jest-dom/vitest';
import { screen, render } from '@testing-library/react';
import { expect, describe, it } from 'vitest';
import classes from './ListItemDecorator.classes';
import { ListItemDecorator } from './ListItemDecorator';

describe('<ListItemItem />', () => {
  it('should have root className', () => {
    const { container } = render(<ListItemDecorator />);
    expect(container.firstChild).toHaveClass(classes.root);
  });

  it('should accept className prop', () => {
    const { container } = render(<ListItemDecorator className="foo-bar" />);
    expect(container.firstChild).toHaveClass('foo-bar');
  });
});
