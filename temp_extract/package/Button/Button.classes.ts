import {
  unstable_generateUtilityClasses as generateUtilityClasses,
  unstable_generateUtilityClass as generateUtilityClass,
} from '@mui/utils';

export interface ButtonClasses {
  /** Styles applied to the root element. */
  root: string;
  /** Styles applied to the Button if `color="primary"`. */
  colorPrimary: string;
  /** Styles applied to the Button if `color="error"`. */
  colorError: string;

  /** Styles applied to the Button if `variant="filled"`. */
  filled: string;
  /** Styles applied to the Button if `variant="outlined"`. */
  outlined: string;
  /** Styles applied to the Button if `variant="text"`. */
  text: string;
}

export type ButtonClassKey = keyof ButtonClasses;

export function getButtonUtilityClass(slot: string): string {
  return generateUtilityClass('NovaButton', slot);
}

const buttonClasses: ButtonClasses = generateUtilityClasses('NovaButton', [
  'root',
  'colorPrimary',
  'colorError',
  'filled',
  'outlined',
  'text',
]);

export default buttonClasses;
