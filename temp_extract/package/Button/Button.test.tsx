import React from 'react';
import { Button } from './Button';
import { render, screen, cleanup } from '@testing-library/react';
import { describe, expect, it, afterEach } from 'vitest';

afterEach(() => {
  cleanup();
});
describe('Button', () => {
  it('should render with default slot classes', () => {
    render(<Button>Click Me</Button>);
    const button = screen.getByRole('button');
    expect(button).toHaveClass('NovaButton-root');
  });

  it('should render with disabled slot class', () => {
    render(<Button disabled>Click Me</Button>);
    const button = screen.getByRole('button');
    expect(button).toHaveClass('Mui-disabled');
  });

  it('should render with variant slot class', () => {
    render(<Button variant="outlined">Click Me</Button>);
    const button = screen.getByRole('button');
    expect(button).toHaveClass('NovaButton-outlined');
  });

  it('should render with color slot class', () => {
    render(<Button color="error">Click Me</Button>);
    const button = screen.getByRole('button');
    expect(button).toHaveClass('NovaButton-colorError');
  });

  it('should render with size slot class', () => {
    render(<Button size="large">Click Me</Button>);
    const button = screen.getByRole('button');
    expect(button).toHaveClass('NovaButtonBase-sizeLarge');
  });

  it('should render with startIcon slot class', () => {
    render(<Button startIcon={<span data-testid={'startIcon'}>Icon</span>}>Click Me</Button>);
    const icon = screen.getByTestId('startIcon');
    expect(icon).toBeInTheDocument();
  });

  it('should render with endIcon slot class', () => {
    render(<Button endIcon={<span data-testid={'endIcon'}>Icon</span>}>Click Me</Button>);
    const icon = screen.getByTestId('endIcon');
    expect(icon).toBeInTheDocument();
  });
});
