import { ButtonBaseProps } from '../ButtonBase';

export type ButtonVariant = 'filled' | 'outlined' | 'text';
export type ButtonColor = 'primary' | 'error';

export interface ButtonProps extends ButtonBaseProps {
  /**
   * The color scheme used by the Button
   * @default 'primary'
   */
  color?: ButtonColor;
  /**
   * The style of button to use
   * @default 'filled'
   */
  variant?: ButtonVariant;
}
