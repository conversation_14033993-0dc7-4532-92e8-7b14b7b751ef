'use client';
import * as React from 'react';
import {
  unstable_capitalize as capitalize,
  unstable_composeClasses as composeClasses,
  unstable_useForkRef as useForkRef,
  unstable_useIsFocusVisible as useIsFocusVisible,
} from '@mui/utils';
import { getLinkUtilityClass } from './Link.classes';
import { styled } from '@pigment-css/react';
import useSlotProps from '@mui/utils/useSlotProps';
import { LinkOwnerState, LinkProps } from './Link.types';
import { TypographyVariant } from '../Typography/Typography.types';

const useUtilityClasses = (ownerState: LinkOwnerState) => {
  const { variant, focusVisible, underline, disabled } = ownerState;

  const slots = {
    root: [
      'root',
      disabled && 'disabled',
      variant,
      underline && `underline${capitalize(underline)}`,
      focusVisible && 'focusVisible',
    ],
    startDecorator: ['startDecorator'],
    endDecorator: ['endDecorator'],
  };

  return composeClasses(slots, getLinkUtilityClass, {});
};

const StartDecorator = styled('span')<LinkProps>(() => ({
  display: 'inline-flex',
  float: 'left',
  marginInlineEnd: 'clamp(4px, 0.375em, 0.75rem)',
  '@media (min-width: 0px)': {
    fontSize: 20,
  },
  '@media (min-width: 600px)': {
    fontSize: 20,
  },
  '@media (min-width: 1200px)': {
    fontSize: 24,
  },
  '@media (min-width: 1600px)': {
    fontSize: 24,
  },
  variants: [
    {
      props: (ownerState) =>
        typeof ownerState.startDecorator !== 'string' && (ownerState.sx as any)?.alignItems === 'flex-start',
      style: { marginTop: '2px' },
    },
  ],
}));

const EndDecorator = styled('span')<LinkProps>(() => ({
  display: 'inline-flex',
  float: 'right',
  marginInlineStart: 'clamp(4px, 0.25em, 0.5rem)', // for end decorator, 0.25em looks better.
  '@media (min-width: 0px)': {
    fontSize: 20,
  },
  '@media (min-width: 600px)': {
    fontSize: 20,
  },
  '@media (min-width: 1200px)': {
    fontSize: 24,
  },
  '@media (min-width: 1600px)': {
    fontSize: 24,
  },
  variants: [
    {
      props: (ownerState) => (ownerState.sx as any)?.alignItems === 'flex-start',
      style: { marginTop: '2px' },
    },
  ],
}));

const LinkRoot = styled('a')<LinkProps>(({ theme }) => ({
  '--Icon-color': 'currentColor',
  textDecorationThickness: 'max(0.08em, 1px)', // steal from https://moderncss.dev/modern-css-for-dynamic-component-based-architecture/#css-reset-additions
  textUnderlineOffset: '0.15em', // steal from https://moderncss.dev/modern-css-for-dynamic-component-based-architecture/#css-reset-additions
  display: 'inline-flex',
  alignItems: 'center',
  WebkitTapHighlightColor: 'transparent',
  backgroundColor: 'transparent',
  outline: 0,
  border: 0,
  margin: 0,
  padding: 0,
  cursor: 'pointer',
  userSelect: 'none' as const,
  color: theme.vars.palette.primary,
  letterSpacing: 0,
  MozAppearance: 'none', // Reset
  WebkitAppearance: 'none', // Reset
  position: 'relative',
  '&:focus-visible': {
    borderRadius: '8px',
    outlineOffset: 2,
    outline: `2px solid ${theme.vars.palette.secondary}`,
  },
  '&::-moz-focus-inner': {
    borderStyle: 'none', // Remove Firefox dotted outline.
  },
  '&:hover': {
    '@media (hover: hover)': {
      color: theme.vars.palette.addOn.primaryFixedDim,
    },
  },
  '&:active': {
    color: theme.vars.palette.addOn.primaryFixedDim,
  },
  ...theme.typography.labelMedium,
  variants: [
    ...(
      [
        'displayLarge',
        'displayMedium',
        'displaySmall',
        'headlineLarge',
        'headlineMedium',
        'headlineSmall',
        'titleLarge',
        'titleMedium',
        'titleSmall',
        'bodyLarge',
        'bodyMedium',
        'bodySmall',
        'labelLarge',
        'labelMedium',
        'labelSmall',
      ] as TypographyVariant[]
    ).map((variant: TypographyVariant) => ({
      props: { variant },
      style: {
        ...theme.typography[`${variant}`],
      },
    })),
    {
      props: (props) => Boolean(props.variant && props.variant === 'inherit'),
      style: {
        fontSize: 'inherit',
        lineHeight: 'inherit',
      },
    },
    {
      props: (props) => Boolean(props.startDecorator !== undefined),
      style: { verticalAlign: 'bottom' },
    },
    {
      props: { underline: 'none' },
      style: { textDecoration: 'none' },
    },
    {
      props: { underline: 'hover' },
      style: {
        textDecoration: 'none',
        '&:hover': {
          '@media (hover: hover)': {
            textDecorationLine: 'underline',
          },
        },
      },
    },
    {
      props: { underline: 'always' },
      style: {
        textDecoration: 'underline',
      },
    },
    {
      props: { component: 'button' },
      style: {
        userSelect: 'none',
      },
    },
    {
      props: { disabled: true },
      style: {
        pointerEvents: 'none',
        cursor: 'not-allowed',
        color: theme.vars.palette.onBackgroundDisabled,
      },
    },
  ],
}));

export const Link = React.forwardRef(function Link(props: LinkProps, ref: React.ForwardedRef<Element>) {
  const {
    children,
    disabled = false,
    onBlur,
    onFocus,
    underline = 'always',
    variant = 'labelMedium',
    endDecorator,
    startDecorator,
    component,
    slots = {},
    slotProps = {},
    ...other
  } = props;

  const {
    isFocusVisibleRef,
    onBlur: handleBlurVisible,
    onFocus: handleFocusVisible,
    ref: focusVisibleRef,
  } = useIsFocusVisible();
  const [focusVisible, setFocusVisible] = React.useState(false);

  if (disabled && focusVisible) {
    setFocusVisible(false);
  }
  const handleRef = useForkRef(ref, focusVisibleRef);
  const handleBlur = (event: React.FocusEvent<HTMLAnchorElement>) => {
    handleBlurVisible(event);
    if (isFocusVisibleRef.current === false) {
      setFocusVisible(false);
    }
    if (onBlur) {
      onBlur(event);
    }
  };
  const handleFocus = (event: React.FocusEvent<HTMLAnchorElement>) => {
    handleFocusVisible(event);
    if (isFocusVisibleRef.current === true) {
      setFocusVisible(true);
    }
    if (onFocus) {
      onFocus(event);
    }
  };

  const ownerState = {
    ...props,
    disabled,
    focusVisible,
    variant,
    underline,
  };

  const classes = useUtilityClasses(ownerState);
  const SlotRoot = slots.root ?? LinkRoot;
  const SlotStartDecorator = slots.startDecorator ?? StartDecorator;
  const SlotEndDecorator = slots.endDecorator ?? EndDecorator;
  const rootProps = useSlotProps({
    additionalProps: {
      onBlur: handleBlur,
      onFocus: handleFocus,
      ref: handleRef,
      as: component,
    },
    className: classes.root,
    elementType: LinkRoot,
    externalSlotProps: slotProps.root,
    externalForwardedProps: other,
    ownerState,
  });

  const startDecoratorProps = useSlotProps({
    className: classes.startDecorator,
    elementType: StartDecorator,
    externalSlotProps: slotProps.startDecorator,
    ownerState,
  });

  const endDecoratorProps = useSlotProps({
    className: classes.endDecorator,
    externalSlotProps: slotProps.endDecorator,
    elementType: EndDecorator,
    ownerState,
  });

  return (
    <SlotRoot {...rootProps}>
      {startDecorator && <SlotStartDecorator {...startDecoratorProps}>{startDecorator}</SlotStartDecorator>}
      {children}
      {endDecorator && <SlotEndDecorator {...endDecoratorProps}>{endDecorator}</SlotEndDecorator>}
    </SlotRoot>
  );
});
