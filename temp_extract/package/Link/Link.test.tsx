import React from 'react';
import '@testing-library/jest-dom/vitest';
import { render, fireEvent, cleanup } from '@testing-library/react';
import { afterEach, expect, describe, it, vi } from 'vitest';
import { unstable_capitalize as capitalize } from '@mui/utils';
import { Link } from './Link';
import classes, { LinkClassKey } from './Link.classes';
import { Typography } from '../Typography/Typography';

afterEach(() => {
  cleanup();
});

describe('<Link />', () => {
  it('should render children', () => {
    const { queryByText } = render(<Link href="/">Home</Link>);

    expect(queryByText('Home')).not.to.equal(null);
  });

  describe('event callbacks', () => {
    it('should fire event callbacks', () => {
      const events = ['onBlur', 'onFocus'];

      const handlers = events.reduce((result, n) => {
        result[n] = vi.fn();
        return result;
      }, {});

      const { container } = render(
        <Link href="/" {...handlers}>
          Home
        </Link>,
      );
      const anchor = container.querySelector('a');

      events.forEach((n) => {
        const event = (n.charAt(2).toLowerCase() + n.slice(3)) as keyof typeof fireEvent;
        (fireEvent as any)[event](anchor);
        expect(handlers[n]).toBeCalledTimes(1);
      });
    });
  });

  describe('prop: underline', () => {
    it('always by default', () => {
      const { getByTestId } = render(
        <Link href="/" data-testid="root">
          Hello World
        </Link>,
      );

      expect(getByTestId('root')).toHaveClass(classes.underlineAlways);
    });

    (['none', 'always', 'hover'] as const).forEach((underline) => {
      it(`should render ${underline}`, () => {
        const { getByTestId } = render(
          <Link href="/" data-testid="root" underline={underline}>
            Hello World
          </Link>,
        );

        expect(getByTestId('root')).toHaveClass(classes[`underline${capitalize(underline)}` as LinkClassKey]);
      });
    });
  });

  describe('Typography', () => {
    it('should be a span by default', () => {
      const { container } = render(
        <Link href="/">
          hello <Typography>test</Typography>
        </Link>,
      );
      expect(container.querySelector('p')).toHaveTextContent('test');
    });
  });
});
