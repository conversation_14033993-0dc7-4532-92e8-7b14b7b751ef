import * as React from 'react';
import { CreateSlotsAndSlotProps, SlotProps } from '../types/slot';
import { OverrideProps } from '@mui/types';
import { SxProps } from '../types/theme';
import { TypographyProps } from '../Typography';

export interface LinkSlots {
  /**
   * The component that renders the root.
   * @default 'a'
   */
  root?: React.ElementType;
  /**
   * The component that renders the start decorator.
   * @default 'span'
   */
  startDecorator?: React.ElementType;
  /**
   * The component that renders the end decorator.
   * @default 'span'
   */
  endDecorator?: React.ElementType;
}

export interface LinkPropsColorOverrides {}

export type LinkSlotsAndSlotProps = CreateSlotsAndSlotProps<
  LinkSlots,
  {
    root: SlotProps<'a', object, LinkOwnerState>;
    startDecorator: SlotProps<'span', object, LinkOwnerState>;
    endDecorator: SlotProps<'span', object, LinkOwnerState>;
  }
>;

export interface LinkTypeMap<P = object, D extends React.ElementType = 'a'> {
  props: P & {
    /**
     * The content of the component.
     */
    children?: React.ReactNode;
    /**
     * If `true`, the component is disabled.
     * @default false
     */
    disabled?: boolean;
    /**
     * Element placed before the children.
     */
    startDecorator?: React.ReactNode;
    /**
     * Element placed after the children.
     */
    endDecorator?: React.ReactNode;
    /**
     * The system prop that allows defining system overrides as well as additional CSS styles.
     */
    sx?: SxProps;
    /**
     * Controls when the link should have an underline.
     * @default 'always'
     */
    underline?: 'none' | 'hover' | 'always';
    /**
     * Applies the theme typography styles.
     * @default 'labelMedium'
     */
    variant?: TypographyProps['variant'];
  } & LinkSlotsAndSlotProps;
  defaultComponent: D;
}

export type LinkProps<
  D extends React.ElementType = LinkTypeMap['defaultComponent'],
  P = {
    component?: React.ElementType;
    focusVisible?: boolean;
  },
> = OverrideProps<LinkTypeMap<P, D>, D>;

export interface LinkOwnerState extends LinkProps {
  /**
   * If `true`, the element's focus is visible.
   */
  focusVisible?: boolean;
}
