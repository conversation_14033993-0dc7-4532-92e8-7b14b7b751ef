import * as React from 'react';
import { OverrideProps } from '@mui/types';
import { CreateSlotsAndSlotProps, SlotProps } from '../types/slot';

export interface BadgeOrigin {
  vertical: 'top' | 'bottom';
  horizontal: 'left' | 'right';
}

export type SystemColor = 'primary' | 'error' | 'info' | 'warning' | 'success';

export interface BadgeRootSlotPropsOverrides {}
export interface BadgeBadgeSlotPropsOverrides {}

export interface BadgeSlots {
  /**
   * The component that renders the root.
   * @default 'span'
   */
  root?: React.ElementType;
  /**
   * The component that renders the badge.
   * @default 'span'
   */
  badge?: React.ElementType;
}

export type BadgeSlotsAndSlotProps = CreateSlotsAndSlotProps<
  BadgeSlots,
  {
    root: SlotProps<'span', BadgeRootSlotPropsOverrides, BadgeOwnerState>;
    badge: SlotProps<'span', BadgeBadgeSlotPropsOverrides, BadgeOwnerState>;
  }
>;

export interface BadgeTypeMap<P = object, D extends React.ElementType = 'span'> {
  props: P &
    BadgeSlotsAndSlotProps & {
      /**
       * The content rendered within the badge.
       */
      badgeContent?: React.ReactNode;
      /**
       * If `true`, the badge is invisible.
       * @default false
       */
      invisible?: boolean;
      /**
       * Max count to show.
       * @default 99
       */
      max?: number;
      /**
       * Controls whether the badge is hidden when `badgeContent` is zero.
       * @default false
       */
      showZero?: boolean;
      /**
       * The badge size 'small' or 'large'
       * @default 'large'
       */
      size?: 'small' | 'large';
      /**
       * The system color of badge
       * @default 'primary'
       */
      color?: SystemColor;
      /**
       * The disable state of badge
       */
      disabled?: boolean;
      /**
       * The anchor of the badge.
       * @default {
       *   vertical: 'top',
       *   horizontal: 'right',
       * }
       */
      anchorOrigin?: {
        vertical: 'top' | 'bottom';
        horizontal: 'left' | 'right';
      };
      /**
       * The badge will be added relative to this node.
       */
      children?: React.ReactNode;
    };
  defaultComponent: D;
}

export type BadgeProps<
  D extends React.ElementType = BadgeTypeMap['defaultComponent'],
  P = {
    component?: React.ElementType;
  },
> = OverrideProps<BadgeTypeMap<P, D>, D>;

export interface BadgeOwnerState extends BadgeProps {
  hasChild: boolean;
}
