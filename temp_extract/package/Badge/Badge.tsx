'use client';

import React from 'react';
import { styled } from '@pigment-css/react';
import {
  unstable_capitalize as capitalize,
  usePreviousProps,
  unstable_composeClasses as composeClasses,
} from '@mui/utils';
import useSlotProps from '@mui/utils/useSlotProps';
import { BadgeOwnerState, BadgeProps, SystemColor } from './Badge.types';
import badgeClasses, { getBadgeUtilityClass } from './Badge.classes';
import { useBadge } from '../internal/hooks/useBadge';
import { ColorPaletteSystem } from '../types/colorSystem';

const useUtilityClasses = (ownerState: BadgeOwnerState) => {
  const { color, size, anchorOrigin, invisible, disabled, hasChild } = ownerState;

  const slots = {
    root: ['root'],
    badge: [
      'badge',
      invisible && 'invisible',
      disabled && 'disabled',
      hasChild &&
        anchorOrigin &&
        `anchorOrigin${capitalize(anchorOrigin.vertical)}${capitalize(anchorOrigin.horizontal)}`,
      color && `color${capitalize(color)}`,
      size && `size${capitalize(size)}`,
    ],
  };

  return composeClasses(slots, getBadgeUtilityClass, {});
};

const BadgeRoot = styled('span')<BadgeProps>(() => ({
  position: 'relative',
  display: 'inline-flex',
  verticalAlign: 'middle',
  flexShrink: 0,
}));

const BadgeContent = styled('span')<BadgeProps>(({ theme }) => ({
  display: 'flex',
  flexDirection: 'row',
  flexWrap: 'wrap',
  justifyContent: 'center',
  alignContent: 'center',
  alignItems: 'center',
  position: 'absolute',
  boxSizing: 'border-box',
  fontSize: '12px',
  lineHeight: '18px',
  fontFamily: theme.typography.fontFamily,
  minWidth: 'var(--nova-badge-size)',
  height: 'var(--nova-badge-size)',
  '--nova-badge-large-size': '16px',
  '--nova-badge-small-size': '4px',
  '--nova-badge-size': 'var(--nova-badge-large-size)',
  '--nova-badge-inset': 'calc(var(--nova-badge-size) - 4px)',
  [`&.${badgeClasses.anchorOriginBottomLeft}`]: {
    top: `calc(100% - var(--nova-badge-inset))`,
    right: `calc(100% - var(--nova-badge-inset))`,
  },
  [`&.${badgeClasses.anchorOriginBottomRight}`]: {
    top: `calc(100% - var(--nova-badge-inset))`,
    left: `calc(100% - var(--nova-badge-inset))`,
  },
  [`&.${badgeClasses.anchorOriginTopLeft}`]: {
    bottom: `calc(100% - var(--nova-badge-inset))`,
    right: `calc(100% - var(--nova-badge-inset))`,
  },
  [`&.${badgeClasses.anchorOriginTopRight}`]: {
    bottom: `calc(100% - var(--nova-badge-inset))`,
    left: `calc(100% - var(--nova-badge-inset))`,
  },
  [`&.${badgeClasses.invisible}`]: {
    transform: 'scale(0)',
  },
  transform: 'scale(1)',
  transformOrigin: 'calc(var(--nova-badge-size) / 2)',
  zIndex: 1,
  variants: [
    {
      props: { hasChild: false },
      style: {
        position: 'relative',
        bottom: 'unset',
        left: 'unset',
      },
    },
    {
      props: { size: 'small' },
      style: {
        height: '8px',
        width: '8px',
        borderRadius: '50%',
        '--nova-badge-size': 'var(--nova-badge-small-size)',
        '--nova-badge-inset': 'var(--nova-badge-size)',
      },
    },
    {
      props: { size: 'large' },
      style: {
        height: '18px',
        paddingLeft: '0.5rem',
        paddingRight: '0.5rem',
        borderRadius: '1.25rem',
      },
    },
    {
      props: { color: 'primary' },
      style: {
        backgroundColor: theme.vars.palette.primary,
        color: theme.vars.palette.onPrimary,
      },
    },
    {
      props: { color: 'error' },
      style: {
        backgroundColor: theme.vars.palette.error,
        color: theme.vars.palette.onError,
      },
    },
    ...(['success', 'info', 'warning'] as SystemColor[]).map((color: SystemColor) => ({
      props: { color },
      style: {
        backgroundColor: theme.vars.palette.system[color as ColorPaletteSystem],
        color: theme.vars.palette.system[`on${capitalize(color)}` as ColorPaletteSystem],
      },
    })),
    {
      props: { disabled: true, size: 'small' },
      style: {
        backgroundColor: theme.vars.palette.onBackgroundDisabled,
      },
    },
    {
      props: { disabled: true, size: 'large' },
      style: {
        color: theme.vars.palette.onBackgroundDisabled,
        backgroundColor: `color-mix(in srgb, transparent, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.disabled})`,
      },
    },
  ],
}));

// eslint-disable-next-line react/display-name
export const Badge = React.forwardRef((props: BadgeProps, ref: React.ForwardedRef<Element>) => {
  const badgeProps: BadgeProps = {
    max: 99,
    size: 'large',
    color: 'primary',
    invisible: false,
    showZero: false,
    anchorOrigin: { vertical: 'top', horizontal: 'right' },
    slots: {},
    slotProps: {},
    ...props,
  };

  const {
    className,
    children,
    max: maxProp,
    size: sizeProp,
    color: colorProp,
    invisible: invisibleProp,
    showZero: showZeroProp,
    anchorOrigin: anchorOriginProp,
    slots: slotsProp = {},
    slotProps = {},
    component,
    badgeContent: badgeContentProp,
    ...rest
  } = badgeProps;

  const {
    badgeContent,
    invisible: invisibleFromHook,
    max,
    displayValue: displayValueFromHook,
  } = useBadge({
    max: maxProp,
    invisible: invisibleProp,
    badgeContent: badgeContentProp,
    showZero: showZeroProp,
  });

  const isSmall = sizeProp === 'small';

  const prevProps = usePreviousProps({
    anchorOrigin: anchorOriginProp,
    color: colorProp,
    size: sizeProp,
    badgeContent: badgeContentProp,
  });

  const invisible = invisibleFromHook || (badgeContent == null && !isSmall);

  const { anchorOrigin = anchorOriginProp, size = sizeProp, color = colorProp } = invisible ? prevProps : props;

  const displayValue = !isSmall ? displayValueFromHook : undefined;

  const RootSlot = slotsProp.root ?? BadgeRoot;
  const BadgeSlot = slotsProp.badge ?? BadgeContent;

  const ownerState = {
    ...props,
    badgeContent,
    invisible,
    max,
    displayValue,
    showZero: showZeroProp,
    anchorOrigin,
    color,
    size,
    hasChild: !!children,
  };

  const classes = useUtilityClasses(ownerState);

  const rootSlotProps = useSlotProps({
    elementType: RootSlot,
    externalSlotProps: slotProps.root,
    externalForwardedProps: rest,
    additionalProps: {
      ref,
      as: component,
    },
    ownerState: ownerState as BadgeOwnerState,
    className: [classes.root, className],
  });

  const badgeSlotProps = useSlotProps({
    elementType: BadgeSlot,
    externalSlotProps: slotProps.badge,
    ownerState: ownerState as BadgeOwnerState,
    className: classes.badge,
  });

  return (
    <RootSlot {...rootSlotProps}>
      {children}
      <BadgeSlot {...badgeSlotProps}>{displayValue}</BadgeSlot>
    </RootSlot>
  );
});
