'use client';
import * as React from 'react';
import { OverridableComponent } from '@mui/types';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import BoxRoot from '@pigment-css/react/Box';
import { BoxProps, BoxTypeMap } from './Box.types';
import { getBoxUtilityClass } from './Box.classes';
import clsx from 'clsx';

const useUtilityClasses = () => {
  const slots = {
    root: ['root'],
  };

  return composeClasses(slots, getBoxUtilityClass, {});
};

// eslint-disable-next-line react/display-name
export const Box = React.forwardRef((props: BoxProps, ref: React.ForwardedRef<Element>) => {
  const { className, ...rest } = props;
  const classes = useUtilityClasses();
  return <BoxRoot ref={ref} className={clsx(classes.root, className)} {...(rest as any)} />;
}) as OverridableComponent<BoxTypeMap>;
