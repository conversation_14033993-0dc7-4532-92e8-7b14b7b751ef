import * as React from 'react';
import { OverrideProps } from '@mui/types';
import { SxProps } from '../types/theme';

export interface BoxTypeMap<P = object, D extends React.ElementType = 'div'> {
  props: P & {
    /**
     * The system prop that allows defining system overrides as well as additional CSS styles.
     */
    sx?: SxProps;
  };
  defaultComponent: D;
}

export type BoxProps<
  D extends React.ElementType = BoxTypeMap['defaultComponent'],
  P = { component?: React.ElementType },
> = OverrideProps<BoxTypeMap<P, D>, D>;

export interface BoxOwnerState extends BoxProps {}
