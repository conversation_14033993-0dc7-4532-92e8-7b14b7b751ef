import '@testing-library/jest-dom/vitest';
import { render, screen, cleanup } from '@testing-library/react';
import React from 'react';
import { describe, expect, it, afterEach } from 'vitest';
import { Box } from './Box';

afterEach(() => {
  cleanup();
});

describe('Box', () => {
  it('should render normal', () => {
    render(<Box data-testid="NovaBox-root" />);
    expect(screen.getByTestId('NovaBox-root')).toHaveClass('NovaBox-root');
  });
});
