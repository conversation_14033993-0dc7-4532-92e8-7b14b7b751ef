import path from 'path';
import fs from 'fs';
import * as ts from 'typescript';

export interface TypeScriptProject {
  name: string;
  rootPath: string;
  program: ts.Program;
  checker: ts.TypeChecker;
}

/**
 * Creates a TypeScript program with the given file paths
 */
export function createProgram(filePaths: string[]): ts.Program {
  // Add common type definition files that might be needed
  const tsConfigPath = path.resolve(process.cwd(), 'tsconfig.json');
  let compilerOptions: ts.CompilerOptions = {
    target: ts.ScriptTarget.Latest,
    module: ts.ModuleKind.ESNext,
    jsx: ts.JsxEmit.React,
    allowJs: true,
    declaration: true,
    noEmit: true,
    esModuleInterop: true,
    resolveJsonModule: true,
    moduleResolution: ts.ModuleResolutionKind.NodeJs,
  };

  // Try to load tsconfig.json if it exists
  if (fs.existsSync(tsConfigPath)) {
    try {
      const tsConfig = JSON.parse(fs.readFileSync(tsConfigPath, 'utf8'));
      if (tsConfig.compilerOptions) {
        compilerOptions = { ...compilerOptions, ...tsConfig.compilerOptions };
      }
    } catch (error) {
      console.log(`Error loading tsconfig.json: ${error}`);
    }
  }

  return ts.createProgram(filePaths, compilerOptions);
}

/**
 * Creates a TypeScript project for analyzing files
 */
export function createTypeScriptProject(filePaths: string[]): TypeScriptProject {
  const program = createProgram(filePaths);
  const checker = program.getTypeChecker();

  return {
    name: 'default',
    rootPath: process.cwd(),
    program,
    checker,
  };
}
