import * as fs from 'fs';
import * as path from 'path';
import { COMPONENT_NAME_MAPPING, FOLDER_TITLE_MAPPING } from './constant';

function toAnchorId(str: string): string {
  // Convert to lowercase and remove any punctuation
  return str.toLowerCase().replace(/[^\w\s]/g, '');
}

function createApiMdxFiles(storybookDir: string, folderName: string, apiComponents: string[]) {
  try {
    const componentDir = path.join(storybookDir, 'src/stories', folderName);
    const apiMdxPath = path.join(componentDir, `${folderName}API.mdx`);
    const combinedApiPath = path.join(componentDir, `${folderName}API.md`);

    console.log(`Processing ${folderName} API MDX...`);

    // First create the table of contents
    let combinedApiContent = '# API Documentation\n\n';
    combinedApiContent += apiComponents
      .map((componentName) => {
        const displayName = COMPONENT_NAME_MAPPING[componentName] || componentName;
        return `- [${displayName}](#${toAnchorId(displayName)})`;
      })
      .join('\n');
    combinedApiContent += '\n\n';

    // Then add all API docs content
    apiComponents.forEach((componentName, index) => {
      const apiPath = path.join(componentDir, `${componentName}.api.md`);
      let apiContent = fs.readFileSync(apiPath, 'utf-8');

      // Add spacing between components
      if (index > 0) {
        combinedApiContent += '<br><br>\n\n';
      }

      // Replace the component heading with mapped name
      const displayName = COMPONENT_NAME_MAPPING[componentName] || componentName;
      apiContent = apiContent.replace(/^# .+$/m, `# ${displayName}`);

      // Add component content
      combinedApiContent += apiContent;

      // Delete the individual API file
      fs.unlinkSync(apiPath);
      console.log(`✓ Removed ${componentName}.api.md`);
    });

    // Write the combined API markdown file
    fs.writeFileSync(combinedApiPath, combinedApiContent);
    console.log(`✓ Created combined API markdown at: ${combinedApiPath}`);

    // Create the MDX file with proper meta title
    const mdxContent = `
import { Meta, Markdown } from '@storybook/blocks';
import ApiContent from './${folderName}API.md?raw';
  
<Meta title="@hxnova/mi-react-components/${FOLDER_TITLE_MAPPING[folderName] || folderName}/API" />

<Markdown>
  {ApiContent}
</Markdown>`;

    // Write the MDX file
    fs.writeFileSync(apiMdxPath, mdxContent);
    console.log(`✓ Created API MDX file at: ${apiMdxPath}`);
  } catch (error) {
    console.error(`Error creating API MDX for ${folderName}:`, error);
  }
}

// Script execution
try {
  const storybookDir = path.resolve(__dirname, '../../apps/storybook');

  console.log('Starting API MDX creation...');
  console.log(`Storybook directory: ${storybookDir}\n`);

  // Read the stories directory
  const storiesDir = path.join(storybookDir, 'src/stories');
  const storyDirs = fs
    .readdirSync(storiesDir, { withFileTypes: true })
    .filter((dirent) => dirent.isDirectory())
    .map((dirent) => dirent.name);

  // Process each directory
  storyDirs.forEach((folderName) => {
    const componentDir = path.join(storiesDir, folderName);

    // Find all API markdown files in the directory
    const apiComponents = fs
      .readdirSync(componentDir)
      .filter((file) => file.endsWith('.api.md'))
      .map((file) => file.replace('.api.md', ''));

    if (apiComponents.length > 0) {
      createApiMdxFiles(storybookDir, folderName, apiComponents);
    }
  });

  console.log('\nAPI MDX creation complete!');
} catch (error) {
  console.error('Fatal error:', error);
  process.exit(1);
}
