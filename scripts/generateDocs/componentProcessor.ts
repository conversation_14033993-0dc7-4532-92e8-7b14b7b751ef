/* eslint-disable @typescript-eslint/no-explicit-any */
import * as fs from 'fs';
import path from 'path';
import { COMPONENT_FOLDER_MAPPING, COMPONENT_NAME_MAPPING, IGNORED_FOLDERS, IGNORED_PATTERNS } from './constant';
import { ParserFactory } from './core/ParserFactory';
import { ParsedComponent } from './types';

export class ComponentProcessor {
  private componentsDir: string;
  private storybookDir: string;
  private parserFactory: ParserFactory;

  constructor(componentsDir: string, storybookDir: string) {
    this.componentsDir = componentsDir;
    this.storybookDir = storybookDir;
    const tsConfigPath = path.resolve(process.cwd(), 'tsconfig.json');
    this.parserFactory = new ParserFactory(tsConfigPath);
  }

  public findComponents(): string[] {
    console.log(`Scanning for components in: ${this.componentsDir}`);
    const components = new Set<string>();

    const scanDirectory = (dir: string) => {
      try {
        const entries = fs.readdirSync(dir, { withFileTypes: true });

        for (const entry of entries) {
          const fullPath = path.join(dir, entry.name);

          if (entry.isDirectory()) {
            if (IGNORED_FOLDERS.includes(entry.name)) {
              console.log(`Skipping ignored folder: ${entry.name}`);
              continue;
            }

            const dirContents = fs.readdirSync(fullPath);
            dirContents
              .filter((file) => file.endsWith('.types.ts'))
              .filter((file) => !IGNORED_PATTERNS.some((pattern) => pattern.test(file)))
              .forEach((file) => {
                const componentName = file.replace('.types.ts', '');
                console.log(`Found component: ${componentName} in ${fullPath}`);
                components.add(componentName);
              });

            scanDirectory(fullPath);
          }
        }
      } catch (error) {
        console.error(`Error scanning directory ${dir}:`, error);
      }
    };

    scanDirectory(this.componentsDir);
    return Array.from(components).sort();
  }

  public findComponentFiles(componentName: string): { typeFile: string; classFile?: string } | null {
    let typeFile: string | null = null;

    const findFile = (dir: string): boolean => {
      try {
        const entries = fs.readdirSync(dir, { withFileTypes: true });

        for (const entry of entries) {
          const fullPath = path.join(dir, entry.name);

          if (entry.isDirectory() && !IGNORED_FOLDERS.includes(entry.name)) {
            if (findFile(fullPath)) return true;
          } else if (entry.name === `${componentName}.types.ts`) {
            typeFile = fullPath;
            return true;
          }
        }
        return false;
      } catch (error) {
        console.error(`Error searching for files in ${dir}:`, error);
        return false;
      }
    };

    findFile(this.componentsDir);

    if (!typeFile) {
      return null;
    }

    const componentDir = path.dirname(typeFile);
    const classFile = path.join(componentDir, `${componentName}.classes.ts`);

    return {
      typeFile,
      classFile: fs.existsSync(classFile) ? classFile : undefined,
    };
  }

  private findStoryFolder(componentName: string): string | null {
    // First check if there's a direct mapping
    if (COMPONENT_FOLDER_MAPPING[componentName]) {
      const mappedFolder = COMPONENT_FOLDER_MAPPING[componentName];
      const mappedPath = path.join(this.storybookDir, 'src/stories', mappedFolder);
      if (fs.existsSync(mappedPath)) {
        return mappedFolder;
      }
    }

    const storiesDir = path.join(this.storybookDir, 'src/stories');
    if (!fs.existsSync(storiesDir)) return null;

    const storyDirs = fs
      .readdirSync(storiesDir, { withFileTypes: true })
      .filter((dirent) => dirent.isDirectory())
      .map((dirent) => dirent.name);

    // Exact match
    if (storyDirs.includes(componentName)) {
      return componentName;
    }

    // Base component match (e.g., "List" for "ListItem")
    const baseComponentName = componentName.match(/^[A-Z][a-z]+/)?.[0];
    if (baseComponentName && storyDirs.includes(baseComponentName)) {
      return baseComponentName;
    }

    // Prefix match
    return storyDirs.find((dir) => componentName.startsWith(dir)) || null;
  }

  public processComponent(componentName: string): void {
    try {
      console.log(`\nProcessing ${componentName}...`);

      // Find component files
      const files = this.findComponentFiles(componentName);
      if (!files) {
        console.warn(`No files found for ${componentName}`);
        if (process.argv[2] === componentName) {
          console.log(`Attempting to find ${componentName} in all possible locations...`);
        }
        return;
      }

      console.log(`Types file: ${files.typeFile}`);
      if (files.classFile) {
        console.log(`Classes file: ${files.classFile}`);
      }

      // Parse component using the existing parserFactory instance
      const parsedComponent = this.parserFactory.parseComponent(files.typeFile, componentName);

      // Find story folder
      const storyFolder = this.findStoryFolder(componentName);
      let outputDir: string;
      let outputPath: string;

      if (!storyFolder) {
        // Fallback to docs/api folder
        outputDir = path.join(process.cwd(), 'docs/api');
        // Create docs/api directory if it doesn't exist
        if (!fs.existsSync(outputDir)) {
          fs.mkdirSync(outputDir, { recursive: true });
        }
        outputPath = path.join(outputDir, `${componentName}.api.md`);
        console.log(`No matching story folder found for ${componentName}, saving to docs/api folder instead.`);
      } else {
        outputDir = path.join(this.storybookDir, 'src/stories', storyFolder);
        if (!fs.existsSync(outputDir)) {
          console.warn(`Story folder not found: ${outputDir}, falling back to docs/api folder`);
          outputDir = path.join(process.cwd(), 'docs/api');
          if (!fs.existsSync(outputDir)) {
            fs.mkdirSync(outputDir, { recursive: true });
          }
        }
        outputPath = path.join(outputDir, `${componentName}.api.md`);
      }

      // Generate and write documentation
      const content = this.generateMarkdown(componentName, parsedComponent);

      fs.mkdirSync(outputDir, { recursive: true });
      fs.writeFileSync(outputPath, content, 'utf-8');

      console.log(`✓ Generated API documentation for ${componentName}`);
      console.log(`  Output: ${outputPath}`);
    } catch (error) {
      console.error(`Error processing ${componentName}:`, error);
    }
  }

  private generateMarkdown(componentName: string, component: ParsedComponent): string {
    const escapeTableCell = (text: string): string => {
      /**
       * Escape pipes and backticks in table cells
       *
       * Replace the standard vertical bar '|' (U+007C) with the vertical line symbol '⏐' (U+23D0)
       * to prevent breaking table formatting without using escape characters '\|'.
       */
      return text.replace(/\|/g, '⏐').replace(/`/g, '\\`');
    };

    const formatDescription = (text: string): string => {
      if (!text) return '';

      // Replace newlines with <br> for table cells
      return text
        .split('\n')
        .map((line) => line.trim())
        .filter(Boolean) // Remove empty lines
        .join('<br>');
    };

    const formatType = (type: string): string => {
      const formattedType = type.replaceAll('"', "'");
      // Handle types with special characters
      if (type.includes('|') || type.includes('<') || type.includes('>')) {
        return `${escapeTableCell(formattedType)}`;
      }
      return formattedType;
    };

    // Update the title to use the mapping if available
    const displayName = COMPONENT_NAME_MAPPING[componentName] || componentName;
    let content = `# ${displayName} API\n\n`;
    content += `API reference docs for the React ${displayName} component. Learn about the props, CSS, and other APIs of this exported module.\n\n`;

    // Fixed import section with proper formatting (no leading spaces)
    content += `## Import\n\n`;
    content += `\`\`\`jsx\nimport { ${componentName} } from '@hxnova/mi-react-components/${componentName}';\n// or\nimport { ${componentName} } from '@hxnova/mi-react-components';\n\`\`\`\n\n`;

    if (component.description) {
      content += `${component.description.replace(/\n/g, '\n')}\n\n`;
    }

    // Props table
    if (component.props.length > 0) {
      content += `## Props\n\n`;
      content += `| Name | Type | Default | Description |\n`;
      content += `| ---- | ---- | ------- | ----------- |\n`;
      content += component.props
        .map((prop) => {
          const name = prop.required ? `${prop.name}*` : prop.name;
          let description = formatDescription(prop.description);

          if (prop.signature) {
            description += `  \n\n**Signature:**  \n\`${escapeTableCell(prop.signature)}\``;
          }

          if (prop.signatureArgs?.length) {
            description += '  \n\n**Parameters:**  \n';
            prop.signatureArgs.forEach((arg) => {
              description += `- \`${arg.name}\`: ${formatDescription(arg.description)}  \n`;
            });
          }

          if (prop.signatureReturn) {
            description += `  \n**Returns:** ${formatDescription(prop.signatureReturn.description)}`;
          }

          if (prop.isDeprecated) {
            description = `⚠️ **Deprecated**${prop.deprecationInfo ? ': ' + prop.deprecationInfo : ''}<br><br>${description}`;
          }

          const defaultValue = prop.defaultValue ? escapeTableCell(prop.defaultValue) : '-';
          const type = formatType(prop.type);

          return `| **${name}** | \`${type}\` | ${defaultValue === '-' ? '-' : `\`${defaultValue}\``} | ${description} |`;
        })
        .join('\n');
      content += '\n\n';
    }

    content += `## CSS classes\n\n`;
    content += `| Class name | Rule name | Description |\n`;
    content += `| ---------- | --------- | ----------- |\n`;
    content += component.classes
      .map((cls) => {
        const name = `.MIxNova${componentName}-${cls.name}`;
        return `| ${name} | \`${cls.name}\` | ${formatDescription(cls.description)} |`;
      })
      .join('\n');
    content += '\n\n';

    return content;
  }
}
