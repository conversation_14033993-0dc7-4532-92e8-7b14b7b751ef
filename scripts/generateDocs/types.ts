/* eslint-disable @typescript-eslint/no-explicit-any */
// ===== Type Definitions =====
interface BaseDetail {
  name: string;
  description: string;
  isDeprecated?: boolean;
  deprecationInfo?: string;
}
export interface PropDetail extends BaseDetail {
  type: string;
  defaultValue: string;
  required?: boolean;
  tags?: {
    params?: Record<string, { type: string; description: string } | string>;
    [key: string]: any;
  };
  inheritedFrom?: string; // Add this line to track inheritance

  // Function signature properties for callback props
  signature?: string;
  signatureArgs?: Array<{ name: string; description: string }>;
  signatureReturn?: { name: string; description: string };
}

export interface ClassDetail extends BaseDetail {
  prefix?: string; // Optional prefix for class names
  type?: string; // Type of class (e.g., 'css', 'state', etc.)
  deprecationInfo?: string;
}

export interface ParsedComponent {
  props: PropDetail[];
  classes: ClassDetail[];
  description?: string;
  tags?: Record<string, string>;
}
