/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
import * as path from 'path';
import { ComponentProcessor } from './componentProcessor';

/**
 * Command-line interface for generating API documentation
 * This script processes components and generates documentation files
 *
 * Usage:
 *   ts-node cli.ts [componentName]
 *
 * If componentName is provided, only that component will be processed.
 * Otherwise, all components will be processed.
 */
async function main() {
  try {
    const componentsDir = path.resolve(__dirname, '../../packages/mixnova-react-components');
    const storybookDir = path.resolve(__dirname, '../../apps/storybook');

    console.log('Starting API documentation generation...');
    console.log(`Components directory: ${componentsDir}`);
    console.log(`Storybook directory: ${storybookDir}\n`);

    const processor = new ComponentProcessor(componentsDir, storybookDir);

    // Check if a specific component was requested via command line
    const targetComponent = process.argv[2];

    if (targetComponent) {
      console.log(`Generating documentation for a single component: ${targetComponent}`);
      processor.processComponent(targetComponent);
    } else {
      // Process all components
      const components = processor.findComponents();
      console.log(`\nFound ${components.length} components to process:`);
      console.log(components.join(', '), '\n');

      for (const componentName of components) {
        processor.processComponent(componentName);
      }
    }

    console.log('\nDocumentation generation complete!');
  } catch (error) {
    console.error('Fatal error:', error);
    process.exit(1);
  }
}

main();
