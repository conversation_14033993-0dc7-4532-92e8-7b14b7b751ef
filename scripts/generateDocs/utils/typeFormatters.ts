/**
 * Utilities for formatting TypeScript types in a readable way
 */
import * as ts from 'typescript';

/**
 * Formats a TypeScript type into a readable string representation
 * @param typeNode The TypeScript type node to format
 * @param checker The TypeScript type checker
 * @param depth Current recursion depth (for preventing infinite recursion)
 * @returns A formatted string representation of the type
 */
export function formatTypeDescription(typeNode: ts.TypeNode, checker: ts.TypeChecker, depth: number = 0): string {
  if (depth > 3) {
    // Prevent infinite recursion with complex types
    return typeNode.getText();
  }

  // Handle function types with better formatting
  if (ts.isFunctionTypeNode(typeNode)) {
    // Extract parameters and return type with proper formatting
    const params = typeNode.parameters
      .map((p) => {
        const paramName = p.name.getText();
        const paramType = p.type ? formatTypeDescription(p.type, checker, depth + 1) : 'any';
        const isOptional = p.questionToken ? '?' : '';
        return `${paramName}${isOptional}: ${paramType}`;
      })
      .join(', ');

    const returnType = typeNode.type ? formatTypeDescription(typeNode.type, checker, depth + 1) : 'void';

    return `(${params}) => ${returnType}`;
  }

  // Get the actual type
  try {
    const type = checker.getTypeAtLocation(typeNode);

    // Handle union types specially
    if (type.isUnion()) {
      return type.types.map((t) => checker.typeToString(t, undefined, ts.TypeFormatFlags.NoTruncation)).join(' | ');
    }

    // Default handling using TypeScript's built-in stringification
    return checker.typeToString(
      type,
      undefined,
      ts.TypeFormatFlags.NoTruncation | ts.TypeFormatFlags.WriteArrowStyleSignature,
    );
  } catch (error) {
    // Fallback to getText() if type checker fails
    return typeNode.getText();
  }
}

/**
 * Interface for docgen type objects
 */
export interface DocgenTypeInfo {
  name?: string;
  raw?: string;
  value?: DocgenTypeInfo[] | Record<string, DocgenTypeInfo> | string;
  required?: boolean;
}

/**
 * Interface for docgen enum value
 */
export interface DocgenEnumValue {
  value?: string | number | boolean;
  toString(): string;
}

/**
 * Formats complex type descriptions in a more readable way
 * Similar to the original formatTypeDescription but for docgen types
 * @param type The type object from react-docgen-typescript
 * @returns A formatted string representation of the type
 */
export function formatTypeDescriptionFromDocgen(type: DocgenTypeInfo | string): string {
  if (!type) return 'any';

  // Handle primitive types directly
  if (typeof type === 'string') return type;

  // If we have raw type info, use it directly for better accuracy
  if (type.raw) {
    // Simplify complex ElementType expressions
    if (type.raw === 'boolean') {
      return 'bool';
    }
    if (type.raw.includes('ElementType')) {
      return 'ElementType';
    }
    if (
      type.raw.includes('HTMLElementTagNameMap') ||
      type.raw.includes('ReactNode') ||
      type.raw.includes('Ref') ||
      type.raw === 'SxProps'
    ) {
      return type.raw;
    }
    // Special handling for common custom types
    if (type.raw === 'elementTypeAcceptingRef') {
      return 'ElementType';
    }
    if (type.raw === 'elementAcceptingRef' || type.raw.includes('elementAcceptingRef')) {
      return 'ReactElement';
    }
    if (type.raw === 'refType' || type.raw.includes('RefType')) {
      return 'Ref';
    }
    if (type.raw === 'HTMLElementType') {
      return 'HTML element';
    }
    if (type.raw === '() => null') {
      return 'any';
    }
  }

  // Handle different type structures
  switch (type.name) {
    case 'shape':
    case 'object':
      if (!type.value) return 'object';
      return `{ ${Object.keys(type.value as Record<string, DocgenTypeInfo>)
        .map((key) => {
          const subType = (type.value as Record<string, DocgenTypeInfo>)[key];
          return `${key}${subType.required ? '' : '?'}: ${formatTypeDescriptionFromDocgen(subType)}`;
        })
        .join(', ')} }`;

    case 'union':
      if (!type.value) return 'union';
      // Format union types with proper spacing
      return (type.value as DocgenTypeInfo[])
        .map((t) => formatTypeDescriptionFromDocgen(t))
        .filter(Boolean)
        .join(' | ');

    case 'enum':
      if (!type.value) return 'enum';
      // Format enum values with proper quotes
      return (type.value as (DocgenTypeInfo | DocgenEnumValue)[])
        .map((v) => {
          if ('value' in v && v.value !== undefined) {
            return typeof v.value === 'string' ? `${v.value}` : String(v.value);
          }
          return String(v);
        })
        .join(' | ');

    case 'arrayOf':
      if (!type.value) return 'Array<any>';
      return `Array<${formatTypeDescriptionFromDocgen((type.value as DocgenTypeInfo[])[0])}>`;

    case 'func':
      return 'function';

    case 'instanceOf':
      if (type.value && typeof type.value === 'string') {
        if (type.value.startsWith('typeof')) {
          const match = /typeof (.*) ===/.exec(type.value);
          if (match && match[1]) {
            return match[1];
          }
        }
        return type.value;
      }
      return String(type.value);

    // Special handling for common React types
    case 'element':
      return 'ReactElement';

    case 'node':
      return 'ReactNode';

    case 'elementType':
      return 'ElementType';

    case 'custom':
      return type.raw || 'any';

    default:
      return type.name || 'any';
  }
}
