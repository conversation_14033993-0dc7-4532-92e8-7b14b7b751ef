/**
 * Utilities for parsing JSDoc comments and retrieving documentation from TypeScript nodes
 */
import * as ts from 'typescript';

/**
 * Represents a parsed JSDoc tag
 */
export interface JSDocTag {
  name?: string;
  text?: string;
  type?: string;
}

/**
 * Represents a fully parsed JSDoc comment
 */
export interface ParsedJSDoc {
  description: string;
  tags: Record<string, JSDocTag | string | Record<string, JSDocTag | string>>;
}

/**
 * Extracts information about deprecated elements
 * @param description The JSDoc description to parse
 * @returns Object containing deprecation status and info
 */
export function getDeprecatedInfo(description: string): { isDeprecated: boolean; info?: string } {
  const match = description.match(/@deprecated\s+(.*?)(\n|$)/);
  if (match) {
    return {
      isDeprecated: true,
      info: match[1].trim(),
    };
  }
  return { isDeprecated: false };
}

/**
 * Parses a JSDoc comment into description and tags
 * @param comment The raw JSDoc comment string
 * @returns Parsed JSDoc with description and tags
 */
export function parseJSDocComment(comment: string): ParsedJSDoc {
  const description: string[] = [];
  const tags: Record<string, JSDocTag | string | Record<string, JSDocTag | string>> = {};
  let currentTag: string | null = null;
  let currentTagContent: string[] = [];

  if (!comment) {
    return { description: '', tags: {} };
  }

  const lines = comment
    .replace(/\/\*\*|\*\//g, '')
    .split('\n')
    .map((line) => line.replace(/^\s*\*\s?/, '').trim());

  for (const line of lines) {
    if (!line) continue;

    // Parse @param tags with type information
    const paramMatch = line.match(/@param\s+{([^}]+)}\s+(\w+)\s*(.*)/);
    if (paramMatch) {
      const [, type, name, desc] = paramMatch;
      if (!tags.params) {
        tags.params = {};
      }
      (tags.params as Record<string, JSDocTag>)[name] = {
        type: type.trim(),
        text: desc.trim(),
      };
      continue;
    }

    // Handle other tags
    const tagMatch = line.match(/@(\w+)\s*(.*)/);
    if (tagMatch) {
      if (currentTag && currentTagContent.length) {
        if (currentTag === 'params') {
          // Skip params as they're handled separately
          continue;
        }
        tags[currentTag] = currentTagContent.join(' ').trim();
      }
      currentTag = tagMatch[1];
      currentTagContent = [tagMatch[2]];
    } else if (currentTag) {
      currentTagContent.push(line);
    } else {
      description.push(line);
    }
  }

  // Handle last tag
  if (currentTag && currentTagContent.length && currentTag !== 'params') {
    tags[currentTag] = currentTagContent.join(' ').trim();
  }

  return {
    description: description.join('\n').trim(),
    tags,
  };
}

/**
 * Extracts JSDoc comments from a TypeScript node
 * @param node The TypeScript node to extract comments from
 * @param sourceFile The source file containing the node
 * @returns The raw JSDoc comment string
 */
export function getNodeComments(node: ts.Node, sourceFile: ts.SourceFile): string {
  const fullStart = node.getFullStart();
  const commentRanges = ts.getLeadingCommentRanges(sourceFile.text, fullStart);

  if (!commentRanges?.length) return '';

  return commentRanges
    .map((range) => sourceFile.text.slice(range.pos, range.end))
    .filter((comment) => comment.startsWith('/**'))
    .join('\n');
}
