/**
 * ParserFactory - Factory for creating and managing parser instances
 */
import { ClassParser } from '../parsers/ClassParser';
import { PropParser } from '../parsers/PropParser';
import { ClassDetail, ParsedComponent, PropDetail } from '../types';

/**
 * Factory class for creating and managing component parsers
 * Provides a centralized way to access parser functionality
 */
export class ParserFactory {
  // Expose parsers as public properties for direct access when needed
  public propParser: PropParser;
  public classParser: ClassParser | null = null;

  /**
   * Creates a new ParserFactory
   * @param tsConfigPath Optional path to the TypeScript config file
   */
  constructor(tsConfigPath?: string) {
    // Initialize parsers with the TypeScript config
    this.propParser = new PropParser(tsConfigPath);
  }

  /**
   * Initializes parsers for a specific file if they haven't been initialized yet
   * @param filePath Path to the component file
   */
  private ensureParsersInitialized(filePath: string): void {
    if (!this.classParser) this.classParser = new ClassParser(filePath);
  }

  /**
   * Parses a component file and returns all component details
   * @param filePath Path to the component file
   * @param componentName Name of the component
   * @returns Complete parsed component info
   */
  public parseComponent(filePath: string, componentName: string): ParsedComponent {
    // Initialize parsers that require file path
    this.ensureParsersInitialized(filePath);

    // Parse the component file
    const props = this.parseProps(filePath, componentName);
    const classes = this.parseClasses(filePath, componentName);

    return {
      props,
      classes,
    };
  }

  /**
   * Parses props from a component file
   * @param filePath Path to the component file
   * @param componentName Name of the component
   * @returns Array of PropDetail objects
   */
  public parseProps(filePath: string, componentName: string): PropDetail[] {
    return this.propParser.parseProps(filePath, componentName);
  }

  /**
   * Parses CSS classes from a component file
   * @param filePath Path to the component file
   * @param componentName Name of the component
   * @returns Array of ClassDetail objects
   */
  public parseClasses(filePath: string, componentName: string): ClassDetail[] {
    this.ensureParsersInitialized(filePath);

    // First try the standard classes file approach
    let classes = this.classParser!.parseClasses(filePath, componentName);

    // If no classes found, try to extract from component source
    if (classes.length === 0) {
      classes = this.classParser!.parseClassesFromComponentSource(filePath, componentName);
    }

    return classes;
  }
}
