/**
 * TypeScriptProject - Utilities for creating TypeScript projects for analysis
 */
import * as ts from 'typescript';
import * as path from 'path';
import * as fs from 'fs';

/**
 * TypeScript project configuration
 */
export interface TypeScriptProject {
  name: string;
  rootPath: string;
  program: ts.Program;
  checker: ts.TypeChecker;
}

/**
 * Creates a TypeScript program with the given file paths
 * @param filePaths Array of file paths to include in the program
 * @returns A TypeScript Program object
 */
export function createProgram(filePaths: string[]): ts.Program {
  // Add common type definition files that might be needed
  const tsConfigPath = path.resolve(process.cwd(), 'tsconfig.json');
  let compilerOptions: ts.CompilerOptions = {
    target: ts.ScriptTarget.Latest,
    module: ts.ModuleKind.ESNext,
    jsx: ts.JsxEmit.React,
    allowJs: true,
    declaration: true,
    noEmit: true,
    esModuleInterop: true,
    resolveJsonModule: true,
    moduleResolution: ts.ModuleResolutionKind.NodeJs,
  };

  // Try to load tsconfig.json if it exists
  if (fs.existsSync(tsConfigPath)) {
    try {
      const tsConfig = JSON.parse(fs.readFileSync(tsConfigPath, 'utf8'));
      if (tsConfig.compilerOptions) {
        compilerOptions = { ...compilerOptions, ...tsConfig.compilerOptions };
      }
    } catch (error) {
      console.log(`Error loading tsconfig.json: ${error}`);
    }
  }

  return ts.createProgram(filePaths, compilerOptions);
}

/**
 * Creates a TypeScript project for analyzing files
 * @param filePaths Array of file paths to include in the project
 * @param tsConfigPath Optional path to tsconfig.json
 * @param name Optional project name
 * @returns A TypeScriptProject object with program and type checker
 */
export function createTypeScriptProject(
  filePaths: string[],
  tsConfigPath?: string,
  name: string = 'default',
): TypeScriptProject {
  let compilerOptions: ts.CompilerOptions = {
    target: ts.ScriptTarget.Latest,
    module: ts.ModuleKind.ESNext,
    jsx: ts.JsxEmit.React,
    allowJs: true,
    declaration: true,
    noEmit: true,
    esModuleInterop: true,
    resolveJsonModule: true,
    moduleResolution: ts.ModuleResolutionKind.NodeJs,
  };

  // Use provided tsconfig if available
  if (tsConfigPath && fs.existsSync(tsConfigPath)) {
    try {
      const tsConfig = JSON.parse(fs.readFileSync(tsConfigPath, 'utf8'));
      if (tsConfig.compilerOptions) {
        compilerOptions = { ...compilerOptions, ...tsConfig.compilerOptions };
      }
    } catch (error) {
      console.log(`Error loading provided tsconfig.json (${tsConfigPath}): ${error}`);
    }
  } else {
    // Try to load tsconfig.json from the default location
    const defaultTsConfigPath = path.resolve(process.cwd(), 'tsconfig.json');
    if (fs.existsSync(defaultTsConfigPath)) {
      try {
        const tsConfig = JSON.parse(fs.readFileSync(defaultTsConfigPath, 'utf8'));
        if (tsConfig.compilerOptions) {
          compilerOptions = { ...compilerOptions, ...tsConfig.compilerOptions };
        }
      } catch (error) {
        console.log(`Error loading default tsconfig.json: ${error}`);
      }
    }
  }

  const program = ts.createProgram(filePaths, compilerOptions);
  const checker = program.getTypeChecker();

  return {
    name,
    rootPath: process.cwd(),
    program,
    checker,
  };
}
