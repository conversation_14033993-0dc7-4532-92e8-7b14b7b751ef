/**
 * ClassParser - Parses component CSS classes using TypeScript compiler API
 */
import * as fs from 'fs';
import * as path from 'path';
import * as ts from 'typescript';
import { createTypeScriptProject } from '../core/TypeScriptProject';
import { ClassDetail } from '../types';
import { getNodeComments, parseJSDocComment } from '../utils/docCommentUtils';

/**
 * ClassParser class for extracting and formatting component CSS classes
 */
export class ClassParser {
  private checker: ts.TypeChecker;
  private program: ts.Program;

  /**
   * Creates a new ClassParser
   * @param componentFilePath Path to the component file
   */
  constructor(componentFilePath: string) {
    // Initialize the TypeScript project with the component path
    // This will be used to resolve the classes file later
    const project = createTypeScriptProject([componentFilePath]);
    this.program = project.program;
    this.checker = project.checker;
  }

  /**
   * Finds a matching classes file for a component
   * @param componentFilePath Path to the component file
   * @param componentName Name of the component
   * @returns Path to the classes file or null if not found
   */
  private findClassesFile(componentFilePath: string, componentName: string): string | null {
    const dir = path.dirname(componentFilePath);

    // Common patterns for classes files
    const classesFile = path.join(dir, `${componentName}.classes.ts`);
    if (fs.existsSync(classesFile)) {
      return classesFile;
    }

    // Log the path we looked for, for debugging
    console.log(`Looked for classes file at: ${classesFile}`);
    return null;
  }

  /**
   * Parses a CSS classes file to extract class documentation
   * @param filePath Path to the component file
   * @param componentName Name of the component
   * @returns Array of ClassDetail objects
   */
  public parseClasses(filePath: string, componentName: string): ClassDetail[] {
    // Try to find a matching classes file
    const classesFilePath = this.findClassesFile(filePath, componentName);
    if (!classesFilePath || !fs.existsSync(classesFilePath)) {
      return [];
    }
    // Create a project for the classes file
    const project = createTypeScriptProject([classesFilePath]);
    const sourceFile = project.program.getSourceFile(classesFilePath);
    if (!sourceFile) {
      return [];
    }

    const classes: ClassDetail[] = [];
    let componentPrefix: string | undefined;

    // First pass: find the generateUtilityClasses call to get the component prefix
    const findPrefix = (node: ts.Node): void => {
      if (
        ts.isCallExpression(node) &&
        node.expression.getText() === 'generateUtilityClasses' &&
        node.arguments.length >= 2
      ) {
        // Extract the component prefix (first argument)
        if (ts.isStringLiteral(node.arguments[0])) {
          componentPrefix = node.arguments[0].text;
        }
      }

      ts.forEachChild(node, findPrefix);
    };

    findPrefix(sourceFile);

    // Second pass: find the interface with class definitions
    const visit = (node: ts.Node): void => {
      if (ts.isInterfaceDeclaration(node) && node.name.text.endsWith('Classes')) {
        node.members.forEach((member) => {
          if (ts.isPropertySignature(member)) {
            const comment = getNodeComments(member, sourceFile);
            const { description, tags } = parseJSDocComment(comment);
            const className = member.name.getText().replace(/['"]/g, '');

            // Extract string values safely from tags
            const typeTag = typeof tags.type === 'string' ? tags.type : 'css';
            const prefixTag = typeof tags.prefix === 'string' ? tags.prefix : undefined;
            const deprecatedTag = tags.deprecated !== undefined;
            const deprecationInfo = typeof tags.deprecated === 'string' ? tags.deprecated : undefined;

            classes.push({
              name: className, // Store the raw class name
              description: description || '',
              type: typeTag,
              prefix: componentPrefix || prefixTag, // Use component prefix as default
              isDeprecated: deprecatedTag,
              deprecationInfo,
            });
          }
        });
      }
      ts.forEachChild(node, visit);
    };

    visit(sourceFile);
    return classes;
  }

  /**
   * Finds CSS classes directly from a component's source file by looking for classnames prop
   * This is an advanced method to use if no classes file exists
   * @param filePath Path to the component file
   * @param componentName Name of the component
   * @returns Array of ClassDetail objects
   */
  public parseClassesFromComponentSource(filePath: string, componentName: string): ClassDetail[] {
    if (!fs.existsSync(filePath)) {
      return [];
    }

    // First try the standard approach
    const classesFromFile = this.parseClasses(filePath, componentName);
    if (classesFromFile.length > 0) {
      return classesFromFile;
    }

    // If no classes file was found, try to extract from the component source
    const sourceFile = this.program.getSourceFile(filePath);
    if (!sourceFile) {
      return [];
    }

    const classes: ClassDetail[] = [];

    // Look for classnames in the component or styles
    // This is a simple implementation that can be extended
    const visit = (node: ts.Node): void => {
      // Look for className assignments
      if (
        ts.isPropertyAssignment(node) &&
        node.name.getText() === 'className' &&
        ts.isStringLiteral(node.initializer)
      ) {
        const classValue = node.initializer.text;

        // Extract individual class names from className="a b c" format
        const classNames = classValue.split(/\s+/).filter(Boolean);

        for (const className of classNames) {
          // Skip if it looks like a dynamic class
          if (className.includes('${') || className.includes('(')) {
            continue;
          }

          // Get any associated comment
          const comment = getNodeComments(node, sourceFile);
          const { description } = parseJSDocComment(comment);

          classes.push({
            name: className,
            description: description || `CSS class used in ${componentName}`,
            type: 'css',
          });
        }
      }

      ts.forEachChild(node, visit);
    };

    visit(sourceFile);
    return classes;
  }
}
