# MIxNova Documentation Generator

This package provides tools for generating API documentation from TypeScript components in the Nova design system. It automatically extracts props, CSS classes, and documentation from TypeScript source files.

## Architecture

The documentation generator uses a robust parser-based architecture with several key components:

### Core Components

- **ParserFactory**: Coordinates the parsing process and provides a unified API for component documentation
  - Manages parser instances
  - Handles file resolution
  - Coordinates parsing of props, and classes
- **TypeScriptProject**: Manages TypeScript compiler configuration and program creation
  - Sets up compiler options
  - Creates and manages TypeScript Program instances
  - Provides type checking capabilities

### Parsers

- **PropParser**: Extracts prop definitions, types, and JSDoc comments
  - Handles complex TypeScript types
  - Processes function signatures
  - Extracts default values and requirements
  - Supports inheritance from base components
- **ClassParser**: Extracts CSS class definitions and documentation
  - Finds class files automatically
  - Processes utility classes
  - Handles class inheritance
  - Supports prefix customization

### Utils

- **docCommentUtils**: Utilities for parsing JSDoc comments
  - Extracts descriptions and tags
  - Processes deprecation information
  - Handles multi-line comments
  - Supports custom tags
- **typeFormatters**: Utilities for formatting TypeScript types
  - Formats complex types readably
  - Handles union and intersection types
  - Processes function signatures
  - Supports generic types

### Support

- **adapters**: Provides backward compatibility with legacy API
- **componentProcessor**: Handles component discovery and documentation
- **constant**: Defines system-wide constants and mappings
- **createProject**: Manages TypeScript project creation
- **createApiMdx**: Generates Storybook MDX documentation

## Usage

### Command-Line Interface

Generate documentation for all components:

```sh
npx ts-node scripts/generateDocs/cli.ts
```

Generate documentation for a specific component:

```sh
npx ts-node scripts/generateDocs/cli.ts Button
```

Create Storybook MDX documentation:

```sh
npx ts-node scripts/generateDocs/createApiMdx.ts
```

### API Usage

```typescript
import { ParserFactory } from './scripts/generateDocs/core/ParserFactory';
import { PropParser, ClassParser } from './scripts/generateDocs/parsers';

// Using ParserFactory (recommended)
const factory = new ParserFactory();
const componentDocs = factory.parseComponent('/path/to/component.types.ts', 'ComponentName');

// Using individual parsers
const propParser = new PropParser('/path/to/component.types.ts');
const props = propParser.parseProps('ComponentName');

const classParser = new ClassParser('/path/to/component.types.ts');
const classes = classParser.parseClasses('ComponentName');
```

## Documentation Format

### Output Structure

The generator produces three types of documentation:

1. **Individual API Files** (`.api.md`)
   - Component description
   - Import information
   - Props documentation
   - CSS classes documentation

2. **Combined API Documentation** (`.md`)
   - Table of contents
   - All component APIs in one file
   - Cross-component references

3. **Storybook MDX Files** (`.mdx`)
   - Storybook integration
   - Interactive documentation
   - Component previews

### JSDoc Comment Format

#### Component Documentation

```typescript
/**
 * A description of the component.
 * 
 * @since 1.0.0
 * @deprecated Use NewComponent instead
 * @example
 * ```jsx
 * <ComponentName prop="value" />
 * ```
 */
```

#### Props Documentation

```typescript
/**
 * A description of the prop.
 * 
 * @default "default value"
 * @required
 * @deprecated Use newProp instead
 * @param {string} paramName - Description of the parameter
 * @returns {boolean} Description of the return value
 * @example
 * ```jsx
 * <Component propName="example" />
 * ```
 */
```

#### CSS Classes Documentation

```typescript
/**
 * A description of the CSS class.
 * 
 * @type css|state|modifier
 * @prefix component
 * @deprecated Use newClass instead
 * @example
 * ```css
 * .NovaComponent-root { /* styles */ }
 * ```
 */
```
