/**
 * Adapters - Provides backward compatibility with old API functions
 * Allows for gradual migration to the new parser classes
 */
/* eslint-disable @typescript-eslint/no-unused-vars */
import * as fs from 'fs';
import * as path from 'path';
import { ParserFactory } from './core/ParserFactory';
import { ClassDetail, ParsedComponent, PropDetail } from './types';

// Create a single ParserFactory instance to reuse
const parserFactory = new ParserFactory();

/**
 * Legacy adapter for parseProps function
 * @param filePath Path to the component types file
 * @param componentName Name of the component
 * @returns Array of PropDetail objects
 */
export function parseProps(filePath: string, componentName: string): PropDetail[] {
  return parserFactory.parseProps(filePath, componentName);
}

/**
 * Legacy adapter for parseComponent function
 * @param filePath Path to the component types file
 * @param componentName Name of the component
 * @returns ParsedComponent object with props, slots, and classes
 */
export function parseComponent(filePath: string, componentName: string): ParsedComponent {
  return parserFactory.parseComponent(filePath, componentName);
}

/**
 * Legacy adapter for findClassesFile function
 * @param filePath Path to the component types file
 * @param componentName Name of the component
 * @returns Path to the classes file or null if not found
 */
export function findClassesFile(filePath: string, componentName: string): string | null {
  const componentDir = path.dirname(filePath);
  const classesFile = path.join(componentDir, `${componentName}.classes.ts`);
  return fs.existsSync(classesFile) ? classesFile : null;
}

/**
 * Legacy adapter for parseClassesFile function
 * @param filePath Path to the component types file
 * @param componentName Name of the component
 * @returns Array of ClassDetail objects
 */
export function parseClassesFile(filePath: string, componentName: string): ClassDetail[] {
  return parserFactory.parseClasses(filePath, componentName);
}
