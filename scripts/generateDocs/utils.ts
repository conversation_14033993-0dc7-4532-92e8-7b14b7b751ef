/* eslint-disable @typescript-eslint/no-explicit-any */
import ts from 'typescript';

// Utility functions for JSDoc parsing
export function parseJSDocComment(comment: string): {
  description: string;
  tags: {
    params?: Record<string, { type: string; description: string }>;
    [key: string]: any;
  };
} {
  const description: string[] = [];
  const tags: { [key: string]: any } = {};
  let currentTag: string | null = null;
  let currentTagContent: string[] = [];

  if (!comment) {
    return { description: '', tags: {} };
  }

  const lines = comment
    .replace(/\/\*\*|\*\//g, '')
    .split('\n')
    .map((line) => line.replace(/^\s*\*\s?/, '').trim());

  for (const line of lines) {
    if (!line) continue;

    // Parse @param tags with type information
    const paramMatch = line.match(/@param\s+{([^}]+)}\s+(\w+)\s*(.*)/);
    if (paramMatch) {
      const [, type, name, desc] = paramMatch;
      if (!tags.params) {
        tags.params = {};
      }
      tags.params[name] = {
        type: type.trim(),
        description: desc.trim(),
      };
      continue;
    }

    // Handle other tags
    const tagMatch = line.match(/@(\w+)\s*(.*)/);
    if (tagMatch) {
      if (currentTag && currentTagContent.length) {
        if (currentTag === 'params') {
          // Skip params as they're handled separately
          continue;
        }
        tags[currentTag] = currentTagContent.join(' ').trim();
      }
      currentTag = tagMatch[1];
      currentTagContent = [tagMatch[2]];
    } else if (currentTag) {
      currentTagContent.push(line);
    } else {
      description.push(line);
    }
  }

  // Handle last tag
  if (currentTag && currentTagContent.length && currentTag !== 'params') {
    tags[currentTag] = currentTagContent.join(' ').trim();
  }

  // Return without stringifying the params
  return {
    description: description.join('\n').trim(),
    tags: {
      ...Object.fromEntries(
        Object.entries(tags)
          .filter(([key]) => key !== 'params')
          .map(([key, value]) => [key, typeof value === 'string' ? value.trim() : value]),
      ),
      ...(tags.params && { params: tags.params }),
    },
  };
}

export function getNodeComments(node: ts.Node, sourceFile: ts.SourceFile): string {
  const fullStart = node.getFullStart();
  const commentRanges = ts.getLeadingCommentRanges(sourceFile.text, fullStart);

  if (!commentRanges?.length) return '';

  return commentRanges
    .map((range) => sourceFile.text.slice(range.pos, range.end))
    .filter((comment) => comment.startsWith('/**'))
    .join('\n');
}
